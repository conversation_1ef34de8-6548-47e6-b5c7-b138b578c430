package logger

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"bdb-backend/pkg/config"

	"github.com/rs/zerolog"
	"gopkg.in/natefinch/lumberjack.v2"
)

var (
	Log zerolog.Logger

	// 中国时区
	chinaLocation *time.Location
)

// Init initializes the logger with the given configuration.
func Init(cfg config.LoggerConfig, appMode string) {
	// 初始化中国时区
	var err error
	chinaLocation, err = time.LoadLocation("Asia/Shanghai")
	if err != nil {
		fmt.Printf("Warning: Failed to load China timezone, using UTC: %v\n", err)
		chinaLocation = time.UTC
	}

	var writers []io.Writer

	// Configure console writer for development
	if appMode == "debug" {
		writers = append(writers, zerolog.NewConsoleWriter(func(w *zerolog.ConsoleWriter) {
			w.Out = os.Stderr
			w.TimeFormat = "2006-01-02 15:04:05"
			w.FormatTimestamp = func(i interface{}) string {
				if t, ok := i.(string); ok {
					if parsed, err := time.Parse(time.RFC3339, t); err == nil {
						return parsed.In(chinaLocation).Format("2006-01-02 15:04:05")
					}
				}
				return fmt.Sprintf("%v", i)
			}
		}))
	}

	// Configure file writer for all environments
	fileLogger := &lumberjack.Logger{
		Filename:   getLogFile(cfg.OutputPaths),
		MaxSize:    cfg.Rotation.MaxSize,
		MaxBackups: cfg.Rotation.MaxBackups,
		MaxAge:     cfg.Rotation.MaxAge,
		Compress:   cfg.Rotation.Compress,
	}
	writers = append(writers, fileLogger)

	// Create a multi-level writer
	mw := io.MultiWriter(writers...)

	// Set log level
	level, err := zerolog.ParseLevel(cfg.Level)
	if err != nil {
		level = zerolog.InfoLevel
	}

	// 设置全局时间格式为中国时间
	zerolog.TimeFieldFormat = time.RFC3339
	zerolog.TimestampFunc = func() time.Time {
		return time.Now().In(chinaLocation)
	}

	// Create the logger with enhanced context
	Log = zerolog.New(mw).
		Level(level).
		With().
		Timestamp().
		Str("service", "bdb-backend").
		Logger().
		Hook(LocationHook{})
}

// LocationHook 用于添加代码调用位置信息
type LocationHook struct{}

func (h LocationHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	// 只在错误和致命错误级别添加详细的调用栈信息
	if level >= zerolog.ErrorLevel {
		if pc, file, line, ok := runtime.Caller(4); ok { // 4层调用栈
			funcName := runtime.FuncForPC(pc).Name()
			// 简化文件路径，只保留相对路径
			if idx := strings.LastIndex(file, "/"); idx >= 0 {
				file = file[idx+1:]
			}
			e.Str("caller", fmt.Sprintf("%s:%d", file, line))
			e.Str("func", funcName)
		}
	}
}

// getLogFile 获取日志文件路径，使用app-2025-03-19格式
func getLogFile(paths []string) string {
	baseDir := "./logs"

	// 从配置的路径中提取目录
	for _, p := range paths {
		if !strings.Contains(p, "stdout") && !strings.Contains(p, "stderr") {
			baseDir = filepath.Dir(p)
			break
		}
	}

	// 确保日志目录存在
	if err := os.MkdirAll(baseDir, 0750); err != nil {
		fmt.Printf("Failed to create log directory: %v\n", err)
	}

	// 使用中国时间生成格式为 app-2025-03-19.log 的文件名
	var today string
	if chinaLocation != nil {
		today = time.Now().In(chinaLocation).Format("2006-01-02")
	} else {
		today = time.Now().Format("2006-01-02")
	}
	filename := fmt.Sprintf("app-%s.log", today)

	return filepath.Join(baseDir, filename)
}

// ============= 基础日志方法 =============

// Debug 记录调试信息
func Debug(message string, fields ...interface{}) {
	logWithFields(Log.Debug(), message, fields...)
}

// Info 记录一般信息
func Info(message string, fields ...interface{}) {
	logWithFields(Log.Info(), message, fields...)
}

// Warn 记录警告信息
func Warn(message string, fields ...interface{}) {
	logWithFields(Log.Warn(), message, fields...)
}

// Error 记录错误信息
func Error(message string, err error, fields ...interface{}) {
	event := Log.Error()
	if err != nil {
		event = event.Err(err)
	}
	logWithFields(event, message, fields...)
}

// Fatal 记录致命错误并退出程序
func Fatal(message string, err error, fields ...interface{}) {
	event := Log.Fatal()
	if err != nil {
		event = event.Err(err)
	}
	logWithFields(event, message, fields...)
}

// ============= 带上下文的日志方法 =============

// DebugCtx 带上下文的调试日志
func DebugCtx(ctx context.Context, message string, fields ...interface{}) {
	logWithContext(ctx, Log.Debug(), message, fields...)
}

// InfoCtx 带上下文的信息日志
func InfoCtx(ctx context.Context, message string, fields ...interface{}) {
	logWithContext(ctx, Log.Info(), message, fields...)
}

// WarnCtx 带上下文的警告日志
func WarnCtx(ctx context.Context, message string, fields ...interface{}) {
	logWithContext(ctx, Log.Warn(), message, fields...)
}

// ErrorCtx 带上下文的错误日志
func ErrorCtx(ctx context.Context, message string, err error, fields ...interface{}) {
	event := Log.Error()
	if err != nil {
		event = event.Err(err)
	}
	logWithContext(ctx, event, message, fields...)
}

// ============= 辅助方法 =============

// logWithFields 处理字段参数的通用方法
func logWithFields(event *zerolog.Event, message string, fields ...interface{}) {
	// 处理fields参数，支持key-value对
	for i := 0; i < len(fields)-1; i += 2 {
		key, ok := fields[i].(string)
		if !ok {
			continue
		}
		value := fields[i+1]
		event = event.Interface(key, value)
	}
	event.Msg(message)
}

// logWithContext 带上下文的日志记录
func logWithContext(ctx context.Context, event *zerolog.Event, message string, fields ...interface{}) {
	event = event.Ctx(ctx)

	// 尝试从上下文中提取请求ID等信息
	if requestID := ctx.Value("request_id"); requestID != nil {
		event = event.Interface("request_id", requestID)
	}

	if userID := ctx.Value("user_id"); userID != nil {
		event = event.Interface("user_id", userID)
	}

	if sessionID := ctx.Value("session_id"); sessionID != nil {
		event = event.Interface("session_id", sessionID)
	}

	if deviceID := ctx.Value("device_id"); deviceID != nil {
		event = event.Interface("device_id", deviceID)
	}

	logWithFields(event, message, fields...)
}

// ============= 业务级别日志方法 =============

// LogBusinessEvent 记录业务事件（用户登录、订单创建等重要业务操作）
func LogBusinessEvent(ctx context.Context, event string, fields ...interface{}) {
	allFields := append([]interface{}{"event_type", "business", "event", event}, fields...)
	InfoCtx(ctx, "Business event", allFields...)
}

// LogSecurityEvent 记录安全事件（登录失败、权限错误等）
func LogSecurityEvent(ctx context.Context, event string, fields ...interface{}) {
	allFields := append([]interface{}{"event_type", "security", "event", event}, fields...)
	WarnCtx(ctx, "Security event", allFields...)
}

// LogPerformanceEvent 记录性能事件（慢查询、高延迟等）
func LogPerformanceEvent(ctx context.Context, operation string, duration time.Duration, fields ...interface{}) {
	allFields := append([]interface{}{
		"event_type", "performance",
		"operation", operation,
		"duration_ms", duration.Milliseconds(),
	}, fields...)

	if duration > time.Second {
		WarnCtx(ctx, "Slow operation detected", allFields...)
	} else {
		InfoCtx(ctx, "Performance metric", allFields...)
	}
}

// LogAPICall 记录API调用
func LogAPICall(ctx context.Context, method, path string, statusCode int, duration time.Duration, fields ...interface{}) {
	allFields := append([]interface{}{
		"event_type", "api_call",
		"method", method,
		"path", path,
		"status_code", statusCode,
		"duration_ms", duration.Milliseconds(),
	}, fields...)

	var level zerolog.Level
	if statusCode >= 500 {
		level = zerolog.ErrorLevel
	} else if statusCode >= 400 {
		level = zerolog.WarnLevel
	} else {
		level = zerolog.InfoLevel
	}

	event := Log.WithLevel(level).Ctx(ctx)
	logWithFields(event, "API call", allFields...)
}

// LogDatabaseOperation 记录数据库操作
func LogDatabaseOperation(ctx context.Context, operation string, table string, duration time.Duration, err error, fields ...interface{}) {
	allFields := append([]interface{}{
		"event_type", "database",
		"operation", operation,
		"table", table,
		"duration_ms", duration.Milliseconds(),
	}, fields...)

	if err != nil {
		allFields = append(allFields, "error", err.Error())
		ErrorCtx(ctx, "Database operation failed", err, allFields...)
	} else if duration > 500*time.Millisecond {
		WarnCtx(ctx, "Slow database operation", allFields...)
	} else {
		DebugCtx(ctx, "Database operation", allFields...)
	}
}

package storage

import (
	"io"
	"time"
)

// Storage 存储服务接口
type Storage interface {
	// UploadFile 上传文件
	UploadFile(key string, data io.Reader, size int64) (*UploadResult, error)

	// UploadFileWithMimeType 上传文件（指定MIME类型）
	UploadFileWithMimeType(key string, data io.Reader, size int64, mimeType string) (*UploadResult, error)

	// DeleteFile 删除文件
	DeleteFile(key string) error

	// GetFileInfo 获取文件信息
	GetFileInfo(key string) (*FileInfo, error)

	// GenerateUploadToken 生成上传令牌
	GenerateUploadToken(key string, expires time.Duration) (string, error)

	// GenerateDownloadURL 生成下载URL
	GenerateDownloadURL(key string, expires time.Duration) (string, error)

	// ListFiles 列出文件
	ListFiles(prefix string, limit int) ([]*FileInfo, error)
}

// UploadResult 上传结果
type UploadResult struct {
	Key      string `json:"key"`      // 文件键名
	Hash     string `json:"hash"`     // 文件哈希值
	Size     int64  `json:"size"`     // 文件大小
	MimeType string `json:"mimeType"` // MIME类型
	URL      string `json:"url"`      // 访问URL
}

// FileInfo 文件信息
type FileInfo struct {
	Key        string    `json:"key"`        // 文件键名
	Hash       string    `json:"hash"`       // 文件哈希值
	Size       int64     `json:"size"`       // 文件大小
	MimeType   string    `json:"mimeType"`   // MIME类型
	PutTime    time.Time `json:"putTime"`    // 上传时间
	ModifyTime time.Time `json:"modifyTime"` // 修改时间
	URL        string    `json:"url"`        // 访问URL
}

// UploadPolicy 上传策略
type UploadPolicy struct {
	Scope               string `json:"scope"`                         // 指定上传的目标资源空间Bucket和资源键Key
	Deadline            int64  `json:"deadline"`                      // 上传凭证有效截止时间
	InsertOnly          int    `json:"insertOnly,omitempty"`          // 限制为"新增"语义
	SaveKey             string `json:"saveKey,omitempty"`             // 自定义资源名
	EndUser             string `json:"endUser,omitempty"`             // 唯一属主标识
	ReturnURL           string `json:"returnUrl,omitempty"`           // Web端文件上传成功后，浏览器执行303跳转的URL
	ReturnBody          string `json:"returnBody,omitempty"`          // 上传成功后，自定义七牛云最终返回給上传端的数据
	CallbackURL         string `json:"callbackUrl,omitempty"`         // 上传成功后，七牛云向业务服务器发送POST请求的URL
	CallbackBody        string `json:"callbackBody,omitempty"`        // 上传成功后，七牛云向业务服务器发送Content-Type为application/x-www-form-urlencoded的POST请求
	CallbackBodyType    string `json:"callbackBodyType,omitempty"`    // 上传成功后，七牛云向业务服务器发送回调通知callbackBody的Content-Type
	PersistentOps       string `json:"persistentOps,omitempty"`       // 资源上传成功后触发执行的预转持久化处理指令列表
	PersistentNotifyURL string `json:"persistentNotifyUrl,omitempty"` // 接收持久化处理结果通知的URL
	PersistentPipeline  string `json:"persistentPipeline,omitempty"`  // 转码队列名
	FsizeMin            int64  `json:"fsizeMin,omitempty"`            // 限制上传文件大小最小值，单位为字节
	FsizeLimit          int64  `json:"fsizeLimit,omitempty"`          // 限制上传文件大小最大值，单位为字节
	DetectMime          int    `json:"detectMime,omitempty"`          // 开启MimeType侦测功能
	MimeLimit           string `json:"mimeLimit,omitempty"`           // 限制上传文件类型
}

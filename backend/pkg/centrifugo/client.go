package centrifugo

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"bdb-backend/internal/api/apiproto"
	"bdb-backend/pkg/config"

	"github.com/golang-jwt/jwt/v5"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
)

// Client Centrifugo gRPC客户端
type Client struct {
	config     *config.CentrifugoConfig
	conn       *grpc.ClientConn
	grpcClient apiproto.CentrifugoApiClient
}

// NewClient 创建新的Centrifugo客户端
func NewClient(cfg *config.Config) (*Client, error) {
	client := &Client{
		config: &cfg.Centrifugo,
	}

	if err := client.connect(); err != nil {
		return nil, fmt.Errorf("failed to connect to centrifugo: %w", err)
	}

	return client, nil
}

// connect 连接到Centrifugo gRPC服务器
func (c *Client) connect() error {
	// 构建连接地址
	address := fmt.Sprintf("localhost:%d", c.config.GRPCPort)

	// 创建gRPC连接
	conn, err := grpc.NewClient(address, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return fmt.Errorf("failed to create grpc connection: %w", err)
	}

	c.conn = conn
	c.grpcClient = apiproto.NewCentrifugoApiClient(conn)

	return nil
}

// Close 关闭连接
func (c *Client) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}

// getAuthContext 获取带认证信息的上下文
func (c *Client) getAuthContext(ctx context.Context) context.Context {
	if c.config.APIKey != "" {
		md := metadata.New(map[string]string{
			"authorization": "apikey " + c.config.APIKey,
		})
		return metadata.NewOutgoingContext(ctx, md)
	}
	return ctx
}

// PublishMessage 发布消息到指定频道
type PublishMessage struct {
	Channel        string            `json:"channel"`
	Data           interface{}       `json:"data"`
	SkipHistory    bool              `json:"skip_history,omitempty"`
	Tags           map[string]string `json:"tags,omitempty"`
	IdempotencyKey string            `json:"idempotency_key,omitempty"`
}

// PublishResult 发布结果
type PublishResult struct {
	Offset uint64 `json:"offset"`
	Epoch  string `json:"epoch"`
}

// Publish 发布消息
func (c *Client) Publish(ctx context.Context, msg *PublishMessage) (*PublishResult, error) {
	// 序列化数据
	data, err := json.Marshal(msg.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal data: %w", err)
	}

	// 构建请求
	req := &apiproto.PublishRequest{
		Channel:        msg.Channel,
		Data:           data,
		SkipHistory:    msg.SkipHistory,
		Tags:           msg.Tags,
		IdempotencyKey: msg.IdempotencyKey,
	}

	// 调用gRPC接口
	authCtx := c.getAuthContext(ctx)
	resp, err := c.grpcClient.Publish(authCtx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to publish message: %w", err)
	}

	// 检查错误
	if resp.Error != nil {
		return nil, fmt.Errorf("centrifugo error: %s (code: %d)", resp.Error.Message, resp.Error.Code)
	}

	return &PublishResult{
		Offset: resp.Result.Offset,
		Epoch:  resp.Result.Epoch,
	}, nil
}

// BroadcastMessage 广播消息到多个频道
type BroadcastMessage struct {
	Channels       []string          `json:"channels"`
	Data           interface{}       `json:"data"`
	SkipHistory    bool              `json:"skip_history,omitempty"`
	Tags           map[string]string `json:"tags,omitempty"`
	IdempotencyKey string            `json:"idempotency_key,omitempty"`
}

// Broadcast 广播消息
func (c *Client) Broadcast(ctx context.Context, msg *BroadcastMessage) error {
	// 序列化数据
	data, err := json.Marshal(msg.Data)
	if err != nil {
		return fmt.Errorf("failed to marshal data: %w", err)
	}

	// 构建请求
	req := &apiproto.BroadcastRequest{
		Channels:       msg.Channels,
		Data:           data,
		SkipHistory:    msg.SkipHistory,
		Tags:           msg.Tags,
		IdempotencyKey: msg.IdempotencyKey,
	}

	// 调用gRPC接口
	authCtx := c.getAuthContext(ctx)
	resp, err := c.grpcClient.Broadcast(authCtx, req)
	if err != nil {
		return fmt.Errorf("failed to broadcast message: %w", err)
	}

	// 检查错误
	if resp.Error != nil {
		return fmt.Errorf("centrifugo error: %s (code: %d)", resp.Error.Message, resp.Error.Code)
	}

	return nil
}

// Subscribe 订阅用户到频道
func (c *Client) Subscribe(ctx context.Context, channel, user string, info interface{}) error {
	var infoData []byte
	var err error

	if info != nil {
		infoData, err = json.Marshal(info)
		if err != nil {
			return fmt.Errorf("failed to marshal info: %w", err)
		}
	}

	req := &apiproto.SubscribeRequest{
		Channel: channel,
		User:    user,
		Info:    infoData,
	}

	authCtx := c.getAuthContext(ctx)
	resp, err := c.grpcClient.Subscribe(authCtx, req)
	if err != nil {
		return fmt.Errorf("failed to subscribe: %w", err)
	}

	if resp.Error != nil {
		return fmt.Errorf("centrifugo error: %s (code: %d)", resp.Error.Message, resp.Error.Code)
	}

	return nil
}

// Unsubscribe 取消用户订阅
func (c *Client) Unsubscribe(ctx context.Context, channel, user string) error {
	req := &apiproto.UnsubscribeRequest{
		Channel: channel,
		User:    user,
	}

	authCtx := c.getAuthContext(ctx)
	resp, err := c.grpcClient.Unsubscribe(authCtx, req)
	if err != nil {
		return fmt.Errorf("failed to unsubscribe: %w", err)
	}

	if resp.Error != nil {
		return fmt.Errorf("centrifugo error: %s (code: %d)", resp.Error.Message, resp.Error.Code)
	}

	return nil
}

// Disconnect 断开用户连接
func (c *Client) Disconnect(ctx context.Context, user string) error {
	req := &apiproto.DisconnectRequest{
		User: user,
	}

	authCtx := c.getAuthContext(ctx)
	resp, err := c.grpcClient.Disconnect(authCtx, req)
	if err != nil {
		return fmt.Errorf("failed to disconnect user: %w", err)
	}

	if resp.Error != nil {
		return fmt.Errorf("centrifugo error: %s (code: %d)", resp.Error.Message, resp.Error.Code)
	}

	return nil
}

// PresenceInfo 在线状态信息
type PresenceInfo struct {
	Client string      `json:"client"`
	User   string      `json:"user"`
	ConnID string      `json:"conn_id"`
	Info   interface{} `json:"info"`
}

// GetPresence 获取频道在线用户
func (c *Client) GetPresence(ctx context.Context, channel string) ([]*PresenceInfo, error) {
	req := &apiproto.PresenceRequest{
		Channel: channel,
	}

	authCtx := c.getAuthContext(ctx)
	resp, err := c.grpcClient.Presence(authCtx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get presence: %w", err)
	}

	if resp.Error != nil {
		return nil, fmt.Errorf("centrifugo error: %s (code: %d)", resp.Error.Message, resp.Error.Code)
	}

	var result []*PresenceInfo
	for _, presence := range resp.Result.Presence {
		var info interface{}
		if len(presence.ConnInfo) > 0 {
			if err := json.Unmarshal(presence.ConnInfo, &info); err != nil {
				log.Printf("Failed to unmarshal presence info: %v", err)
			}
		}

		result = append(result, &PresenceInfo{
			Client: presence.Client,
			User:   presence.User,
			ConnID: presence.Client, // 使用Client作为ConnID
			Info:   info,
		})
	}

	return result, nil
}

// PresenceStats 在线状态统计
type PresenceStats struct {
	NumClients int `json:"num_clients"`
	NumUsers   int `json:"num_users"`
}

// GetPresenceStats 获取频道在线统计
func (c *Client) GetPresenceStats(ctx context.Context, channel string) (*PresenceStats, error) {
	req := &apiproto.PresenceStatsRequest{
		Channel: channel,
	}

	authCtx := c.getAuthContext(ctx)
	resp, err := c.grpcClient.PresenceStats(authCtx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get presence stats: %w", err)
	}

	if resp.Error != nil {
		return nil, fmt.Errorf("centrifugo error: %s (code: %d)", resp.Error.Message, resp.Error.Code)
	}

	return &PresenceStats{
		NumClients: int(resp.Result.NumClients),
		NumUsers:   int(resp.Result.NumUsers),
	}, nil
}

// HistoryMessage 历史消息
type HistoryMessage struct {
	Data   interface{}       `json:"data"`
	Info   interface{}       `json:"info"`
	Offset uint64            `json:"offset"`
	Tags   map[string]string `json:"tags"`
}

// HistoryResult 历史消息结果
type HistoryResult struct {
	Publications []*HistoryMessage `json:"publications"`
	Offset       uint64            `json:"offset"`
	Epoch        string            `json:"epoch"`
}

// GetHistory 获取频道历史消息
func (c *Client) GetHistory(ctx context.Context, channel string, limit int32, since *uint64, reverse bool) (*HistoryResult, error) {
	req := &apiproto.HistoryRequest{
		Channel: channel,
		Limit:   limit,
		Reverse: reverse,
	}

	if since != nil {
		req.Since = &apiproto.StreamPosition{
			Offset: *since,
		}
	}

	authCtx := c.getAuthContext(ctx)
	resp, err := c.grpcClient.History(authCtx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get history: %w", err)
	}

	if resp.Error != nil {
		return nil, fmt.Errorf("centrifugo error: %s (code: %d)", resp.Error.Message, resp.Error.Code)
	}

	var publications []*HistoryMessage
	for _, pub := range resp.Result.Publications {
		var data, info interface{}

		if len(pub.Data) > 0 {
			if err := json.Unmarshal(pub.Data, &data); err != nil {
				log.Printf("Failed to unmarshal publication data: %v", err)
			}
		}

		// pub.Info 是 *ClientInfo 类型，处理连接信息
		if pub.Info != nil {
			clientInfo := map[string]interface{}{
				"user":   pub.Info.User,
				"client": pub.Info.Client,
			}
			if len(pub.Info.ConnInfo) > 0 {
				var connInfo interface{}
				if err := json.Unmarshal(pub.Info.ConnInfo, &connInfo); err == nil {
					clientInfo["conn_info"] = connInfo
				}
			}
			if len(pub.Info.ChanInfo) > 0 {
				var chanInfo interface{}
				if err := json.Unmarshal(pub.Info.ChanInfo, &chanInfo); err == nil {
					clientInfo["chan_info"] = chanInfo
				}
			}
			info = clientInfo
		}

		publications = append(publications, &HistoryMessage{
			Data:   data,
			Info:   info,
			Offset: pub.Offset,
			Tags:   pub.Tags,
		})
	}

	return &HistoryResult{
		Publications: publications,
		Offset:       resp.Result.Offset,
		Epoch:        resp.Result.Epoch,
	}, nil
}

// RemoveHistory 删除频道历史消息
func (c *Client) RemoveHistory(ctx context.Context, channel string) error {
	req := &apiproto.HistoryRemoveRequest{
		Channel: channel,
	}

	authCtx := c.getAuthContext(ctx)
	resp, err := c.grpcClient.HistoryRemove(authCtx, req)
	if err != nil {
		return fmt.Errorf("failed to remove history: %w", err)
	}

	if resp.Error != nil {
		return fmt.Errorf("centrifugo error: %s (code: %d)", resp.Error.Message, resp.Error.Code)
	}

	return nil
}

// JWT Token Claims 用于Centrifugo连接认证
type TokenClaims struct {
	Sub      string            `json:"sub"`      // 用户ID
	Exp      int64             `json:"exp"`      // 过期时间
	Iat      int64             `json:"iat"`      // 签发时间
	Info     interface{}       `json:"info"`     // 用户信息
	B64Info  string            `json:"b64info"`  // base64编码的用户信息
	Subs     map[string]string `json:"subs"`     // 订阅权限
	Channels []string          `json:"channels"` // 可访问的频道列表
	jwt.RegisteredClaims
}

// GenerateToken 生成JWT连接令牌
func (c *Client) GenerateToken(userID string, ttl time.Duration, info interface{}, channels []string) (string, error) {
	now := time.Now()
	claims := &TokenClaims{
		Sub:      userID,
		Exp:      now.Add(ttl).Unix(),
		Iat:      now.Unix(),
		Info:     info,
		Channels: channels,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(c.config.HMACSecret))
}

// GenerateChannelToken 生成频道订阅令牌
func (c *Client) GenerateChannelToken(userID, channel string, ttl time.Duration, info interface{}) (string, error) {
	now := time.Now()
	claims := jwt.MapClaims{
		"sub":     userID,
		"channel": channel,
		"exp":     now.Add(ttl).Unix(),
		"iat":     now.Unix(),
	}

	if info != nil {
		claims["info"] = info
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(c.config.HMACSecret))
}

// ValidateToken 验证JWT令牌
func (c *Client) ValidateToken(tokenString string) (*TokenClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &TokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(c.config.HMACSecret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*TokenClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// GetChannelName 生成标准化的频道名称
func GetChannelName(prefix string, id interface{}) string {
	return fmt.Sprintf("%s:%v", prefix, id)
}

// 常用频道前缀
const (
	ChannelPrefixChatRoom     = "chat"         // 聊天室频道
	ChannelPrefixPrivateChat  = "private"      // 私聊频道
	ChannelPrefixUserStatus   = "user_status"  // 用户状态频道
	ChannelPrefixNotification = "notification" // 通知频道
	ChannelPrefixSystem       = "system"       // 系统频道
)

// GetChatRoomChannel 获取聊天室频道名称
func GetChatRoomChannel(roomID uint64) string {
	return GetChannelName(ChannelPrefixChatRoom, roomID)
}

// GetPrivateChatChannel 获取私聊频道名称 (确保用户ID顺序一致)
func GetPrivateChatChannel(userID1, userID2 uint64) string {
	if userID1 > userID2 {
		userID1, userID2 = userID2, userID1
	}
	return GetChannelName(ChannelPrefixPrivateChat, fmt.Sprintf("%d_%d", userID1, userID2))
}

// GetUserStatusChannel 获取用户状态频道名称
func GetUserStatusChannel(userID uint64) string {
	return GetChannelName(ChannelPrefixUserStatus, userID)
}

// GetNotificationChannel 获取通知频道名称
func GetNotificationChannel(userID uint64) string {
	return GetChannelName(ChannelPrefixNotification, userID)
}

// GetSystemChannel 获取系统频道名称
func GetSystemChannel() string {
	return ChannelPrefixSystem
}

package response

// 业务状态码常量
// 与前端保持一致: 0=成功, 1=错误
const (
	CodeSuccess = 0 // 成功
	CodeError   = 1 // 错误
)

// HTTP状态码常量
const (
	HTTPStatusOK                  = 200
	HTTPStatusBadRequest          = 400
	HTTPStatusUnauthorized        = 401
	HTTPStatusForbidden           = 403
	HTTPStatusNotFound            = 404
	HTTPStatusMethodNotAllowed    = 405
	HTTPStatusTooManyRequests     = 429
	HTTPStatusInternalServerError = 500
	HTTPStatusBadGateway          = 502
	HTTPStatusServiceUnavailable  = 503
)

// 响应提示语常量
const (
	MessageSuccess = "操作成功"
	MessageError   = "操作失败"

	// 网络相关
	MessageNetworkError   = "网络错误，请检查网络连接"
	MessageRequestTimeout = "请求超时，请稍后重试"

	// 认证相关
	MessageUnauthorized = "登录已过期，请重新登录"
	MessageTokenInvalid = "Token无效，请重新登录"
	MessageForbidden    = "您没有权限访问该资源"

	// 请求相关
	MessageBadRequest       = "请求参数错误"
	MessageNotFound         = "接口不存在"
	MessageMethodNotAllowed = "请求方法不允许"
	MessageTooManyRequests  = "请求过于频繁，请稍后重试"

	// 服务器相关
	MessageServerError        = "服务器错误，请稍后重试"
	MessageServiceUnavailable = "服务暂时不可用，请稍后重试"
)

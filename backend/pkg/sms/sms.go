package sms

import "time"

// SMSService 短信服务接口
type SMSService interface {
	// SendVerificationCode 发送验证码
	SendVerificationCode(phone, code string) error

	// SendTemplate 发送模板短信
	SendTemplate(phone, templateCode string, params map[string]string) error

	// SendBatch 批量发送短信
	SendBatch(phones []string, templateCode string, params []map[string]string) error

	// QuerySendDetails 查询发送详情
	QuerySendDetails(phone string, bizId string, sendDate time.Time, pageSize int64, currentPage int64) (*QueryResult, error)
}

// SendResult 发送结果
type SendResult struct {
	BizId     string `json:"bizId"`     // 发送回执ID
	Code      string `json:"code"`      // 状态码
	Message   string `json:"message"`   // 状态码的描述
	RequestId string `json:"requestId"` // 请求ID
}

// QueryResult 查询结果
type QueryResult struct {
	TotalCount        int64            `json:"totalCount"`        // 短信发送总条数
	SmsSendDetailDTOs []*SMSSendDetail `json:"smsSendDetailDTOs"` // 短信发送明细
}

// SMSSendDetail 短信发送明细
type SMSSendDetail struct {
	PhoneNum     string    `json:"phoneNum"`     // 接收短信的手机号码
	SendStatus   int64     `json:"sendStatus"`   // 短信发送状态。1：等待回执，2：发送失败，3：发送成功
	ErrCode      string    `json:"errCode"`      // 运营商短信状态码
	TemplateCode string    `json:"templateCode"` // 短信模板ID
	Content      string    `json:"content"`      // 短信内容
	SendDate     time.Time `json:"sendDate"`     // 短信发送日期和时间
	ReceiveDate  time.Time `json:"receiveDate"`  // 短信接收日期和时间
	OutId        string    `json:"outId"`        // 外部流水扩展字段
}

// TemplateParam 模板参数
type TemplateParam struct {
	Code string `json:"code"` // 验证码
	Name string `json:"name"` // 姓名
	Time string `json:"time"` // 时间
	// 可以根据需要添加更多参数
}

// SMSConfig 短信配置
type SMSConfig struct {
	AccessKeyId     string `json:"accessKeyId"`
	AccessKeySecret string `json:"accessKeySecret"`
	SignName        string `json:"signName"`
	RegionId        string `json:"regionId"`
	EndpointName    string `json:"endpointName"`
	Product         string `json:"product"`
	Domain          string `json:"domain"`
}

// VerificationCodeTemplate 验证码模板常量
const (
	// 登录验证码模板
	LoginVerificationTemplate = "SMS_123456789"
	// 注册验证码模板
	RegisterVerificationTemplate = "SMS_123456790"
	// 找回密码验证码模板
	ResetPasswordVerificationTemplate = "SMS_123456791"
	// 绑定手机号验证码模板
	BindPhoneVerificationTemplate = "SMS_123456792"
)

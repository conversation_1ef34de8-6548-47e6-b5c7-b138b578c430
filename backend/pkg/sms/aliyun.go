package sms

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"bdb-backend/pkg/config"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/dysmsapi"
)

// AliyunSMS 阿里云短信服务
type AliyunSMS struct {
	client   *dysmsapi.Client
	signName string
}

// NewAliyunSMS 创建阿里云短信服务实例
func NewAliyunSMS(config config.AliyunSMSConfig) (SMSService, error) {
	client, err := dysmsapi.NewClientWithAccessKey("cn-hangzhou", config.AccessKey, config.SecretKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create aliyun sms client: %w", err)
	}

	return &AliyunSMS{
		client:   client,
		signName: config.SignName,
	}, nil
}

// SendVerificationCode 发送验证码
func (a *AliyunSMS) SendVerificationCode(phone, code string) error {
	// 使用默认的验证码模板
	templateCode := LoginVerificationTemplate
	params := map[string]string{
		"code": code,
	}

	return a.SendTemplate(phone, templateCode, params)
}

// SendTemplate 发送模板短信
func (a *AliyunSMS) SendTemplate(phone, templateCode string, params map[string]string) error {
	request := dysmsapi.CreateSendSmsRequest()
	request.Scheme = "https"
	request.PhoneNumbers = phone
	request.SignName = a.signName
	request.TemplateCode = templateCode

	// 将参数转换为JSON字符串
	if params != nil {
		templateParam, err := json.Marshal(params)
		if err != nil {
			return fmt.Errorf("failed to marshal template params: %w", err)
		}
		request.TemplateParam = string(templateParam)
	}

	response, err := a.client.SendSms(request)
	if err != nil {
		return fmt.Errorf("failed to send sms: %w", err)
	}

	if response.Code != "OK" {
		return fmt.Errorf("sms send failed: code=%s, message=%s", response.Code, response.Message)
	}

	return nil
}

// SendBatch 批量发送短信
func (a *AliyunSMS) SendBatch(phones []string, templateCode string, params []map[string]string) error {
	request := dysmsapi.CreateSendBatchSmsRequest()
	request.Scheme = "https"

	// 转换手机号数组为JSON字符串
	phoneNumbersJson, err := json.Marshal(phones)
	if err != nil {
		return fmt.Errorf("failed to marshal phone numbers: %w", err)
	}
	request.PhoneNumberJson = string(phoneNumbersJson)

	// 签名名称数组，这里使用相同的签名
	signNames := make([]string, len(phones))
	for i := range signNames {
		signNames[i] = a.signName
	}
	signNamesJson, err := json.Marshal(signNames)
	if err != nil {
		return fmt.Errorf("failed to marshal sign names: %w", err)
	}
	request.SignNameJson = string(signNamesJson)

	// 模板代码
	request.TemplateCode = templateCode

	// 模板参数
	if params != nil {
		templateParamJson, err := json.Marshal(params)
		if err != nil {
			return fmt.Errorf("failed to marshal template params: %w", err)
		}
		request.TemplateParamJson = string(templateParamJson)
	}

	response, err := a.client.SendBatchSms(request)
	if err != nil {
		return fmt.Errorf("failed to send batch sms: %w", err)
	}

	if response.Code != "OK" {
		return fmt.Errorf("batch sms send failed: code=%s, message=%s", response.Code, response.Message)
	}

	return nil
}

// QuerySendDetails 查询发送详情
func (a *AliyunSMS) QuerySendDetails(phone string, bizId string, sendDate time.Time, pageSize int64, currentPage int64) (*QueryResult, error) {
	request := dysmsapi.CreateQuerySendDetailsRequest()
	request.Scheme = "https"
	request.PhoneNumber = phone
	request.BizId = bizId
	request.SendDate = sendDate.Format("20060102")
	request.PageSize = requests.NewInteger(int(pageSize))
	request.CurrentPage = requests.NewInteger(int(currentPage))

	response, err := a.client.QuerySendDetails(request)
	if err != nil {
		return nil, fmt.Errorf("failed to query send details: %w", err)
	}

	if response.Code != "OK" {
		return nil, fmt.Errorf("query send details failed: code=%s, message=%s", response.Code, response.Message)
	}

	// 转换响应数据
	var details []*SMSSendDetail
	for _, detail := range response.SmsSendDetailDTOs.SmsSendDetailDTO {
		sendDate, _ := time.Parse("2006-01-02 15:04:05", detail.SendDate)
		receiveDate, _ := time.Parse("2006-01-02 15:04:05", detail.ReceiveDate)

		details = append(details, &SMSSendDetail{
			PhoneNum:     detail.PhoneNum,
			SendStatus:   detail.SendStatus,
			ErrCode:      detail.ErrCode,
			TemplateCode: detail.TemplateCode,
			Content:      detail.Content,
			SendDate:     sendDate,
			ReceiveDate:  receiveDate,
			OutId:        detail.OutId,
		})
	}

	totalCount, _ := strconv.ParseInt(response.TotalCount, 10, 64)
	return &QueryResult{
		TotalCount:        totalCount,
		SmsSendDetailDTOs: details,
	}, nil
}

// SendLoginVerificationCode 发送登录验证码
func (a *AliyunSMS) SendLoginVerificationCode(phone, code string) error {
	params := map[string]string{
		"code": code,
	}
	return a.SendTemplate(phone, LoginVerificationTemplate, params)
}

// SendRegisterVerificationCode 发送注册验证码
func (a *AliyunSMS) SendRegisterVerificationCode(phone, code string) error {
	params := map[string]string{
		"code": code,
	}
	return a.SendTemplate(phone, RegisterVerificationTemplate, params)
}

// SendResetPasswordVerificationCode 发送重置密码验证码
func (a *AliyunSMS) SendResetPasswordVerificationCode(phone, code string) error {
	params := map[string]string{
		"code": code,
	}
	return a.SendTemplate(phone, ResetPasswordVerificationTemplate, params)
}

// SendBindPhoneVerificationCode 发送绑定手机号验证码
func (a *AliyunSMS) SendBindPhoneVerificationCode(phone, code string) error {
	params := map[string]string{
		"code": code,
	}
	return a.SendTemplate(phone, BindPhoneVerificationTemplate, params)
}

// ValidatePhone 验证手机号格式
func ValidatePhone(phone string) bool {
	if len(phone) != 11 {
		return false
	}

	// 简单的手机号验证，以1开头的11位数字
	if phone[0] != '1' {
		return false
	}

	// 检查是否全是数字
	for _, r := range phone {
		if r < '0' || r > '9' {
			return false
		}
	}

	return true
}

// GenerateVerificationCode 生成验证码
func GenerateVerificationCode(length int) string {
	if length <= 0 {
		length = 6
	}

	code := ""
	for i := 0; i < length; i++ {
		code += strconv.Itoa(int(time.Now().UnixNano() % 10))
	}

	return code
}

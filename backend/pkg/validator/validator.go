package validator

import (
	"bdb-backend/pkg/response"
	"fmt"
	"reflect"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

// Validator 验证器接口
type Validator interface {
	// ValidateStruct 验证结构体
	ValidateStruct(obj interface{}) error

	// ValidateQuery 验证查询参数
	ValidateQuery(c *gin.Context, obj interface{}) error

	// ValidateJSON 验证JSON数据
	ValidateJSON(c *gin.Context, obj interface{}) error

	// CheckJSON 验证JSON数据并直接响应错误
	CheckJSON(c *gin.Context, obj interface{}) bool

	// CheckQuery 验证Query参数并直接响应错误
	CheckQuery(c *gin.Context, obj interface{}) bool

	// ValidateForm 验证表单数据
	ValidateForm(c *gin.Context, obj interface{}) error

	// ValidateVar 验证单个变量
	ValidateVar(field interface{}, tag string) error

	// RegisterCustomValidator 注册自定义验证器
	RegisterCustomValidator(tag string, fn validator.Func) error
}

// CustomValidator 自定义验证器
type CustomValidator struct {
	validator *validator.Validate
}

// NewValidator 创建新的验证器实例
func NewValidator() Validator {
	validate := validator.New()

	// 注册自定义字段名获取函数
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	cv := &CustomValidator{
		validator: validate,
	}

	// 注册内置的自定义验证器
	cv.registerBuiltinValidators()

	return cv
}

// ValidateStruct 验证结构体
func (cv *CustomValidator) ValidateStruct(obj interface{}) error {
	if err := cv.validator.Struct(obj); err != nil {
		return cv.formatValidationErrors(err)
	}
	return nil
}

// ValidateQuery 验证查询参数
func (cv *CustomValidator) ValidateQuery(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindQuery(obj); err != nil {
		return fmt.Errorf("绑定查询参数失败: %w", err)
	}
	return cv.ValidateStruct(obj)
}

// ValidateJSON 验证JSON数据
func (cv *CustomValidator) ValidateJSON(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindJSON(obj); err != nil {
		return fmt.Errorf("绑定JSON数据失败: %w", err)
	}
	return cv.ValidateStruct(obj)
}

// CheckJSON 验证JSON数据并直接响应错误
func (cv *CustomValidator) CheckJSON(c *gin.Context, obj interface{}) bool {
	if err := c.ShouldBindJSON(obj); err != nil {
		// 对于绑定失败，通常是客户端请求格式问题，直接返回BadRequest
		response.BadRequest(c, fmt.Sprintf("请求参数绑定失败: %v", err))
		return false
	}
	if err := cv.ValidateStruct(obj); err != nil {
		// ValidateStruct 已经格式化了错误信息，直接使用
		response.BadRequest(c, err.Error())
		return false
	}
	return true
}

// CheckQuery 验证Query参数并直接响应错误
func (cv *CustomValidator) CheckQuery(c *gin.Context, obj interface{}) bool {
	if err := c.ShouldBindQuery(obj); err != nil {
		// 绑定失败，通常是客户端请求格式问题
		response.BadRequest(c, fmt.Sprintf("查询参数绑定失败: %v", err))
		return false
	}
	if err := cv.ValidateStruct(obj); err != nil {
		// 结构体验证失败，返回格式化后的错误
		response.BadRequest(c, err.Error())
		return false
	}
	return true
}

// ValidateForm 验证表单数据
func (cv *CustomValidator) ValidateForm(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBind(obj); err != nil {
		return fmt.Errorf("绑定表单数据失败: %w", err)
	}
	return cv.ValidateStruct(obj)
}

// ValidateVar 验证单个变量
func (cv *CustomValidator) ValidateVar(field interface{}, tag string) error {
	if err := cv.validator.Var(field, tag); err != nil {
		return cv.formatValidationErrors(err)
	}
	return nil
}

// RegisterCustomValidator 注册自定义验证器
func (cv *CustomValidator) RegisterCustomValidator(tag string, fn validator.Func) error {
	return cv.validator.RegisterValidation(tag, fn)
}

// formatValidationErrors 格式化验证错误
func (cv *CustomValidator) formatValidationErrors(err error) error {
	var errors []string

	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErrors {
			errors = append(errors, cv.formatFieldError(fieldErr))
		}
	} else {
		errors = append(errors, err.Error())
	}

	return fmt.Errorf("验证失败: %s", strings.Join(errors, ", "))
}

// formatFieldError 格式化字段错误
func (cv *CustomValidator) formatFieldError(fieldErr validator.FieldError) string {
	field := fieldErr.Field()
	tag := fieldErr.Tag()
	param := fieldErr.Param()

	switch tag {
	case "required":
		return fmt.Sprintf("%s是必填项", field)
	case "min":
		return fmt.Sprintf("%s长度不能小于%s", field, param)
	case "max":
		return fmt.Sprintf("%s长度不能大于%s", field, param)
	case "email":
		return fmt.Sprintf("%s必须是有效的邮箱地址", field)
	case "phone":
		return fmt.Sprintf("%s必须是有效的手机号", field)
	case "password":
		return fmt.Sprintf("%s必须包含大小写字母和数字，长度8-20位", field)
	case "idcard":
		return fmt.Sprintf("%s必须是有效的身份证号", field)
	case "len":
		return fmt.Sprintf("%s长度必须等于%s", field, param)
	case "gte":
		return fmt.Sprintf("%s必须大于或等于%s", field, param)
	case "lte":
		return fmt.Sprintf("%s必须小于或等于%s", field, param)
	case "gt":
		return fmt.Sprintf("%s必须大于%s", field, param)
	case "lt":
		return fmt.Sprintf("%s必须小于%s", field, param)
	case "oneof":
		return fmt.Sprintf("%s必须是以下值之一: %s", field, param)
	case "numeric":
		return fmt.Sprintf("%s必须是数字", field)
	case "alpha":
		return fmt.Sprintf("%s只能包含字母", field)
	case "alphanum":
		return fmt.Sprintf("%s只能包含字母和数字", field)
	case "url":
		return fmt.Sprintf("%s必须是有效的URL", field)
	case "uri":
		return fmt.Sprintf("%s必须是有效的URI", field)
	case "uuid":
		return fmt.Sprintf("%s必须是有效的UUID", field)
	case "datetime":
		return fmt.Sprintf("%s必须是有效的日期时间格式", field)
	case "date":
		return fmt.Sprintf("%s必须是有效的日期格式", field)
	case "time":
		return fmt.Sprintf("%s必须是有效的时间格式", field)
	default:
		return fmt.Sprintf("%s验证失败", field)
	}
}

// registerBuiltinValidators 注册内置的自定义验证器
func (cv *CustomValidator) registerBuiltinValidators() {
	// 手机号验证
	_ = cv.validator.RegisterValidation("phone", func(fl validator.FieldLevel) bool {
		phone := fl.Field().String()
		return isValidPhone(phone)
	})

	// 密码强度验证
	_ = cv.validator.RegisterValidation("password", func(fl validator.FieldLevel) bool {
		password := fl.Field().String()
		return isValidPassword(password)
	})

	// 身份证号验证
	_ = cv.validator.RegisterValidation("idcard", func(fl validator.FieldLevel) bool {
		idcard := fl.Field().String()
		return isValidIDCard(idcard)
	})

	// 中文验证
	_ = cv.validator.RegisterValidation("chinese", func(fl validator.FieldLevel) bool {
		text := fl.Field().String()
		return isValidChinese(text)
	})

	// 数字或字母验证
	_ = cv.validator.RegisterValidation("alphanumeric", func(fl validator.FieldLevel) bool {
		text := fl.Field().String()
		return isValidAlphaNumeric(text)
	})
}

// isValidPhone 验证手机号格式
func isValidPhone(phone string) bool {
	if len(phone) != 11 {
		return false
	}

	// 中国大陆手机号验证
	if phone[0] != '1' {
		return false
	}

	// 第二位数字验证
	validSecond := []byte{'3', '4', '5', '6', '7', '8', '9'}
	found := false
	for _, v := range validSecond {
		if phone[1] == v {
			found = true
			break
		}
	}
	if !found {
		return false
	}

	// 检查是否全是数字
	for _, r := range phone {
		if r < '0' || r > '9' {
			return false
		}
	}

	return true
}

// isValidPassword 验证密码强度
func isValidPassword(password string) bool {
	if len(password) < 8 || len(password) > 20 {
		return false
	}

	hasUpper := false
	hasLower := false
	hasDigit := false

	for _, char := range password {
		switch {
		case char >= 'A' && char <= 'Z':
			hasUpper = true
		case char >= 'a' && char <= 'z':
			hasLower = true
		case char >= '0' && char <= '9':
			hasDigit = true
		}
	}

	return hasUpper && hasLower && hasDigit
}

// isValidIDCard 验证身份证号
func isValidIDCard(idcard string) bool {
	if len(idcard) != 18 {
		return false
	}

	// 前17位必须是数字
	for i := 0; i < 17; i++ {
		if idcard[i] < '0' || idcard[i] > '9' {
			return false
		}
	}

	// 最后一位可以是数字或X
	lastChar := idcard[17]
	if lastChar != 'X' && lastChar != 'x' && (lastChar < '0' || lastChar > '9') {
		return false
	}

	return true
}

// isValidChinese 验证中文
func isValidChinese(text string) bool {
	for _, r := range text {
		if !((r >= 0x4e00 && r <= 0x9fff) || (r >= 0x3400 && r <= 0x4dbf) || (r >= 0x20000 && r <= 0x2a6df)) {
			return false
		}
	}
	return true
}

// isValidAlphaNumeric 验证字母数字
func isValidAlphaNumeric(text string) bool {
	for _, r := range text {
		if !((r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9')) {
			return false
		}
	}
	return true
}

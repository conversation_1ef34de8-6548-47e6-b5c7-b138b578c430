package wechat

import (
	"context"
	"encoding/json"
	"errors"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram"
	"github.com/rs/zerolog/log"
)

// NotifyService 微信支付回调服务接口
type NotifyService interface {
	// HandlePaymentNotify 处理支付回调
	HandlePaymentNotify(ctx context.Context, body []byte, signature string) (*PaymentNotifyResult, error)
	// VerifyNotifySignature 验证回调签名
	VerifyNotifySignature(body []byte, signature string) error
}

type notifyService struct {
	miniProgram *miniProgram.MiniProgram
}

// NewNotifyService 创建微信回调服务
func NewNotifyService(miniProgram *miniProgram.MiniProgram) NotifyService {
	return &notifyService{
		miniProgram: miniProgram,
	}
}

// HandlePaymentNotify 处理支付回调
func (s *notifyService) HandlePaymentNotify(ctx context.Context, body []byte, signature string) (*PaymentNotifyResult, error) {
	// 1. 验证签名
	if err := s.VerifyNotifySignature(body, signature); err != nil {
		log.Error().Err(err).Msg("Failed to verify payment notify signature")
		return nil, errors.New("回调签名验证失败")
	}

	// 2. 解析回调数据
	var notifyData PaymentNotifyData
	if err := json.Unmarshal(body, &notifyData); err != nil {
		log.Error().Err(err).Msg("Failed to unmarshal payment notify data")
		return nil, errors.New("回调数据解析失败")
	}

	// 3. 解密回调内容
	result, err := s.decryptNotifyResource(&notifyData.Resource)
	if err != nil {
		log.Error().Err(err).Msg("Failed to decrypt payment notify resource")
		return nil, errors.New("回调数据解密失败")
	}

	log.Info().Str("out_trade_no", result.OutTradeNo).Str("trade_state", result.TradeState).Msg("Payment notify processed")

	return result, nil
}

// VerifyNotifySignature 验证回调签名
func (s *notifyService) VerifyNotifySignature(body []byte, signature string) error {
	// TODO: 实现微信支付回调签名验证
	// 使用PowerWeChat的验证方法
	log.Info().Str("signature", signature).Msg("Verifying payment notify signature")

	return nil // 暂时返回成功，实际需要验证
}

// decryptNotifyResource 解密回调资源
func (s *notifyService) decryptNotifyResource(resource *PaymentNotifyResource) (*PaymentNotifyResult, error) {
	// TODO: 实现微信支付回调数据解密
	// 使用PowerWeChat的解密方法
	log.Info().Str("algorithm", resource.Algorithm).Msg("Decrypting payment notify resource")

	// 暂时返回模拟数据
	return &PaymentNotifyResult{
		OutTradeNo:    "test_order_123",
		TransactionID: "wx_transaction_456",
		TradeState:    "SUCCESS",
	}, nil
}

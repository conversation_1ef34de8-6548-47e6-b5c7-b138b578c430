package wechat

import "time"

// ==================== 微信认证相关类型 ====================

// SessionInfo 微信小程序登录会话信息
type SessionInfo struct {
	OpenID     string `json:"openid"`      // 用户唯一标识
	SessionKey string `json:"session_key"` // 会话密钥
	UnionID    string `json:"unionid"`     // 用户在开放平台的唯一标识符
	ErrCode    int    `json:"errcode"`     // 错误码
	ErrMsg     string `json:"errmsg"`      // 错误信息
}

// PhoneInfo 微信小程序获取用户手机号信息
type PhoneInfo struct {
	PhoneNumber     string `json:"phoneNumber"`     // 用户绑定的手机号（国外手机号会有区号）
	PurePhoneNumber string `json:"purePhoneNumber"` // 没有区号的手机号
	CountryCode     string `json:"countryCode"`     // 区号
}

// ==================== 微信支付相关类型 ====================

// PaymentOrderRequest 创建微信支付订单的请求参数
type PaymentOrderRequest struct {
	OutTradeNo  string `json:"out_trade_no" validate:"required"` // 商户订单号，必须
	Description string `json:"description" validate:"required"`  // 商品描述，必须
	Amount      int64  `json:"amount" validate:"required,min=1"` // 订单金额(分)，必须大于0
	OpenID      string `json:"openid" validate:"required"`       // 用户openid，必须
	NotifyURL   string `json:"notify_url"`                       // 支付结果通知地址，可选
	AttachData  string `json:"attach"`                           // 附加数据，在查询API和支付通知中原样返回
}

// PaymentOrderResponse 微信支付订单创建成功后的响应
type PaymentOrderResponse struct {
	PrepayID  string `json:"prepay_id"`  // 预支付交易会话标识
	Package   string `json:"package"`    // 统一下单接口返回的prepay_id参数值
	NonceStr  string `json:"nonce_str"`  // 随机字符串
	TimeStamp string `json:"time_stamp"` // 时间戳
	PaySign   string `json:"pay_sign"`   // 签名
	SignType  string `json:"sign_type"`  // 签名类型，默认为RSA
}

// OrderQueryRequest 查询订单状态的请求参数
type OrderQueryRequest struct {
	OutTradeNo    string `json:"out_trade_no,omitempty"`   // 商户订单号
	TransactionID string `json:"transaction_id,omitempty"` // 微信支付订单号
}

// OrderQueryResponse 查询订单状态的响应
type OrderQueryResponse struct {
	AppID          string              `json:"appid"`            // 应用ID
	MchID          string              `json:"mchid"`            // 商户号
	OutTradeNo     string              `json:"out_trade_no"`     // 商户订单号
	TransactionID  string              `json:"transaction_id"`   // 微信支付订单号
	TradeType      string              `json:"trade_type"`       // 交易类型
	TradeState     string              `json:"trade_state"`      // 交易状态
	TradeStateDesc string              `json:"trade_state_desc"` // 交易状态描述
	BankType       string              `json:"bank_type"`        // 付款银行
	Attach         string              `json:"attach"`           // 附加数据
	SuccessTime    *time.Time          `json:"success_time"`     // 支付完成时间
	Payer          PaymentNotifyPayer  `json:"payer"`            // 支付者信息
	Amount         PaymentNotifyAmount `json:"amount"`           // 订单金额信息
}

// ==================== 微信支付回调相关类型 ====================

// PaymentNotifyData 微信支付结果通知的数据结构
type PaymentNotifyData struct {
	ID           string                `json:"id"`            // 通知ID
	CreateTime   time.Time             `json:"create_time"`   // 通知创建时间
	ResourceType string                `json:"resource_type"` // 通知的资源数据类型
	EventType    string                `json:"event_type"`    // 通知的类型
	Summary      string                `json:"summary"`       // 回调摘要
	Resource     PaymentNotifyResource `json:"resource"`      // 通知资源数据
}

// PaymentNotifyResource 微信支付回调中的加密资源数据
type PaymentNotifyResource struct {
	Algorithm      string `json:"algorithm"`       // 加密算法类型
	Ciphertext     string `json:"ciphertext"`      // 数据密文
	AssociatedData string `json:"associated_data"` // 附加数据
	OriginalType   string `json:"original_type"`   // 原始类型
	Nonce          string `json:"nonce"`           // 随机串
}

// PaymentNotifyResult 微信支付回调解密后的支付结果
type PaymentNotifyResult struct {
	MchID          string              `json:"mchid"`            // 商户号
	AppID          string              `json:"appid"`            // 应用ID
	OutTradeNo     string              `json:"out_trade_no"`     // 商户订单号
	TransactionID  string              `json:"transaction_id"`   // 微信支付订单号
	TradeType      string              `json:"trade_type"`       // 交易类型
	TradeState     string              `json:"trade_state"`      // 交易状态
	TradeStateDesc string              `json:"trade_state_desc"` // 交易状态描述
	BankType       string              `json:"bank_type"`        // 付款银行
	Attach         string              `json:"attach"`           // 附加数据
	SuccessTime    *time.Time          `json:"success_time"`     // 支付完成时间
	Payer          PaymentNotifyPayer  `json:"payer"`            // 支付者信息
	Amount         PaymentNotifyAmount `json:"amount"`           // 订单金额信息
}

// PaymentNotifyPayer 微信支付回调中的支付者信息
type PaymentNotifyPayer struct {
	OpenID string `json:"openid"` // 用户在直连商户appid下的唯一标识
}

// PaymentNotifyAmount 微信支付回调中的金额信息
type PaymentNotifyAmount struct {
	Total         int    `json:"total"`          // 订单总金额，单位为分
	PayerTotal    int    `json:"payer_total"`    // 用户支付金额，单位为分
	Currency      string `json:"currency"`       // 货币类型，符合ISO 4217标准的三位字母代码
	PayerCurrency string `json:"payer_currency"` // 用户支付币种
}

// ==================== 退款相关类型 ====================

// RefundRequest 微信支付退款请求参数
type RefundRequest struct {
	OutTradeNo  string `json:"out_trade_no" validate:"required"`  // 商户订单号，必须
	OutRefundNo string `json:"out_refund_no" validate:"required"` // 商户退款单号，必须
	Reason      string `json:"reason"`                            // 退款原因
	Amount      int64  `json:"amount" validate:"required,min=1"`  // 退款金额(分)，必须大于0
	Total       int64  `json:"total" validate:"required,min=1"`   // 原订单金额(分)，必须大于0
	NotifyURL   string `json:"notify_url"`                        // 退款结果通知地址
}

// RefundResponse 微信支付退款响应
type RefundResponse struct {
	RefundID      string     `json:"refund_id"`      // 微信支付退款单号
	OutRefundNo   string     `json:"out_refund_no"`  // 商户退款单号
	TransactionID string     `json:"transaction_id"` // 微信支付订单号
	OutTradeNo    string     `json:"out_trade_no"`   // 商户订单号
	Channel       string     `json:"channel"`        // 退款渠道
	UserReceived  int64      `json:"user_received"`  // 退款入账账户，修改为int64类型
	SuccessTime   *time.Time `json:"success_time"`   // 退款成功时间，可能为空
	CreateTime    time.Time  `json:"create_time"`    // 退款创建时间
	Status        string     `json:"status"`         // 退款状态
}

// ==================== 常量定义 ====================

// 微信支付交易状态常量
const (
	TradeStateSuccess    = "SUCCESS"    // 支付成功
	TradeStateRefund     = "REFUND"     // 转入退款
	TradeStateNotPay     = "NOTPAY"     // 未支付
	TradeStateClosed     = "CLOSED"     // 已关闭
	TradeStateRevoked    = "REVOKED"    // 已撤销（刷卡支付）
	TradeStateUserPaying = "USERPAYING" // 用户支付中
	TradeStatePayError   = "PAYERROR"   // 支付失败
)

// 微信支付退款状态常量
const (
	RefundStatusSuccess    = "SUCCESS"    // 退款成功
	RefundStatusClosed     = "CLOSED"     // 退款关闭
	RefundStatusProcessing = "PROCESSING" // 退款处理中
	RefundStatusAbnormal   = "ABNORMAL"   // 退款异常
)

package wechat

import (
	"context"
	"crypto/md5"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram"
	"github.com/rs/zerolog/log"
)

// PaymentService 微信支付服务接口
type PaymentService interface {
	// CreateOrder 创建支付订单
	CreateOrder(ctx context.Context, req *PaymentOrderRequest) (*PaymentOrderResponse, error)
	// QueryOrder 查询订单状态
	QueryOrder(ctx context.Context, outTradeNo string) (*PaymentNotifyResult, error)
	// Refund 申请退款
	Refund(ctx context.Context, req *RefundRequest) (*RefundResponse, error)
	// HandleNotify 处理支付回调
	HandleNotify(ctx context.Context, body []byte) (*PaymentNotifyResult, error)
}

type paymentService struct {
	miniProgram *miniProgram.MiniProgram
}

// NewPaymentService 创建微信支付服务
func NewPaymentService(miniProgram *miniProgram.MiniProgram) PaymentService {
	return &paymentService{
		miniProgram: miniProgram,
	}
}

// CreateOrder 创建支付订单
func (s *paymentService) CreateOrder(ctx context.Context, req *PaymentOrderRequest) (*PaymentOrderResponse, error) {
	if req == nil {
		return nil, errors.New("支付请求不能为空")
	}

	if err := s.validateOrderRequest(req); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	log.Info().
		Str("out_trade_no", req.OutTradeNo).
		Int64("amount", req.Amount).
		Str("openid", req.OpenID).
		Msg("Creating wechat payment order")

	// TODO: 实现微信支付统一下单
	// 这里需要使用微信支付API v3或v2版本
	// 由于PowerWeChat的支付模块有问题，这里先返回模拟数据

	// 生成模拟的prepay_id
	prepayID := fmt.Sprintf("wx%d%s", time.Now().Unix(), req.OutTradeNo[:8])

	// 生成小程序调起支付的参数
	timeStamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonceStr := s.generateNonceStr()
	paySign := s.generatePaySign(prepayID, timeStamp, nonceStr)

	response := &PaymentOrderResponse{
		PrepayID:  prepayID,
		Package:   fmt.Sprintf("prepay_id=%s", prepayID),
		NonceStr:  nonceStr,
		TimeStamp: timeStamp,
		PaySign:   paySign,
		SignType:  "MD5",
	}

	log.Info().
		Str("out_trade_no", req.OutTradeNo).
		Str("prepay_id", prepayID).
		Msg("Wechat payment order created successfully (MOCK)")

	return response, nil
}

// QueryOrder 查询订单状态
func (s *paymentService) QueryOrder(ctx context.Context, outTradeNo string) (*PaymentNotifyResult, error) {
	if outTradeNo == "" {
		return nil, errors.New("订单号不能为空")
	}

	log.Info().Str("out_trade_no", outTradeNo).Msg("Querying wechat payment order")

	// TODO: 实现微信支付订单查询
	// 这里先返回模拟数据
	response := &PaymentNotifyResult{
		AppID:          s.getAppID(),
		MchID:          s.getMchID(),
		OutTradeNo:     outTradeNo,
		TransactionID:  fmt.Sprintf("4200%d", time.Now().Unix()),
		TradeType:      "JSAPI",
		TradeState:     "SUCCESS",
		TradeStateDesc: "支付成功",
		BankType:       "OTHERS",
		Amount: PaymentNotifyAmount{
			Total:         int(1),
			PayerTotal:    int(1),
			Currency:      "CNY",
			PayerCurrency: "CNY",
		},
		Payer: PaymentNotifyPayer{
			OpenID: "mock_openid",
		},
		SuccessTime: func() *time.Time { t := time.Now(); return &t }(),
	}

	log.Info().
		Str("out_trade_no", outTradeNo).
		Str("trade_state", response.TradeState).
		Msg("Wechat payment order query completed (MOCK)")

	return response, nil
}

// Refund 申请退款
func (s *paymentService) Refund(ctx context.Context, req *RefundRequest) (*RefundResponse, error) {
	if req == nil {
		return nil, errors.New("退款请求不能为空")
	}

	if err := s.validateRefundRequest(req); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	log.Info().
		Str("out_trade_no", req.OutTradeNo).
		Str("out_refund_no", req.OutRefundNo).
		Int64("amount", req.Amount).
		Msg("Processing wechat payment refund")

	// TODO: 实现微信支付退款
	// 这里先返回模拟数据
	response := &RefundResponse{
		RefundID:      fmt.Sprintf("50%d", time.Now().Unix()),
		OutRefundNo:   req.OutRefundNo,
		TransactionID: fmt.Sprintf("4200%d", time.Now().Unix()),
		OutTradeNo:    req.OutTradeNo,
		Channel:       "ORIGINAL",
		UserReceived:  req.Amount,
		Status:        "SUCCESS",
		CreateTime:    time.Now(),
		SuccessTime:   func() *time.Time { t := time.Now(); return &t }(),
	}

	log.Info().
		Str("out_refund_no", req.OutRefundNo).
		Str("refund_id", response.RefundID).
		Msg("Wechat payment refund applied successfully (MOCK)")

	return response, nil
}

// HandleNotify 处理支付回调
func (s *paymentService) HandleNotify(ctx context.Context, body []byte) (*PaymentNotifyResult, error) {
	if len(body) == 0 {
		return nil, errors.New("回调数据为空")
	}

	log.Info().Msg("Processing wechat payment notify")

	// TODO: 实现微信支付回调处理
	// 这里需要验证签名和解密数据

	// 暂时返回模拟数据
	result := &PaymentNotifyResult{
		AppID:          s.getAppID(),
		MchID:          s.getMchID(),
		OutTradeNo:     "mock_out_trade_no",
		TransactionID:  fmt.Sprintf("4200%d", time.Now().Unix()),
		TradeType:      "JSAPI",
		TradeState:     "SUCCESS",
		TradeStateDesc: "支付成功",
		BankType:       "OTHERS",
		Amount: PaymentNotifyAmount{
			Total:         int(1),
			PayerTotal:    int(1),
			Currency:      "CNY",
			PayerCurrency: "CNY",
		},
		Payer: PaymentNotifyPayer{
			OpenID: "mock_openid",
		},
		SuccessTime: func() *time.Time { t := time.Now(); return &t }(),
	}

	log.Info().
		Str("out_trade_no", result.OutTradeNo).
		Str("transaction_id", result.TransactionID).
		Str("trade_state", result.TradeState).
		Msg("Wechat payment notify processed successfully (MOCK)")

	return result, nil
}

// validateOrderRequest 验证订单请求参数
func (s *paymentService) validateOrderRequest(req *PaymentOrderRequest) error {
	if req.OutTradeNo == "" {
		return errors.New("商户订单号不能为空")
	}
	if req.Description == "" {
		return errors.New("商品描述不能为空")
	}
	if req.Amount <= 0 {
		return errors.New("订单金额必须大于0")
	}
	if req.OpenID == "" {
		return errors.New("用户OpenID不能为空")
	}
	if req.NotifyURL == "" {
		return errors.New("回调地址不能为空")
	}
	return nil
}

// validateRefundRequest 验证退款请求参数
func (s *paymentService) validateRefundRequest(req *RefundRequest) error {
	if req.OutTradeNo == "" {
		return errors.New("商户订单号不能为空")
	}
	if req.OutRefundNo == "" {
		return errors.New("商户退款单号不能为空")
	}
	if req.Amount <= 0 {
		return errors.New("退款金额必须大于0")
	}
	if req.Total <= 0 {
		return errors.New("原订单金额必须大于0")
	}
	if req.Amount > req.Total {
		return errors.New("退款金额不能大于原订单金额")
	}
	return nil
}

// generateNonceStr 生成随机字符串
func (s *paymentService) generateNonceStr() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// generatePaySign 生成支付签名
func (s *paymentService) generatePaySign(prepayID, timeStamp, nonceStr string) string {
	appID := s.getAppID()
	signData := map[string]string{
		"appId":     appID,
		"timeStamp": timeStamp,
		"nonceStr":  nonceStr,
		"package":   fmt.Sprintf("prepay_id=%s", prepayID),
		"signType":  "MD5",
	}

	// 按照微信支付签名规则进行排序和拼接
	var keys []string
	for k := range signData {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var signStr strings.Builder
	for _, k := range keys {
		if signData[k] != "" {
			signStr.WriteString(k)
			signStr.WriteString("=")
			signStr.WriteString(signData[k])
			signStr.WriteString("&")
		}
	}

	// 添加key
	key := s.getPaymentKey()
	signStr.WriteString("key=")
	signStr.WriteString(key)

	// MD5加密并转大写
	hash := md5.Sum([]byte(signStr.String()))
	return fmt.Sprintf("%X", hash)
}

// getAppID 获取AppID
func (s *paymentService) getAppID() string {
	if s.miniProgram != nil && s.miniProgram.Config != nil {
		return s.miniProgram.Config.GetString("app_id", "")
	}
	return ""
}

// getMchID 获取商户号
func (s *paymentService) getMchID() string {
	if s.miniProgram != nil && s.miniProgram.Config != nil {
		return s.miniProgram.Config.GetString("mch_id", "")
	}
	return ""
}

// getPaymentKey 获取支付密钥
func (s *paymentService) getPaymentKey() string {
	if s.miniProgram != nil && s.miniProgram.Config != nil {
		return s.miniProgram.Config.GetString("key", "")
	}
	return ""
}

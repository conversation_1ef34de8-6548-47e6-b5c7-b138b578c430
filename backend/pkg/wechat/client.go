package wechat

import (
	"bdb-backend/pkg/config"
	"log"
	"os"
	"path/filepath"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram"
)

// Client 微信客户端
type Client struct {
	miniProgram *miniProgram.MiniProgram
}

var MiniApp *miniProgram.MiniProgram

// NewClient 创建微信客户端
func NewClient(cfg *config.Config) (*Client, error) {
	miniProgram, err := NewMiniProgram(cfg)
	if err != nil {
		log.Fatal("Failed to create mini program client:", err)
		return nil, err
	}

	log.Println("miniProgram client created successfully")

	MiniApp = miniProgram
	return &Client{
		miniProgram: miniProgram,
	}, nil
}

// GetMiniProgram 获取小程序实例
func (c *Client) GetMiniProgram() *miniProgram.MiniProgram {
	return c.miniProgram
}

// NewMiniProgram 创建微信小程序实例
func NewMiniProgram(cfg *config.Config) (*miniProgram.MiniProgram, error) {
	// Ensure log directory exists
	logDir := "./logs"
	if err := os.MkdirAll(logDir, 0750); err != nil {
		log.Printf("Failed to create log directory: %v", err)
	}

	logFile := filepath.Join(logDir, "wechat.log")

	miniConfig := &miniProgram.UserConfig{
		AppID:  cfg.Wechat.AppID,
		Secret: cfg.Wechat.AppSecret,

		HttpDebug: cfg.Server.Mode == "debug",
		Log: miniProgram.Log{
			Level: cfg.Logger.Level,
			File:  logFile,
		},
	}

	return miniProgram.NewMiniProgram(miniConfig)
}

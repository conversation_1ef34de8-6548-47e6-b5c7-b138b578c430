server:
  port: "8080"
  mode: "release"
  read_timeout: 30s
  write_timeout: 30s

database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "password"
  dbname: "bdb"
  sslmode: "disable"
  max_idle_conns: 10
  max_open_conns: 100

redis:
  addr: "localhost:6379"
  password: ""
  db: 0
  pool_size: 10

jwt:
  secret: "your-jwt-secret-key"
  ttl: "24h"
  refresh_secret: "your-jwt-refresh-secret-key"
  refresh_ttl: "168h" # 7 days

storage:
  qiniu:
    access_key: "your-access-key"
    secret_key: "your-secret-key"
    bucket: "bdb-files"
    domain: "your-domain.com"

sms:
  aliyun:
    access_key: "your-access-key"
    secret_key: "your-secret-key"
    sign_name: "本地宝"
    template_code: "SMS_123456789"

payment:
  wechat:
    app_id: "your-wechat-app-id"
    mch_id: "your-wechat-mch-id"
    api_key: "your-wechat-api-key"
  alipay:
    app_id: "your-alipay-app-id"
    private_key: "your-alipay-private-key"
    public_key: "your-alipay-public-key"

logger:
  level: "info"
  format: "json"
  output_paths: ["stdout", "./logs/app.log"]
  error_output_paths: ["stderr", "./logs/error.log"]
  rotation:
    max_size: 50 # megabytes
    max_age: 30 # days
    max_backups: 10
    compress: false

 
server:
  port: "8088"
  mode: "debug"
  read_timeout: 30s
  write_timeout: 30s

database:
  host: "*************"
  port: 30779
  user: "postgres"
  password: "aaLRjyNejzDXyRh8"
  dbname: "fno2o_dev"
  sslmode: "disable"
  max_idle_conns: 10
  max_open_conns: 100

redis:
  addr: "localhost:6379"
  password: ""
  db: 0
  pool_size: 10

jwt:
  secret: "dev-jwt-secret-key-change-in-production"
  ttl: "900h"
  refresh_secret: "dev-jwt-refresh-secret-key-change-in-production"
  refresh_ttl: "1680h" # 7 days

storage:
  access_key: "dtt632BXLev50BnFgXm6VUXdzc_qVMd7v7Xe4yjz"
  secret_key: "uHl1s27VkuFfd-odvVIiEu1ZKSxADXaeillL3-J5"
  bucket: "o2o-mini"
  domain: "http://sze5syb8d.hb-bkt.clouddn.com"
  token_expires: 3600

sms:
  aliyun:
    access_key: "your-dev-access-key"
    secret_key: "your-dev-secret-key"
    sign_name: "本地宝测试"
    template_code: "SMS_123456789"

wechat:
  app_id: "wxfe48d2a722d4267e"
  app_secret: "be3a8066ea871dbf3df1bcb7b4899eb8"
  mch_id: "your-dev-wechat-mch-id"
  api_key: "your-dev-wechat-api-key"

logger:
  level: "debug"
  format: "console"
  output_paths: ["stdout", "./logs/app.log"]
  error_output_paths: ["stderr", "./logs/error.log"]
  rotation:
    max_size: 50
    max_age: 7
    max_backups: 3
    compress: false

centrifugo:
  url: "http://localhost:8000/api"
  api_key: "your-centrifugo-api-key"
  hmac_secret: "your-centrifugo-hmac-secret"
  grpc_port: 10000

cors:
  enabled: true
  allowed_origins:
    - "http://localhost:3000"
    - "http://localhost:8080"
    - "http://127.0.0.1:3000"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Origin"
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
  allow_credentials: true

rate_limit:
  enabled: true
  requests_per_second: 100
  burst: 200

security:
  bcrypt_cost: 10
  max_login_attempts: 5
  lockout_duration: "30m"
  password_min_length: 8
  password_require_special: true
  password_require_number: true
  password_require_uppercase: true
  password_require_lowercase: true

file_upload:
  max_file_size: 10485760 # 10MB
  allowed_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "image/webp"
    - "video/mp4"
    - "video/quicktime"
    - "audio/mpeg"
    - "audio/wav"
    - "application/pdf"
    - "application/msword"
    - "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
  upload_path: "./uploads"

cache:
  enabled: true
  ttl: "1h"
  prefix: "bdb:dev:"

notification:
  enabled: true
  push_service: "jpush"
  jpush:
    app_key: "your-jpush-app-key"
    master_secret: "your-jpush-master-secret"
    apns_production: false

monitoring:
  enabled: true
  prometheus:
    port: "9090"
    path: "/metrics"
  health_check:
    enabled: true
    path: "/health"

swagger:
  enabled: true
  title: "本地宝API"
  description: "本地宝后端API文档"
  version: "1.0.0"
  host: "localhost:8080"
  base_path: "/api/v1"

dev_login:
  enabled: true
  allowed_users:
    - "13800138001" # 求职者测试账号
    - "13800138002" # 企业招聘方测试账号
    - "13800138003" # 管理员测试账号

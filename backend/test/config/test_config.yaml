server:
  port: "8088"
  mode: "debug"
  read_timeout: 30s
  write_timeout: 30s

database:
  host: "*************"
  port: 30779
  user: "postgres"
  password: "aaLRjyNejzDXyRh8"
  dbname: "fno2o_test"  # 测试数据库
  sslmode: "disable"
  max_idle_conns: 5
  max_open_conns: 20

redis:
  addr: "localhost:6379"
  password: ""
  db: 1  # 使用不同的db
  pool_size: 5

jwt:
  secret: "test-jwt-secret-key-for-testing-only"
  ttl: "24h"
  refresh_secret: "test-jwt-refresh-secret-key-for-testing-only"
  refresh_ttl: "168h" # 7 days

storage:
  access_key: "test-access-key"
  secret_key: "test-secret-key"
  bucket: "test-bucket"
  domain: "http://test-domain.com"
  token_expires: 3600

wechat:
  app_id: "test-app-id"
  app_secret: "test-app-secret"
  mch_id: "test-mch-id"
  api_key: "test-api-key"

logger:
  level: "debug"
  format: "console"
  output_paths: ["stdout"]
  error_output_paths: ["stderr"]
  rotation:
    max_size: 10
    max_age: 1
    max_backups: 1
    compress: false

centrifugo:
  url: "http://localhost:8000/api"
  api_key: "test-centrifugo-api-key"
  hmac_secret: "test-centrifugo-hmac-secret"
  grpc_port: 10000

cors:
  enabled: true
  allowed_origins:
    - "*"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Origin"
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
  allow_credentials: true

rate_limit:
  enabled: false  # 测试时禁用限流
  requests_per_second: 1000
  burst: 2000

cache:
  enabled: true
  ttl: "5m"  # 测试时使用较短的TTL
  prefix: "bdb:test:"

dev_login:
  enabled: true
  allowed_users:
    - "13800138001" # 求职者测试账号
    - "13800138002" # 企业招聘方测试账号
    - "13800138003" # 管理员测试账号
package common

import (
	"context"
	"errors"
	"fmt"

	"bdb-backend/internal/model"
	"bdb-backend/pkg/jwt"

	"gorm.io/gorm"
)

// TestUser 测试用户信息
type TestUser struct {
	ID           uint   `json:"id"`
	UID          string `json:"uid"`
	Phone        string `json:"phone"`
	Nickname     string `json:"nickname"`
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	DeviceID     string `json:"device_id"`
}

// TestUsers 预定义的测试用户
var TestUsers = map[string]*TestUser{
	"jobseeker": {
		ID:       1,
		UID:      "test_jobseeker_001",
		Phone:    "***********",
		Nickname: "测试用户-求职者",
		DeviceID: "test_device_jobseeker",
	},
	"employer": {
		ID:       2,
		UID:      "test_employer_002",
		Phone:    "***********",
		Nickname: "测试企业-招聘方",
		DeviceID: "test_device_employer",
	},
	"admin": {
		ID:       3,
		UID:      "test_admin_003",
		Phone:    "***********",
		Nickname: "测试管理员",
		DeviceID: "test_device_admin",
	},
}

// GenerateTestTokens 为所有测试用户生成token
func GenerateTestTokens(jwtService jwt.JWTService) error {
	for key, user := range TestUsers {
		accessToken, err := jwtService.GenerateUserToken(user.ID, user.DeviceID)
		if err != nil {
			return fmt.Errorf("failed to generate access token for %s: %w", key, err)
		}

		refreshToken, err := jwtService.GenerateRefreshToken(user.ID, user.DeviceID)
		if err != nil {
			return fmt.Errorf("failed to generate refresh token for %s: %w", key, err)
		}

		user.AccessToken = accessToken
		user.RefreshToken = refreshToken
	}

	return nil
}

// CreateTestUsers 创建测试用户到数据库
func CreateTestUsers(db *gorm.DB) error {
	for _, testUser := range TestUsers {
		user := &model.User{
			BaseModel: model.BaseModel{ID: testUser.ID},
			UID:       testUser.UID,
			Phone:     testUser.Phone,
			Nickname:  testUser.Nickname,
			Source:    "dev-test",
			Gender:    getTestUserGender(testUser.Phone),
			Avatar:    getTestUserAvatar(testUser.Phone),
		}

		// 为不同的测试账号设置不同的角色和权限
		switch testUser.Phone {
		case "***********":
			// 求职者测试账号
			user.Gender = 1
			user.IsVerified = false
			user.Points = 100
		case "***********":
			// 企业招聘方测试账号
			user.Gender = 2
			user.IsVerified = true
			user.PersonalVerificationID = 1 // 模拟已认证
			user.EnterpriseID = 1           // 模拟关联企业
			user.Points = 500
		case "***********":
			// 管理员测试账号
			user.Gender = 1
			user.IsVerified = true
			user.PersonalVerificationID = 1
			user.Points = 9999
		}

		// 使用FirstOrCreate确保用户存在
		var existingUser model.User
		result := db.Where("phone = ?", user.Phone).First(&existingUser)
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 用户不存在，创建新用户
			if err := db.Create(user).Error; err != nil {
				return fmt.Errorf("failed to create test user %s: %w", user.Phone, err)
			}
		} else if result.Error != nil {
			return fmt.Errorf("failed to query test user %s: %w", user.Phone, result.Error)
		} else {
			// 用户已存在，更新信息
			if err := db.Model(&existingUser).Updates(user).Error; err != nil {
				return fmt.Errorf("failed to update test user %s: %w", user.Phone, err)
			}
		}
	}

	return nil
}

// GetTestUserByRole 根据角色获取测试用户
func GetTestUserByRole(role string) *TestUser {
	if user, exists := TestUsers[role]; exists {
		return user
	}
	return nil
}

// GetTestUserAccessToken 获取测试用户的AccessToken
func GetTestUserAccessToken(role string) string {
	if user := GetTestUserByRole(role); user != nil {
		return user.AccessToken
	}
	return ""
}

// GetTestUserRefreshToken 获取测试用户的RefreshToken
func GetTestUserRefreshToken(role string) string {
	if user := GetTestUserByRole(role); user != nil {
		return user.RefreshToken
	}
	return ""
}

// AuthHeaders 生成认证头部
func AuthHeaders(role string) map[string]string {
	token := GetTestUserAccessToken(role)
	if token == "" {
		return nil
	}

	return map[string]string{
		"Authorization": fmt.Sprintf("Bearer %s", token),
	}
}

// MockAuthContext 模拟认证上下文
func MockAuthContext(ctx context.Context, role string) context.Context {
	user := GetTestUserByRole(role)
	if user == nil {
		return ctx
	}

	// 在实际的中间件中，用户ID会被设置到context中
	// 这里我们模拟这个过程
	return context.WithValue(ctx, "user_id", user.ID)
}

// ValidateTestToken 验证测试token是否有效
func ValidateTestToken(jwtService jwt.JWTService, token string) (*jwt.UserClaims, error) {
	return jwtService.ValidateUserToken(token)
}

// RegenerateExpiredTokens 重新生成过期的token
func RegenerateExpiredTokens(jwtService jwt.JWTService) error {
	for key, user := range TestUsers {
		// 检查当前token是否过期
		if user.AccessToken != "" {
			_, err := jwtService.ValidateUserToken(user.AccessToken)
			if err != nil {
				// Token过期或无效，重新生成
				accessToken, err := jwtService.GenerateUserToken(user.ID, user.DeviceID)
				if err != nil {
					return fmt.Errorf("failed to regenerate access token for %s: %w", key, err)
				}
				user.AccessToken = accessToken
			}
		}

		if user.RefreshToken != "" {
			_, err := jwtService.ValidateRefreshToken(user.RefreshToken)
			if err != nil {
				// Refresh token过期或无效，重新生成
				refreshToken, err := jwtService.GenerateRefreshToken(user.ID, user.DeviceID)
				if err != nil {
					return fmt.Errorf("failed to regenerate refresh token for %s: %w", key, err)
				}
				user.RefreshToken = refreshToken
			}
		}
	}

	return nil
}

// getTestUserGender 获取测试用户性别
func getTestUserGender(phone string) int16 {
	switch phone {
	case "***********":
		return 1 // 男
	case "***********":
		return 2 // 女
	case "***********":
		return 1 // 男
	default:
		return 0 // 未知
	}
}

// getTestUserAvatar 获取测试用户头像
func getTestUserAvatar(phone string) string {
	switch phone {
	case "***********":
		return "/static/images/avatar-male.png"
	case "***********":
		return "/static/images/avatar-female.png"
	case "***********":
		return "/static/images/default-avatar.png"
	default:
		return "/static/images/default-avatar.png"
	}
}

// TestAuthFlow 测试完整的认证流程
type TestAuthFlow struct {
	suite *TestSuite
}

// NewTestAuthFlow 创建测试认证流程
func NewTestAuthFlow(suite *TestSuite) *TestAuthFlow {
	return &TestAuthFlow{suite: suite}
}

// LoginWithDevAccount 使用开发账号登录
func (flow *TestAuthFlow) LoginWithDevAccount(phone, deviceID string) (*TestUser, error) {
	// 模拟调用DevLogin API
	token, err := flow.suite.JWTService.GenerateUserToken(getTestUserIDByPhone(phone), deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	refreshToken, err := flow.suite.JWTService.GenerateRefreshToken(getTestUserIDByPhone(phone), deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	return &TestUser{
		ID:           getTestUserIDByPhone(phone),
		Phone:        phone,
		AccessToken:  token,
		RefreshToken: refreshToken,
		DeviceID:     deviceID,
	}, nil
}

// RefreshUserToken 刷新用户token
func (flow *TestAuthFlow) RefreshUserToken(refreshToken string) (*TestUser, error) {
	claims, err := flow.suite.JWTService.ValidateRefreshToken(refreshToken)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	newAccessToken, err := flow.suite.JWTService.GenerateUserToken(claims.UserID, claims.DeviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new access token: %w", err)
	}

	newRefreshToken, err := flow.suite.JWTService.GenerateRefreshToken(claims.UserID, claims.DeviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new refresh token: %w", err)
	}

	return &TestUser{
		ID:           claims.UserID,
		AccessToken:  newAccessToken,
		RefreshToken: newRefreshToken,
		DeviceID:     claims.DeviceID,
	}, nil
}

// getTestUserIDByPhone 根据手机号获取测试用户ID
func getTestUserIDByPhone(phone string) uint {
	switch phone {
	case "***********":
		return 1
	case "***********":
		return 2
	case "***********":
		return 3
	default:
		return 0
	}
}

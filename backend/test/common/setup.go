package common

import (
	"context"
	"fmt"
	"log"
	"os"
	"testing"

	"bdb-backend/pkg/cache"
	"bdb-backend/pkg/config"
	"bdb-backend/pkg/database"
	"bdb-backend/pkg/jwt"
	"bdb-backend/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// TestSuite 测试套件结构体
type TestSuite struct {
	DB         *gorm.DB
	Cache      cache.Cache
	JWTService jwt.JWTService
	Config     *config.Config
	Router     *gin.Engine
}

var GlobalTestSuite *TestSuite

// SetupTest 设置测试环境
func SetupTest() *TestSuite {
	// 设置测试环境变量
	os.Setenv("GO_ENV", "test")

	// 加载测试配置
	cfg := loadTestConfig()

	// 初始化日志
	logger.Init(cfg.Logger, cfg.Server.Mode)

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 初始化数据库
	db := setupTestDatabase(cfg)

	// 初始化缓存
	cacheClient := setupTestCache(cfg)

	// 初始化JWT服务
	jwtService := jwt.NewJWTService(cfg)

	// 创建测试套件
	suite := &TestSuite{
		DB:         db,
		Cache:      cacheClient,
		JWTService: jwtService,
		Config:     cfg,
	}

	GlobalTestSuite = suite
	return suite
}

// TeardownTest 清理测试环境
func TeardownTest() {
	if GlobalTestSuite != nil {
		// 清理数据库连接
		if GlobalTestSuite.DB != nil {
			sqlDB, err := GlobalTestSuite.DB.DB()
			if err == nil {
				sqlDB.Close()
			}
		}

		// 清理缓存
		if GlobalTestSuite.Cache != nil {
			// Redis连接会自动清理
		}
	}
}

// loadTestConfig 加载测试配置
func loadTestConfig() *config.Config {
	// 设置配置文件路径
	configPath := "./test/config/test_config.yaml"
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// 如果在test目录下运行，尝试相对路径
		configPath = "./config/test_config.yaml"
		if _, err := os.Stat(configPath); os.IsNotExist(err) {
			// 如果还是找不到，尝试从项目根目录
			configPath = "./backend/test/config/test_config.yaml"
		}
	}

	// 临时设置配置文件路径环境变量
	os.Setenv("CONFIG_PATH", configPath)

	cfg := config.Load()
	if cfg == nil {
		log.Fatal("Failed to load test config")
	}

	return cfg
}

// setupTestDatabase 设置测试数据库
func setupTestDatabase(cfg *config.Config) *gorm.DB {
	db := database.NewPostgresClient(cfg.Database)
	if db == nil {
		log.Fatalf("Failed to connect to test database")
	}

	// 运行数据库迁移
	err := runTestMigrations(db)
	if err != nil {
		log.Fatalf("Failed to run test migrations: %v", err)
	}

	return db
}

// setupTestCache 设置测试缓存
func setupTestCache(cfg *config.Config) cache.Cache {
	return cache.NewRedisCache(cfg.Redis)
}

// runTestMigrations 运行测试数据库迁移
func runTestMigrations(db *gorm.DB) error {
	// 这里可以运行必要的数据库迁移
	// 或者直接使用AutoMigrate来确保表结构正确
	return nil
}

// CleanDatabase 清理数据库数据
func CleanDatabase(db *gorm.DB) error {
	// 删除测试数据，但保留表结构
	tables := []string{
		"user_conversations",
		"messages",
		"conversations",
		"gig_applications",
		"gigs",
		"users",
		"notifications",
		// 添加其他需要清理的表
	}

	for _, table := range tables {
		if err := db.Exec(fmt.Sprintf("TRUNCATE TABLE %s RESTART IDENTITY CASCADE", table)).Error; err != nil {
			return fmt.Errorf("failed to truncate table %s: %w", table, err)
		}
	}

	return nil
}

// FlushCache 清理缓存数据
func FlushCache(cache cache.Cache) error {
	ctx := context.Background()

	// 删除所有测试相关的缓存键
	pattern := "bdb:test:*"
	keys, err := cache.Keys(ctx, pattern)
	if err != nil {
		return fmt.Errorf("failed to get cache keys: %w", err)
	}

	for _, key := range keys {
		if err := cache.Delete(ctx, key); err != nil {
			return fmt.Errorf("failed to delete cache key %s: %w", key, err)
		}
	}

	return nil
}

// RunTest 运行单个测试，提供自动清理
func RunTest(t *testing.T, testFunc func(t *testing.T, suite *TestSuite)) {
	suite := SetupTest()
	defer TeardownTest()

	// 测试前清理数据
	if err := CleanDatabase(suite.DB); err != nil {
		t.Fatalf("Failed to clean database: %v", err)
	}

	if err := FlushCache(suite.Cache); err != nil {
		t.Fatalf("Failed to flush cache: %v", err)
	}

	// 运行测试
	testFunc(t, suite)
}

package common

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// HTTPTestClient HTTP测试客户端
type HTTPTestClient struct {
	Router *gin.Engine
	t      *testing.T
}

// NewHTTPTestClient 创建HTTP测试客户端
func NewHTTPTestClient(t *testing.T, router *gin.Engine) *HTTPTestClient {
	return &HTTPTestClient{
		Router: router,
		t:      t,
	}
}

// TestRequest 测试请求结构体
type TestRequest struct {
	Method  string
	URL     string
	Headers map[string]string
	Body    interface{}
}

// TestResponse 测试响应结构体
type TestResponse struct {
	StatusCode int
	Body       map[string]interface{}
	RawBody    string
}

// Request 发送HTTP请求
func (c *HTTPTestClient) Request(req TestRequest) *TestResponse {
	var body io.Reader

	// 处理请求体
	if req.Body != nil {
		jsonData, err := json.Marshal(req.Body)
		assert.NoError(c.t, err, "Failed to marshal request body")
		body = bytes.NewBuffer(jsonData)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequest(req.Method, req.URL, body)
	assert.NoError(c.t, err, "Failed to create HTTP request")

	// 设置默认Content-Type
	if req.Body != nil {
		httpReq.Header.Set("Content-Type", "application/json")
	}

	// 设置额外的头部
	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}

	// 执行请求
	recorder := httptest.NewRecorder()
	c.Router.ServeHTTP(recorder, httpReq)

	// 解析响应
	response := &TestResponse{
		StatusCode: recorder.Code,
		RawBody:    recorder.Body.String(),
	}

	// 尝试解析JSON响应
	if recorder.Body.Len() > 0 {
		var bodyMap map[string]interface{}
		if err := json.Unmarshal(recorder.Body.Bytes(), &bodyMap); err == nil {
			response.Body = bodyMap
		}
	}

	return response
}

// GET 发送GET请求
func (c *HTTPTestClient) GET(url string, headers ...map[string]string) *TestResponse {
	req := TestRequest{
		Method: "GET",
		URL:    url,
	}

	if len(headers) > 0 {
		req.Headers = headers[0]
	}

	return c.Request(req)
}

// POST 发送POST请求
func (c *HTTPTestClient) POST(url string, body interface{}, headers ...map[string]string) *TestResponse {
	req := TestRequest{
		Method: "POST",
		URL:    url,
		Body:   body,
	}

	if len(headers) > 0 {
		req.Headers = headers[0]
	}

	return c.Request(req)
}

// PUT 发送PUT请求
func (c *HTTPTestClient) PUT(url string, body interface{}, headers ...map[string]string) *TestResponse {
	req := TestRequest{
		Method: "PUT",
		URL:    url,
		Body:   body,
	}

	if len(headers) > 0 {
		req.Headers = headers[0]
	}

	return c.Request(req)
}

// DELETE 发送DELETE请求
func (c *HTTPTestClient) DELETE(url string, headers ...map[string]string) *TestResponse {
	req := TestRequest{
		Method: "DELETE",
		URL:    url,
	}

	if len(headers) > 0 {
		req.Headers = headers[0]
	}

	return c.Request(req)
}

// WithAuth 添加认证头部
func (c *HTTPTestClient) WithAuth(token string) map[string]string {
	return map[string]string{
		"Authorization": fmt.Sprintf("Bearer %s", token),
	}
}

// AssertResponse 断言响应
type ResponseAssertion struct {
	response *TestResponse
	t        *testing.T
}

// Assert 创建响应断言
func (r *TestResponse) Assert(t *testing.T) *ResponseAssertion {
	return &ResponseAssertion{
		response: r,
		t:        t,
	}
}

// StatusCode 断言状态码
func (a *ResponseAssertion) StatusCode(expected int) *ResponseAssertion {
	assert.Equal(a.t, expected, a.response.StatusCode,
		"Expected status code %d, got %d. Response: %s",
		expected, a.response.StatusCode, a.response.RawBody)
	return a
}

// HasField 断言响应包含字段
func (a *ResponseAssertion) HasField(field string) *ResponseAssertion {
	assert.Contains(a.t, a.response.Body, field,
		"Response should contain field '%s'. Response: %s",
		field, a.response.RawBody)
	return a
}

// FieldEquals 断言字段值相等
func (a *ResponseAssertion) FieldEquals(field string, expected interface{}) *ResponseAssertion {
	if assert.Contains(a.t, a.response.Body, field) {
		// gin框架可能将数字解析为float64, 这里统一转换
		expectedValue := expected
		if _, ok := a.response.Body[field].(float64); ok {
			switch e := expected.(type) {
			case int:
				expectedValue = float64(e)
			case int32:
				expectedValue = float64(e)
			case int64:
				expectedValue = float64(e)
			}
		}
		assert.Equal(a.t, expectedValue, a.response.Body[field],
			"Field '%s' should equal %v, got %v",
			field, expected, a.response.Body[field])
	}
	return a
}

// DataField 获取data字段中的值
func (a *ResponseAssertion) DataField(field string) interface{} {
	if data, ok := a.response.Body["data"].(map[string]interface{}); ok {
		return data[field]
	}
	return nil
}

// DataFieldEquals 断言data字段中的值
func (a *ResponseAssertion) DataFieldEquals(field string, expected interface{}) *ResponseAssertion {
	value := a.DataField(field)
	assert.Equal(a.t, expected, value,
		"Data field '%s' should equal %v, got %v",
		field, expected, value)
	return a
}

// Success 断言请求成功（code=0）
func (a *ResponseAssertion) Success() *ResponseAssertion {
	return a.FieldEquals("code", 0)
}

// Error 断言请求失败（code!=0）
func (a *ResponseAssertion) Error() *ResponseAssertion {
	if code, ok := a.response.Body["code"]; ok {
		assert.NotEqual(a.t, 0, int(code.(float64)),
			"Expected error response, but got success. Response: %s",
			a.response.RawBody)
	} else {
		a.t.Errorf("Response missing 'code' field. Response: %s", a.response.RawBody)
	}
	return a
}

// ErrorMessage 断言错误消息
func (a *ResponseAssertion) ErrorMessage(expected string) *ResponseAssertion {
	return a.FieldEquals("message", expected)
}

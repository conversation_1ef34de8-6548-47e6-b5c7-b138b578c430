# Official API Design and Naming Convention

This document serves as the "codex" for our team. All APIs, both new and old, must adhere to these conventions.

## 1. Guiding Principles

1.  **Resource-Oriented**: The core of the API is nouns (resources), not verbs (actions).
2.  **Explicit Methods**: Strictly use HTTP methods (`GET`, `POST`, `PUT`, `PATCH`, `DELETE`) to express operations on resources.
3.  **Unified Case**: **Globally enforce `snake_case`** for URL paths, query parameters, and JSON data bodies.
4.  **Stateless**: The server does not maintain client state. Every request must contain all necessary information.
5.  **Versioning**: All APIs must be versioned via the path, e.g., `/api/v1/...`, to prepare for future evolution.

## 2. URL Structure Convention

### Resource Naming

- **MUST** use plural nouns to represent collections of resources.
- ✅ Correct: `/gigs`, `/users`, `/job_posts`
- ❌ Incorrect: `/gig`, `/userList`, `/create-job-post`

### Path Parameters

- **MUST** be enclosed in curly braces `{}` and use `snake_case`.
- ✅ Correct: `/gigs/{gig_id}/comments/{comment_id}`
- ❌ Incorrect: `/gigs/:gigId`

### Query Parameters

- **MUST** use `snake_case`.
- **Sorting**: Use the `sort` parameter. Separate multiple fields with a comma. Use a `-` prefix for descending order.
  - `GET /gigs?sort=-created_at,title` (Sort by creation time descending, then by title ascending)
- **Filtering**: Use parameter names directly for filtering.
  - `GET /gigs?status=active&category_id=3`
- **Pagination**:
  - `GET /gigs?page=1&page_size=20`
- **Sparse Fieldsets**: Allow the client to specify which fields to return to reduce bandwidth.
  - `GET /gigs?fields=id,title,salary_min,salary_max`

## 3. JSON Body Data Convention

- **Keys**: **MUST** use `snake_case`.
- **Timestamps**: **MUST** use `ISO 8601` format with timezone `Z`, e.g., `2023-10-27T15:04:05Z`.

### Standard Response Structure

#### Single Resource

```json
{
  "data": {
    "id": 123,
    "title": "...",
    "created_at": "2023-10-27T15:04:05Z"
  }
}
```

#### Resource Collection (with Pagination)

```json
{
  "data": [
    { "id": 123, "title": "..." },
    { "id": 124, "title": "..." }
  ],
  "meta": {
    "pagination": {
      "total": 50,
      "page": 1,
      "page_size": 20,
      "total_pages": 3
    }
  }
}
```

#### Error Response

```json
{
  "error": {
    "code": "INVALID_INPUT",
    "message": "Validation failed.",
    "details": {
      "title": "Title cannot be empty."
    }
  }
}
```

## 4. Frontend TypeScript Type Naming Convention

1.  **Interfaces/Types**: **MUST** use `PascalCase`, optionally with a descriptive suffix.
    - `Gig`: Represents the core gig object.
    - `GigCreateRequest`: The request body type for creating a gig.
    - `GigUpdateRequest`: The request body type for updating a gig.
    - `PaginatedGigsResponse`: The response type for a paginated list of gigs.

2.  **Variables/Properties**: **MUST** use `camelCase`.
    - **Note**: It is an industry standard for frontend and backend data formats to differ. The JS/TS ecosystem widely uses `camelCase`. We should implement an automatic transformation between `snake_case` (API) ⇔ `camelCase` (frontend app) in the network layer (e.g., `alova` or `axios` interceptors). Business logic should only ever deal with `camelCase`.
    - ✅ Correct: `const gigTitle = gig.jobTitle;`
    - ❌ Incorrect: `const gig_title = gig.job_title;` (in business logic)

3.  **Enums**: **MUST** use `PascalCase` for the enum name and its members.
    ```typescript
    export enum GigStatus {
      Active = 'active',
      Paused = 'paused',
      Closed = 'closed',
    }
    ``` 
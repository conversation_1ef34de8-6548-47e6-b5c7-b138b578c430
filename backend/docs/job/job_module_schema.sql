-- =================================================================
-- Filename: job_module_schema.sql
-- Description: 求职招聘模块核心数据表结构定义 (V3.0)
-- Author: Senior Technical Expert
-- Date: 2025-07-16
-- Version: 3.0 - 优化字段类型，增强功能，添加业务支持表
-- =================================================================

-- 启用必要的 PostgreSQL 扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";  -- 用于模糊搜索
CREATE EXTENSION IF NOT EXISTS "zhparser"; -- 用于中文分词

-- =================================================================
-- 基础用户与认证表
-- =================================================================

-- 用户实名认证表 (敏感信息隔离)
CREATE TABLE "user_verifications" (
    "user_id" int PRIMARY KEY,
    "encrypted_real_name" text DEFAULT NULL,
    "encrypted_id_number" text DEFAULT NULL,
    "encrypted_id_photos" text DEFAULT NULL,
    "masked_real_name" varchar(255) DEFAULT NULL,
    "masked_id_number" varchar(255) DEFAULT NULL,
    "status" varchar(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    "reject_reason" text DEFAULT NULL,
    "created_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE "user_verifications" IS '用户个人实名认证信息表';
COMMENT ON COLUMN "user_verifications"."user_id" IS '关联的用户ID';
COMMENT ON COLUMN "user_verifications"."encrypted_real_name" IS '加密后的真实姓名';
COMMENT ON COLUMN "user_verifications"."encrypted_id_number" IS '加密后的身份证号码';
COMMENT ON COLUMN "user_verifications"."encrypted_id_photos" IS '加密后的身份证照片URL(JSON)';
COMMENT ON COLUMN "user_verifications"."masked_real_name" IS '脱敏后的真实姓名 (例如: 张**)';
COMMENT ON COLUMN "user_verifications"."masked_id_number" IS '脱敏后的身份证号 (例如: 123******789)';
COMMENT ON COLUMN "user_verifications"."status" IS '认证状态 (pending, approved, rejected)';
COMMENT ON COLUMN "user_verifications"."reject_reason" IS '拒绝原因';

-- =================================================================
-- 企业与认证表
-- =================================================================

-- 企业信息表
CREATE TABLE "enterprises" (
    "id" serial PRIMARY KEY,
    "user_id" int NOT NULL,
    "name" varchar(255) NOT NULL DEFAULT '',
    "description" text DEFAULT '',
    "logo_url" text DEFAULT '',
    "type" varchar(20) NOT NULL DEFAULT 'enterprise' CHECK (type IN ('enterprise', 'small_business', 'individual')),
    "industry" varchar(100) DEFAULT '',
    "company_size" varchar(50) DEFAULT '',
    "contact_person" varchar(100) DEFAULT '',
    "contact_phone" varchar(50) DEFAULT '',
    "address" text DEFAULT '',
    "location" geometry(Point, 4326) DEFAULT NULL,
    "welfare_tags" text[] DEFAULT '{}',
    "created_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_del" smallint NOT NULL DEFAULT 0
);
COMMENT ON TABLE "enterprises" IS '企业/个体户信息档案表';
COMMENT ON COLUMN "enterprises"."id" IS '企业ID';
COMMENT ON COLUMN "enterprises"."user_id" IS '企业档案创建者/管理员的用户ID';
COMMENT ON COLUMN "enterprises"."name" IS '企业或个体户名称';
COMMENT ON COLUMN "enterprises"."description" IS '企业介绍';
COMMENT ON COLUMN "enterprises"."logo_url" IS '企业Logo图片URL';
COMMENT ON COLUMN "enterprises"."type" IS '主体类型 (enterprise:企业, small_business:个体户, individual:个人)';
COMMENT ON COLUMN "enterprises"."industry" IS '所属行业';
COMMENT ON COLUMN "enterprises"."company_size" IS '公司规模范围';
COMMENT ON COLUMN "enterprises"."contact_person" IS '招聘联系人';
COMMENT ON COLUMN "enterprises"."contact_phone" IS '招聘联系电话';
COMMENT ON COLUMN "enterprises"."address" IS '企业详细地址（用于展示）';
COMMENT ON COLUMN "enterprises"."location" IS '地理坐标（用于计算距离）';
COMMENT ON COLUMN "enterprises"."welfare_tags" IS '公司福利标签数组';
COMMENT ON COLUMN "enterprises"."is_del" IS '软删除标志 (0:未删除, 1:已删除)';

-- 企业资质认证表 (敏感信息隔离)
CREATE TABLE "enterprise_verifications" (
    "enterprise_id" int PRIMARY KEY,
    "verification_type" varchar(20) NOT NULL DEFAULT 'enterprise' CHECK (verification_type IN ('enterprise', 'small_business', 'individual')),
    "encrypted_credit_code" text DEFAULT NULL,
    "encrypted_license_url" text DEFAULT NULL,
    "encrypted_legal_person_name" text DEFAULT NULL,
    "encrypted_legal_person_id_number" text DEFAULT NULL,
    "encrypted_additional_docs" text DEFAULT NULL,
    "status" varchar(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    "reject_reason" text DEFAULT NULL,
    "created_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE "enterprise_verifications" IS '企业资质认证信息表';
COMMENT ON COLUMN "enterprise_verifications"."enterprise_id" IS '关联的企业ID';
COMMENT ON COLUMN "enterprise_verifications"."verification_type" IS '认证类型 (enterprise:企业, small_business:个体户, individual:个人)';
COMMENT ON COLUMN "enterprise_verifications"."encrypted_credit_code" IS '加密后的统一社会信用代码/营业执照号';
COMMENT ON COLUMN "enterprise_verifications"."encrypted_license_url" IS '加密后的营业执照URL';
COMMENT ON COLUMN "enterprise_verifications"."encrypted_legal_person_name" IS '加密后的法人姓名';
COMMENT ON COLUMN "enterprise_verifications"."encrypted_legal_person_id_number" IS '加密后的法人身份证号';
COMMENT ON COLUMN "enterprise_verifications"."encrypted_additional_docs" IS '加密后的其他补充材料(JSON)';
COMMENT ON COLUMN "enterprise_verifications"."status" IS '认证状态 (pending, approved, rejected)';
COMMENT ON COLUMN "enterprise_verifications"."reject_reason" IS '拒绝原因';

-- =================================================================
-- 职位与简历核心表
-- =================================================================

-- 职位信息表 (增强功能)
CREATE TABLE "jobs" (
    "id" serial PRIMARY KEY,
    "enterprise_id" int NOT NULL,
    "user_id" int NOT NULL,
    "title" varchar(255) NOT NULL DEFAULT '',
    "description" text DEFAULT '',
    "status" varchar(20) NOT NULL DEFAULT 'pending_review' CHECK (
        status IN ('pending_review', 'active', 'paused', 'closed', 'expired', 'rejected')
    ),
    "salary_min" int NOT NULL DEFAULT 0,
    "salary_max" int NOT NULL DEFAULT 0,
    "experience_req" smallint NOT NULL DEFAULT 0,
    "education_req" smallint NOT NULL DEFAULT 0,
    "work_type" smallint NOT NULL DEFAULT 1,
    "work_location" varchar(200) DEFAULT '',
    "location" geometry(Point, 4326) DEFAULT NULL,
    "remote_work_support" boolean DEFAULT false,
    "benefits" text[] DEFAULT '{}',
    "job_highlights" text[] DEFAULT '{}',
    "requirements" text[] DEFAULT '{}',
    "contact_method" varchar(20) DEFAULT 'phone' CHECK (contact_method IN ('phone', 'wechat', 'email')),
    "is_urgent" boolean NOT NULL DEFAULT false,
    "urgent_expires_at" timestamp(0) DEFAULT NULL,
    "last_refreshed_at" timestamp(0) DEFAULT CURRENT_TIMESTAMP,
    "today_refresh_count" int NOT NULL DEFAULT 0,
    "view_count" int NOT NULL DEFAULT 0,
    "application_count" int NOT NULL DEFAULT 0,
    "tsv" tsvector DEFAULT NULL,
    "created_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_del" smallint NOT NULL DEFAULT 0
);

-- 职位表索引优化
CREATE INDEX "jobs_status_idx" ON "jobs" ("status");
CREATE INDEX "jobs_enterprise_status_idx" ON "jobs" ("enterprise_id", "status");
CREATE INDEX "jobs_location_idx" ON "jobs" USING GIST ("location");
CREATE INDEX "jobs_composite_idx" ON "jobs" ("status", "last_refreshed_at" DESC, "is_urgent" DESC);
CREATE INDEX "jobs_salary_idx" ON "jobs" ("salary_min", "salary_max");
CREATE INDEX "jobs_work_type_idx" ON "jobs" ("work_type");
CREATE INDEX "jobs_tsv_idx" ON "jobs" USING GIN ("tsv");

COMMENT ON TABLE "jobs" IS '核心职位信息表';
COMMENT ON COLUMN "jobs"."id" IS '职位ID';
COMMENT ON COLUMN "jobs"."enterprise_id" IS '发布职位的企业ID';
COMMENT ON COLUMN "jobs"."user_id" IS '发布人ID';
COMMENT ON COLUMN "jobs"."title" IS '职位名称';
COMMENT ON COLUMN "jobs"."description" IS '职位详细描述';
COMMENT ON COLUMN "jobs"."status" IS '职位状态 (pending_review:待审核, active:招聘中, paused:暂停, closed:关闭, expired:过期, rejected:拒绝)';
COMMENT ON COLUMN "jobs"."salary_min" IS '最低薪资 (单位:元)';
COMMENT ON COLUMN "jobs"."salary_max" IS '最高薪资 (单位:元)';
COMMENT ON COLUMN "jobs"."experience_req" IS '经验要求 (使用constants/standards定义)';
COMMENT ON COLUMN "jobs"."education_req" IS '学历要求 (使用constants/standards定义)';
COMMENT ON COLUMN "jobs"."work_type" IS '工作类型 (1:全职, 2:兼职, 3:临时工, 4:远程)';
COMMENT ON COLUMN "jobs"."work_location" IS '工作地点';
COMMENT ON COLUMN "jobs"."location" IS '地理坐标（用于计算距离）';
COMMENT ON COLUMN "jobs"."remote_work_support" IS '是否支持远程工作';
COMMENT ON COLUMN "jobs"."benefits" IS '福利待遇数组';
COMMENT ON COLUMN "jobs"."job_highlights" IS '职位亮点数组';
COMMENT ON COLUMN "jobs"."requirements" IS '任职要求数组';
COMMENT ON COLUMN "jobs"."contact_method" IS '联系方式 (phone:电话, wechat:微信, email:邮箱)';
COMMENT ON COLUMN "jobs"."is_urgent" IS '是否为急聘';
COMMENT ON COLUMN "jobs"."urgent_expires_at" IS '急聘状态的过期时间';
COMMENT ON COLUMN "jobs"."last_refreshed_at" IS '最后刷新时间（用于排序）';
COMMENT ON COLUMN "jobs"."today_refresh_count" IS '当日已刷新次数';
COMMENT ON COLUMN "jobs"."view_count" IS '职位被浏览次数';
COMMENT ON COLUMN "jobs"."application_count" IS '申请人数';
COMMENT ON COLUMN "jobs"."tsv" IS '全文搜索的tsvector字段';
COMMENT ON COLUMN "jobs"."is_del" IS '软删除标志 (0:未删除, 1:已删除)';

-- 简历表 (去除状态字段，增强功能)
CREATE TABLE "resumes" (
    "id" serial PRIMARY KEY,
    "user_id" int NOT NULL UNIQUE,
    "name" varchar(100) DEFAULT '',
    "age" int DEFAULT 0,
    "gender" int DEFAULT 0,
    "phone" varchar(20) DEFAULT '',
    "email" varchar(100) DEFAULT '',
    "avatar_url" text DEFAULT '',
    "work_experience" jsonb DEFAULT '[]',
    "education_history" jsonb DEFAULT '[]',
    "skills" jsonb DEFAULT '[]',
    "job_intentions" jsonb DEFAULT '[]',
    "expected_salary_min" int DEFAULT 0,
    "expected_salary_max" int DEFAULT 0,
    "preferred_locations" text[] DEFAULT '{}',
    "work_status" varchar(20) DEFAULT 'currently_employed' CHECK (work_status IN ('currently_employed', 'unemployed', 'student')),
    "availability_date" date DEFAULT NULL,
    "self_introduction" text DEFAULT '',
    "created_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE "resumes" IS '用户简历表';
COMMENT ON COLUMN "resumes"."id" IS '简历ID';
COMMENT ON COLUMN "resumes"."user_id" IS '关联的用户ID (一个用户一份简历)';
COMMENT ON COLUMN "resumes"."name" IS '姓名';
COMMENT ON COLUMN "resumes"."age" IS '年龄';
COMMENT ON COLUMN "resumes"."gender" IS '性别 (使用constants/standards定义)';
COMMENT ON COLUMN "resumes"."phone" IS '手机号';
COMMENT ON COLUMN "resumes"."email" IS '邮箱';
COMMENT ON COLUMN "resumes"."avatar_url" IS '头像URL';
COMMENT ON COLUMN "resumes"."work_experience" IS '工作经历 (JSON数组)';
COMMENT ON COLUMN "resumes"."education_history" IS '教育背景 (JSON数组)';
COMMENT ON COLUMN "resumes"."skills" IS '技能标签 (JSON数组)';
COMMENT ON COLUMN "resumes"."job_intentions" IS '求职意向 (JSON数组)';
COMMENT ON COLUMN "resumes"."expected_salary_min" IS '期望薪资最低值 (单位:元)';
COMMENT ON COLUMN "resumes"."expected_salary_max" IS '期望薪资最高值 (单位:元)';
COMMENT ON COLUMN "resumes"."preferred_locations" IS '意向工作地点数组';
COMMENT ON COLUMN "resumes"."work_status" IS '工作状态 (currently_employed:在职, unemployed:离职, student:学生)';
COMMENT ON COLUMN "resumes"."availability_date" IS '可到岗时间';
COMMENT ON COLUMN "resumes"."self_introduction" IS '自我介绍';

-- 职位投递记录表 (增强状态管理)
CREATE TABLE "job_applications" (
    "id" serial PRIMARY KEY,
    "job_id" int NOT NULL,
    "user_id" int NOT NULL,
    "status" varchar(30) NOT NULL DEFAULT 'submitted' CHECK (
        status IN (
            'submitted', 'viewed', 'interview_invited', 'interviewing', 
            'interview_passed', 'interview_failed', 'hired', 'rejected'
        )
    ),
    "resume_snapshot" jsonb DEFAULT '{}',
    "recruiter_note" text DEFAULT '',
    "interview_time" timestamp(0) DEFAULT NULL,
    "interview_address" text DEFAULT '',
    "created_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE ("job_id", "user_id")
);

-- 投递记录表索引
CREATE INDEX "job_applications_user_status_idx" ON "job_applications" ("user_id", "status");
CREATE INDEX "job_applications_job_status_idx" ON "job_applications" ("job_id", "status");
CREATE INDEX "job_applications_created_at_idx" ON "job_applications" ("created_at" DESC);

COMMENT ON TABLE "job_applications" IS '职位投递记录表';
COMMENT ON COLUMN "job_applications"."id" IS '申请ID';
COMMENT ON COLUMN "job_applications"."job_id" IS '职位ID';
COMMENT ON COLUMN "job_applications"."user_id" IS '申请人ID';
COMMENT ON COLUMN "job_applications"."status" IS '投递状态 (submitted:已投递, viewed:已查看, interview_invited:邀请面试, interviewing:面试中, interview_passed:面试通过, interview_failed:面试未通过, hired:已录用, rejected:已拒绝)';
COMMENT ON COLUMN "job_applications"."resume_snapshot" IS '投递时简历的完整快照(JSONB)';
COMMENT ON COLUMN "job_applications"."recruiter_note" IS '招聘者备注';
COMMENT ON COLUMN "job_applications"."interview_time" IS '面试时间';
COMMENT ON COLUMN "job_applications"."interview_address" IS '面试地点';

-- =================================================================
-- 商业化与会员系统表
-- =================================================================

-- 会员套餐模板表
CREATE TABLE "membership_plans" (
    "id" serial PRIMARY KEY,
    "key" varchar(100) NOT NULL UNIQUE,
    "name" varchar(255) NOT NULL DEFAULT '',
    "description" text DEFAULT '',
    "price" decimal(10,2) DEFAULT 0.00,
    "duration_days" int DEFAULT 30,
    "benefits" jsonb DEFAULT '{}',
    "is_active" boolean NOT NULL DEFAULT true,
    "sort_order" int DEFAULT 0,
    "created_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE "membership_plans" IS '会员套餐模板表，由运营定义';
COMMENT ON COLUMN "membership_plans"."id" IS '套餐ID';
COMMENT ON COLUMN "membership_plans"."key" IS '套餐的唯一代码标识，如: spark_plan';
COMMENT ON COLUMN "membership_plans"."name" IS '套餐名称，如: 星火计划';
COMMENT ON COLUMN "membership_plans"."description" IS '套餐描述';
COMMENT ON COLUMN "membership_plans"."price" IS '套餐价格 (单位:元)';
COMMENT ON COLUMN "membership_plans"."duration_days" IS '有效期天数';
COMMENT ON COLUMN "membership_plans"."benefits" IS '套餐包含的权益详情(JSONB)';
COMMENT ON COLUMN "membership_plans"."is_active" IS '该套餐是否仍在售卖';
COMMENT ON COLUMN "membership_plans"."sort_order" IS '排序权重';

-- 企业订阅合约表
CREATE TABLE "subscriptions" (
    "id" serial PRIMARY KEY,
    "enterprise_id" int NOT NULL,
    "plan_id" int DEFAULT NULL,
    "plan_details_snapshot" jsonb DEFAULT '{}',
    "start_date" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "end_date" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" varchar(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'expired', 'cancelled')),
    "created_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 订阅表索引
CREATE INDEX "subscriptions_enterprise_id_idx" ON "subscriptions" ("enterprise_id");
CREATE INDEX "subscriptions_end_date_idx" ON "subscriptions" ("end_date");
CREATE INDEX "subscriptions_status_idx" ON "subscriptions" ("status");

COMMENT ON TABLE "subscriptions" IS '企业订阅合约表';
COMMENT ON COLUMN "subscriptions"."id" IS '订阅ID';
COMMENT ON COLUMN "subscriptions"."enterprise_id" IS '企业ID';
COMMENT ON COLUMN "subscriptions"."plan_id" IS '源套餐ID，可为空，用于追溯';
COMMENT ON COLUMN "subscriptions"."plan_details_snapshot" IS '购买时套餐的完整权益快照(JSONB)';
COMMENT ON COLUMN "subscriptions"."start_date" IS '订阅开始时间';
COMMENT ON COLUMN "subscriptions"."end_date" IS '订阅结束时间';
COMMENT ON COLUMN "subscriptions"."status" IS '订阅状态 (active:有效, expired:过期, cancelled:取消)';

-- =================================================================
-- 业务支持表
-- =================================================================

-- 收藏表
CREATE TABLE "favorites" (
    "id" serial PRIMARY KEY,
    "user_id" int NOT NULL,
    "job_id" int NOT NULL,
    "created_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE("user_id", "job_id")
);
CREATE INDEX "favorites_user_idx" ON "favorites" ("user_id");
CREATE INDEX "favorites_job_idx" ON "favorites" ("job_id");

COMMENT ON TABLE "favorites" IS '用户收藏职位表';
COMMENT ON COLUMN "favorites"."id" IS '收藏ID';
COMMENT ON COLUMN "favorites"."user_id" IS '用户ID';
COMMENT ON COLUMN "favorites"."job_id" IS '职位ID';

-- 浏览历史表
CREATE TABLE "view_history" (
    "id" serial PRIMARY KEY,
    "user_id" int NOT NULL,
    "job_id" int NOT NULL,
    "view_time" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX "view_history_user_time_idx" ON "view_history" ("user_id", "view_time" DESC);
CREATE INDEX "view_history_job_idx" ON "view_history" ("job_id");

COMMENT ON TABLE "view_history" IS '用户浏览历史表';
COMMENT ON COLUMN "view_history"."id" IS '浏览记录ID';
COMMENT ON COLUMN "view_history"."user_id" IS '用户ID';
COMMENT ON COLUMN "view_history"."job_id" IS '职位ID';
COMMENT ON COLUMN "view_history"."view_time" IS '浏览时间';

-- 消息通知表
CREATE TABLE "notifications" (
    "id" serial PRIMARY KEY,
    "user_id" int NOT NULL,
    "title" varchar(255) NOT NULL DEFAULT '',
    "content" text DEFAULT '',
    "type" varchar(20) NOT NULL DEFAULT 'system' CHECK (type IN ('system', 'application', 'interview', 'offer')),
    "related_id" int DEFAULT NULL,
    "is_read" boolean DEFAULT false,
    "created_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX "notifications_user_read_idx" ON "notifications" ("user_id", "is_read");
CREATE INDEX "notifications_type_idx" ON "notifications" ("type");

COMMENT ON TABLE "notifications" IS '消息通知表';
COMMENT ON COLUMN "notifications"."id" IS '通知ID';
COMMENT ON COLUMN "notifications"."user_id" IS '用户ID';
COMMENT ON COLUMN "notifications"."title" IS '通知标题';
COMMENT ON COLUMN "notifications"."content" IS '通知内容';
COMMENT ON COLUMN "notifications"."type" IS '通知类型 (system:系统, application:申请, interview:面试, offer:录用)';
COMMENT ON COLUMN "notifications"."related_id" IS '相关ID (如job_id, application_id等)';
COMMENT ON COLUMN "notifications"."is_read" IS '是否已读';

-- 反馈举报表
CREATE TABLE "reports" (
    "id" serial PRIMARY KEY,
    "reporter_user_id" int NOT NULL,
    "reported_job_id" int DEFAULT NULL,
    "reported_user_id" int DEFAULT NULL,
    "reason" varchar(100) NOT NULL DEFAULT '',
    "description" text DEFAULT '',
    "status" varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'resolved', 'rejected')),
    "created_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX "reports_reporter_idx" ON "reports" ("reporter_user_id");
CREATE INDEX "reports_status_idx" ON "reports" ("status");

COMMENT ON TABLE "reports" IS '反馈举报表';
COMMENT ON COLUMN "reports"."id" IS '举报ID';
COMMENT ON COLUMN "reports"."reporter_user_id" IS '举报人ID';
COMMENT ON COLUMN "reports"."reported_job_id" IS '被举报职位ID';
COMMENT ON COLUMN "reports"."reported_user_id" IS '被举报用户ID';
COMMENT ON COLUMN "reports"."reason" IS '举报原因';
COMMENT ON COLUMN "reports"."description" IS '详细描述';
COMMENT ON COLUMN "reports"."status" IS '处理状态 (pending:待处理, processing:处理中, resolved:已解决, rejected:已拒绝)';

-- =================================================================
-- 全文搜索配置
-- =================================================================

-- 创建中文全文搜索配置
CREATE TEXT SEARCH CONFIGURATION chinese_cfg (COPY = simple);
ALTER TEXT SEARCH CONFIGURATION chinese_cfg ALTER MAPPING FOR word, asciiword WITH zhparser;

-- 创建触发器函数更新tsvector
CREATE OR REPLACE FUNCTION update_job_tsv() RETURNS TRIGGER AS $$
BEGIN
    NEW.tsv := to_tsvector('chinese_cfg', COALESCE(NEW.title, '') || ' ' || COALESCE(NEW.description, ''));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER update_job_tsv_trigger
    BEFORE INSERT OR UPDATE ON jobs
    FOR EACH ROW EXECUTE FUNCTION update_job_tsv();

-- =================================================================
-- 表关系约束
-- =================================================================

-- 用户相关约束
ALTER TABLE "user_verifications"
ADD CONSTRAINT "fk_user_verifications_user_id" 
FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;

-- 企业相关约束
ALTER TABLE "enterprises"
ADD CONSTRAINT "fk_enterprises_user_id" 
FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE RESTRICT;

ALTER TABLE "enterprise_verifications"
ADD CONSTRAINT "fk_enterprise_verifications_enterprise_id" 
FOREIGN KEY ("enterprise_id") REFERENCES "enterprises" ("id") ON DELETE CASCADE;

-- 职位相关约束
ALTER TABLE "jobs"
ADD CONSTRAINT "fk_jobs_enterprise_id" 
FOREIGN KEY ("enterprise_id") REFERENCES "enterprises" ("id") ON DELETE CASCADE;

ALTER TABLE "jobs"
ADD CONSTRAINT "fk_jobs_user_id" 
FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;

-- 简历相关约束
ALTER TABLE "resumes"
ADD CONSTRAINT "fk_resumes_user_id" 
FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;

-- 申请相关约束
ALTER TABLE "job_applications"
ADD CONSTRAINT "fk_job_applications_job_id" 
FOREIGN KEY ("job_id") REFERENCES "jobs" ("id") ON DELETE CASCADE;

ALTER TABLE "job_applications"
ADD CONSTRAINT "fk_job_applications_user_id" 
FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;

-- 订阅相关约束
ALTER TABLE "subscriptions"
ADD CONSTRAINT "fk_subscriptions_enterprise_id" 
FOREIGN KEY ("enterprise_id") REFERENCES "enterprises" ("id") ON DELETE CASCADE;

ALTER TABLE "subscriptions"
ADD CONSTRAINT "fk_subscriptions_plan_id" 
FOREIGN KEY ("plan_id") REFERENCES "membership_plans" ("id") ON DELETE SET NULL;

-- 业务支持表约束
ALTER TABLE "favorites"
ADD CONSTRAINT "fk_favorites_user_id" 
FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;

ALTER TABLE "favorites"
ADD CONSTRAINT "fk_favorites_job_id" 
FOREIGN KEY ("job_id") REFERENCES "jobs" ("id") ON DELETE CASCADE;

ALTER TABLE "view_history"
ADD CONSTRAINT "fk_view_history_user_id" 
FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;

ALTER TABLE "view_history"
ADD CONSTRAINT "fk_view_history_job_id" 
FOREIGN KEY ("job_id") REFERENCES "jobs" ("id") ON DELETE CASCADE;

ALTER TABLE "notifications"
ADD CONSTRAINT "fk_notifications_user_id" 
FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;

ALTER TABLE "reports"
ADD CONSTRAINT "fk_reports_reporter_user_id" 
FOREIGN KEY ("reporter_user_id") REFERENCES "users" ("id") ON DELETE CASCADE;

ALTER TABLE "reports"
ADD CONSTRAINT "fk_reports_reported_job_id" 
FOREIGN KEY ("reported_job_id") REFERENCES "jobs" ("id") ON DELETE CASCADE;

ALTER TABLE "reports"
ADD CONSTRAINT "fk_reports_reported_user_id" 
FOREIGN KEY ("reported_user_id") REFERENCES "users" ("id") ON DELETE CASCADE;

-- =================================================================
-- 初始化数据
-- =================================================================

-- 插入基础会员套餐
INSERT INTO "membership_plans" ("key", "name", "description", "price", "duration_days", "benefits", "sort_order") VALUES
('free_trial', '免费试用', '新用户免费试用套餐', 0.00, 7, '{"job_post_limit": 2, "daily_refresh_limit": 1, "resume_view_limit": 10}', 1),
('basic_plan', '基础套餐', '个人招聘者基础套餐', 99.00, 30, '{"job_post_limit": 5, "daily_refresh_limit": 3, "resume_view_limit": 50, "priority_display": false}', 2),
('standard_plan', '标准套餐', '中小企业标准套餐', 299.00, 30, '{"job_post_limit": 15, "daily_refresh_limit": 5, "resume_view_limit": 200, "priority_display": true, "urgent_job_limit": 3}', 3),
('premium_plan', '高级套餐', '大型企业高级套餐', 599.00, 30, '{"job_post_limit": 50, "daily_refresh_limit": 10, "resume_view_limit": 500, "priority_display": true, "urgent_job_limit": 10, "analytics_access": true}', 4);
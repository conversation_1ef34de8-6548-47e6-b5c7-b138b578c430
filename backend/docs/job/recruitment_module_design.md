# 招聘模块 - 功能架构与实施方案

## 1. 概述

本文档是为“求职招聘”核心模块制定的V2.0版本技术与产品方案。基于V1.0方案的反馈，本方案在数据结构深度、安全性、业务逻辑灵活性及未来可扩展性上进行了显著加强，旨在为项目构建一个健壮、可扩展的招聘平台。

**核心设计原则:**
- **数据驱动:** 所有功能设计都围绕核心数据模型展开。
- **安全第一:** 对用户敏感信息采用应用层加密和物理隔离存储。
- **解耦与快照:** 通过“合约快照”模式解耦业务逻辑，保证系统的长期灵活性。
- **高性能:** 针对核心查询场景（如职位搜索）设计专门的数据库优化方案。
- **可扩展性:** 预留通知、IM、数据分析等系统的接口和设计。

## 2. 最终数据库设计蓝图 (ERD V2.0)

```mermaid
erDiagram
    users {
        uint id PK
        string openid
        string phone
        string nickname
        string avatar_url
    }

    user_verifications {
        uint user_id PK, FK
        string encrypted_real_name
        string encrypted_id_number
        string encrypted_id_photos
        varchar masked_real_name
        varchar masked_id_number
        varchar status "pending, approved, rejected"
    }

    enterprises {
        uint id PK
        uint owner_user_id FK
        string name
        text description
        string logo_url
        varchar type "enterprise, small_business"
        string industry
        varchar company_size
        string contact_person
        string contact_phone
        text address
        geometry location
        text[] welfare_tags
    }

    enterprise_verifications {
        uint enterprise_id PK, FK
        string encrypted_credit_code
        string encrypted_license_url
        string encrypted_legal_person_name
        string encrypted_legal_person_id_number
        varchar status "pending, approved, rejected"
    }

    jobs {
        uint id PK
        uint enterprise_id FK
        uint user_id FK
        string title
        text description
        varchar status "active, closed, pending_review, rejected"
        int salary_min
        int salary_max
        varchar experience_req
        varchar education_req
        boolean is_urgent
        timestamp urgent_expires_at
        timestamp last_refreshed_at
        int today_refresh_count
        int view_count
        tsvector tsv
    }

    resumes {
        uint id PK
        uint user_id FK
        string name
        int age
        varchar gender
        jsonb work_experience
        jsonb education_history
        jsonb skills
        jsonb job_intentions
    }

    job_applications {
        uint id PK
        uint job_id FK
        uint user_id FK
        varchar status "submitted, viewed, interview_requested, interviewed, offer_sent, rejected, hired"
        jsonb resume_snapshot
    }

    membership_plans {
        uint id PK
        string key
        string name
        jsonb benefits
    }

    subscriptions {
        uint id PK
        uint enterprise_id FK
        uint plan_id FK
        jsonb plan_details_snapshot
        timestamp start_date
        timestamp end_date
    }

    users ||--|{ user_verifications : "实名认证"
    users ||--o{ enterprises : "创建"
    users ||--o{ resumes : "拥有"
    users ||--o{ job_applications : "申请"
    enterprises ||--|{ enterprise_verifications : "资质认证"
    enterprises ||--o{ jobs : "发布"
    enterprises ||--o{ subscriptions : "订阅"
    jobs ||--o{ job_applications : "收到申请"
    subscriptions }|..|| membership_plans : "源于"
```

## 3. 核心表结构详解

**注：** 根据要求，所有`ENUM`类型均替换为`VARCHAR`，并通过`CHECK`约束保证数据完整性。

### 3.1 `users` & `user_verifications`
- **`users`**: 存储用户基础信息。
- **`user_verifications`**: (1-to-1 with `users`) 存储用户个人实名认证的敏感信息。
  - `encrypted_*` 字段: **必须在应用层使用AES等强加密算法加密后存储**。
  - `masked_*` 字段: 用于存储脱敏后的信息（如`张**`，`123******789`），以便安全地在前端展示。
  - `status`: `VARCHAR(20) CHECK (status IN ('pending', 'approved', 'rejected'))`。
  - 应用层加密 (Application-Level Encryption):
在将数据存入 verifications 表之前，在 Go 后端应用层对敏感字段（如 credit_code, license_url, id_card_number, id_card_photos）进行加密处理（推荐使用 AES-256-GCM 算法）。
数据库中只存储加密后的密文。密钥通过安全的方式管理（如环境变量、HashiCorp Vault 或云服务商的 KMS），绝不硬编码。
优势: 即使数据库被脱库，攻击者也无法获取原始敏感信息，最大限度保障用户数据安全。

### 3.2 `enterprises` & `enterprise_verifications`
- **`enterprises`**: 存储企业/个体户的公开信息和档案。
  - `type`: `VARCHAR(20) CHECK (type IN ('enterprise', 'small_business'))`。
  - `company_size`: `VARCHAR(20) CHECK (company_size IN ('1-49人', '50-199人', '200-999人', '1000人以上'))`。
  - `location`: PostGIS `geometry(Point, 4326)` 类型，用于地理位置计算。
- **`enterprise_verifications`**: (1-to-1 with `enterprises`) 存储企业资质认证的敏感信息。
  - `encrypted_*` 字段: **应用层加密存储**，包括法人的姓名和身份证号。
  - `status`: `VARCHAR(20) CHECK (status IN ('pending', 'approved', 'rejected'))`。

### 3.3 `jobs`
- **`jobs`**: 核心职位信息表。
  - `status`: `VARCHAR(20) CHECK (status IN ('active', 'closed', 'pending_review', 'rejected'))`。
  - `experience_req`, `education_req`: `VARCHAR` 类型，前端定义范围，后端校验。
  - `last_refreshed_at`: 排序关键字段。每次刷新职位时更新此时间戳。
  - `today_refresh_count`: 当日已刷新次数，用于配合会员权益。**需要后台定时任务每日清零**。
  - `tsv`: `TSVECTOR` 类型，用于支持中文全文检索。**通过触发器自动生成和更新**。

### 3.4 `resumes`
- **`resumes`**: 用户简历表。
  - `work_experience`, `education_history`, `skills`, `job_intentions`: 采用 `JSONB` 类型，提供灵活性和高性能的内部查询。

### 3.5 `job_applications`
- **`job_applications`**: 职位投递记录表。
  - `status`: `VARCHAR(30) CHECK (status IN ('submitted', 'viewed', 'interview_requested', 'interviewed', 'offer_sent', 'rejected', 'hired'))`。
  - `resume_snapshot`: `JSONB` 类型，**核心字段**。在投递时，将`resumes`表内容完整复制一份作为快照，保证招聘方看到的是投递时刻的简历。

### 3.6 `membership_plans` & `subscriptions`
- **`membership_plans`**: 会员套餐模板表，由运营定义。
  - `benefits`: `JSONB` 类型，存储该套餐的具体权益，如 `{"job_post_limit": 10, "daily_refresh_limit": 5}`。
- **`subscriptions`**: 企业订阅合约表，记录企业购买的具体合约。
  - `plan_details_snapshot`: `JSONB` 类型，**核心字段**。购买时，将`membership_plans`的`benefits`及价格、名称等信息完整复制一份作为快照。**系统判断权益时，只应读取此字段**。

## 4. 实施计划 (Roadmap)

### 阶段一：核心 MVP - 跑通双边流程 (预计 3 周)

此阶段目标是让求职者能找到并投递职位，招聘者能发布并管理职位。

- **Step 1.1: 数据库与基础架构 (3 天)**
  - [ ] 在 PostgreSQL 中创建 V2.0 方案的所有数据表、`CHECK`约束和关系。
  - [ ] 初始化 Go 项目结构，包括 `internal/model`, `internal/repository`, `internal/service`, `internal/controller` 等目录。
  - [ ] 配置好数据库连接、日志、配置管理等基础组件。

- **Step 1.2: 用户认证与简历模块 (4 天)**
  - [ ] **后端**: 开发用户注册/登录 (微信小程序) API。
  - [ ] **后端**: 开发简历的 `CRUD` (创建/读取/更新/删除) API。
  - [ ] **前端**: 对接上述 API，确保用户可以登录并完整地编辑和保存自己的简历。

- **Step 1.3: 企业认证与职位发布模块 (5 天)**
  - [ ] **后端**: 开发企业/个体户/个人认证信息提交 API (包含加密逻辑)。
  - [ ] **后端**: 开发职位 `CRUD` API，允许已认证用户发布和编辑职位。
  - [ ] **前端**: 对接企业认证和职位发布页面。

- **Step 1.4: 核心流程闭环 (3 天)**
  - [ ] **后端**: 开发职位列表获取 API (此时为简单分页，不含高级搜索)。
  - [ ] **后端**: 开发职位详情获取 API。
  - [ ] **后端**: 开发职位投递 (`job_applications`) API，包含“简历快照”逻辑。
  - [ ] **前端**: 对接职位列表、详情页，并实现投递功能。

### 阶段二：增强体验与商业化 (预计 4 周)

此阶段目标是提升核心功能的使用体验，并上线付费模块。

- **Step 2.1: 高级搜索与筛选 (5 天)**
  - [ ] **DBA/后端**: 在 PostgreSQL 中安装 `zhparser` 插件，并为`jobs`表的`tsv`字段设置好触发器和`GIN`索引。
  - [ ] **后端**: 改造职位列表 API，支持基于`tsvector`的全文检索和多条件组合筛选。
  - [ ] **前端**: 对接新的搜索和筛选 API，实现丝滑的职位查找体验。

- **Step 2.2: 职位与人才管理 (5 天)**
  - [ ] **后端**: 开发职位管理相关 API：刷新、下线、数据统计(`view_count`等)。
  - [ ] **后端**: 开发后台调度器，用于每日重置`today_refresh_count`。
  - [ ] **后端**: 开发人才库(简历)的搜索与筛选 API。
  - [ ] **前端**: 完善职位管理页面和人才库页面的功能。

- **Step 2.3: 会员与支付系统 (6 天)**
  - [ ] **后端**: 开发会员套餐(`membership_plans`)的后台管理接口。
  - [ ] **后端**: 开发创建订阅(`subscriptions`)合约的 API，并集成微信支付。
  - [ ] **后端**: 开发支付回调处理逻辑，成功后更新`subscriptions`表。
  - [ ] **前端**: 开发会员购买页面，并对接支付流程。

### 阶段三：生态与智能 (持续迭代)

此阶段目标是丰富产品生态，并通过数据驱动提升平台价值。

- **Step 3.1: 消息与通知系统 (按需)**
  - [ ] **后端**: 设计并开发统一的通知中心 API。
  - [ ] **后端**: 集成`Centrifugo`或`WebSocket`，开发实时聊天(IM)的核心 API。
  - [ ] **前端**: 开发消息中心和聊天界面。

- **Step 3.2: 数据分析与运营后台 (按需)**
  - [ ] **后端**: 建立数据埋点管道，将用户行为数据发送至数据仓库。
  - [ ] **后端**: 开发运营后台，提供数据报表、用户管理、内容审核等功能。

- **Step 3.3: 智能化推荐 (长期)**
  - [ ] **算法/后端**: 基于用户行为和职位画像，开发职位推荐、人才推荐算法模型。
  - [ ] **后端**: 将推荐算法集成到核心列表 API 中。 
# 求职招聘模块 - 详细开发计划

## 📋 文档信息

- **创建时间**: 2025-07-16
- **版本**: v1.0
- **负责人**: 开发团队
- **状态**: 待执行

## 🎯 项目概述

本文档为求职招聘模块的详细开发计划，基于前端页面分析和数据库设计，制定完整的开发路线图。该模块是应用的核心功能，支持双角色（求职者/招聘者）的完整业务流程。

### 核心功能特性
- 🏢 企业认证与管理
- 💼 职位发布与管理
- 📄 简历管理与投递
- 🔍 智能搜索与匹配
- 💰 会员系统与支付
- 📊 数据统计与分析

## 📊 业务状态体系

### 1. 职位状态 (Job Status)

| 状态值 | 中文名称 | 描述 | 可转换状态 |
|--------|----------|------|-----------|
| `pending_review` | 待审核 | 刚发布的职位，等待平台审核 | `active`, `rejected` |
| `active` | 招聘中 | 审核通过，正在招聘 | `paused`, `closed`, `expired` |
| `paused` | 暂停招聘 | 企业主动暂停招聘 | `active`, `closed` |
| `closed` | 已关闭 | 企业主动关闭或招聘完成 | - |
| `expired` | 已过期 | 职位到期自动过期 | - |
| `rejected` | 审核拒绝 | 平台审核不通过 | - |

### 2. 简历状态 (Resume Status)

| 状态值 | 中文名称 | 描述 | 可转换状态 |
|--------|----------|------|-----------|
| `draft` | 草稿 | 简历编辑中，未公开 | `published` |
| `published` | 已发布 | 简历公开，可投递 | `paused` |
| `paused` | 暂停投递 | 暂停接收新的职位推荐 | `published` |

### 3. 投递申请状态 (Application Status)

| 状态值 | 中文名称 | 描述 | 可转换状态 |
|--------|----------|------|-----------|
| `submitted` | 已投递 | 求职者刚提交申请 | `viewed`, `rejected` |
| `viewed` | 已查看 | 招聘者查看了简历 | `interview_invited`, `rejected` |
| `interview_invited` | 邀请面试 | 邀请求职者面试 | `interviewing`, `rejected` |
| `interviewing` | 面试中 | 面试进行中 | `interview_passed`, `interview_failed` |
| `interview_passed` | 面试通过 | 面试通过待录用 | `hired`, `rejected` |
| `interview_failed` | 面试未通过 | 面试未通过 | - |
| `hired` | 已录用 | 成功录用 | - |
| `rejected` | 已拒绝 | 任何阶段被拒绝 | - |

### 4. 认证状态 (Verification Status)

| 状态值 | 中文名称 | 描述 | 可转换状态 |
|--------|----------|------|-----------|
| `pending` | 待认证 | 提交认证资料，等待审核 | `approved`, `rejected` |
| `approved` | 认证通过 | 认证审核通过 | - |
| `rejected` | 认证拒绝 | 认证审核不通过 | `pending` |

### 5. 订阅状态 (Subscription Status)

| 状态值 | 中文名称 | 描述 | 可转换状态 |
|--------|----------|------|-----------|
| `active` | 有效 | 订阅有效期内 | `expired`, `cancelled` |
| `expired` | 已过期 | 订阅过期 | `active` |
| `cancelled` | 已取消 | 主动取消订阅 | `active` |

## 🔄 前后端状态常量定义

### 后端 Go 常量定义

```go
// backend/internal/constants/job_constants.go
package constants

// 职位状态
const (
    JobStatusPendingReview = "pending_review"  // 待审核
    JobStatusActive        = "active"          // 招聘中
    JobStatusPaused        = "paused"          // 暂停招聘
    JobStatusClosed        = "closed"          // 已关闭
    JobStatusExpired       = "expired"         // 已过期
    JobStatusRejected      = "rejected"        // 审核拒绝
)

// 简历状态
const (
    ResumeStatusDraft     = "draft"     // 草稿
    ResumeStatusPublished = "published" // 已发布
    ResumeStatusPaused    = "paused"    // 暂停投递
)

// 投递申请状态
const (
    ApplicationStatusSubmitted        = "submitted"         // 已投递
    ApplicationStatusViewed          = "viewed"            // 已查看
    ApplicationStatusInterviewInvited = "interview_invited" // 邀请面试
    ApplicationStatusInterviewing    = "interviewing"      // 面试中
    ApplicationStatusInterviewPassed = "interview_passed"  // 面试通过
    ApplicationStatusInterviewFailed = "interview_failed"  // 面试未通过
    ApplicationStatusHired           = "hired"             // 已录用
    ApplicationStatusRejected        = "rejected"          // 已拒绝
)

// 认证状态
const (
    VerificationStatusPending  = "pending"  // 待认证
    VerificationStatusApproved = "approved" // 认证通过
    VerificationStatusRejected = "rejected" // 认证拒绝
)

// 订阅状态
const (
    SubscriptionStatusActive    = "active"    // 有效
    SubscriptionStatusExpired   = "expired"   // 已过期
    SubscriptionStatusCancelled = "cancelled" // 已取消
)

// 企业认证类型
const (
    EnterpriseTypeEnterprise    = "enterprise"     // 企业
    EnterpriseTypeSmallBusiness = "small_business" // 个体户
    EnterpriseTypeIndividual    = "individual"     // 个人
)

// 工作类型
const (
    WorkTypeFullTime   = "full_time"   // 全职
    WorkTypePartTime   = "part_time"   // 兼职
    WorkTypeInternship = "internship"  // 实习
    WorkTypeContract   = "contract"    // 合同工
)

// 经验要求
const (
    ExperienceNoLimit     = "no_limit"      // 经验不限
    ExperienceEntry       = "entry"         // 应届生
    ExperienceJunior      = "1-3_years"     // 1-3年
    ExperienceMiddle      = "3-5_years"     // 3-5年
    ExperienceSenior      = "5-10_years"    // 5-10年
    ExperienceExpert      = "10_plus_years" // 10年以上
)

// 学历要求
const (
    EducationNoLimit     = "no_limit"      // 学历不限
    EducationJuniorHigh  = "junior_high"   // 初中
    EducationHigh        = "high_school"   // 高中
    EducationJuniorCollege = "junior_college" // 大专
    EducationBachelor    = "bachelor"      // 本科
    EducationMaster      = "master"        // 硕士
    EducationDoctorate   = "doctorate"     // 博士
)
```

### 前端 TypeScript 枚举定义

```typescript
// front/src/constants/jobConstants.ts

// 职位状态
export enum JobStatus {
  PendingReview = 'pending_review',  // 待审核
  Active = 'active',                 // 招聘中
  Paused = 'paused',                 // 暂停招聘
  Closed = 'closed',                 // 已关闭
  Expired = 'expired',               // 已过期
  Rejected = 'rejected',             // 审核拒绝
}

// 简历状态
export enum ResumeStatus {
  Draft = 'draft',                   // 草稿
  Published = 'published',           // 已发布
  Paused = 'paused',                 // 暂停投递
}

// 投递申请状态
export enum ApplicationStatus {
  Submitted = 'submitted',           // 已投递
  Viewed = 'viewed',                 // 已查看
  InterviewInvited = 'interview_invited', // 邀请面试
  Interviewing = 'interviewing',     // 面试中
  InterviewPassed = 'interview_passed',   // 面试通过
  InterviewFailed = 'interview_failed',   // 面试未通过
  Hired = 'hired',                   // 已录用
  Rejected = 'rejected',             // 已拒绝
}

// 认证状态
export enum VerificationStatus {
  Pending = 'pending',               // 待认证
  Approved = 'approved',             // 认证通过
  Rejected = 'rejected',             // 认证拒绝
}

// 订阅状态
export enum SubscriptionStatus {
  Active = 'active',                 // 有效
  Expired = 'expired',               // 已过期
  Cancelled = 'cancelled',           // 已取消
}

// 企业认证类型
export enum EnterpriseType {
  Enterprise = 'enterprise',         // 企业
  SmallBusiness = 'small_business',  // 个体户
  Individual = 'individual',         // 个人
}

// 工作类型
export enum WorkType {
  FullTime = 'full_time',            // 全职
  PartTime = 'part_time',            // 兼职
  Internship = 'internship',         // 实习
  Contract = 'contract',             // 合同工
}

// 经验要求
export enum ExperienceRequirement {
  NoLimit = 'no_limit',              // 经验不限
  Entry = 'entry',                   // 应届生
  Junior = '1-3_years',              // 1-3年
  Middle = '3-5_years',              // 3-5年
  Senior = '5-10_years',             // 5-10年
  Expert = '10_plus_years',          // 10年以上
}

// 学历要求
export enum EducationRequirement {
  NoLimit = 'no_limit',              // 学历不限
  JuniorHigh = 'junior_high',        // 初中
  High = 'high_school',              // 高中
  JuniorCollege = 'junior_college',  // 大专
  Bachelor = 'bachelor',             // 本科
  Master = 'master',                 // 硕士
  Doctorate = 'doctorate',           // 博士
}

// 状态显示文本映射
export const JobStatusText = {
  [JobStatus.PendingReview]: '待审核',
  [JobStatus.Active]: '招聘中',
  [JobStatus.Paused]: '暂停招聘',
  [JobStatus.Closed]: '已关闭',
  [JobStatus.Expired]: '已过期',
  [JobStatus.Rejected]: '审核拒绝',
}

export const ApplicationStatusText = {
  [ApplicationStatus.Submitted]: '已投递',
  [ApplicationStatus.Viewed]: '已查看',
  [ApplicationStatus.InterviewInvited]: '邀请面试',
  [ApplicationStatus.Interviewing]: '面试中',
  [ApplicationStatus.InterviewPassed]: '面试通过',
  [ApplicationStatus.InterviewFailed]: '面试未通过',
  [ApplicationStatus.Hired]: '已录用',
  [ApplicationStatus.Rejected]: '已拒绝',
}

export const VerificationStatusText = {
  [VerificationStatus.Pending]: '待认证',
  [VerificationStatus.Approved]: '认证通过',
  [VerificationStatus.Rejected]: '认证拒绝',
}
```

## 🗃️ 数据库设计优化

### 核心表结构改进

#### 1. 职位表 (jobs) 增强字段

```sql
-- 在原有基础上增加字段
ALTER TABLE jobs ADD COLUMN location geometry(Point, 4326);
ALTER TABLE jobs ADD COLUMN work_type varchar(20) CHECK (work_type IN ('full_time', 'part_time', 'internship', 'contract'));
ALTER TABLE jobs ADD COLUMN benefits text[];
ALTER TABLE jobs ADD COLUMN contact_method varchar(20) CHECK (contact_method IN ('phone', 'wechat', 'email'));
ALTER TABLE jobs ADD COLUMN work_location varchar(200);
ALTER TABLE jobs ADD COLUMN remote_work_support boolean DEFAULT false;
ALTER TABLE jobs ADD COLUMN job_highlights text[];
ALTER TABLE jobs ADD COLUMN requirements text[];
```

#### 2. 简历表 (resumes) 增强字段

```sql
-- 在原有基础上增加字段
ALTER TABLE resumes ADD COLUMN expected_salary_min int;
ALTER TABLE resumes ADD COLUMN expected_salary_max int;
ALTER TABLE resumes ADD COLUMN preferred_locations text[];
ALTER TABLE resumes ADD COLUMN work_status varchar(20) CHECK (work_status IN ('currently_employed', 'unemployed', 'student'));
ALTER TABLE resumes ADD COLUMN availability_date date;
ALTER TABLE resumes ADD COLUMN status varchar(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'paused'));
```

#### 3. 新增业务支持表

```sql
-- 收藏表
CREATE TABLE favorites (
    id serial PRIMARY KEY,
    user_id int NOT NULL,
    job_id int NOT NULL,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, job_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE
);

-- 浏览历史表
CREATE TABLE view_history (
    id serial PRIMARY KEY,
    user_id int NOT NULL,
    job_id int NOT NULL,
    view_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE
);

-- 消息通知表
CREATE TABLE notifications (
    id serial PRIMARY KEY,
    user_id int NOT NULL,
    title varchar(255) NOT NULL,
    content text,
    type varchar(20) CHECK (type IN ('system', 'application', 'interview', 'offer')),
    is_read boolean DEFAULT false,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 反馈举报表
CREATE TABLE reports (
    id serial PRIMARY KEY,
    reporter_user_id int NOT NULL,
    reported_job_id int,
    reported_user_id int,
    reason varchar(100) NOT NULL,
    description text,
    status varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'resolved', 'rejected')),
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (reporter_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### 4. 索引优化

```sql
-- 核心查询优化索引
CREATE INDEX jobs_location_idx ON jobs USING GIST (location);
CREATE INDEX jobs_composite_idx ON jobs (status, last_refreshed_at DESC, is_urgent DESC);
CREATE INDEX jobs_salary_idx ON jobs (salary_min, salary_max);
CREATE INDEX jobs_enterprise_status_idx ON jobs (enterprise_id, status);
CREATE INDEX jobs_work_type_idx ON jobs (work_type);

-- 应用投递查询优化
CREATE INDEX job_applications_user_status_idx ON job_applications (user_id, status);
CREATE INDEX job_applications_job_status_idx ON job_applications (job_id, status);

-- 浏览历史查询优化
CREATE INDEX view_history_user_time_idx ON view_history (user_id, view_time DESC);

-- 收藏查询优化
CREATE INDEX favorites_user_idx ON favorites (user_id);

-- 通知查询优化
CREATE INDEX notifications_user_read_idx ON notifications (user_id, is_read);
```

## 🚀 详细开发计划

### Phase 1: 数据库创建与模型定义 (第1周)

**优先级：🔥 极高**

#### 1.1 数据库创建 (2天)
- [ ] 执行完整的建表SQL脚本
- [ ] 创建所有索引和约束
- [ ] 验证表结构和关系
- [ ] 初始化基础数据（会员套餐等）

#### 1.2 Go模型定义 (2天)
- [ ] 定义所有表对应的struct
- [ ] 配置GORM标签和关系
- [ ] 创建状态常量文件
- [ ] 实现模型验证方法

#### 1.3 前端类型定义 (1天)
- [ ] 创建TypeScript接口
- [ ] 定义状态枚举
- [ ] 创建常量文件
- [ ] 配置API类型

**交付物：**
- 完整的数据库结构
- 后端模型定义
- 前端类型定义
- 状态常量文件

---

### Phase 2: 企业认证与会员系统 (第2-3周)

**优先级：🔥 极高**

#### 2.1 企业认证系统 (第2周)

**后端开发：**
- [ ] 企业档案CRUD API
- [ ] 企业认证API（三种类型）
- [ ] 敏感信息加密/解密工具
- [ ] 认证状态管理

**前端开发：**
- [ ] 企业认证页面 (`company-auth.vue`)
- [ ] 三种认证类型支持
- [ ] 文件上传组件
- [ ] 认证状态展示

**API设计：**
```yaml
POST /api/v1/enterprises           # 创建企业档案
GET /api/v1/enterprises/me         # 获取我的企业信息
PUT /api/v1/enterprises/{id}       # 更新企业信息
POST /api/v1/enterprise_verifications    # 提交认证申请
GET /api/v1/enterprise_verifications/me  # 获取认证状态
```

#### 2.2 会员套餐系统 (第3周)

**后端开发：**
- [ ] 会员套餐管理API
- [ ] 订阅购买API
- [ ] 权益验证中间件
- [ ] 套餐权益计算

**前端开发：**
- [ ] 会员套餐页面 (`enterprise-cooperation.vue`)
- [ ] 权益展示组件
- [ ] 购买流程页面
- [ ] 会员状态展示

**交付物：**
- 企业认证完整流程
- 会员套餐系统
- 权益验证机制

---

### Phase 3: 简历管理系统 (第4周)

**优先级：🔥 极高**

#### 3.1 简历CRUD系统 (前半周)

**后端开发：**
- [ ] 简历CRUD API
- [ ] 简历状态管理
- [ ] 简历搜索API
- [ ] 简历导出功能

**前端开发：**
- [ ] 简历编辑页面 (`resume/edit.vue`)
- [ ] 简历预览页面 (`resume/preview.vue`)
- [ ] 简历状态管理
- [ ] 工作经历组件

#### 3.2 投递申请系统 (后半周)

**后端开发：**
- [ ] 投递申请API
- [ ] 简历快照逻辑
- [ ] 申请状态管理
- [ ] 申请记录查询

**前端开发：**
- [ ] 投递申请流程
- [ ] 申请状态展示
- [ ] 申请记录页面
- [ ] 状态变更通知

**交付物：**
- 完整的简历管理系统
- 投递申请流程
- 简历快照机制

---

### Phase 4: 职位发布与管理 (第5-6周)

**优先级：🔥 极高**

#### 4.1 职位发布系统 (第5周)

**后端开发：**
- [ ] 职位CRUD API
- [ ] 发布权益验证
- [ ] 职位审核流程
- [ ] 职位状态管理

**前端开发：**
- [ ] 职位发布页面 (`publish.vue`)
- [ ] 表单验证组件
- [ ] 权益提示组件
- [ ] 发布流程优化

#### 4.2 职位管理系统 (第6周)

**后端开发：**
- [ ] 职位管理API
- [ ] 刷新功能（权益限制）
- [ ] 职位统计API
- [ ] 申请人管理API

**前端开发：**
- [ ] 职位管理页面 (`manage.vue`)
- [ ] 申请人列表
- [ ] 职位状态操作
- [ ] 数据统计展示

**交付物：**
- 职位发布系统
- 职位管理功能
- 权益验证机制

---

### Phase 5: 搜索与匹配 (第7-8周)

**优先级：🔥 高**

#### 5.1 职位搜索系统 (第7周)

**后端开发：**
- [ ] 全文搜索API
- [ ] 地理位置搜索
- [ ] 多条件筛选
- [ ] 搜索结果排序

**前端开发：**
- [ ] 求职者主页 (`jobseeker/index.vue`)
- [ ] 搜索筛选组件 (`JobFilterPopup.vue`)
- [ ] 职位列表组件
- [ ] 地图展示功能

#### 5.2 人才搜索系统 (第8周)

**后端开发：**
- [ ] 人才搜索API（会员功能）
- [ ] 简历筛选逻辑
- [ ] 人才推荐算法
- [ ] 联系方式获取权限

**前端开发：**
- [ ] 招聘者主页 (`recruiter/index.vue`)
- [ ] 人才筛选组件 (`TalentFilterPopup.vue`)
- [ ] 人才详情页面 (`recruiter/talent-detail.vue`)
- [ ] 联系方式展示

**交付物：**
- 职位搜索系统
- 人才搜索系统
- 地理位置功能

---

### Phase 6: 支付与商业化 (第9-10周)

**优先级：🔥 中高**

#### 6.1 支付系统 (第9周)

**后端开发：**
- [ ] 微信支付集成
- [ ] 订单管理系统
- [ ] 支付回调处理
- [ ] 退款处理逻辑

**前端开发：**
- [ ] 支付页面
- [ ] 订单确认页面
- [ ] 支付状态展示
- [ ] 支付结果处理

#### 6.2 数据统计系统 (第10周)

**后端开发：**
- [ ] 数据统计API
- [ ] 招聘效果分析
- [ ] 用户行为分析
- [ ] 报表生成

**前端开发：**
- [ ] 数据分析页面 (`recruiter/analytics.vue`)
- [ ] 图表展示组件
- [ ] 数据导出功能
- [ ] 趋势分析

**交付物：**
- 支付系统
- 数据统计系统
- 商业化功能

---

## 🎯 API设计规范


### 核心API列表

#### 企业相关
```yaml
# 企业档案
POST /api/v1/enterprises           # 创建企业档案
GET /api/v1/enterprises/me         # 获取我的企业信息
PUT /api/v1/enterprises/{id}       # 更新企业信息
DELETE /api/v1/enterprises/{id}    # 删除企业档案

# 企业认证
POST /api/v1/enterprise_verifications    # 提交认证申请
GET /api/v1/enterprise_verifications/me  # 获取认证状态
PUT /api/v1/enterprise_verifications/{id} # 更新认证信息
```

#### 职位相关
```yaml
# 职位管理
GET /api/v1/jobs                   # 职位列表（支持搜索筛选）
GET /api/v1/jobs/{job_id}          # 职位详情
POST /api/v1/jobs                  # 发布职位
PUT /api/v1/jobs/{job_id}          # 更新职位
DELETE /api/v1/jobs/{job_id}       # 删除职位
POST /api/v1/jobs/{job_id}/refresh # 刷新职位
POST /api/v1/jobs/{job_id}/pause   # 暂停职位
POST /api/v1/jobs/{job_id}/resume  # 恢复职位
```

#### 简历相关
```yaml
# 简历管理
GET /api/v1/resumes/me             # 获取我的简历
POST /api/v1/resumes               # 创建简历
PUT /api/v1/resumes/{resume_id}    # 更新简历
GET /api/v1/resumes/{resume_id}    # 查看简历详情
POST /api/v1/resumes/{resume_id}/publish # 发布简历
POST /api/v1/resumes/{resume_id}/pause   # 暂停简历
```

#### 申请相关
```yaml
# 投递申请
POST /api/v1/job_applications      # 投递简历
GET /api/v1/job_applications       # 我的申请记录
GET /api/v1/job_applications/{id}  # 申请详情
PUT /api/v1/job_applications/{id}/status # 更新申请状态
```

#### 用户功能
```yaml
# 收藏功能
POST /api/v1/favorites             # 收藏职位
DELETE /api/v1/favorites/{job_id}  # 取消收藏
GET /api/v1/favorites              # 我的收藏

# 浏览历史
GET /api/v1/view_history           # 浏览历史
DELETE /api/v1/view_history        # 清空历史

# 通知消息
GET /api/v1/notifications          # 获取通知
PUT /api/v1/notifications/{id}/read # 标记已读
DELETE /api/v1/notifications/{id}  # 删除通知
```

#### 会员相关
```yaml
# 套餐管理
GET /api/v1/membership_plans       # 获取可用套餐
GET /api/v1/membership_plans/{id}  # 获取套餐详情

# 订阅管理
POST /api/v1/subscriptions         # 购买套餐
GET /api/v1/subscriptions/me       # 我的订阅
PUT /api/v1/subscriptions/{id}     # 更新订阅
GET /api/v1/subscriptions/{id}/benefits # 获取权益详情
```

## 🔧 技术实现要点

### 1. 权益验证中间件

```go
// 权益验证中间件示例
func (m *PermissionMiddleware) CheckJobPublishPermission() gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := GetUserID(c)
        enterpriseID := GetEnterpriseID(c)
        
        // 1. 检查企业认证状态
        if !m.enterpriseService.IsVerified(enterpriseID) {
            c.JSON(403, gin.H{
                "code": 403,
                "message": "企业未认证",
            })
            c.Abort()
            return
        }
        
        // 2. 检查会员权益
        subscription := m.subscriptionService.GetActiveSubscription(enterpriseID)
        if subscription == nil {
            c.JSON(403, gin.H{
                "code": 403,
                "message": "无有效会员套餐",
            })
            c.Abort()
            return
        }
        
        // 3. 检查发布数量限制
        currentCount := m.jobService.GetActiveJobCount(enterpriseID)
        if currentCount >= subscription.Benefits.JobPostLimit {
            c.JSON(403, gin.H{
                "code": 403,
                "message": "已达到职位发布上限",
            })
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

### 2. 敏感信息加密

```go
// 敏感信息加密工具
type EncryptionService struct {
    key []byte
}

func (e *EncryptionService) Encrypt(plaintext string) (string, error) {
    // 使用AES-256-GCM加密
    block, err := aes.NewCipher(e.key)
    if err != nil {
        return "", err
    }
    
    aesGCM, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }
    
    nonce := make([]byte, aesGCM.NonceSize())
    rand.Read(nonce)
    
    ciphertext := aesGCM.Seal(nonce, nonce, []byte(plaintext), nil)
    return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func (e *EncryptionService) Decrypt(ciphertext string) (string, error) {
    // 解密逻辑
    // ...
}
```

### 3. 简历快照机制

```go
// 简历快照生成
func (s *JobApplicationService) CreateApplication(req *CreateApplicationRequest) error {
    // 获取当前简历
    resume, err := s.resumeService.GetByUserID(req.UserID)
    if err != nil {
        return err
    }
    
    // 创建快照
    snapshot := &ResumeSnapshot{
        Name:            resume.Name,
        Age:             resume.Age,
        Gender:          resume.Gender,
        WorkExperience:  resume.WorkExperience,
        EducationHistory: resume.EducationHistory,
        Skills:          resume.Skills,
        JobIntentions:   resume.JobIntentions,
        SnapshotTime:    time.Now(),
    }
    
    // 保存申请记录
    application := &JobApplication{
        JobID:          req.JobID,
        UserID:         req.UserID,
        Status:         ApplicationStatusSubmitted,
        ResumeSnapshot: snapshot,
    }
    
    return s.repository.Create(application)
}
```

### 4. 地理位置搜索

```go
// 地理位置搜索
func (s *JobService) SearchByLocation(lat, lng float64, radius int) ([]*Job, error) {
    query := `
        SELECT * FROM jobs 
        WHERE status = 'active' 
        AND ST_DWithin(location, ST_GeomFromText('POINT($1 $2)', 4326), $3)
        ORDER BY ST_Distance(location, ST_GeomFromText('POINT($1 $2)', 4326))
    `
    
    var jobs []*Job
    err := s.db.Raw(query, lng, lat, radius*1000).Scan(&jobs).Error
    return jobs, err
}
```

## 📊 性能优化策略

### 1. 缓存策略

```go
// Redis缓存配置
type CacheConfig struct {
    JobListTTL         time.Duration // 职位列表缓存10分钟
    JobDetailTTL       time.Duration // 职位详情缓存30分钟
    EnterpriseInfoTTL  time.Duration // 企业信息缓存1小时
    UserResumeTTL      time.Duration // 用户简历缓存30分钟
    SearchResultTTL    time.Duration // 搜索结果缓存5分钟
}
```

### 2. 数据库优化

```sql
-- 分页查询优化（使用游标分页）
SELECT * FROM jobs 
WHERE status = 'active' 
AND (last_refreshed_at, id) < ('2025-07-16 10:00:00', 12345)
ORDER BY last_refreshed_at DESC, id DESC
LIMIT 20;

-- 复合索引优化
CREATE INDEX jobs_search_idx ON jobs (status, last_refreshed_at DESC, is_urgent DESC, salary_min, salary_max);
```

### 3. API限流

```go
// API限流配置
type RateLimitConfig struct {
    JobPublish   int // 职位发布：每小时10次
    ResumeSubmit int // 简历投递：每天50次
    Search       int // 搜索查询：每分钟100次
}
```

## 🧪 测试策略

### 1. 单元测试

```go
// 示例单元测试
func TestJobService_CreateJob(t *testing.T) {
    // 准备测试数据
    mockRepo := &MockJobRepository{}
    service := NewJobService(mockRepo)
    
    // 测试用例
    req := &CreateJobRequest{
        Title:       "前端开发工程师",
        Description: "负责前端开发工作",
        SalaryMin:   8000,
        SalaryMax:   15000,
    }
    
    // 执行测试
    job, err := service.CreateJob(req)
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, "前端开发工程师", job.Title)
}
```

### 2. 集成测试

```go
// API集成测试
func TestJobAPI_CreateJob(t *testing.T) {
    // 设置测试环境
    router := setupTestRouter()
    
    // 准备请求数据
    reqBody := `{
        "title": "后端开发工程师",
        "description": "负责后端开发",
        "salary_min": 10000,
        "salary_max": 20000
    }`
    
    // 发送请求
    req := httptest.NewRequest("POST", "/api/v1/jobs", strings.NewReader(reqBody))
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    // 验证响应
    assert.Equal(t, 200, w.Code)
}
```

### 3. 性能测试

```bash
# 使用wrk进行压力测试
wrk -t12 -c400 -d30s --script=job_search.lua http://localhost:8080/api/v1/jobs
```

## 🔒 安全考虑

### 1. 数据安全
- 敏感信息应用层AES加密
- 数据库访问权限控制
- API接口权限验证
- 输入数据验证和过滤

### 2. 业务安全
- 防止恶意刷职位
- 防止虚假简历
- 防止恶意投递
- 内容审核机制

### 3. 系统安全
- API限流防止DDoS
- SQL注入防护
- XSS防护
- CSRF防护

## 📈 监控与运维

### 1. 日志监控
```go
// 结构化日志
log.WithFields(log.Fields{
    "user_id":    userID,
    "job_id":     jobID,
    "action":     "job_publish",
    "enterprise": enterpriseID,
}).Info("Job published successfully")
```

### 2. 性能监控
- API响应时间监控
- 数据库查询性能监控
- 缓存命中率监控
- 业务指标监控

### 3. 错误监控
- 异常捕获和上报
- 错误率监控
- 服务可用性监控

## 🎯 部署计划

### 1. 环境准备
- 开发环境：本地开发和测试
- 测试环境：集成测试和用户验收测试
- 生产环境：线上服务

### 2. 部署流程
1. 代码提交和Code Review
2. 自动化测试
3. 构建和打包
4. 部署到测试环境
5. 用户验收测试
6. 部署到生产环境
7. 监控和回滚准备

### 3. 回滚策略
- 数据库版本控制
- 应用版本管理
- 配置回滚
- 数据备份和恢复

## 📋 验收标准

### 1. 功能验收
- [ ] 企业认证流程完整
- [ ] 职位发布和管理功能正常
- [ ] 简历编辑和投递功能正常
- [ ] 搜索和筛选功能准确
- [ ] 会员权益验证正确
- [ ] 支付流程完整

### 2. 性能验收
- [ ] API响应时间 < 500ms
- [ ] 搜索响应时间 < 1s
- [ ] 并发用户数 > 1000
- [ ] 数据库查询优化
- [ ] 缓存命中率 > 80%

### 3. 安全验收
- [ ] 敏感信息加密存储
- [ ] 权限验证正确
- [ ] 输入验证完整
- [ ] 审计日志完整

## 🔄 持续改进

### 1. 数据分析
- 用户行为分析
- 功能使用统计
- 性能瓶颈分析
- 业务转化分析

### 2. 功能迭代
- 用户反馈收集
- 功能优化建议
- 新功能规划
- 技术债务清理

### 3. 技术优化
- 性能优化
- 架构优化
- 代码重构
- 技术升级

---

## 📞 联系信息

- **项目负责人**: [填写]
- **技术负责人**: [填写]
- **测试负责人**: [填写]
- **运维负责人**: [填写]

---

*本文档将根据开发进度持续更新，请及时关注最新版本。*
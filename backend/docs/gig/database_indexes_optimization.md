# 零工模块数据库索引优化建议

## 概述

基于对 `gig_repo.go` 中查询模式的分析，以下是针对零工模块的数据库索引优化建议。这些索引将显著提升查询性能，特别是分页查询和复杂条件筛选。

## 核心索引

### 1. 零工表 (gigs) 索引

```sql
-- 基础查询索引
CREATE INDEX CONCURRENTLY idx_gigs_status ON gigs (status) WHERE is_del = 0;
CREATE INDEX CONCURRENTLY idx_gigs_user_id ON gigs (user_id) WHERE is_del = 0;
CREATE INDEX CONCURRENTLY idx_gigs_created_desc ON gigs (created_at DESC) WHERE is_del = 0;

-- 复合查询索引  
CREATE INDEX CONCURRENTLY idx_gigs_user_status ON gigs (user_id, status) WHERE is_del = 0;
CREATE INDEX CONCURRENTLY idx_gigs_status_created ON gigs (status, created_at DESC) WHERE is_del = 0;

-- 日期范围查询索引
CREATE INDEX CONCURRENTLY idx_gigs_user_date_range ON gigs (user_id, start_time) WHERE is_del = 0;
CREATE INDEX CONCURRENTLY idx_gigs_date_range ON gigs (start_time, end_time) WHERE is_del = 0;

-- 过期零工查询索引
CREATE INDEX CONCURRENTLY idx_gigs_expire_status ON gigs (expire_time, status) WHERE is_del = 0;

-- 地理位置查询索引 (如果需要附近搜索)
CREATE INDEX CONCURRENTLY idx_gigs_location ON gigs USING GIST (location) WHERE is_del = 0;
```

### 2. 零工申请表 (gig_applications) 索引

```sql
-- 基础查询索引
CREATE INDEX CONCURRENTLY idx_gig_apps_user_id ON gig_applications (user_id) WHERE is_del = 0;
CREATE INDEX CONCURRENTLY idx_gig_apps_gig_id ON gig_applications (gig_id) WHERE is_del = 0;
CREATE INDEX CONCURRENTLY idx_gig_apps_created_desc ON gig_applications (created_at DESC) WHERE is_del = 0;

-- 复合查询索引
CREATE INDEX CONCURRENTLY idx_gig_apps_gig_user ON gig_applications (gig_id, user_id) WHERE is_del = 0;
CREATE INDEX CONCURRENTLY idx_gig_apps_user_status ON gig_applications (user_id, status) WHERE is_del = 0;
CREATE INDEX CONCURRENTLY idx_gig_apps_gig_status ON gig_applications (gig_id, status) WHERE is_del = 0;

-- 分页查询优化索引
CREATE INDEX CONCURRENTLY idx_gig_apps_user_created ON gig_applications (user_id, created_at DESC) WHERE is_del = 0;
CREATE INDEX CONCURRENTLY idx_gig_apps_gig_created ON gig_applications (gig_id, created_at DESC) WHERE is_del = 0;
```

## 索引应用场景

### GetList 查询优化
- **使用索引**: `idx_gigs_status_created`
- **查询模式**: `WHERE status = 'recruiting' ORDER BY created_at DESC`
- **预期提升**: 分页查询性能提升 40-60%

### FindByUser 查询优化
- **使用索引**: `idx_gigs_user_status` (有状态条件) 或 `idx_gigs_user_id` (无状态条件)
- **查询模式**: `WHERE user_id = ? [AND status = ?] ORDER BY created_at DESC`
- **预期提升**: 用户零工列表查询性能提升 50-70%

### CheckUserApplied 查询优化
- **使用索引**: `idx_gig_apps_gig_user`
- **查询模式**: `WHERE gig_id = ? AND user_id = ? LIMIT 1`
- **预期提升**: 申请状态检查性能提升 80%+

### 日期范围查询优化
- **使用索引**: `idx_gigs_user_date_range`
- **查询模式**: `WHERE user_id = ? AND start_time >= ? AND start_time <= ?`
- **预期提升**: 日历视图查询性能提升 60-80%

### JOIN 查询优化
- **使用索引**: `idx_gig_apps_user_id` + `idx_gigs_date_range`
- **查询模式**: `gig_applications JOIN gigs ON ... WHERE user_id = ? AND start_time BETWEEN`
- **预期提升**: 复合查询性能提升 40-50%

## 高级优化建议

### 1. 部分索引优化

仅对活跃数据创建索引，减少索引维护开销：

```sql
-- 仅对招聘中的零工创建索引
CREATE INDEX CONCURRENTLY idx_gigs_recruiting_created 
ON gigs (created_at DESC) 
WHERE status = 'recruiting' AND is_del = 0;

-- 仅对待处理的申请创建索引
CREATE INDEX CONCURRENTLY idx_gig_apps_pending_created 
ON gig_applications (created_at DESC) 
WHERE status = 'pending' AND is_del = 0;
```

### 2. 表达式索引

对经常使用的计算字段创建索引：

```sql
-- 日期截取索引 (按天分组统计时使用)
CREATE INDEX CONCURRENTLY idx_gigs_date_trunc 
ON gigs (DATE_TRUNC('day', created_at)) 
WHERE is_del = 0;

-- 工作时长分组索引
CREATE INDEX CONCURRENTLY idx_gigs_duration_range 
ON gigs ((work_duration / 60)) 
WHERE is_del = 0;
```

### 3. 覆盖索引

减少回表查询，直接从索引返回数据：

```sql
-- 零工列表覆盖索引
CREATE INDEX CONCURRENTLY idx_gigs_list_covering 
ON gigs (status, created_at DESC) 
INCLUDE (id, user_id, title, salary, start_time, end_time, address)
WHERE is_del = 0;
```

## 索引维护

### 监控索引使用情况

```sql
-- 检查索引使用统计
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
  AND (tablename = 'gigs' OR tablename = 'gig_applications')
ORDER BY idx_tup_read DESC;
```

### 定期维护任务

```sql
-- 重建索引 (在低峰期执行)
REINDEX INDEX CONCURRENTLY idx_gigs_status;

-- 更新表统计信息
ANALYZE gigs;
ANALYZE gig_applications;
```

## 部署建议

### 分阶段部署

1. **第一阶段**: 部署核心业务索引
   - `idx_gigs_status_created`
   - `idx_gigs_user_status` 
   - `idx_gig_apps_gig_user`

2. **第二阶段**: 部署性能优化索引
   - 日期范围查询索引
   - 覆盖索引

3. **第三阶段**: 部署高级优化索引
   - 部分索引
   - 表达式索引

### 性能测试

在每个阶段部署后，使用以下查询测试性能提升：

```sql
-- 测试零工列表查询
EXPLAIN (ANALYZE, BUFFERS) 
SELECT id, title, status, created_at 
FROM gigs 
WHERE status = 'recruiting' 
ORDER BY created_at DESC 
LIMIT 20;

-- 测试用户申请检查
EXPLAIN (ANALYZE, BUFFERS)
SELECT 1 
FROM gig_applications 
WHERE gig_id = 123 AND user_id = 456 
LIMIT 1;
```

## 预期收益

- **查询响应时间**: 平均减少 50-80%
- **并发处理能力**: 提升 2-3 倍
- **数据库CPU使用率**: 降低 30-40%
- **分页查询性能**: 提升 60-80%

## 注意事项

1. **索引维护成本**: 每个索引会增加写操作开销，建议定期监控
2. **存储空间**: 索引会占用额外存储空间，预估增加 20-30%
3. **并发建索引**: 使用 `CONCURRENTLY` 选项避免锁表
4. **定期清理**: 删除未使用的索引以减少维护开销

---

*文档版本: v1.0*  
*创建时间: 2025-07-29*  
*维护者: 后端开发团队*
# 零工模块产品设计与状态规划

## 1. 产品思考与核心理念

零工（Gig）的特点是 **短期、快速、高频**。模块设计的核心目标是：

- **信息清晰透明**: 发布者和申请者都清晰了解当前阶段和下一步操作。
- **流程高效简洁**: 减少不必要的操作环节，加速匹配过程。
- **权责明确**: 不同状态下的操作权限明确，避免误操作。
- **自动化的闭环**: 通过系统自动处理状态变更（如招满后自动关闭），形成完整的业务闭环。

## 2. 核心问题解答：名额占用机制

这是本模块设计的核心逻辑之一。

**问题**: 申请人提交了申请，但在发布者审核前，是否占用招聘名额？

**方案**: **申请时（Pending状态）不占用名额，录用时（Confirmed状态）才占用名额。**

**详细逻辑**:
1.  **无限申请**: 在一个零工岗位状态为 `Published`（招募中）且未招满时，可以有无限多的用户提交申请。所有这些申请都处于 `Pending` 状态。
2.  **录用即扣减名额**: 当发布者将一个 `Pending` 的申请变更为 `Confirmed`（已录用）时，系统会执行以下操作：
    *   对应 `gigs` 表的 `current_people_count`（当前已招募人数）字段 `+1`。
3.  **招满自动关闭**: 每次 `current_people_count` 增加后，系统会检查 `current_people_count` 是否已等于 `people_count`（总招聘人数）。
    *   如果相等，系统会自动将该 `gigs` 的状态从 `Published` 更新为 `RecruitingClosed`（招募结束）。
    *   同时，系统可以将所有剩余的 `Pending` 申请自动更新为 `Rejected`（未录用），并通知这些申请人“岗位已招满”。
4.  **超额保障**: 数据库和业务逻辑层需要有事务和校验，确保 `current_people_count` 不会超过 `people_count`，防止超额录用。

这种设计既给了发布者充分选择优秀申请人的权利，也通过系统自动化能力保证了流程的严谨性。

## 3. 零工 (Gig) 的状态生命周期

这是从一个“岗位”的视角来看，从创建到结束的全过程。

| 状态 (Status)      | 中文名       | 核心描述                             | 对申请人的影响                       | 发布者可执行操作                               |
| :----------------- | :----------- | :----------------------------------- | :----------------------------------- | :--------------------------------------------- |
| `Draft`            | **草稿**     | 仅发布者可见，未发布。               | 看不见，无法申请。                   | `发布`、`编辑`、`删除`                         |
| `Published`        | **招募中**   | 已公开发布，正在接受用户报名。       | **可以申请**。                       | `关闭招募`、`查看申请人`、`编辑`（非关键信息） |
| `RecruitingClosed` | **招募结束** | 人工关闭或已招满，不再接受新报名。   | **无法申请**，已申请者状态继续流转。 | `查看申请人`、`管理已录用人员`、`重新开启招募` |
| `Ongoing`          | **进行中**   | 零工已到开始时间，正在执行。         | 无法申请。                           | `标记完成`、处理突发（如标记某人未到场）       |
| `Completed`        | **已完成**   | 零工已结束，等待结算或评价。         | 无法申请。                           | `支付薪酬`、`双方互评`、`查看历史`             |
| `Cancelled`        | **已取消**   | 零工在开始前被发布者取消。           | 无法申请，已申请者会收到通知。       | `查看历史`                                     |
| `Expired`          | **已过期**   | 超过报名截止日期后，由系统自动关闭。 | 无法申请。                           | `查看历史`                                     |


**状态流转图 (Gig Lifecycle):**
```mermaid
graph TD
    A[Draft] -->|发布| B(Published);
    B -->|关闭招募/招满| C(RecruitingClosed);
    B -->|到期| G(Expired);
    B -->|取消| F(Cancelled);
    C -->|重新开启| B;
    C -->|开始工作| D(Ongoing);
    C -->|取消| F;
    D -->|工作结束| E(Completed);
    
    subgraph 正常流程
        A --> B --> C --> D --> E;
    end
    subgraph 异常/终止流程
        B --> F;
        B --> G;
        C --> F;
    end
```

## 4. 零工申请 (Gig Application) 的状态生命周期 (已简化)

根据反馈，**已移除申请者的二次确认环节**，流程更高效。发布者录用即代表双方达成意向。

| 状态 (Status) | 中文名     | 核心描述                                   | 申请人可执行操作                   | 发布者可执行操作              |
| :------------ | :--------- | :----------------------------------------- | :--------------------------------- | :---------------------------- |
| `Pending`     | **待处理** | 申请已提交，等待发布者审核。               | `撤回申请`                         | **`录用`**、`拒绝`            |
| `Withdrawn`   | **已撤回** | 申请人在发布者处理前，主动撤销。           | -                                  | 查看（不可操作）              |
| `Confirmed`   | **已录用** | 发布者已录用，**名额已锁定**。             | `联系对方`、`取消参加`（条件限制） | `联系对方`、`标记完成/未到场` |
| `Rejected`    | **未录用** | 发布者明确拒绝了申请，或因招满被系统拒绝。 | -                                  | 查看（不可操作）              |
| `Cancelled`   | **已取消** | 因整个零工被取消，或发布者在录用后取消。   | -                                  | 查看（不可操作）              |
| `Completed`   | **已完成** | 申请人按时完成工作。                       | `评价`、`查看历史`                 | `评价`、`支付`、`查看历史`    |
| `NoShow`      | **未到场** | 申请人录用后但未到场，影响信用。           | -                                  | `标记未到场`（触发惩罚机制）  |

**状态流转图 (Application Lifecycle - Simplified):**
```mermaid
graph TD
    subgraph 申请阶段
        A[Pending] -->|申请人撤回| B(Withdrawn);
        A -->|发布者拒绝| D(Rejected);
        A -->|发布者录用| E(Confirmed);
    end
    
    subgraph 工作阶段
        E -->|完成工作| G(Completed);
        E -->|未到场| H(NoShow);
        E -->|发布者取消录用| F(Cancelled);
    end
```

## 5. 高级功能规划 (V2)

### 5.1. 自动审核（抢占式）功能

为了增加功能的灵活性，允许发布者选择不同的报名方式。

- **功能描述**: 发布者在发布零工时，可以选择【手动审核】或【自动录用】。
  - **手动审核 (Manual)**: 默认模式，发布者需要对每个申请进行人工审核。
  - **自动录用 (Auto-Confirm)**: 抢占式，系统按申请时间顺序自动录用，先到先得，直到名额满员。

- **数据库字段 (`gigs` table)**:
  - `approval_mode VARCHAR(20) DEFAULT 'manual' NOT NULL` -- 审批模式: manual-手动, auto-自动录用
  - `check_in_method VARCHAR(20) DEFAULT ''` -- 打卡方式: gps, qrcode

- **后端逻辑**:
  - **申请接口**: 提交申请时，检查 `approval_mode`。
    - **manual**: 创建 `Pending` 状态的申请。
    - **auto**: 在事务中检查名额，若有名额则直接创建 `Confirmed` 状态的申请并扣减名额；若无名额则直接拒绝。

### 5.2. 上班/下班打卡功能 (预留设计)

为确保工作真实性和工时准确性，预留打卡功能。

- **功能描述**: 在工作开始和结束时，申请人需要通过指定方式打卡。
  - **GPS打卡**: 在工作地点指定范围内打卡。
  - **二维码打卡**: 扫描发布者提供的动态二维码进行打卡。

- **数据库字段 (`gigs` table)**:
  - `check_in_qr_code_token VARCHAR(255) DEFAULT ''` -- 用于签到的动态二维码令牌。

- **数据库字段 (`gig_applications` table)**:
  - `check_in_at TIMESTAMP(0) DEFAULT NULL` -- 上班打卡时间
  - `check_out_at TIMESTAMP(0) DEFAULT NULL` -- 下班打卡时间

### 5.3. 发布者管理视图

- **功能描述**: 发布者在零工管理页面，可以清晰看到每个已录用人员的打卡状态（打卡时间、未打卡等）和最终状态（如：已完成、未到场）。 
-- =================================================================
-- Job模块数据库迁移脚本（最终版本）
-- 创建时间: 2025-07-23
-- 描述: 为现有项目添加求职招聘模块表结构（不依赖PostGIS，统一表名规范）
-- 版本: v1.1-final (No Foreign Keys)
-- =================================================================

-- =================================================================
-- 用户实名认证表 (扩展现有认证体系)
-- =================================================================

CREATE TABLE IF NOT EXISTS user_verifications (
    user_id BIGINT PRIMARY KEY,
    encrypted_real_name TEXT DEFAULT NULL,
    encrypted_id_number TEXT DEFAULT NULL,
    encrypted_id_photos TEXT DEFAULT NULL,
    masked_real_name VARCHAR(255) DEFAULT NULL,
    masked_id_number VARCHAR(255) DEFAULT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    reject_reason TEXT DEFAULT NULL,
    created_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_user_verifications_status ON user_verifications (status);
COMMENT ON TABLE user_verifications IS '用户个人实名认证信息表(Job模块)';

-- =================================================================
-- 企业表 (移除job_前缀)
-- =================================================================

CREATE TABLE IF NOT EXISTS enterprises (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL DEFAULT '',
    description TEXT DEFAULT '',
    logo_url TEXT DEFAULT '',
    type VARCHAR(20) NOT NULL DEFAULT 'enterprise' CHECK (type IN ('enterprise', 'small_business')),
    industry VARCHAR(100) DEFAULT '',
    company_size VARCHAR(50) DEFAULT '',
    contact_person VARCHAR(100) DEFAULT '',
    contact_phone VARCHAR(50) DEFAULT '',
    address TEXT DEFAULT '',
    latitude DECIMAL(10,7) DEFAULT 0,
    longitude DECIMAL(10,7) DEFAULT 0,
    welfare_tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_del SMALLINT NOT NULL DEFAULT 0
);

CREATE INDEX IF NOT EXISTS idx_enterprises_user_id ON enterprises (user_id);
CREATE INDEX IF NOT EXISTS idx_enterprises_type ON enterprises (type);
CREATE INDEX IF NOT EXISTS idx_enterprises_location ON enterprises (latitude, longitude);
COMMENT ON TABLE enterprises IS 'Job模块企业信息档案表';

-- =================================================================
-- 企业认证表 (移除job_前缀)
-- =================================================================

CREATE TABLE IF NOT EXISTS enterprise_verifications (
    enterprise_id BIGINT PRIMARY KEY,
    verification_type VARCHAR(20) NOT NULL DEFAULT 'enterprise' CHECK (verification_type IN ('enterprise', 'small_business')),
    encrypted_credit_code TEXT DEFAULT NULL,
    encrypted_license_url TEXT DEFAULT NULL,
    encrypted_legal_person_name TEXT DEFAULT NULL,
    encrypted_legal_person_id_number TEXT DEFAULT NULL,
    encrypted_additional_docs TEXT DEFAULT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    reject_reason TEXT DEFAULT NULL,
    created_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_enterprise_verifications_status ON enterprise_verifications (status);
COMMENT ON TABLE enterprise_verifications IS 'Job模块企业资质认证信息表';

-- =================================================================
-- 职位信息表
-- =================================================================

CREATE TABLE IF NOT EXISTS jobs (
    id BIGSERIAL PRIMARY KEY,
    enterprise_id BIGINT NOT NULL DEFAULT 0,
    user_id BIGINT NOT NULL DEFAULT 0,
    title VARCHAR(255) NOT NULL DEFAULT '',
    description TEXT DEFAULT '',
    status VARCHAR(20) NOT NULL DEFAULT 'pending_review' CHECK (
        status IN ('pending_review', 'active', 'paused', 'closed', 'expired', 'rejected')
    ),
    salary_min INTEGER NOT NULL DEFAULT 0, --单位:元
    salary_max INTEGER NOT NULL DEFAULT 0, --单位:元
    experience_req SMALLINT NOT NULL DEFAULT 0,
    education_req SMALLINT NOT NULL DEFAULT 0,
    work_type SMALLINT NOT NULL DEFAULT 1,
    work_location VARCHAR(200) DEFAULT '',
    latitude DECIMAL(10,10) DEFAULT 0,
    longitude DECIMAL(10,10) DEFAULT 0,
    remote_work_support BOOLEAN DEFAULT FALSE,
    benefits TEXT[] DEFAULT '{}',
    job_highlights TEXT[] DEFAULT '{}',
    requirements TEXT[] DEFAULT '{}',
    contact_method VARCHAR(20) DEFAULT 'phone' CHECK (contact_method IN ('phone', 'wechat', 'email')),
    is_urgent BOOLEAN NOT NULL DEFAULT FALSE,
    urgent_expires_at TIMESTAMP(0) DEFAULT NULL,
    last_refreshed_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    today_refresh_count INTEGER NOT NULL DEFAULT 0,
    view_count INTEGER NOT NULL DEFAULT 0,
    application_count INTEGER NOT NULL DEFAULT 0,
    search_content TEXT DEFAULT '',
    created_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_del SMALLINT NOT NULL DEFAULT 0
);

-- 职位表索引优化
CREATE INDEX IF NOT EXISTS idx_jobs_status ON jobs (status);
CREATE INDEX IF NOT EXISTS idx_jobs_enterprise_status ON jobs (enterprise_id, status);
CREATE INDEX IF NOT EXISTS idx_jobs_location ON jobs (latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_jobs_composite ON jobs (status, last_refreshed_at DESC, is_urgent DESC);
CREATE INDEX IF NOT EXISTS idx_jobs_salary ON jobs (salary_min, salary_max);
CREATE INDEX IF NOT EXISTS idx_jobs_work_type ON jobs (work_type);
CREATE INDEX IF NOT EXISTS idx_jobs_search_content_gin ON jobs USING GIN (to_tsvector('simple', search_content));

COMMENT ON TABLE jobs IS '核心职位信息表';

-- =================================================================
-- 简历表
-- =================================================================

CREATE TABLE IF NOT EXISTS resumes (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    name VARCHAR(100) DEFAULT '',
    age INTEGER DEFAULT 0,
    gender INTEGER DEFAULT 0,
    phone VARCHAR(20) DEFAULT '',
    email VARCHAR(100) DEFAULT '',
    avatar_url TEXT DEFAULT '',
    work_experience JSONB DEFAULT '[]',
    education_history JSONB DEFAULT '[]',
    skills JSONB DEFAULT '[]',
    job_intentions JSONB DEFAULT '[]',
    expected_salary_min INTEGER DEFAULT 0,
    expected_salary_max INTEGER DEFAULT 0,
    preferred_locations TEXT[] DEFAULT '{}',
    work_status VARCHAR(20) DEFAULT 'currently_employed' CHECK (work_status IN ('currently_employed', 'unemployed', 'student')),
    availability_date DATE DEFAULT NULL,
    self_introduction TEXT DEFAULT '',
    created_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_resumes_user_id ON resumes (user_id);
CREATE INDEX IF NOT EXISTS idx_resumes_work_status ON resumes (work_status);
COMMENT ON TABLE resumes IS '用户简历表';

-- =================================================================
-- 职位投递记录表
-- =================================================================

CREATE TABLE IF NOT EXISTS job_applications (
    id BIGSERIAL PRIMARY KEY,
    job_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    status VARCHAR(30) NOT NULL DEFAULT 'submitted' CHECK (
        status IN (
            'submitted', 'viewed', 'interview_invited', 'interviewing', 
            'interview_passed', 'interview_failed', 'hired', 'rejected'
        )
    ),
    resume_snapshot JSONB DEFAULT '{}',
    recruiter_note TEXT DEFAULT '',
    interview_time TIMESTAMP(0) DEFAULT NULL,
    interview_address TEXT DEFAULT '',
    created_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (job_id, user_id)
);

CREATE INDEX IF NOT EXISTS idx_job_applications_user_status ON job_applications (user_id, status);
CREATE INDEX IF NOT EXISTS idx_job_applications_job_status ON job_applications (job_id, status);
CREATE INDEX IF NOT EXISTS idx_job_applications_created_at ON job_applications (created_at DESC);
COMMENT ON TABLE job_applications IS '职位投递记录表';

-- =================================================================
-- 商业化与会员系统表
-- =================================================================

CREATE TABLE IF NOT EXISTS membership_plans (
    id BIGSERIAL PRIMARY KEY,
    key VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL DEFAULT '',
    description TEXT DEFAULT '',
    price DECIMAL(10,2) DEFAULT 0.00,
    duration_days INTEGER DEFAULT 30,
    benefits JSONB DEFAULT '{}',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_membership_plans_is_active ON membership_plans (is_active);
CREATE INDEX IF NOT EXISTS idx_membership_plans_sort_order ON membership_plans (sort_order);
COMMENT ON TABLE membership_plans IS '会员套餐模板表，由运营定义';

CREATE TABLE IF NOT EXISTS subscriptions (
    id BIGSERIAL PRIMARY KEY,
    enterprise_id BIGINT NOT NULL,
    plan_id BIGINT DEFAULT NULL,
    plan_details_snapshot JSONB DEFAULT '{}',
    start_date TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'expired', 'cancelled')),
    created_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_subscriptions_enterprise_id ON subscriptions (enterprise_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_end_date ON subscriptions (end_date);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions (status);
COMMENT ON TABLE subscriptions IS '企业订阅合约表';

-- =================================================================
-- 业务支持表
-- =================================================================

CREATE TABLE IF NOT EXISTS favorites (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    job_id BIGINT NOT NULL,
    created_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, job_id)
);

CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites (user_id);
CREATE INDEX IF NOT EXISTS idx_favorites_job_id ON favorites (job_id);
COMMENT ON TABLE favorites IS '用户收藏职位表';

CREATE TABLE IF NOT EXISTS view_history (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    job_id BIGINT NOT NULL,
    view_time TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_view_history_user_time ON view_history (user_id, view_time DESC);
CREATE INDEX IF NOT EXISTS idx_view_history_job_id ON view_history (job_id);
COMMENT ON TABLE view_history IS '用户浏览历史表';

CREATE TABLE IF NOT EXISTS job_notifications (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL DEFAULT '',
    content TEXT DEFAULT '',
    type VARCHAR(20) NOT NULL DEFAULT 'system' CHECK (type IN ('system', 'application', 'interview', 'offer')),
    related_id BIGINT DEFAULT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_job_notifications_user_read ON job_notifications (user_id, is_read);
CREATE INDEX IF NOT EXISTS idx_job_notifications_type ON job_notifications (type);
COMMENT ON TABLE job_notifications IS 'Job模块消息通知表';

CREATE TABLE IF NOT EXISTS reports (
    id BIGSERIAL PRIMARY KEY,
    reporter_user_id BIGINT NOT NULL,
    reported_job_id BIGINT DEFAULT NULL,
    reported_user_id BIGINT DEFAULT NULL,
    reason VARCHAR(100) NOT NULL DEFAULT '',
    description TEXT DEFAULT '',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'resolved', 'rejected')),
    created_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_reports_reporter_idx ON reports (reporter_user_id);
CREATE INDEX IF NOT EXISTS idx_reports_status_idx ON reports (status);
COMMENT ON TABLE reports IS '反馈举报表';

-- =================================================================
-- 搜索内容更新触发器
-- =================================================================

CREATE OR REPLACE FUNCTION update_job_search_content() RETURNS TRIGGER AS $$
BEGIN
    NEW.search_content := COALESCE(NEW.title, '') || ' ' || COALESCE(NEW.description, '') || ' ' || COALESCE(NEW.work_location, '');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_job_search_content_trigger ON jobs;
CREATE TRIGGER update_job_search_content_trigger
    BEFORE INSERT OR UPDATE ON jobs
    FOR EACH ROW EXECUTE FUNCTION update_job_search_content();

-- =================================================================
-- 初始化数据
-- =================================================================

INSERT INTO membership_plans (key, name, description, price, duration_days, benefits, sort_order)
VALUES 
    ('free_trial', '免费试用', '新用户免费试用套餐', 0.00, 7, '{"job_post_limit": 2, "daily_refresh_limit": 1, "resume_view_limit": 10}', 1),
    ('basic_plan', '基础套餐', '个人招聘者基础套餐', 99.00, 30, '{"job_post_limit": 5, "daily_refresh_limit": 3, "resume_view_limit": 50, "priority_display": false}', 2),
    ('standard_plan', '标准套餐', '中小企业标准套餐', 299.00, 30, '{"job_post_limit": 15, "daily_refresh_limit": 5, "resume_view_limit": 200, "priority_display": true, "urgent_job_limit": 3}', 3),
    ('premium_plan', '高级套餐', '大型企业高级套餐', 599.00, 30, '{"job_post_limit": 50, "daily_refresh_limit": 10, "resume_view_limit": 500, "priority_display": true, "urgent_job_limit": 10, "analytics_access": true}', 4)
ON CONFLICT (key) DO NOTHING;

-- 完成提示
SELECT 'Job模块数据库迁移完成（v1.1-final, No Foreign Keys）' AS result;

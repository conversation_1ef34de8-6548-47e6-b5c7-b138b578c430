# Makefile
.PHONY: build run dev test clean docker-build docker-run \
	test-setup test-api test-auth test-unit test-integration test-clean test-db-clean \
	test-coverage test-coverage-api test-coverage-full test-coverage-check \
	test-all test-ci test-quick test-dev test-release test-help \
	fmt imports vet fix tidy sec staticcheck errcheck check check-all \
	install-tools benchmark docs deps verify clean-cache

# 项目名称
PROJECT_NAME=bdb-backend
BINARY_NAME=server

# 构建
build:
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/$(BINARY_NAME) cmd/server/main.go

# 运行
run:
	go run cmd/server/main.go

# 开发模式（热重载）
dev:
	air -c .air.toml

# ===== 测试相关命令 =====

# 基础测试
test:
	@echo "🧪 运行所有测试..."
	go test -v ./...
	@echo "✅ 测试完成"

# 测试环境设置  
test-setup:
	@echo "🔧 设置测试环境..."
	@echo "检查测试配置..."
	@if [ ! -f "test/config/test_config.yaml" ]; then \
		echo "❌ 测试配置文件不存在"; \
		exit 1; \
	fi
	@echo "✅ 测试环境设置完成"

# API测试
test-api:
	@echo "🌐 运行API测试..."
	GO_ENV=test go test -v ./test/api/...
	@echo "✅ API测试完成"

# 认证模块测试
test-auth:
	@echo "🔐 运行认证模块测试..."
	GO_ENV=test go test -v ./test/api/ -run TestAuthTestSuite
	@echo "✅ 认证测试完成"

# 单元测试
test-unit:
	@echo "🔬 运行单元测试..."
	go test -v ./internal/... ./pkg/...
	@echo "✅ 单元测试完成"

# 集成测试
test-integration:
	@echo "🔗 运行集成测试..."
	GO_ENV=test go test -v ./test/...
	@echo "✅ 集成测试完成"

# 测试清理
test-clean:
	@echo "🧹 清理测试数据..."
	@if [ -f "coverage.out" ]; then rm coverage.out; fi
	@if [ -f "coverage.html" ]; then rm coverage.html; fi
	@echo "✅ 测试数据清理完成"

# 测试数据库清理（需要根据实际情况调整）
test-db-clean:
	@echo "🗄️  清理测试数据库..."
	@echo "⚠️  请手动清理测试数据库或实现自动清理脚本"
	@echo "✅ 测试数据库清理提醒完成"

# 清理
clean:
	rm -rf bin/

# Docker构建
docker-build:
	docker build -t $(PROJECT_NAME):latest .

# Docker运行
docker-run:
	docker-compose up -d

# 停止Docker
docker-stop:
	docker-compose down

# 数据库迁移
migrate-up:
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/bdb?sslmode=disable" up

migrate-down:
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/bdb?sslmode=disable" down

# ===== Go 官方工具 - 代码质量检查 =====

# 代码格式化（官方工具）
fmt:
	@echo "🎨 格式化代码..."
	go fmt ./...
	@echo "✅ 代码格式化完成"

# 导入整理（推荐安装）
imports:
	@echo "📦 整理导入..."
	@which goimports > /dev/null || (echo "安装 goimports..." && go install golang.org/x/tools/cmd/goimports@latest)
	goimports -w .
	@echo "✅ 导入整理完成"

# 代码检查（官方工具）
vet:
	@echo "🔍 代码静态检查..."
	go vet ./...
	@echo "✅ 静态检查完成"

# 代码修复（实验性）
fix:
	@echo "🔧 自动修复代码..."
	go fix ./...
	@echo "✅ 代码修复完成"

# 模块整理
tidy:
	@echo "📚 整理模块依赖..."
	go mod tidy
	@echo "✅ 模块依赖整理完成"

# 安全检查（可选，需要安装）
sec:
	@echo "🔒 安全检查..."
	@which gosec > /dev/null || (echo "安装 gosec..." && go install github.com/securego/gosec/v2/cmd/gosec@latest)
	@gosec -quiet ./... || echo "⚠️  发现安全问题，请检查"

# 静态分析检查（推荐）
staticcheck:
	@echo "🔍 静态分析检查..."
	@which staticcheck > /dev/null || (echo "安装 staticcheck..." && go install honnef.co/go/tools/cmd/staticcheck@latest)
	@staticcheck ./... || echo "⚠️  发现静态分析问题，请检查"

# 简单的错误检查（可选，需要安装）
errcheck:
	@echo "❌ 错误处理检查..."
	@which errcheck > /dev/null || (echo "安装 errcheck..." && go install github.com/kisielk/errcheck@latest)
	@errcheck ./... || echo "⚠️  发现未处理的错误，请检查"

# 一键质量检查（推荐日常使用）
check: fmt imports vet tidy
	@echo "🎉 代码质量检查完成！"

# 完整质量检查（包含静态分析、安全和错误检查）
check-all: fmt imports vet staticcheck fix tidy sec errcheck
	@echo "🎉 完整代码质量检查完成！"

# 安装推荐的开发工具
install-tools:
	@echo "📦 安装 Go 开发工具..."
	go install golang.org/x/tools/cmd/goimports@latest
	go install honnef.co/go/tools/cmd/staticcheck@latest
	go install github.com/securego/gosec/v2/cmd/gosec@latest
	go install github.com/kisielk/errcheck@latest
	go install github.com/cosmtrek/air@latest
	@echo "✅ 开发工具安装完成"

# ===== 测试覆盖率和报告 =====

# 基础覆盖率测试
test-coverage:
	@echo "📊 生成测试覆盖率报告..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "✅ 覆盖率报告生成: coverage.html"

# API测试覆盖率
test-coverage-api:
	@echo "📊 生成API测试覆盖率报告..."
	GO_ENV=test go test -v -coverprofile=coverage_api.out ./test/api/...
	go tool cover -html=coverage_api.out -o coverage_api.html
	@echo "✅ API覆盖率报告生成: coverage_api.html"

# 完整覆盖率测试（包括集成测试）
test-coverage-full:
	@echo "📊 生成完整测试覆盖率报告..."
	GO_ENV=test go test -v -coverprofile=coverage_full.out ./...
	go tool cover -html=coverage_full.out -o coverage_full.html
	go tool cover -func=coverage_full.out
	@echo "✅ 完整覆盖率报告生成: coverage_full.html"

# 覆盖率检查（确保覆盖率达到指定阈值）
test-coverage-check:
	@echo "🎯 检查测试覆盖率..."
	@go test -v -coverprofile=coverage_check.out ./... > /dev/null
	@coverage=$$(go tool cover -func=coverage_check.out | grep total | awk '{print substr($$3, 1, length($$3)-1)}'); \
	echo "当前覆盖率: $$coverage%"; \
	if [ "$$(echo "$$coverage >= 70" | bc -l)" -eq 1 ]; then \
		echo "✅ 覆盖率达标 ($$coverage% >= 70%)"; \
	else \
		echo "❌ 覆盖率不足 ($$coverage% < 70%)"; \
		exit 1; \
	fi
	@rm -f coverage_check.out

# 基准测试
benchmark:
	go test -bench=. -benchmem ./...

# 生成API文档
docs:
	swag init -g cmd/server/main.go -o ./docs

# 安装依赖
deps:
	go mod tidy
	go mod download

# 验证依赖
verify:
	go mod verify

# 清理模块缓存
clean-cache:
	go clean -modcache

# ===== 综合测试工作流程 =====

# 完整测试流程（推荐日常使用）
test-all: test-setup test-unit test-integration test-coverage-check
	@echo "🎉 所有测试完成！"

# CI/CD测试流程
test-ci: test-setup check test-unit test-integration test-coverage-full
	@echo "🚀 CI/CD测试流程完成！"

# 快速测试（仅单元测试）
test-quick: test-unit
	@echo "⚡ 快速测试完成！"

# 开发者测试工作流程
test-dev: test-clean test-setup test-auth test-coverage-api
	@echo "👩‍💻 开发者测试完成！"

# 发布前测试（最严格）
test-release: test-clean test-setup check-all test-unit test-integration test-coverage-full test-coverage-check
	@echo "🏆 发布前测试完成！代码质量已达到发布标准！"

# 测试帮助信息
test-help:
	@echo "🔍 可用的测试命令："
	@echo ""
	@echo "基础测试："
	@echo "  make test              - 运行所有测试"
	@echo "  make test-unit         - 运行单元测试"
	@echo "  make test-integration  - 运行集成测试"
	@echo "  make test-api          - 运行API测试"
	@echo "  make test-auth         - 运行认证模块测试"
	@echo ""
	@echo "测试覆盖率："
	@echo "  make test-coverage     - 生成基础覆盖率报告"
	@echo "  make test-coverage-api - 生成API测试覆盖率"
	@echo "  make test-coverage-full - 生成完整覆盖率报告"
	@echo "  make test-coverage-check - 检查覆盖率是否达标"
	@echo ""
	@echo "工作流程："
	@echo "  make test-all          - 完整测试流程（推荐日常使用）"
	@echo "  make test-ci           - CI/CD测试流程"
	@echo "  make test-quick        - 快速测试（仅单元测试）"
	@echo "  make test-dev          - 开发者测试工作流程"
	@echo "  make test-release      - 发布前测试（最严格）"
	@echo ""
	@echo "维护："
	@echo "  make test-setup        - 设置测试环境"
	@echo "  make test-clean        - 清理测试数据"
	@echo "  make test-db-clean     - 清理测试数据库" 
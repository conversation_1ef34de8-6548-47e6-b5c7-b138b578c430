# 本地宝 - 后端服务

## 🚀 项目概述

本地宝后端服务是一个基于 Go 语言构建的高性能、可扩展的本地生活服务平台后端系统。采用现代化的微服务架构设计，为前端小程序、H5 等多端应用提供稳定可靠的 API 服务。

## 🎯 核心功能

### 用户系统
- **用户认证** - 微信登录、手机号登录、实名认证
- **用户管理** - 个人资料、企业认证、权限控制
- **隐私保护** - 数据加密、隐私设置、安全机制

### 业务模块
- **求职招聘** - 职位发布、简历管理、人才匹配
- **零工广场** - 临时工作发布、接单管理、结算系统
- **房产服务** - 房源信息、租赁管理、交易服务
- **同城交友** - 用户匹配、动态发布、社交互动
- **即时通讯** - 实时聊天、消息推送、群组管理

### 系统功能
- **文件存储** - 图片上传、云存储管理
- **支付系统** - 在线支付、资金管理
- **通知系统** - 消息推送、邮件通知
- **数据分析** - 用户行为、业务统计

## 🏗️ 技术架构

### 核心技术栈
- **语言**: Go 1.24.0
- **Web 框架**: Gin 1.10.0
- **数据库**: PostgreSQL (主数据库)
- **缓存**: Redis 9.10.0
- **消息队列**: Centrifugo (实时通信)
- **ORM**: GORM 1.30.0
- **依赖注入**: Google Wire 0.6.0
- **配置管理**: Viper 1.20.0
- **日志系统**: Zerolog 1.33.0
- **JWT 认证**: golang-jwt/jwt/v5 5.2.2
- **参数验证**: validator/v10 10.27.0
- **定时任务**: gocron/v2 2.16.2

### 第三方服务集成
- **微信生态**: PowerWeChat/v3 3.4.20
- **短信服务**: 阿里云短信 SDK
- **云存储**: 七牛云存储 SDK
- **支付系统**: 微信支付、支付宝

### 架构特点
- **分层架构** - Controller → Service → Repository → Model
- **依赖注入** - 使用 Wire 进行依赖管理
- **中间件模式** - 统一的请求处理流程
- **错误处理** - 标准化的错误响应格式
- **日志记录** - 结构化日志，支持多级别
- **配置管理** - 环境隔离的配置系统
- **数据库设计** - 软删除、审计字段、索引优化

## 📁 项目结构

```
backend/
├── cmd/                          # 应用程序入口
│   └── server/                   # 服务器启动
│       └── main.go              # 主程序入口
├── configs/                      # 配置文件
│   ├── config.dev.yaml          # 开发环境配置
│   ├── config.prod.yaml         # 生产环境配置
│   └── config.yaml              # 当前环境配置
├── deployments/                  # 部署相关
│   ├── docker-compose.yml       # Docker 编排
│   └── Dockerfile               # Docker 镜像
├── docs/                         # 项目文档
│   ├── api_design_and_naming_convention.md  # API 设计规范
│   ├── gig/                      # 零工模块文档
│   └── job/                      # 招聘模块文档
├── internal/                     # 内部包
│   ├── api/                      # API 层
│   │   ├── apiproto/            # gRPC 协议定义
│   │   ├── controller/          # 控制器层
│   │   │   ├── auth_ctl.go      # 认证控制器
│   │   │   ├── chat_ctl.go      # 聊天控制器
│   │   │   ├── gig_ctl.go       # 零工控制器
│   │   │   ├── message_ctl.go   # 消息控制器
│   │   │   ├── notification_ctl.go # 通知控制器
│   │   │   ├── user_ctl.go      # 用户控制器
│   │   │   └── controller.go    # 控制器基类
│   │   ├── middleware/          # 中间件
│   │   │   ├── auth.go          # 认证中间件
│   │   │   ├── cors.go          # CORS 中间件
│   │   │   └── logger.go        # 日志中间件
│   │   ├── router/              # 路由配置
│   │   │   └── router.go        # 路由定义
│   │   ├── server.go            # 服务器配置
│   │   ├── wire.go              # 依赖注入配置
│   │   └── wire_gen.go          # 自动生成的依赖注入代码
│   ├── constants/               # 常量定义
│   │   ├── business.go          # 业务常量
│   │   ├── gig.go               # 零工相关常量
│   │   └── standards.go         # 标准常量
│   ├── model/                   # 数据模型
│   │   ├── chat.go              # 聊天模型
│   │   ├── conversation.go      # 会话模型
│   │   ├── gig.go               # 零工模型
│   │   ├── message.go           # 消息模型
│   │   ├── user.go              # 用户模型
│   │   └── base.go              # 基础模型
│   ├── repository/              # 数据访问层
│   │   ├── conversation_repo.go # 会话数据访问
│   │   ├── gig_repo.go          # 零工数据访问
│   │   ├── message_repo.go      # 消息数据访问
│   │   └── user_repo.go         # 用户数据访问
│   ├── scheduler/               # 定时任务
│   │   ├── gig_scheduler.go     # 零工定时任务
│   │   ├── job.go               # 任务定义
│   │   └── scheduler.go         # 调度器
│   ├── service/                 # 业务逻辑层
│   │   ├── auth_svc.go          # 认证服务
│   │   ├── conversation_svc.go  # 会话服务
│   │   ├── gig_svc.go           # 零工服务
│   │   ├── message_svc.go       # 消息服务
│   │   └── user_svc.go          # 用户服务
│   ├── types/                   # 类型定义
│   │   ├── auth_types.go        # 认证相关类型
│   │   ├── chat_types.go        # 聊天相关类型
│   │   ├── common_types.go      # 通用类型
│   │   └── user_types.go        # 用户相关类型
│   ├── utils/                   # 工具函数
│   │   ├── crypto.go            # 加密工具
│   │   ├── http.go              # HTTP 工具
│   │   └── string.go            # 字符串工具
│   └── worker/                  # 后台工作器
│       ├── gig_worker.go        # 零工工作器
│       └── wire.go              # 依赖注入
├── migrations/                  # 数据库迁移
│   ├── README.md               # 迁移说明
│   └── schema.sql              # 数据库结构
├── pkg/                         # 公共包
│   ├── cache/                   # 缓存管理
│   │   ├── cache.go             # 缓存接口
│   │   └── redis.go             # Redis 实现
│   ├── centrifugo/              # 实时通信
│   │   └── client.go            # Centrifugo 客户端
│   ├── config/                  # 配置管理
│   │   └── config.go            # 配置结构
│   ├── database/                # 数据库连接
│   │   └── postgres.go          # PostgreSQL 连接
│   ├── jwt/                     # JWT 工具
│   │   └── jwt.go               # JWT 处理
│   ├── logger/                  # 日志系统
│   │   └── logger.go            # 日志配置
│   ├── response/                # 响应处理
│   │   ├── constants.go         # 响应常量
│   │   ├── examples.go          # 响应示例
│   │   └── response.go          # 响应工具
│   ├── sms/                     # 短信服务
│   │   ├── aliyun.go            # 阿里云短信
│   │   └── sms.go               # 短信接口
│   ├── storage/                 # 文件存储
│   │   ├── qiniu.go             # 七牛云存储
│   │   └── storage.go           # 存储接口
│   ├── validator/               # 参数验证
│   │   └── validator.go         # 验证工具
│   ├── wechat/                  # 微信服务
│   │   ├── auth.go              # 微信认证
│   │   ├── client.go            # 微信客户端
│   │   └── notify.go            # 微信通知
│   └── utils/                   # 通用工具
├── scripts/                     # 脚本文件
│   └── install-tools.sh         # 工具安装脚本
├── wechat/                      # 微信相关
├── logs/                        # 日志文件
├── tmp/                         # 临时文件
├── bin/                         # 编译输出
├── .vscode/                     # VS Code 配置
├── .air.toml                    # 热重载配置
├── go.mod                       # Go 模块定义
├── go.sum                       # 依赖校验
├── Makefile                     # 构建脚本
└── README.md                    # 项目说明
```

## 🚀 快速开始

### 环境要求
- Go 1.24.0+
- PostgreSQL 17+
- Redis 7+
- Make

### 安装开发工具
```bash
# 使用脚本安装
./scripts/install-tools.sh

# 或使用 Makefile
make install-tools
```

### 环境配置
```bash
# 复制配置文件
cp configs/config.dev.yaml configs/config.yaml

# 编辑配置文件
vim configs/config.yaml
```

### 数据库初始化
```bash
# 创建数据库
createdb bdb_dev

# 执行数据库迁移
psql -d bdb_dev -f migrations/schema.sql
```

### 启动服务
```bash
# 开发模式（热重载）
make dev

# 生产模式
make build && ./bin/server

# 使用 air 热重载
air
```

## 🛠️ 开发指南

### 代码质量检查
```bash
# 基础检查（日常推荐）
make check

# 完整检查（提交前推荐）
make check-all

# 单独工具
make fmt          # 代码格式化
make imports      # 整理导入
make vet          # 静态检查
make staticcheck  # 静态分析
make sec          # 安全检查
```

### 测试
```bash
# 运行所有测试
make test

# 运行单元测试
make test-unit

# 运行集成测试
make test-integration

# 生成测试覆盖率报告
make test-coverage
```

### 构建
```bash
# 构建可执行文件
make build

# 构建 Docker 镜像
make docker-build

# 清理构建文件
make clean
```

## 📋 开发规范

### Go 编程规范

#### 核心原则
- **可读性优先** - 代码是给人读的，不是给机器读的
- **错误处理明确** - 每个错误都要有明确的处理策略
- **并发安全** - 正确使用 Go 的并发特性
- **性能考虑** - 避免常见的性能陷阱
- **一致性** - 统一的编码风格

#### 代码风格

**1. 包命名**
```go
// ✅ 正确
package user
package auth
package database

// ❌ 错误
package User
package userService
package common
```

**2. 函数命名**
```go
// ✅ 正确
func GetUserByID(id uint) (*User, error)
func CreateUser(user *User) error
func IsValidEmail(email string) bool

// ❌ 错误
func getUser(id uint) (*User, error)
func create_user(user *User) error
```

**3. 变量命名**
```go
// ✅ 正确
var (
    userID    uint
    userName  string
    isActive  bool
    maxRetries = 3
)

// ❌ 错误
var (
    UserID    uint
    user_name string
    IsActive  bool
)
```

#### 错误处理

**1. 错误类型选择**
```go
// 静态错误消息
var ErrUserNotFound = errors.New("用户未找到")

// 动态错误消息
return fmt.Errorf("创建用户失败: %w", err)
```

**2. 错误处理模式**
```go
// ✅ 正确 - 使用 %w 保留原始错误
if err != nil {
    return fmt.Errorf("数据库查询失败: %w", err)
}

// ✅ 正确 - 只处理一次错误
if err := doSomething(); err != nil {
    log.Printf("操作失败: %v", err)
    return nil // 已处理，返回 nil
}

// ❌ 错误 - 重复处理错误
if err := doSomething(); err != nil {
    log.Printf("操作失败: %v", err)
    return err // 又返回错误
}
```

#### 并发编程

**1. Channel 使用**
```go
// ✅ 正确 - 无缓冲或大小为 1
c := make(chan int)        // 无缓冲
c := make(chan int, 1)     // 大小为 1

// ❌ 错误 - 任意大小需要仔细考虑
c := make(chan int, 64)
```

**2. 原子操作**
```go
// ✅ 正确 - 使用标准库原子操作
type Counter struct {
    value atomic.Int64
}

func (c *Counter) Add(delta int64) {
    c.value.Add(delta)
}
```

#### 性能优化

**1. 字符串处理**
```go
// ✅ 正确 - 使用 strconv
s := strconv.Itoa(123)

// ❌ 错误 - 使用 fmt
s := fmt.Sprint(123)
```

**2. 切片和映射**
```go
// ✅ 正确 - 指定容量
data := make([]int, 0, expectedSize)
m := make(map[string]int, expectedSize)

// ✅ 正确 - 在边界处复制
func (d *Driver) SetTrips(trips []Trip) {
    d.trips = make([]Trip, len(trips))
    copy(d.trips, trips)
}
```

### API 设计规范

#### RESTful API 设计
- **资源导向** - API 的核心是名词（资源），而不是动词（操作）
- **方法明确** - 严格使用 HTTP 方法 (`GET`, `POST`, `PUT`, `PATCH`, `DELETE`)
- **格式统一** - 查询参数及 JSON 数据体全局统一使用 `snake_case`
- **版本化** - 所有 API 通过路径进行版本控制，如 `/api/v1/...`

#### URL 结构规范
```bash
# ✅ 正确示例
GET    /api/v1/users              # 获取用户列表
GET    /api/v1/users/{id}         # 获取单个用户
POST   /api/v1/users              # 创建用户
PUT    /api/v1/users/{id}         # 更新用户
DELETE /api/v1/users/{id}         # 删除用户
GET    /api/v1/user-orders        # 获取用户订单（复合资源）

# ❌ 避免的反模式
GET /getUsers                     # 冗余动词
GET /get_user_list                # 蛇形命名
GET /getUserOrders                # 驼峰命名
```

#### 查询参数规范
```bash
# 分页参数
GET /api/v1/users?page=1&page_size=20

# 排序参数
GET /api/v1/users?sort=-created_at,title

# 筛选参数
GET /api/v1/users?status=active&category_id=3

# 字段选择
GET /api/v1/users?fields=id,title,salary_min,salary_max
```

#### 响应格式
所有 API 接口返回统一的 JSON 格式：
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

分页响应格式：
```json
{
  "code": 0,
  "message": "success",
  "data": {
   "list":[],
   "total": 100,
   "page": 1,
   "page_size": 20
  }
}
```

### 数据库设计规范
- 使用Postgresql数据支持类型，合理的字段类型与索引类型，增加查询效率与性能
- 包含审计字段（`created_at`, `updated_at`），如果使用软删除（`is_del` 字段）
- 禁止在表中使用外键约束、ENUM等影响性能或维护困难字段
- 状态类型字段使用string类型，并使用CHECK进行约束，例如：``` status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft','recruiting','paused','locked','in_progress','completed','closed')), ```， 除了学历、性别、is_del这种全局使用、状态全局固定的可以使用smallint类型
- 数据表sql尽可能NOT NULL 并设计 DEFAULT
- 表中涉及到交易、订单、产品价格的都使用int类型存储，单位为：分；除了招聘中 8000-10000这种薪可使用元为单位，因为不涉及到交易和支付以及账单
  

#### 表结构规范
```sql
-- ✅ 正确 - 包含审计字段
CREATE TABLE users (
    id SERIAL PRIMARY KEY, -- 主键ID使用 SERIAL
    user_id BIGINT NOT NULL,           -- 使用 uint 类型
    nickname VARCHAR(50) NOT NULL,
    -- 基础字段
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    is_del SMALLINT NOT NULL DEFAULT 0, -- 如果需要软删除
);

-- ✅ 正确 - 软删除和索引
CREATE INDEX idx_users_user_id ON users(user_id);
CREATE INDEX idx_users_deleted_at ON users(deleted_at);
```

### 日志记录规范

#### 日志级别使用
```go
// 基础日志
logger.Info("User login successful")

// 带参数的日志
logger.Info("Processing request", "user_id", userID, "action", "login")

// 带上下文的日志
logger.InfoCtx(ctx, "User created", "user_id", user.ID, "nickname", user.Nickname)

// 错误日志
logger.ErrorCtx(ctx, "Database operation failed", err, "table", "users")

// 调试日志
logger.Debug("Processing step", "step", "validation", "data", data)
```

#### 日志记录原则
- 使用结构化日志，支持 key-value 对
- 关键业务操作必须记录日志
- 错误处理时记录详细错误信息
- 包含请求上下文信息（request_id, user_id 等）

### 控制器开发规范

#### 参数验证
```go
// ✅ 正确 - 使用 validator.CheckQuery() 进行参数绑定和验证
  var req types.GetUsersRequest
  if !c.validator.CheckJSON(ctx, &req) {
    return
  }
```

#### 事务处理
```go
// ✅ 正确 - 使用事务确保数据一致性
func (s *userService) CreateUserWithProfile(ctx context.Context, req types.CreateUserRequest) error {
    return s.db.Transaction(func(tx *gorm.DB) error {
        // 创建用户
        user := &model.User{...}
        if err := tx.Create(user).Error; err != nil {
            return err
        }
        
        // 创建用户资料
        profile := &model.UserProfile{UserID: user.ID, ...}
        if err := tx.Create(profile).Error; err != nil {
            return err
        }
        
        return nil
    })
}
```


#### 单元测试
```go
// ✅ 正确 - 表驱动测试
func TestUserService_CreateUser(t *testing.T) {
    tests := []struct {
        name    string
        req     types.CreateUserRequest
        wantErr bool
    }{
        {
            name: "valid user",
            req: types.CreateUserRequest{
                Nickname: "test",
                Phone:    "13800138000",
                Password: "123456",
            },
            wantErr: false,
        },
        {
            name: "invalid phone",
            req: types.CreateUserRequest{
                Nickname: "test",
                Phone:    "invalid",
                Password: "123456",
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 测试逻辑
        })
    }
}
```

## 🗄️ 数据库设计

### 核心表结构
- **users** - 用户表
- **enterprises** - 企业表
- **gigs** - 零工表
- **jobs** - 职位表
- **houses** - 房源表
- **conversations** - 会话表
- **messages** - 消息表
- **posts** - 动态表

### 认证与授权
- JWT Token 认证
- 设备 ID 安全机制
- 登录状态管理

### 数据安全
- 敏感数据加密存储
- API 请求签名验证
- SQL 注入防护
- XSS 攻击防护

### 隐私保护
- 用户数据脱敏
- 隐私设置控制
- 数据访问审计
- 合规性检查

## 📊 监控与运维

### 日志管理
- 结构化日志记录
- 日志文件轮转
- 错误日志告警
- 性能监控

### 健康检查
- 服务健康检查接口
- 数据库连接检查
- Redis 连接检查
- 第三方服务检查

### 性能优化
- 数据库查询优化
- 缓存策略优化
- 连接池管理
- 并发控制

## 🚀 部署指南

### Docker 部署
```bash
# 构建镜像
make docker-build

# 运行容器
make docker-run

# 使用 docker-compose
docker-compose up -d
```

### 生产环境部署
1. 配置生产环境变量
2. 构建可执行文件：`make build`
3. 配置反向代理（Nginx）
4. 配置 SSL 证书
5. 启动服务：`./bin/server`

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -m 'feat: add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建 Pull Request

### 提交规范
- `feat:` - 新功能
- `fix:` - 修复问题
- `docs:` - 文档更新
- `style:` - 代码格式调整
- `refactor:` - 代码重构
- `test:` - 测试相关
- `chore:` - 构建过程或辅助工具的变动

## 📄 许可证

MIT License

## 📞 联系方式

- 项目地址：[GitHub Repository]
- 问题反馈：[Issues]
- 技术交流：[Discussions]

---

**本地宝后端服务** - 为本地生活提供强大的技术支撑 🚀💪 
package main

import (
	"bdb-backend/internal/api"
	"bdb-backend/pkg/config"
	"bdb-backend/pkg/logger"
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	// 初始化配置
	cfg := config.Load()

	// 初始化日志
	logger.Init(cfg.Logger, cfg.Server.Mode)

	// 使用新的全局日志记录器
	logger.Log.Info().Msg("服务器启动中...")

	// 使用 Wire 构建服务器实例
	server, err := api.BuildServer(cfg)
	if err != nil {
		logger.Log.Fatal().Err(err).Msg("服务器构建失败")
	}

	// 创建 context 用于优雅关闭
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 在 goroutine 中启动服务器
	go func() {
		logger.Log.Info().Str("port", cfg.Server.Port).Msg("服务器启动成功")
		if err := server.Run(ctx); err != nil && err != http.ErrServerClosed {
			logger.Log.Fatal().Err(err).Msg("服务器运行失败")
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Log.Info().Msg("服务器正在关闭...")

	// 取消 context，通知服务器开始关闭
	cancel()

	// 设置关闭超时
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// 优雅关闭服务器
	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Log.Error().Err(err).Msg("服务器强制关闭")
	} else {
		logger.Log.Info().Msg("服务器已优雅关闭")
	}
}

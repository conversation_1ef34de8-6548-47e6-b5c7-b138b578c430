# IM聊天系统设计方案 V2.0

本文档基于对原有设计的分析和改进，提供一个更符合IM最佳实践的系统设计方案。

## 1. 核心设计理念

- **读写分离思想**：将会话的基础信息与用户的个人状态完全分离
- **最后消息动态获取**：避免冗余存储，通过查询获取最新消息
- **消息状态清晰化**：明确区分发送、送达、已读、撤回四种状态
- **缓存优先策略**：大量使用Redis缓存提升性能
- **事件驱动架构**：通过消息队列处理异步任务

## 2. 核心数据模型设计

### 2.1 会话模型 (`Conversation`)

```go
// Conversation 会话基础信息 - 使用经典的user_id1/user_id2设计
type Conversation struct {
    ID        uint64           `json:"id" gorm:"primarykey"`
    Type      ConversationType `json:"type" gorm:"type:varchar(20);not null;default:'single'"`
    UserID1   uint64           `json:"user_id1" gorm:"not null;index"` // 用户ID1
    UserID2   uint64           `json:"user_id2" gorm:"not null;index"` // 用户ID2
    CreatedBy uint64           `json:"created_by" gorm:"not null"`     // 创建者ID
    CreatedAt time.Time        `json:"created_at"`
    
    // 移除 last_message_* 字段，通过查询动态获取
}

// 字段约定：
// - 单聊会话：user_id1 < user_id2，确保唯一性
// - 系统通知：user_id1 = 0，user_id2 = 接收者用户ID
// 
// 添加唯一索引确保会话唯一性
// UNIQUE INDEX uk_conversation_users ON conversations(user_id1, user_id2, type)
```

### 2.2 用户会话状态 (`UserConversation`)

```go
// UserConversation 用户的会话个人状态 - 移除频繁更新的字段
type UserConversation struct {
    UserID         uint64     `json:"user_id" gorm:"primaryKey;index"`
    ConversationID uint64     `json:"conversation_id" gorm:"primaryKey;index"`
    
    // 可见性控制
    IsVisible      bool       `json:"is_visible" gorm:"default:true;index"`
    IsDeleted      bool       `json:"is_deleted" gorm:"default:false"`
    DeletedAt      *time.Time `json:"deleted_at"`
    
    // 个人设置 - 这些字段更新频率低
    IsPinned       bool       `json:"is_pinned" gorm:"default:false;index"`
    IsMuted        bool       `json:"is_muted" gorm:"default:false"`
    CustomName     string     `json:"custom_name" gorm:"type:varchar(100)"` // 自定义会话名称
    
    // 最后活跃时间 - 用于排序，只在发送消息时更新
    LastActiveTime time.Time  `json:"last_active_time" gorm:"index"`
    
    CreatedAt      time.Time  `json:"created_at"`
    UpdatedAt      time.Time  `json:"updated_at"`
    
    // 移除以下字段，改为动态计算：
    // - UnreadCount (通过Redis或查询计算)
    // - LastReadMessageID (通过Redis存储)
    // - LastReadTime (通过消息状态计算)
}
```

### 2.2.1 已读状态独立存储 (`UserReadStatus`)

```go
// UserReadStatus 用户已读状态 - 独立存储，避免频繁更新主表
type UserReadStatus struct {
    UserID            uint64     `json:"user_id" gorm:"primaryKey"`
    ConversationID    uint64     `json:"conversation_id" gorm:"primaryKey;index"`
    LastReadMessageID uint64     `json:"last_read_message_id" gorm:"not null"`
    LastReadTime      time.Time  `json:"last_read_time" gorm:"index"`
    UpdatedAt         time.Time  `json:"updated_at"`
}

// 索引：idx_user_read_status (conversation_id, user_id)
```

### 2.3 消息模型 (`Message`)

```go
// MessageStatus 消息状态枚举
type MessageStatus string

const (
    MessageStatusSent      MessageStatus = "sent"      // 已发送到服务器
    MessageStatusDelivered MessageStatus = "delivered" // 已送达接收方
    MessageStatusRead      MessageStatus = "read"      // 已被阅读
    MessageStatusRevoked   MessageStatus = "revoked"   // 已撤回
    MessageStatusFailed    MessageStatus = "failed"    // 发送失败
)

// Message 消息模型
type Message struct {
    ID             uint64        `json:"id" gorm:"primarykey"`
    MsgID          string        `json:"msg_id" gorm:"type:varchar(36);uniqueIndex;not null"` // 全局唯一消息ID，客户端生成
    ConversationID uint64        `json:"conversation_id" gorm:"not null;index:idx_msg_conversation"`
    SenderID       uint64        `json:"sender_id" gorm:"not null;index"` // 0表示系统消息
    
    // 消息内容
    Content     string      `json:"content" gorm:"type:text"`
    MessageType MessageType `json:"message_type" gorm:"type:varchar(20)"` // text/image/voice/video/file
    Extra       string      `json:"extra" gorm:"type:json"`              // 扩展字段，存储图片尺寸、语音时长等
    
    // 消息状态 - 清晰化设计
    Status      MessageStatus `json:"status" gorm:"type:varchar(20);default:'sent';index"`
    IsRevoked   bool         `json:"is_revoked" gorm:"default:false;index"`
    RevokedAt   *time.Time   `json:"revoked_at"`
    
    // 时间信息
    CreatedAt   time.Time    `json:"created_at" gorm:"index:idx_msg_time"`
    DeliveredAt *time.Time   `json:"delivered_at"`
    ReadAt      *time.Time   `json:"read_at"`
    
    // 索引：idx_msg_conversation (conversation_id, created_at DESC)
    // 索引：idx_msg_time (created_at DESC) 
}
```

### 2.4 消息回执模型 (`MessageReceipt`)

```go
// MessageReceipt 消息回执记录 - 用于记录消息的送达和已读状态
type MessageReceipt struct {
    MessageID    uint64        `json:"message_id" gorm:"primaryKey"`
    ReceiverID   uint64        `json:"receiver_id" gorm:"primaryKey"`
    Status       MessageStatus `json:"status" gorm:"type:varchar(20);not null"`
    ReceiptTime  time.Time     `json:"receipt_time"`
    
    // 索引：idx_receipt_receiver (receiver_id, receipt_time DESC)
}
```

## 3. 缓存设计 (Redis)

### 3.1 会话列表缓存

```redis
# 用户会话列表 - 有序集合，按最后活跃时间排序
# Key: user_conversations:{user_id}
# Score: 最后活跃时间戳
# Member: conversation_id
ZADD user_conversations:123 1672531200 "conv_456"

# 会话详情缓存 - Hash
# Key: conversation:{conversation_id}
HSET conversation:456 
    "id" "456"
    "type" "single" 
    "user_id1" "100"
    "user_id2" "123"
    "created_at" "2023-01-01T00:00:00Z"

# 用户会话状态缓存 - Hash  
# Key: user_conv_state:{user_id}:{conversation_id}
HSET user_conv_state:123:456
    "unread_count" "5"
    "is_pinned" "false"
    "is_muted" "false"
    "last_read_message_id" "789"
```

### 3.2 消息缓存

```redis
# 会话最新消息缓存 - 避免数据库查询
# Key: conv_last_message:{conversation_id}
HSET conv_last_message:456
    "id" "999"
    "msg_id" "550e8400-e29b-41d4-a716-446655440000"
    "content" "Hello World"
    "sender_id" "100"
    "message_type" "text"
    "created_at" "2023-01-01T12:00:00Z"

# 消息内容缓存 - String，过期时间24小时
# Key: message:{message_id}  
SET message:999 "{json_data}" EX 86400

# 用户已读状态缓存 - Hash
# Key: user_read_status:{user_id}
HSET user_read_status:123
    "conv_456" "999"  # conversation_id -> last_read_message_id
    "conv_789" "888"

# 用户未读数缓存 - Hash，动态计算
# Key: user_unread_count:{user_id}  
HSET user_unread_count:123
    "conv_456" "5"   # conversation_id -> unread_count
    "conv_789" "0"
```

### 3.3 在线状态缓存

```redis
# 用户在线状态 - Set
SADD online_users "123" "456" "789"

# 用户最后活跃时间 - Hash
HSET user_last_active
    "123" "1672531200"
    "456" "1672531100"
```

## 4. 核心业务流程优化

### 4.1 发送消息流程（优化版）

```go
func (s *MessageService) SendMessage(ctx context.Context, req *SendMessageRequest) (*Message, error) {
    // 1. 参数验证（包括MsgID验证）
    if err := s.validateSendRequest(req); err != nil {
        return nil, err
    }
    
    // 2. 检查消息MsgID是否已存在（防重复）
    if exists, err := s.checkMessageExists(req.MsgID); err != nil {
        return nil, err
    } else if exists {
        return s.getMessageByMsgID(req.MsgID), nil // 幂等性处理
    }
    
    // 3. 获取或创建会话
    conv, err := s.conversationSvc.GetOrCreateConversation(ctx, req.SenderID, req.ReceiverID)
    if err != nil {
        return nil, err
    }
    
    // 4. 创建消息记录
    message := &Message{
        MsgID:          req.MsgID, // 客户端生成的消息ID
        ConversationID: conv.ID,
        SenderID:       req.SenderID,
        Content:        req.Content,
        MessageType:    req.MessageType,
        Status:         MessageStatusSent,
        CreatedAt:      time.Now(),
    }
    
    // 4. 开启事务
    tx := s.db.Begin()
    defer tx.Rollback()
    
    // 5. 保存消息
    if err := tx.Create(message).Error; err != nil {
        return nil, err
    }
    
    // 6. 更新发送方会话状态
    if err := s.updateSenderConversationState(tx, req.SenderID, conv.ID, message); err != nil {
        return nil, err
    }
    
    // 7. 更新接收方会话状态 (未读+1，恢复可见性)
    if err := s.updateReceiverConversationState(tx, req.ReceiverID, conv.ID, message); err != nil {
        return nil, err
    }
    
    // 8. 提交事务
    if err := tx.Commit().Error; err != nil {
        return nil, err
    }
    
    // 9. 异步任务
    go func() {
        // 更新缓存
        s.updateMessageCache(message)
        s.updateConversationCache(conv.ID, message)
        
        // 实时推送
        s.pushMessage(ctx, message)
        
        // 如果接收方在线，标记为已送达
        if s.isUserOnline(req.ReceiverID) {
            s.markMessageDelivered(ctx, message.ID, req.ReceiverID)
        }
    }()
    
    return message, nil
}
```

### 4.2 获取会话列表流程（优化版）

```go
func (s *ConversationService) GetUserConversations(ctx context.Context, userID uint64, page, size int) ([]*ConversationDetail, error) {
    // 1. 先从缓存获取会话列表
    conversationIDs, err := s.getCachedUserConversations(userID, page, size)
    if err == nil && len(conversationIDs) > 0 {
        return s.buildConversationDetails(ctx, userID, conversationIDs)
    }
    
    // 2. 缓存miss，从数据库查询
    var userConvs []UserConversation
    err = s.db.Where("user_id = ? AND is_visible = true", userID).
        Order("is_pinned DESC, last_active_time DESC").
        Offset((page - 1) * size).
        Limit(size).
        Find(&userConvs).Error
    if err != nil {
        return nil, err
    }
    
    // 3. 构建详细信息
    details := make([]*ConversationDetail, 0, len(userConvs))
    
    for _, uc := range userConvs {
        // 获取会话基础信息
        conv, err := s.getConversationFromCache(uc.ConversationID)
        if err != nil {
            conv, err = s.getConversationFromDB(uc.ConversationID)
            if err != nil {
                continue
            }
        }
        
        // 获取对方用户信息
        otherUserID := s.getOtherUserID(conv, userID)
        otherUser, err := s.userSvc.GetUserInfo(ctx, otherUserID)
        if err != nil {
            continue
        }
        
        // 获取最后一条消息
        lastMessage, err := s.getLastMessage(uc.ConversationID)
        if err != nil {
            continue
        }
        
        // 动态计算未读数和已读状态
        unreadCount := s.calculateUnreadCount(ctx, userID, uc.ConversationID)
        lastReadMessageID := s.getLastReadMessageID(ctx, userID, uc.ConversationID)
        
        detail := &ConversationDetail{
            Conversation:      conv,
            UserConversation:  uc,
            OtherUser:         otherUser,
            LastMessage:       lastMessage,
            UnreadCount:       unreadCount,         // 动态计算
            LastReadMessageID: lastReadMessageID,   // 从缓存获取
        }
        
        details = append(details, detail)
    }
    
    // 4. 更新缓存
    go s.cacheUserConversations(userID, details)
    
    return details, nil
}

// 动态计算未读数
func (s *ConversationService) calculateUnreadCount(ctx context.Context, userID, conversationID uint64) int {
    // 1. 先从Redis缓存获取
    if count, err := s.redis.HGet(ctx, fmt.Sprintf("user_unread_count:%d", userID), 
        fmt.Sprintf("conv_%d", conversationID)).Int(); err == nil {
        return count
    }
    
    // 2. 缓存miss，查询数据库计算
    lastReadMessageID := s.getLastReadMessageID(ctx, userID, conversationID)
    
    var count int64
    s.db.Model(&Message{}).Where(`
        conversation_id = ? 
        AND sender_id != ? 
        AND id > ? 
        AND status != ?
    `, conversationID, userID, lastReadMessageID, MessageStatusRevoked).Count(&count)
    
    // 3. 更新缓存
    s.redis.HSet(ctx, fmt.Sprintf("user_unread_count:%d", userID), 
        fmt.Sprintf("conv_%d", conversationID), count)
    
    return int(count)
}

// 获取用户在会话中的最后已读消息ID  
func (s *ConversationService) getLastReadMessageID(ctx context.Context, userID, conversationID uint64) uint64 {
    // 1. 先从Redis获取
    if msgID, err := s.redis.HGet(ctx, fmt.Sprintf("user_read_status:%d", userID), 
        fmt.Sprintf("conv_%d", conversationID)).Uint64(); err == nil {
        return msgID
    }
    
    // 2. 从数据库获取
    var readStatus UserReadStatus
    if err := s.db.Where("user_id = ? AND conversation_id = ?", userID, conversationID).
        First(&readStatus).Error; err == nil {
        // 更新缓存
        s.redis.HSet(ctx, fmt.Sprintf("user_read_status:%d", userID), 
            fmt.Sprintf("conv_%d", conversationID), readStatus.LastReadMessageID)
        return readStatus.LastReadMessageID
    }
    
    return 0 // 未读过任何消息
}
```

### 4.3 消息已读流程

```go
func (s *MessageService) MarkMessagesAsRead(ctx context.Context, userID, conversationID uint64) error {
    // 1. 获取用户在该会话的未读消息
    var unreadMessages []Message
    err := s.db.Where(`
        conversation_id = ? 
        AND sender_id != ?
        AND status != ?
        AND id > COALESCE((
            SELECT last_read_message_id 
            FROM user_conversations 
            WHERE user_id = ? AND conversation_id = ?
        ), 0)
    `, conversationID, userID, MessageStatusRead, userID, conversationID).
        Find(&unreadMessages).Error
    if err != nil {
        return err
    }
    
    if len(unreadMessages) == 0 {
        return nil
    }
    
    // 2. 开启事务
    tx := s.db.Begin()
    defer tx.Rollback()
    
    // 3. 批量更新消息状态为已读
    messageIDs := make([]uint64, len(unreadMessages))
    for i, msg := range unreadMessages {
        messageIDs[i] = msg.ID
    }
    
    now := time.Now()
    if err := tx.Model(&Message{}).Where("id IN ?", messageIDs).Updates(map[string]interface{}{
        "status":  MessageStatusRead,
        "read_at": now,
    }).Error; err != nil {
        return err
    }
    
    // 4. 更新用户会话状态
    lastMessageID := messageIDs[len(messageIDs)-1]
    if err := tx.Model(&UserConversation{}).
        Where("user_id = ? AND conversation_id = ?", userID, conversationID).
        Updates(map[string]interface{}{
            "unread_count":         0,
            "last_read_message_id": lastMessageID,
            "last_read_time":       now,
        }).Error; err != nil {
        return err
    }
    
    // 5. 记录消息回执
    receipts := make([]MessageReceipt, len(messageIDs))
    for i, msgID := range messageIDs {
        receipts[i] = MessageReceipt{
            MessageID:   msgID,
            ReceiverID:  userID,
            Status:      MessageStatusRead,
            ReceiptTime: now,
        }
    }
    
    if err := tx.CreateInBatches(&receipts, 100).Error; err != nil {
        return err
    }
    
    // 6. 提交事务
    if err := tx.Commit().Error; err != nil {
        return err
    }
    
    // 7. 异步处理
    go func() {
        // 更新缓存
        s.updateReadStatusCache(userID, conversationID)
        
        // 推送已读回执给发送方
        for _, msg := range unreadMessages {
            s.pushReadReceipt(ctx, msg.SenderID, msg.ID, userID)
        }
    }()
    
    return nil
}
```

## 5. 性能优化策略

### 5.1 数据库优化

```sql
-- 核心索引设计
CREATE UNIQUE INDEX uk_conversation_users ON conversations(user_id1, user_id2, type);
CREATE INDEX idx_user_conversations_list ON user_conversations(user_id, is_visible, is_pinned, last_active_time DESC);
CREATE INDEX idx_messages_conversation ON messages(conversation_id, created_at DESC);
CREATE INDEX idx_messages_unread ON messages(conversation_id, sender_id, status, id);
CREATE INDEX idx_message_receipts ON message_receipts(receiver_id, receipt_time DESC);

-- 分区表（可选，对于大数据量）
CREATE TABLE messages_202401 PARTITION OF messages 
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### 5.2 缓存策略

1. **会话列表缓存**：用户会话列表缓存30分钟
2. **消息缓存**：最新消息缓存24小时
3. **用户信息缓存**：用户基础信息缓存1小时
4. **在线状态缓存**：实时更新，5分钟过期

### 5.3 读写分离

- **写操作**：主数据库
- **读操作**：从数据库 + 缓存
- **会话列表查询**：优先缓存，降级到从库
- **历史消息查询**：从库查询

## 6. API接口设计

```http
# 会话管理
POST /api/chat/conversations/list     # 获取会话列表
POST /api/chat/conversations/create   # 创建会话（主要用于群聊）
POST /api/chat/conversations/delete   # 删除会话
POST /api/chat/conversations/pin      # 置顶/取消置顶

# 消息管理  
POST /api/chat/messages/send          # 发送消息
POST /api/chat/messages/list          # 获取消息历史
POST /api/chat/messages/read          # 标记消息已读
POST /api/chat/messages/revoke        # 撤回消息

# 消息状态
POST /api/chat/messages/receipt       # 上报消息回执

# 系统通知
POST /api/chat/notifications/list     # 获取系统通知
```

## 7. 实时推送事件

```javascript
// 新消息事件
{
    "type": "new_message",
    "data": {
        "message": { /* 完整消息对象 */ },
        "conversation": { /* 会话信息 */ },
        "sender": { /* 发送者信息 */ }
    }
}

// 消息状态更新
{
    "type": "message_status_update", 
    "data": {
        "message_id": 123,
        "conversation_id": 456,
        "status": "read", // delivered/read
        "updated_by": 789,
        "updated_at": "2023-01-01T12:00:00Z"
    }
}

// 消息撤回
{
    "type": "message_revoked",
    "data": {
        "message_id": 123,
        "conversation_id": 456, 
        "revoked_by": 100
    }
}

// 会话状态更新
{
    "type": "conversation_update",
    "data": {
        "conversation_id": 456,
        "update_type": "unread_count", // unread_count/pinned/deleted
        "value": 0
    }
}
```

## 8. 核心问题解决方案

### 8.1 UnreadCount更新策略

**问题**：UnreadCount需要频繁更新，特别是用户离线时收到消息

**解决方案**：
1. **移除数据库存储**：不在UserConversation表中存储UnreadCount
2. **Redis缓存策略**：使用`user_unread_count:{user_id}`存储各会话未读数
3. **懒加载计算**：查询会话列表时动态计算未读数
4. **异步更新**：
   - 发送消息时：异步增加接收方未读数缓存
   - 用户上线时：批量刷新未读数缓存
   - 消息已读时：清零对应会话未读数

```go
// 发送消息时更新未读数
func (s *MessageService) updateUnreadCount(ctx context.Context, receiverID, conversationID uint64) {
    key := fmt.Sprintf("user_unread_count:%d", receiverID)
    field := fmt.Sprintf("conv_%d", conversationID)
    s.redis.HIncrBy(ctx, key, field, 1)
}

// 标记已读时清零未读数
func (s *MessageService) clearUnreadCount(ctx context.Context, userID, conversationID uint64) {
    key := fmt.Sprintf("user_unread_count:%d", userID)
    field := fmt.Sprintf("conv_%d", conversationID)
    s.redis.HSet(ctx, key, field, 0)
}
```

### 8.2 消息UUID的作用

1. **全局唯一标识**：客户端和服务端都能通过MsgID识别消息
2. **幂等性保证**：防止消息重复发送
3. **离线消息同步**：客户端可以通过MsgID确认消息是否已发送成功
4. **跨端同步**：多端登录时消息状态同步的依据

```go
// 客户端发送消息时生成MsgID
type SendMessageRequest struct {
    MsgID       string      `json:"msg_id" binding:"required"`      // 客户端生成
    SenderID    uint64      `json:"sender_id" binding:"required"`
    ReceiverID  uint64      `json:"receiver_id" binding:"required"`
    Content     string      `json:"content" binding:"required"`
    MessageType MessageType `json:"message_type" binding:"required"`
}
```

### 8.3 动态查询 vs 存储冗余的对比

| 方案 | 动态查询（新方案） | 存储冗余（原方案） |
|------|------------------|------------------|
| **数据一致性** | ✅ 强一致性 | ❌ 可能不一致 |
| **写入性能** | ✅ 减少50%写入 | ❌ 每发消息都要更新多表 |
| **查询性能** | ✅ Redis缓存<10ms | ⚠️ 需要JOIN查询 |
| **存储成本** | ✅ 无冗余存储 | ❌ 大量冗余数据 |
| **扩展性** | ✅ 易于扩展新状态 | ❌ 需要修改表结构 |
| **维护复杂度** | ✅ 逻辑简单清晰 | ❌ 多表同步复杂 |

## 9. 总结

### 关键改进点

1. **彻底移除冗余存储**：UnreadCount、LastReadMessageID等全部改为动态计算
2. **消息MsgID机制**：保证消息全局唯一性和幂等性
3. **缓存优先策略**：热点数据优先从Redis获取，大幅提升性能
4. **独立已读状态表**：UserReadStatus独立存储，避免频繁更新主表
5. **异步更新策略**：写入和缓存更新分离，提升响应速度
6. **动态计算机制**：在查询时计算未读数，保证数据一致性

### 性能预期

- **会话列表查询**：缓存命中下 < 10ms
- **消息发送**：< 100ms （含事务提交）
- **历史消息查询**：< 50ms （含分页）
- **并发支持**：单机支持 10,000+ 并发连接

这个设计方案解决了原有设计的问题，更符合现代IM系统的最佳实践。 
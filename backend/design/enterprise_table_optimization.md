# 企业表优化设计方案

## 📊 现状问题分析

### 当前设计问题
1. **users表职责过重**：包含了企业会员相关字段
2. **数据耦合度高**：用户基础信息与企业业务信息混合
3. **扩展性受限**：企业功能扩展需要修改用户表
4. **查询性能问题**：企业相关查询需要扫描整个用户表

### 问题字段识别
```sql
-- users表中需要迁移的企业相关字段
member_level        SMALLINT NOT NULL DEFAULT 0 COMMENT '会员等级：0-普通 1-VIP 2-SVIP',
member_expire_at    TIMESTAMP(0) DEFAULT NULL COMMENT '会员到期时间',
```

## 🎯 优化方案设计

### 方案一：企业信息表独立（推荐）

#### 1. 新建企业信息表 (enterprises)
```sql
-- =====================================================
-- 企业信息表 (enterprises) - 存储企业基础信息和会员信息
-- =====================================================
CREATE TABLE enterprises (
    id BIGSERIAL PRIMARY KEY,
    enterprise_id VARCHAR(64) NOT NULL DEFAULT '' COMMENT '企业业务ID，用于对外暴露',
    user_id BIGINT NOT NULL DEFAULT 0 COMMENT '关联用户ID',
    
    -- 企业基础信息（来源于enterprise_verifications）
    enterprise_name VARCHAR(100) NOT NULL DEFAULT '' COMMENT '企业名称',
    enterprise_type VARCHAR(20) NOT NULL DEFAULT 'company' COMMENT '企业类型',
    unified_social_credit_code VARCHAR(32) NOT NULL DEFAULT '' COMMENT '统一社会信用代码（脱敏）',
    legal_representative VARCHAR(20) NOT NULL DEFAULT '' COMMENT '法定代表人（脱敏）',
    registered_address VARCHAR(200) NOT NULL DEFAULT '' COMMENT '注册地址',
    contact_phone VARCHAR(20) NOT NULL DEFAULT '' COMMENT '联系电话',
    contact_email VARCHAR(100) NOT NULL DEFAULT '' COMMENT '联系邮箱',
    
    -- 企业会员信息（从users表迁移）
    member_level SMALLINT NOT NULL DEFAULT 0 COMMENT '企业会员等级：0-普通 1-VIP 2-SVIP 3-PLUS',
    member_expire_at TIMESTAMP(0) DEFAULT NULL COMMENT '会员到期时间',
    member_started_at TIMESTAMP(0) DEFAULT NULL COMMENT '会员开始时间',
    
    -- 企业业务信息
    business_scope TEXT NOT NULL DEFAULT '' COMMENT '经营范围',
    establishment_date DATE DEFAULT NULL COMMENT '成立日期',
    employee_count SMALLINT NOT NULL DEFAULT 0 COMMENT '员工人数',
    registered_capital DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '注册资本（万元）',
    
    -- 企业状态
    status SMALLINT NOT NULL DEFAULT 1 COMMENT '企业状态：1-正常 2-冻结 3-注销',
    verification_status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '认证状态',
    verification_id BIGINT NOT NULL DEFAULT 0 COMMENT '关联认证记录ID',
    
    -- 企业设置
    settings JSONB NOT NULL DEFAULT '{}' COMMENT '企业设置JSON',
    
    -- 时间字段
    created_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    is_del BIGINT NOT NULL DEFAULT 0 COMMENT '软删除标记'
);

-- 企业信息表索引
CREATE UNIQUE INDEX uk_enterprises_enterprise_id ON enterprises(enterprise_id) WHERE is_del = 0;
CREATE UNIQUE INDEX uk_enterprises_user_id ON enterprises(user_id) WHERE is_del = 0;
CREATE INDEX idx_enterprises_member_level ON enterprises(member_level) WHERE is_del = 0;
CREATE INDEX idx_enterprises_member_expire_at ON enterprises(member_expire_at);
CREATE INDEX idx_enterprises_status ON enterprises(status) WHERE is_del = 0;
CREATE INDEX idx_enterprises_verification_status ON enterprises(verification_status) WHERE is_del = 0;
CREATE INDEX idx_enterprises_created_at ON enterprises(created_at);
CREATE INDEX idx_enterprises_is_del ON enterprises(is_del);
```

#### 2. 修改用户表结构
```sql
-- 从users表中移除企业相关字段
ALTER TABLE users DROP COLUMN IF EXISTS member_level;
ALTER TABLE users DROP COLUMN IF EXISTS member_expire_at;

-- 添加企业关联字段
ALTER TABLE users ADD COLUMN enterprise_id BIGINT NOT NULL DEFAULT 0 COMMENT '关联企业ID：0-未认证企业';
CREATE INDEX idx_users_enterprise_id ON users(enterprise_id) WHERE is_del = 0;
```

#### 3. 企业会员权限表 (enterprise_permissions)
```sql
-- =====================================================
-- 企业会员权限表 - 配置不同会员等级的权限
-- =====================================================
CREATE TABLE enterprise_permissions (
    id BIGSERIAL PRIMARY KEY,
    member_level SMALLINT NOT NULL COMMENT '会员等级',
    permission_key VARCHAR(50) NOT NULL COMMENT '权限标识',
    permission_value JSONB NOT NULL DEFAULT '{}' COMMENT '权限配置',
    description VARCHAR(200) NOT NULL DEFAULT '' COMMENT '权限描述',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX uk_enterprise_permissions ON enterprise_permissions(member_level, permission_key);
```

## 📈 数据迁移方案

### 1. 数据迁移脚本
```sql
-- Step 1: 创建新表
-- (执行上述建表语句)

-- Step 2: 迁移已认证企业数据
INSERT INTO enterprises (
    user_id, enterprise_name, enterprise_type, unified_social_credit_code,
    legal_representative, registered_address, contact_phone, contact_email,
    business_scope, establishment_date, member_level, member_expire_at,
    verification_status, verification_id, created_at, updated_at
)
SELECT 
    u.id as user_id,
    ev.enterprise_name,
    ev.enterprise_type,
    ev.unified_social_credit_code,
    ev.legal_representative,
    ev.registered_address,
    ev.contact_phone,
    ev.contact_email,
    ev.business_scope,
    ev.establishment_date,
    u.member_level,
    u.member_expire_at,
    CASE 
        WHEN ev.status = 'approved' THEN 'approved'
        WHEN ev.status = 'rejected' THEN 'rejected'
        ELSE 'pending'
    END as verification_status,
    ev.id as verification_id,
    u.created_at,
    u.updated_at
FROM users u
INNER JOIN enterprise_verifications ev ON u.id = ev.user_id
WHERE u.enterprise_verified = true AND u.is_del = 0 AND ev.is_del = 0;

-- Step 3: 更新用户表的企业关联
UPDATE users SET enterprise_id = (
    SELECT e.id FROM enterprises e WHERE e.user_id = users.id
) WHERE enterprise_verified = true;

-- Step 4: 验证数据完整性
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN enterprise_verified = true THEN 1 END) as enterprise_users,
    COUNT(CASE WHEN enterprise_id > 0 THEN 1 END) as linked_enterprise_users
FROM users WHERE is_del = 0;
```

### 2. 回滚方案
```sql
-- 紧急回滚：恢复用户表字段
ALTER TABLE users ADD COLUMN IF NOT EXISTS member_level SMALLINT DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS member_expire_at TIMESTAMP(0) DEFAULT NULL;

-- 从企业表回写数据
UPDATE users SET 
    member_level = e.member_level,
    member_expire_at = e.member_expire_at
FROM enterprises e 
WHERE users.enterprise_id = e.id;
```

## 🔄 业务逻辑调整

### 1. 模型层调整
```go
// Enterprise 企业模型
type Enterprise struct {
    BaseModel
    EnterpriseID   string     `gorm:"type:varchar(64);uniqueIndex:uk_enterprises_enterprise_id,where:is_del=0" json:"enterprise_id"`
    UserID         uint       `gorm:"uniqueIndex:uk_enterprises_user_id,where:is_del=0" json:"user_id"`
    
    // 企业基础信息
    EnterpriseName string `gorm:"type:varchar(100)" json:"enterprise_name"`
    EnterpriseType string `gorm:"type:varchar(20);default:'company'" json:"enterprise_type"`
    
    // 会员信息
    MemberLevel    int        `gorm:"default:0;index" json:"member_level"`
    MemberExpireAt *time.Time `gorm:"type:timestamp(0)" json:"member_expire_at"`
    
    // 关联关系
    User         *User                     `gorm:"foreignKey:UserID" json:"user,omitempty"`
    Verification *EnterpriseVerification  `gorm:"foreignKey:VerificationID" json:"verification,omitempty"`
}

// 更新User模型
type User struct {
    BaseModel
    // ... 其他字段保持不变
    EnterpriseID uint `gorm:"default:0;index" json:"enterprise_id"`
    
    // 关联关系
    Enterprise *Enterprise `gorm:"foreignKey:EnterpriseID" json:"enterprise,omitempty"`
}
```

### 2. 服务层调整
```go
// EnterpriseService 企业服务
type EnterpriseService struct {
    repo repository.EnterpriseRepository
}

// GetUserEnterprise 获取用户企业信息
func (s *EnterpriseService) GetUserEnterprise(userID uint) (*model.Enterprise, error) {
    return s.repo.GetByUserID(userID)
}

// UpdateMemberLevel 更新会员等级
func (s *EnterpriseService) UpdateMemberLevel(enterpriseID uint, level int, expireAt *time.Time) error {
    return s.repo.UpdateMemberInfo(enterpriseID, level, expireAt)
}
```

## ✅ 方案优势

1. **职责清晰**：用户表专注用户基础信息，企业表专注企业业务
2. **扩展性强**：企业功能扩展不影响用户表结构
3. **查询性能优化**：企业相关查询直接查询企业表
4. **数据一致性**：企业信息变更不影响用户基础数据
5. **业务逻辑清晰**：企业认证、会员管理等业务逻辑独立

## 🚀 实施计划

### 阶段一：设计验证（1天）
- [ ] 评审表结构设计
- [ ] 确认业务逻辑调整点
- [ ] 制定详细实施计划

### 阶段二：代码开发（2-3天）
- [ ] 创建Enterprise模型和相关结构
- [ ] 实现EnterpriseRepository和Service
- [ ] 调整User模型，移除企业相关字段
- [ ] 更新API接口逻辑

### 阶段三：数据迁移（1天）
- [ ] 在测试环境执行迁移脚本
- [ ] 验证数据完整性
- [ ] 准备生产环境迁移方案

### 阶段四：上线部署（1天）
- [ ] 生产环境数据备份
- [ ] 执行数据迁移
- [ ] 验证功能正常
- [ ] 监控系统运行状态

## 🔧 技术要点

1. **事务处理**：数据迁移必须在事务中执行，确保数据一致性
2. **索引优化**：合理设计索引，优化查询性能
3. **缓存更新**：迁移后需要清理相关缓存
4. **API兼容性**：保持对外API的向后兼容
5. **监控告警**：设置数据迁移监控，及时发现问题 
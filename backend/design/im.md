# IM 系统设计方案

本文档为重构后的聊天与通知系统提供了一个全面、可扩展且高性能的设计方案，旨在满足当前及未来的业务需求，并遵循 IM 系统最佳实践。

## 1. 核心设计原则

- **数据驱动与无状态服务**: 核心业务逻辑由清晰的数据模型驱动，服务本身保持无状态，易于水平扩展。
- **读写分离**: 频繁更新的数据（如未读数）与静态数据分离，利用缓存（Redis）处理高频读写，降低数据库压力。
- **用户体验优先**: 设计充分考虑了主流 IM 应用的交互逻辑，特别是在会话管理、消息状态同步和实时性方面。
- **数据一致性**: 通过原子操作（事务）和可靠的事件推送，确保多端数据状态的最终一致性。
- **性能与可伸缩性**: 通过优化的数据库索引、缓存优先策略和异步任务处理，确保系统高性能和高可用性。

## 2. 数据模型设计 (Database Schema)

这是整个 IM 系统的核心。我们采用精简的表结构，将不变的、低频更新的、高频更新的数据分离开来。

### 2.1 `conversations` - 会话表

存储会话的静态、基础信息。一旦创建，记录基本不变。

```sql
CREATE TABLE conversations (
    id          BIGSERIAL PRIMARY KEY,
    type        VARCHAR(20) NOT NULL, -- 'single' 或 'system'
    -- 为保证两个用户间的单聊会话唯一，约定 user_id1 < user_id2
    user_id1    BIGINT NOT NULL,
    user_id2    BIGINT NOT NULL,
    created_by  BIGINT NOT NULL,      -- 会话创建者 User ID
    created_at  TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- 唯一约束，防止重复创建会话
    CONSTRAINT uk_conversation_users UNIQUE (user_id1, user_id2, type)
);
COMMENT ON TABLE conversations IS '会话基础信息表';
```

### 2.2 `user_conversations` - 用户会话表

存储每个用户对一个会话的个性化状态和设置。这是实现“单方面删除会话”等功能的核心。

```sql
CREATE TABLE user_conversations (
    user_id           BIGINT NOT NULL,
    conversation_id   BIGINT NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    -- 用户视角的状态
    is_visible        BOOLEAN NOT NULL DEFAULT true, -- 用户会话列表中是否可见
    deleted_at        TIMESTAMPTZ,                   -- 用户删除会话的时间点
    last_read_message_id BIGINT DEFAULT 0,           -- 该用户在此会话中最后已读的消息ID
    -- 用户个人设置
    is_pinned         BOOLEAN NOT NULL DEFAULT false, -- 是否置顶
    is_muted          BOOLEAN NOT NULL DEFAULT false, -- 是否免打扰
    -- 排序与更新
    last_active_time  TIMESTAMPTZ NOT NULL DEFAULT NOW(), -- 用于会话列表排序
    updated_at        TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    PRIMARY KEY (user_id, conversation_id)
);
COMMENT ON TABLE user_conversations IS '用户与会话的关联表，存储个人状态';
COMMENT ON COLUMN user_conversations.deleted_at IS '记录用户删除会话的时间，用于恢复后不显示历史消息';
```

### 2.3 `messages` - 消息表

存储所有消息。这是数据量最大、增长最快的表。

```sql
CREATE TABLE messages (
    id              BIGSERIAL PRIMARY KEY,
    -- 客户端生成的UUID，用于消息去重和保证幂等性
    msg_id          VARCHAR(36) NOT NULL UNIQUE,
    conversation_id BIGINT NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    sender_id       BIGINT NOT NULL, -- 发送者 User ID (0 代表系统)
    -- 消息内容
    message_type    VARCHAR(20) NOT NULL, -- 'text', 'image', 'audio', 'video', 'emoji', 'location', 'system' 等
    content         TEXT,                 -- 文本内容
    extra           JSONB,                -- 存储富媒体信息 (URL, 尺寸, 时长等)
    -- 消息状态
    is_revoked      BOOLEAN NOT NULL DEFAULT false,
    created_at      TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
COMMENT ON TABLE messages IS '消息存储表';
COMMENT ON COLUMN messages.msg_id IS '客户端生成的唯一ID，用于保证消息发送的幂等性';
COMMENT ON COLUMN messages.extra IS '存储富文本消息的元数据，如图片URL、视频时长等';
```

### 2.4 数据库索引策略

```sql
-- 加速会话查找
CREATE INDEX idx_conversations_users ON conversations(user_id1, user_id2);

-- 优化用户会话列表查询
CREATE INDEX idx_user_conversations_list ON user_conversations(user_id, is_visible, is_pinned, last_active_time DESC);

-- 优化消息历史记录查询
CREATE INDEX idx_messages_conversation_time ON messages(conversation_id, created_at DESC);

-- 自动更新 updated_at 时间戳的触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_user_conversations_updated_at
BEFORE UPDATE ON user_conversations
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 2.5 系统通知处理 (`System Notifications`)

系统通知在架构上被视为一种特殊的“单聊”，即“系统”与“单个用户”之间的对话。我们不采用全局共享的通知会话，而是为每个用户创建其专属的系统通知会话。

- **创建时机**: 用户注册成功后，系统会自动为其创建一条 `type` 为 `system` 的会话记录。
  - `conversations` 表: `(type: 'system', user_id1: 0, user_id2: <user_id>)`
  - `user_conversations` 表: `(user_id: <user_id>, conversation_id: <new_conversation_id>)`
- **优点**:
  - **数据隔离**: 用户可以独立管理自己的系统通知，如删除、标记已读，不影响其他用户。
  - **逻辑复用**: 完全复用现有会话和消息的查询、发送、状态管理逻辑，无需为系统通知编写特殊代码。
  - **状态管理**: 置顶、免打扰等状态可直接记录在 `user_conversations` 表中，保持了模型的一致性。
- **消息发送**: 向特定用户发送系统通知，等同于向这个专属会話中发送一条消息。


## 3. 核心业务流程

### 3.1 发送消息

**场景**: 用户 A 向用户 B 发送一条消息。

1.  **客户端**:
    *   生成一个唯一的 `msg_id` (UUID)。
    *   调用 `POST /api/v1/im/messages` 接口，带上 `msg_id`、接收者 `user_id` 和消息内容。

2.  **后端 (API)**:
    *   **开启数据库事务**。
    *   **获取/创建会话**:
        *   根据 `user_id1` 和 `user_id2` (排序后) 查询 `conversations` 表。
        *   如果会话不存在，则创建一条新记录。
        *   同时，为 A 和 B 在 `user_conversations` 表中创建关联记录。
    *   **检查接收方状态**:
        *   查询用户 B 的 `user_conversations` 记录。如果 `is_visible` 为 `false`，则更新为 `true` 并将 `deleted_at` 设为 `NULL`。这实现了会话的自动恢复。
    *   **存储消息**:
        *   将消息内容（包括 `msg_id`）插入 `messages` 表。
    *   **更新会话活跃时间**:
        *   更新 A 和 B 在 `user_conversations` 表中的 `last_active_time` 为当前时间，以确保会话在双方列表中都置顶。
    *   **提交事务**。

3.  **后端 (异步)**:
    *   **更新缓存**: 在 Redis 中原子地为用户 B 增加此会话的未读数。
    *   **实时推送**: 通过 Centrifugo 将新消息推送到用户 B 的私有频道 `user:{user_id_B}`。

### 3.2 获取会话列表

1.  **客户端**: 调用 `GET /api/v1/im/conversations`。
2.  **后端 (API)**:
    *   查询 `user_conversations` 表，获取当前用户所有 `is_visible = true` 的会话，按 `is_pinned DESC`, `last_active_time DESC` 排序。
    *   **批量获取最后一条消息**: 使用 `LATERAL JOIN` 或 `ROW_NUMBER()` 窗口函数，一次性高效地查询出每个会话的最后一条消息。
    *   **批量获取对方用户信息**: 收集所有对方用户的 `user_id`，一次性从 `users` 表或缓存中查询。
    *   **批量获取未读数**: 从 Redis 的 Hash 结构 (`unread_counts:{user_id}`) 中一次性获取所有会话的未读数。
    *   组合数据并返回给客户端。

### 3.3 获取历史消息

**场景**: 用户 A 进入与用户 B 的聊天界面。

1.  **客户端**: 调用 `GET /api/v1/im/conversations/{conv_id}/messages`。
2.  **后端 (API)**:
    *   查询用户 A 的 `user_conversations` 记录，获取 `deleted_at` 时间戳。
    *   构建查询: `SELECT * FROM messages WHERE conversation_id = ?`。
    *   **应用删除规则**: 如果 `deleted_at` 存在，则增加查询条件 `AND created_at > ?`，值为 `deleted_at` 的时间。这确保了用户看不到删除会话前的消息。
    *   按 `created_at DESC` 排序并分页返回。

### 3.4 删除会话

**场景**: 用户 A 在会话列表左滑，删除与 B 的会话。

1.  **客户端**: 调用 `DELETE /api/v1/im/conversations/{conv_id}`。
2.  **后端 (API)**:
    *   执行 `UPDATE user_conversations SET is_visible = false, deleted_at = NOW() WHERE user_id = ? AND conversation_id = ?`。
    *   清空 Redis 中该会话的未读数。
    *   操作完成。此操作不影响用户 B，也不删除任何消息记录。

### 3.5 标记已读

1.  **客户端**: 进入聊天界面时，调用 `POST /api/v1/im/conversations/{conv_id}/read`。
2.  **后端 (API)**:
    *   获取该会话的最新一条消息 `last_message_id`。
    *   更新 `user_conversations` 表，设置 `last_read_message_id = ?`。
    *   将 Redis 中该会话的未读数清零。
    *   通过 Centrifugo 向对方用户推送一个 "messages_read" 事件，以便对方 UI 更新消息状态（如“已读”角标）。

## 4. 缓存设计 (Redis)

- **用户未读数**: `HASH` - `im:unread:{user_id}`
  - `field`: `conversation_id`
  - `value`: `count`
  - 使用 `HINCRBY` 和 `HSET` 进行原子增减和清零。

- **用户信息**: `HASH` - `user:info:{user_id}`
  - 缓存用户信息，减少会话列表查询时的数据库压力。

- **会话最后一条消息**: `STRING` - `im:last_message:{conversation_id}`
  - 缓存消息对象的 JSON 字符串，在发送新消息时更新。

## 5. 实时通信 (Centrifugo)

- **频道策略**:
  - **用户私有频道**: `user:#{user_id}` (使用 Centrifugo 的私有频道特性)。用于接收所有需要通知到个人的事件，如新消息、状态更新等。
- **核心事件负载 (Payload)**:
  - **新消息 (`new_message`)**:
    ```json
    {
      "type": "new_message",
      "data": {
        "message": { ...MessageObject... },
        "conversation": { ...ConversationInfo... }
      }
    }
    ```
  - **消息已读回执 (`messages_read`)**:
    ```json
    {
      "type": "messages_read",
      "data": {
        "conversation_id": 123,
        "read_by_user_id": 456,
        "last_read_message_id": 789
      }
    }
    ```

## 6. API 接口设计

- `POST /api/v1/im/messages`: 发送消息
- `GET /api/v1/im/conversations`: 获取会话列表
- `GET /api/v1/im/conversations/{id}/messages`: 获取指定会话的历史消息
- `POST /api/v1/im/conversations/{id}/read`: 将会话标记为已读
- `DELETE /api/v1/im/conversations/{id}`: 从列表中删除会话
- `POST /api/v1/im/conversations/{id}/pin`: 置顶/取消置顶会话
- `POST /api/v1/im/conversations/{id}/mute`: 免打扰/取消免打扰

## 7. 未来扩展：群聊

当前设计能平滑过渡到群聊，主要变更点：
1.  **引入 `conversation_members` 表**: 替代 `conversations` 表中的 `user_id1`, `user_id2` 来管理多对多关系。
2.  **修改会话创建逻辑**: 创建群聊时，在 `conversation_members` 中添加多个成员。
3.  **消息@功能**: 在 `messages.extra` JSON 字段中存储被`@`的 `user_id` 列表。

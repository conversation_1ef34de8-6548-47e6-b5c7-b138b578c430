# 🏗️ 认证服务技术架构设计方案

## 📋 架构设计原则

### 1. 单一职责原则
- **个人认证服务**：专注个人实名认证逻辑
- **企业认证服务**：专注企业资质认证逻辑
- **认证状态服务**：统一管理认证状态和权限

### 2. 高内聚低耦合
- 各认证服务独立部署和扩展
- 通过事件驱动机制实现服务间通信
- 共享数据通过统一的数据访问层

### 3. 可扩展性设计
- 预留接口支持未来新增认证类型
- 支持认证流程的动态配置
- 认证规则可插拔设计

## 🏗️ 服务拆分架构

```mermaid
graph TB
    subgraph "API网关层"
        Gateway[API Gateway]
    end
    
    subgraph "认证服务集群"
        PersonalVerifyService[个人认证服务]
        EnterpriseVerifyService[企业认证服务]
        VerificationStatusService[认证状态服务]
        VerificationAuditService[认证审核服务]
    end
    
    subgraph "共享服务层"
        CryptoService[加密服务]
        NotificationService[消息通知服务]
        FileService[文件存储服务]
        ThirdPartyService[第三方验证服务]
    end
    
    subgraph "数据存储层"
        UserDB[(用户主库)]
        VerificationDB[(认证专库)]
        CacheRedis[(Redis缓存)]
        FileStorage[(文件存储)]
    end
    
    Gateway --> PersonalVerifyService
    Gateway --> EnterpriseVerifyService
    Gateway --> VerificationStatusService
    Gateway --> VerificationAuditService
    
    PersonalVerifyService --> CryptoService
    PersonalVerifyService --> FileService
    PersonalVerifyService --> ThirdPartyService
    PersonalVerifyService --> VerificationDB
    
    EnterpriseVerifyService --> CryptoService
    EnterpriseVerifyService --> FileService
    EnterpriseVerifyService --> ThirdPartyService
    EnterpriseVerifyService --> VerificationDB
    
    VerificationStatusService --> UserDB
    VerificationStatusService --> VerificationDB
    VerificationStatusService --> CacheRedis
    
    VerificationAuditService --> VerificationDB
    VerificationAuditService --> NotificationService
```

## 📦 服务模块设计

### 1. 个人认证服务 (PersonalVerificationService)

#### 核心职责
- 个人实名认证流程管理
- 身份证OCR识别和验证
- 人脸识别对比
- 个人认证数据加密存储

#### 接口设计
```go
type PersonalVerificationService interface {
    // 开始个人认证
    StartVerification(ctx context.Context, req *StartPersonalVerificationRequest) (*StartVerificationResponse, error)
    
    // 提交身份证信息
    SubmitIDCard(ctx context.Context, req *SubmitIDCardRequest) (*SubmitIDCardResponse, error)
    
    // 人脸识别验证
    VerifyFace(ctx context.Context, req *VerifyFaceRequest) (*VerifyFaceResponse, error)
    
    // 提交审核
    SubmitForAudit(ctx context.Context, req *SubmitAuditRequest) (*SubmitAuditResponse, error)
    
    // 查询认证状态
    GetVerificationStatus(ctx context.Context, userID uint) (*PersonalVerificationStatus, error)
    
    // 获取认证信息（脱敏）
    GetVerificationInfo(ctx context.Context, userID uint) (*PersonalVerificationInfo, error)
}
```

#### 数据模型
```go
// 个人认证请求
type StartPersonalVerificationRequest struct {
    UserID    uint   `json:"user_id" validate:"required"`
    Source    string `json:"source"`    // 认证来源：manual, ocr
    IP        string `json:"ip"`
    UserAgent string `json:"user_agent"`
}

// 身份证提交请求
type SubmitIDCardRequest struct {
    UserID          uint   `json:"user_id" validate:"required"`
    RealName        string `json:"real_name" validate:"required,min=2,max=20"`
    IDCard          string `json:"id_card" validate:"required,id_card"`
    IDCardFrontImg  string `json:"id_card_front_img"`  // 身份证正面
    IDCardBackImg   string `json:"id_card_back_img"`   // 身份证背面
}

// 人脸识别请求
type VerifyFaceRequest struct {
    UserID        uint   `json:"user_id" validate:"required"`
    FaceImage     string `json:"face_image" validate:"required"`     // Base64编码的人脸照片
    LivenessCheck bool   `json:"liveness_check"`                     // 是否进行活体检测
}
```

### 2. 企业认证服务 (EnterpriseVerificationService)

#### 核心职责
- 企业认证流程管理
- 营业执照OCR识别
- 工商信息查验
- 法人授权验证
- 企业认证数据加密存储

#### 接口设计
```go
type EnterpriseVerificationService interface {
    // 开始企业认证
    StartVerification(ctx context.Context, req *StartEnterpriseVerificationRequest) (*StartVerificationResponse, error)
    
    // 提交营业执照
    SubmitBusinessLicense(ctx context.Context, req *SubmitBusinessLicenseRequest) (*SubmitBusinessLicenseResponse, error)
    
    // 企业信息验证
    VerifyEnterpriseInfo(ctx context.Context, req *VerifyEnterpriseInfoRequest) (*VerifyEnterpriseInfoResponse, error)
    
    // 法人授权确认
    ConfirmLegalAuth(ctx context.Context, req *ConfirmLegalAuthRequest) (*ConfirmLegalAuthResponse, error)
    
    // 提交审核
    SubmitForAudit(ctx context.Context, req *SubmitAuditRequest) (*SubmitAuditResponse, error)
    
    // 查询认证状态
    GetVerificationStatus(ctx context.Context, userID uint) (*EnterpriseVerificationStatus, error)
    
    // 获取认证信息（脱敏）
    GetVerificationInfo(ctx context.Context, userID uint) (*EnterpriseVerificationInfo, error)
}
```

#### 数据模型
```go
// 企业认证开始请求
type StartEnterpriseVerificationRequest struct {
    UserID    uint   `json:"user_id" validate:"required"`
    Type      string `json:"type"`      // 企业类型：company, individual_business, etc.
    IP        string `json:"ip"`
    UserAgent string `json:"user_agent"`
}

// 营业执照提交请求
type SubmitBusinessLicenseRequest struct {
    UserID             uint   `json:"user_id" validate:"required"`
    EnterpriseName     string `json:"enterprise_name" validate:"required,min=2,max=100"`
    CreditCode         string `json:"credit_code" validate:"required"`
    LegalRepresentative string `json:"legal_representative" validate:"required,min=2,max=20"`
    BusinessLicenseImg string `json:"business_license_img" validate:"required"` // 营业执照图片
}

// 企业信息验证请求
type VerifyEnterpriseInfoRequest struct {
    UserID               uint   `json:"user_id" validate:"required"`
    RegisteredAddress    string `json:"registered_address"`
    BusinessScope        string `json:"business_scope"`
    EstablishmentDate    string `json:"establishment_date"`
    ContactPhone         string `json:"contact_phone"`
    ContactEmail         string `json:"contact_email"`
}
```

### 3. 认证状态服务 (VerificationStatusService)

#### 核心职责
- 统一管理所有认证状态
- 认证权限检查
- 认证状态缓存
- 认证数据聚合查询

#### 接口设计
```go
type VerificationStatusService interface {
    // 获取用户完整认证状态
    GetUserVerificationStatus(ctx context.Context, userID uint) (*UserVerificationStatus, error)
    
    // 检查权限
    CheckPermission(ctx context.Context, userID uint, permission string) (bool, error)
    
    // 批量检查权限
    BatchCheckPermission(ctx context.Context, userIDs []uint, permission string) (map[uint]bool, error)
    
    // 更新认证状态（内部调用）
    UpdateVerificationStatus(ctx context.Context, req *UpdateVerificationStatusRequest) error
    
    // 获取认证统计信息
    GetVerificationStatistics(ctx context.Context, req *StatisticsRequest) (*VerificationStatistics, error)
}
```

#### 数据模型
```go
// 用户认证状态
type UserVerificationStatus struct {
    UserID              uint                          `json:"user_id"`
    PersonalVerification *PersonalVerificationStatus  `json:"personal_verification"`
    EnterpriseVerification *EnterpriseVerificationStatus `json:"enterprise_verification"`
    UserLevel           UserLevel                     `json:"user_level"`
    Permissions         []string                      `json:"permissions"`
    LastUpdated         time.Time                     `json:"last_updated"`
}

// 个人认证状态
type PersonalVerificationStatus struct {
    Status      VerificationStatus `json:"status"`
    RealName    string            `json:"real_name"`     // 脱敏显示
    IDCard      string            `json:"id_card"`       // 脱敏显示
    SubmittedAt *time.Time        `json:"submitted_at"`
    AuditedAt   *time.Time        `json:"audited_at"`
    ExpiredAt   *time.Time        `json:"expired_at"`
}

// 企业认证状态
type EnterpriseVerificationStatus struct {
    Status             VerificationStatus `json:"status"`
    EnterpriseName     string            `json:"enterprise_name"`
    CreditCode         string            `json:"credit_code"`      // 脱敏显示
    LegalRepresentative string           `json:"legal_representative"` // 脱敏显示
    SubmittedAt        *time.Time        `json:"submitted_at"`
    AuditedAt          *time.Time        `json:"audited_at"`
    ExpiredAt          *time.Time        `json:"expired_at"`
}
```

### 4. 认证审核服务 (VerificationAuditService)

#### 核心职责
- 认证申请的AI初审
- 人工审核流程管理
- 审核结果处理
- 审核数据统计

#### 接口设计
```go
type VerificationAuditService interface {
    // AI初审
    AIAudit(ctx context.Context, req *AIAuditRequest) (*AIAuditResponse, error)
    
    // 提交人工审核
    SubmitManualAudit(ctx context.Context, req *ManualAuditRequest) (*ManualAuditResponse, error)
    
    // 获取待审核列表
    GetPendingAudits(ctx context.Context, req *GetPendingAuditsRequest) (*PendingAuditsResponse, error)
    
    // 审核操作
    ProcessAudit(ctx context.Context, req *ProcessAuditRequest) (*ProcessAuditResponse, error)
    
    // 获取审核历史
    GetAuditHistory(ctx context.Context, req *GetAuditHistoryRequest) (*AuditHistoryResponse, error)
}
```

## 🔄 服务间通信设计

### 1. 事件驱动通信

#### 事件类型定义
```go
type VerificationEvent struct {
    EventID   string                 `json:"event_id"`
    EventType string                 `json:"event_type"`
    UserID    uint                   `json:"user_id"`
    Data      map[string]interface{} `json:"data"`
    Timestamp time.Time              `json:"timestamp"`
}

// 事件类型常量
const (
    EventPersonalVerificationStarted   = "personal_verification_started"
    EventPersonalVerificationSubmitted = "personal_verification_submitted"
    EventPersonalVerificationApproved  = "personal_verification_approved"
    EventPersonalVerificationRejected  = "personal_verification_rejected"
    
    EventEnterpriseVerificationStarted   = "enterprise_verification_started"
    EventEnterpriseVerificationSubmitted = "enterprise_verification_submitted"
    EventEnterpriseVerificationApproved  = "enterprise_verification_approved"
    EventEnterpriseVerificationRejected  = "enterprise_verification_rejected"
)
```

#### 事件处理流程
```go
// 个人认证服务发布事件
func (s *PersonalVerificationService) publishEvent(eventType string, userID uint, data map[string]interface{}) {
    event := VerificationEvent{
        EventID:   uuid.New().String(),
        EventType: eventType,
        UserID:    userID,
        Data:      data,
        Timestamp: time.Now(),
    }
    
    s.eventBus.Publish("verification.events", event)
}

// 认证状态服务监听事件
func (s *VerificationStatusService) handleVerificationEvent(event VerificationEvent) {
    switch event.EventType {
    case EventPersonalVerificationApproved:
        s.updatePersonalVerificationStatus(event.UserID, VerificationStatusVerified)
        s.updateUserPermissions(event.UserID)
        s.invalidateCache(event.UserID)
    case EventEnterpriseVerificationApproved:
        s.updateEnterpriseVerificationStatus(event.UserID, VerificationStatusVerified)
        s.updateUserPermissions(event.UserID)
        s.invalidateCache(event.UserID)
    }
}
```

### 2. Repository层重新设计

#### 分库设计
```go
// 认证专库Repository
type VerificationRepository interface {
    // 个人认证相关
    CreatePersonalVerification(ctx context.Context, verification *PersonalVerification) error
    UpdatePersonalVerification(ctx context.Context, userID uint, updates map[string]interface{}) error
    GetPersonalVerification(ctx context.Context, userID uint) (*PersonalVerification, error)
    
    // 企业认证相关
    CreateEnterpriseVerification(ctx context.Context, verification *EnterpriseVerification) error
    UpdateEnterpriseVerification(ctx context.Context, userID uint, updates map[string]interface{}) error
    GetEnterpriseVerification(ctx context.Context, userID uint) (*EnterpriseVerification, error)
    
    // 审核记录相关
    CreateAuditRecord(ctx context.Context, record *AuditRecord) error
    GetAuditRecords(ctx context.Context, req *GetAuditRecordsRequest) ([]*AuditRecord, error)
}

// 用户状态Repository（主库）
type UserStatusRepository interface {
    GetUserVerificationSummary(ctx context.Context, userID uint) (*UserVerificationSummary, error)
    UpdateUserVerificationStatus(ctx context.Context, userID uint, updates map[string]interface{}) error
    BatchGetUserVerificationSummary(ctx context.Context, userIDs []uint) (map[uint]*UserVerificationSummary, error)
}
```

## 📁 目录结构重新组织

```
backend/internal/
├── verification/                    # 认证模块
│   ├── personal/                   # 个人认证服务
│   │   ├── service/
│   │   │   ├── personal_verification_svc.go
│   │   │   └── personal_audit_svc.go
│   │   ├── repository/
│   │   │   └── personal_verification_repo.go
│   │   ├── types/
│   │   │   └── personal_types.go
│   │   └── handler/
│   │       └── personal_handler.go
│   │
│   ├── enterprise/                 # 企业认证服务
│   │   ├── service/
│   │   │   ├── enterprise_verification_svc.go
│   │   │   └── enterprise_audit_svc.go
│   │   ├── repository/
│   │   │   └── enterprise_verification_repo.go
│   │   ├── types/
│   │   │   └── enterprise_types.go
│   │   └── handler/
│   │       └── enterprise_handler.go
│   │
│   ├── status/                     # 认证状态服务
│   │   ├── service/
│   │   │   ├── status_svc.go
│   │   │   └── permission_svc.go
│   │   ├── repository/
│   │   │   └── status_repo.go
│   │   ├── types/
│   │   │   └── status_types.go
│   │   └── handler/
│   │       └── status_handler.go
│   │
│   ├── audit/                      # 认证审核服务
│   │   ├── service/
│   │   │   ├── audit_svc.go
│   │   │   └── ai_audit_svc.go
│   │   ├── repository/
│   │   │   └── audit_repo.go
│   │   ├── types/
│   │   │   └── audit_types.go
│   │   └── handler/
│   │       └── audit_handler.go
│   │
│   ├── shared/                     # 共享组件
│   │   ├── crypto/
│   │   │   └── verification_crypto.go
│   │   ├── cache/
│   │   │   └── verification_cache.go
│   │   ├── events/
│   │   │   ├── event_bus.go
│   │   │   └── verification_events.go
│   │   └── constants/
│   │       └── verification_constants.go
│   │
│   └── wire.go                     # 依赖注入配置
│
├── user/                           # 用户基础服务
│   ├── service/
│   │   └── user_svc.go
│   ├── repository/
│   │   └── user_repo.go
│   └── types/
│       └── user_types.go
│
└── ...                            # 其他业务模块
```

## 🚀 数据库设计优化

### 1. 数据库分离
```sql
-- 用户主库 (user_db)
-- 存储用户基础信息和认证状态摘要
CREATE DATABASE user_db;

-- 认证专库 (verification_db)  
-- 存储所有认证相关的详细数据
CREATE DATABASE verification_db;
```

### 2. 用户主库表设计
```sql
-- 用户表（简化版）
CREATE TABLE users (
    id                   BIGSERIAL PRIMARY KEY COMMENT '主键ID',
    uid                  VARCHAR(64) NOT NULL DEFAULT '' COMMENT '用户唯一标识',
    openid               VARCHAR(128) NOT NULL DEFAULT '' COMMENT '微信OpenID',
    phone                VARCHAR(20) NOT NULL DEFAULT '' COMMENT '手机号',
    nickname             VARCHAR(50) DEFAULT '' COMMENT '昵称',
    avatar               VARCHAR(255) DEFAULT '' COMMENT '头像URL',
    gender               SMALLINT NOT NULL DEFAULT 0 COMMENT '性别：0-未知 1-男 2-女',
    status               SMALLINT NOT NULL DEFAULT 1 COMMENT '用户状态：1-正常 2-冻结 3-禁用',
    birthday             DATE DEFAULT NULL COMMENT '生日',
    
    -- 认证状态摘要（用于快速查询）
    personal_verified    BOOLEAN NOT NULL DEFAULT FALSE COMMENT '个人认证状态',
    enterprise_verified  BOOLEAN NOT NULL DEFAULT FALSE COMMENT '企业认证状态',
    user_level           SMALLINT NOT NULL DEFAULT 0 COMMENT '用户等级：0-基础 1-个人认证 2-企业认证 3-双重认证',
    
    -- 其他字段...
    created_at           TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at           TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 创建索引
CREATE INDEX idx_users_personal_verified ON users(personal_verified);
CREATE INDEX idx_users_enterprise_verified ON users(enterprise_verified);
CREATE INDEX idx_users_user_level ON users(user_level);
```

### 3. 认证专库表设计
```sql
-- 个人认证详细表
CREATE TABLE personal_verifications (
    id                   BIGSERIAL PRIMARY KEY COMMENT '主键ID',
    user_id              BIGINT NOT NULL DEFAULT 0 COMMENT '用户ID',
    real_name            VARCHAR(20) NOT NULL DEFAULT '' COMMENT '真实姓名（脱敏显示）',
    real_name_encrypted  TEXT NOT NULL DEFAULT '' COMMENT '真实姓名（加密存储）',
    id_card              VARCHAR(24) NOT NULL DEFAULT '' COMMENT '身份证号（脱敏显示）',
    id_card_encrypted    TEXT NOT NULL DEFAULT '' COMMENT '身份证号（加密存储）',
    real_name_hash       VARCHAR(64) NOT NULL DEFAULT '' COMMENT '姓名哈希（用于查重）',
    id_card_hash         VARCHAR(64) NOT NULL DEFAULT '' COMMENT '身份证哈希（用于查重）',
    verification_docs    JSONB NOT NULL DEFAULT '{}' COMMENT '认证文件信息',
    status               SMALLINT NOT NULL DEFAULT 0 COMMENT '认证状态：0-待审核 1-通过 2-拒绝',
    audit_reason         VARCHAR(500) NOT NULL DEFAULT '' COMMENT '审核原因',
    audited_at           TIMESTAMP(0) DEFAULT NULL COMMENT '审核时间',
    audited_by           BIGINT NOT NULL DEFAULT 0 COMMENT '审核员ID',
    created_at           TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at           TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 企业认证详细表
CREATE TABLE enterprise_verifications (
    id                                      BIGSERIAL PRIMARY KEY COMMENT '主键ID',
    user_id                                 BIGINT NOT NULL DEFAULT 0 COMMENT '用户ID',
    enterprise_name                         VARCHAR(100) NOT NULL DEFAULT '' COMMENT '企业名称',
    unified_social_credit_code              VARCHAR(32) NOT NULL DEFAULT '' COMMENT '统一社会信用代码（脱敏）',
    unified_social_credit_code_encrypted    TEXT NOT NULL DEFAULT '' COMMENT '统一社会信用代码（加密）',
    legal_representative                    VARCHAR(20) NOT NULL DEFAULT '' COMMENT '法定代表人（脱敏）',
    legal_representative_encrypted          TEXT NOT NULL DEFAULT '' COMMENT '法定代表人（加密）',
    enterprise_name_hash                    VARCHAR(64) NOT NULL DEFAULT '' COMMENT '企业名称哈希',
    credit_code_hash                        VARCHAR(64) NOT NULL DEFAULT '' COMMENT '信用代码哈希',
    legal_representative_hash               VARCHAR(64) NOT NULL DEFAULT '' COMMENT '法定代表人哈希',
    verification_docs                       JSONB NOT NULL DEFAULT '{}' COMMENT '认证文件信息',
    status                                  SMALLINT NOT NULL DEFAULT 0 COMMENT '认证状态：0-待审核 1-通过 2-拒绝',
    audit_reason                            VARCHAR(500) NOT NULL DEFAULT '' COMMENT '审核原因',
    audited_at                              TIMESTAMP(0) DEFAULT NULL COMMENT '审核时间',
    audited_by                              BIGINT NOT NULL DEFAULT 0 COMMENT '审核员ID',
    created_at                              TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at                              TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 审核记录表
CREATE TABLE audit_records (
    id              BIGSERIAL PRIMARY KEY COMMENT '主键ID',
    user_id         BIGINT NOT NULL DEFAULT 0 COMMENT '用户ID',
    verification_type VARCHAR(20) NOT NULL DEFAULT '' COMMENT '认证类型：personal, enterprise',
    verification_id BIGINT NOT NULL DEFAULT 0 COMMENT '认证记录ID',
    audit_type      VARCHAR(20) NOT NULL DEFAULT '' COMMENT '审核类型：ai, manual',
    old_status      SMALLINT NOT NULL DEFAULT 0 COMMENT '原状态',
    new_status      SMALLINT NOT NULL DEFAULT 0 COMMENT '新状态',
    audit_result    VARCHAR(20) NOT NULL DEFAULT '' COMMENT '审核结果：approved, rejected',
    audit_reason    VARCHAR(500) NOT NULL DEFAULT '' COMMENT '审核原因',
    auditor_id      BIGINT NOT NULL DEFAULT 0 COMMENT '审核员ID',
    audit_data      JSONB NOT NULL DEFAULT '{}' COMMENT '审核数据',
    created_at      TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);
```

## 🔧 缓存策略设计

### 1. 认证状态缓存
```go
type VerificationStatusCache struct {
    UserID              uint      `json:"user_id"`
    PersonalVerified    bool      `json:"personal_verified"`
    PersonalStatus      int       `json:"personal_status"`
    EnterpriseVerified  bool      `json:"enterprise_verified"`
    EnterpriseStatus    int       `json:"enterprise_status"`
    UserLevel           int       `json:"user_level"`
    Permissions         []string  `json:"permissions"`
    LastUpdated         time.Time `json:"last_updated"`
    TTL                 int       `json:"ttl"`
}

// 缓存键设计
const (
    CacheKeyUserVerificationStatus = "user:verification:status:%d"
    CacheKeyUserPermissions        = "user:permissions:%d"
    CacheKeyVerificationStatistics = "verification:statistics:%s"
)
```

### 2. 权限检查缓存
```go
func (s *VerificationStatusService) CheckPermissionWithCache(ctx context.Context, userID uint, permission string) (bool, error) {
    // 1. 先从缓存获取
    cacheKey := fmt.Sprintf("user:permission:%d:%s", userID, permission)
    if result, err := s.cache.Get(cacheKey); err == nil {
        return result.(bool), nil
    }
    
    // 2. 缓存未命中，从数据库查询
    hasPermission, err := s.checkPermissionFromDB(ctx, userID, permission)
    if err != nil {
        return false, err
    }
    
    // 3. 写入缓存
    s.cache.Set(cacheKey, hasPermission, time.Minute*10)
    
    return hasPermission, nil
}
```

## 📊 监控与告警

### 1. 关键指标监控
```go
type VerificationMetrics struct {
    // 认证相关指标
    PersonalVerificationCount     int64   `json:"personal_verification_count"`
    EnterpriseVerificationCount   int64   `json:"enterprise_verification_count"`
    VerificationSuccessRate       float64 `json:"verification_success_rate"`
    AverageAuditTime             int64   `json:"average_audit_time"`     // 平均审核时长（秒）
    
    // 性能指标
    PermissionCheckLatency       float64 `json:"permission_check_latency"`  // 权限检查延迟（ms）
    CacheHitRate                 float64 `json:"cache_hit_rate"`            // 缓存命中率
    DatabaseConnectionCount      int64   `json:"database_connection_count"`
    
    // 业务指标
    DualVerificationRate         float64 `json:"dual_verification_rate"`    // 双重认证率
    VerificationConversionRate   float64 `json:"verification_conversion_rate"` // 认证转化率
}
```

### 2. 告警配置
```yaml
alerts:
  verification_failure_rate:
    threshold: 0.1  # 认证失败率超过10%
    duration: 5m
    severity: warning
    
  permission_check_latency:
    threshold: 100  # 权限检查延迟超过100ms
    duration: 1m
    severity: critical
    
  cache_hit_rate_low:
    threshold: 0.8  # 缓存命中率低于80%
    duration: 5m
    severity: warning
```

---

## 💡 实施计划

### Phase 1: 基础架构搭建 (2周)
1. 创建认证专库和表结构
2. 实现基础的Repository层
3. 搭建事件总线机制
4. 实现认证状态缓存

### Phase 2: 核心服务开发 (3周)
1. 开发个人认证服务
2. 开发企业认证服务
3. 开发认证状态服务
4. 开发认证审核服务

### Phase 3: 集成测试与优化 (2周)
1. 服务间集成测试
2. 性能优化和缓存优化
3. 监控和告警配置
4. 文档完善

### Phase 4: 灰度发布 (1周)
1. 小规模用户灰度测试
2. 性能和稳定性验证
3. 问题修复和优化
4. 全量上线

这套技术架构设计确保了认证服务的高内聚低耦合，支持独立扩展和维护，同时保证了数据安全和系统性能。 
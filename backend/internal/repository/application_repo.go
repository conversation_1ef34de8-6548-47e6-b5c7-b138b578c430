package repository

import (
	"context"
	"fmt"
	"strings"
	"time"

	"bdb-backend/internal/constants"
	"bdb-backend/internal/model"
	"bdb-backend/internal/types"

	"gorm.io/gorm"
)

type ApplicationRepository interface {
	// Application CRUD operations
	Create(ctx context.Context, application *model.JobApplication) error
	Find(ctx context.Context, id uint) (*model.JobApplication, error)
	Update(ctx context.Context, application *model.JobApplication) error
	Delete(ctx context.Context, id uint) error
	GetList(ctx context.Context, req *types.ApplicationQueryRequest) ([]*model.JobApplication, int64, error)

	// User-specific application operations
	GetUserApplications(ctx context.Context, userID uint, page, pageSize int) ([]*model.JobApplication, int64, error)
	GetUserApplicationByJobID(ctx context.Context, userID, jobID uint) (*model.JobApplication, error)
	CheckUserApplied(ctx context.Context, userID, jobID uint) bool
	GetUserApplicationStats(ctx context.Context, userID uint) (*model.ApplicationStatistics, error)

	// Enterprise/Job-specific application operations
	GetJobApplications(ctx context.Context, jobID uint, page, pageSize int) ([]*model.JobApplication, int64, error)
	GetEnterpriseApplications(ctx context.Context, enterpriseID uint, req *types.ApplicationQueryRequest) ([]*model.JobApplication, int64, error)
	GetApplicationsByStatus(ctx context.Context, jobID uint, status string, page, pageSize int) ([]*model.JobApplication, int64, error)

	// Application status management
	UpdateStatus(ctx context.Context, id uint, status, note string) error
	BatchUpdateStatus(ctx context.Context, ids []uint, status, note string) error
	ScheduleInterview(ctx context.Context, id uint, interviewTime time.Time, address string) error

	// Application analytics and reporting
	GetApplicationStats(ctx context.Context, jobID uint) (*model.ApplicationStatistics, error)
	GetEnterpriseApplicationStats(ctx context.Context, enterpriseID uint) (*model.ApplicationStatistics, error)
	GetApplicationTrends(ctx context.Context, enterpriseID uint, days int) ([]*model.ApplicationTrend, error)

	// Notification and communication
	GetPendingNotifications(ctx context.Context, userID uint) ([]*model.JobNotification, error)
	CreateNotification(ctx context.Context, notification *model.JobNotification) error
	MarkNotificationAsRead(ctx context.Context, notificationID uint) error

	// Favorites and view history
	CreateFavorite(ctx context.Context, favorite *model.JobFavorite) error
	RemoveFavorite(ctx context.Context, userID, jobID uint) error
	GetUserFavorites(ctx context.Context, userID uint, page, pageSize int) ([]*model.JobFavorite, int64, error)
	CheckIsFavorited(ctx context.Context, userID, jobID uint) bool

	CreateViewHistory(ctx context.Context, viewHistory *model.JobViewHistory) error
	GetUserViewHistory(ctx context.Context, userID uint, page, pageSize int) ([]*model.JobViewHistory, int64, error)

	// Reporting and admin
	CreateReport(ctx context.Context, report *model.JobReport) error
	GetReportsByStatus(ctx context.Context, status string, page, pageSize int) ([]*model.JobReport, int64, error)
	UpdateReportStatus(ctx context.Context, reportID uint, status, response string) error

	// DB returns the underlying gorm.DB instance
	DB() *gorm.DB
}

type applicationRepository struct {
	db *gorm.DB
}

func NewApplicationRepository(db *gorm.DB) ApplicationRepository {
	return &applicationRepository{db: db}
}

// DB returns the underlying gorm.DB instance
func (r *applicationRepository) DB() *gorm.DB {
	return r.db
}

// Create creates a new job application
func (r *applicationRepository) Create(ctx context.Context, application *model.JobApplication) error {
	return r.db.WithContext(ctx).Create(application).Error
}

// Find retrieves an application by ID with related data
func (r *applicationRepository) Find(ctx context.Context, id uint) (*model.JobApplication, error) {
	var application model.JobApplication
	err := r.db.WithContext(ctx).
		Preload("Job").
		Preload("Job.Enterprise").
		Preload("User").
		First(&application, id).Error
	return &application, err
}

// Update updates an application
func (r *applicationRepository) Update(ctx context.Context, application *model.JobApplication) error {
	return r.db.WithContext(ctx).Save(application).Error
}

// Delete soft deletes an application
func (r *applicationRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.JobApplication{}, id).Error
}

// GetList retrieves a paginated list of applications with filtering
func (r *applicationRepository) GetList(ctx context.Context, req *types.ApplicationQueryRequest) ([]*model.JobApplication, int64, error) {
	var applications []*model.JobApplication
	var total int64

	db := r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Preload("Job").
		Preload("Job.Enterprise").
		Preload("User")

	// Apply filters
	db = r.applyApplicationFilters(db, req)

	// Count total records
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	db = r.applyApplicationSorting(db, req)

	// Apply pagination
	err := db.Scopes(model.Paginate(req.Page, req.PageSize)).Find(&applications).Error
	return applications, total, err
}

// GetUserApplications retrieves applications for a specific user
func (r *applicationRepository) GetUserApplications(ctx context.Context, userID uint, page, pageSize int) ([]*model.JobApplication, int64, error) {
	var applications []*model.JobApplication
	var total int64

	db := r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("user_id = ?", userID).
		Preload("Job").
		Preload("Job.Enterprise")

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("created_at DESC").
		Find(&applications).Error
	return applications, total, err
}

// GetUserApplicationByJobID retrieves a user's application for a specific job
func (r *applicationRepository) GetUserApplicationByJobID(ctx context.Context, userID, jobID uint) (*model.JobApplication, error) {
	var application model.JobApplication
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND job_id = ?", userID, jobID).
		Preload("Job").
		Preload("Job.Enterprise").
		First(&application).Error
	return &application, err
}

// CheckUserApplied checks if a user has already applied to a job
func (r *applicationRepository) CheckUserApplied(ctx context.Context, userID, jobID uint) bool {
	var count int64
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("user_id = ? AND job_id = ?", userID, jobID).
		Count(&count)
	return count > 0
}

// GetUserApplicationStats retrieves application statistics for a user
func (r *applicationRepository) GetUserApplicationStats(ctx context.Context, userID uint) (*model.ApplicationStatistics, error) {
	stats := &model.ApplicationStatistics{
		UserID: userID,
	}

	// Count total applications
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("user_id = ?", userID).
		Count(&stats.TotalApplications)

	// Count by status
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("user_id = ? AND status = ?", userID, constants.ApplicationStatusSubmitted).
		Count(&stats.PendingApplications)

	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("user_id = ? AND status = ?", userID, constants.ApplicationStatusViewed).
		Count(&stats.ViewedApplications)

	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("user_id = ? AND status IN ?", userID, []string{
			constants.ApplicationStatusInterviewInvited,
			constants.ApplicationStatusInterviewing,
		}).Count(&stats.InterviewApplications)

	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("user_id = ? AND status = ?", userID, constants.ApplicationStatusHired).
		Count(&stats.HiredApplications)

	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("user_id = ? AND status IN ?", userID, []string{
			constants.ApplicationStatusRejected,
			constants.ApplicationStatusInterviewFailed,
		}).Count(&stats.RejectedApplications)

	return stats, nil
}

// GetJobApplications retrieves applications for a specific job
func (r *applicationRepository) GetJobApplications(ctx context.Context, jobID uint, page, pageSize int) ([]*model.JobApplication, int64, error) {
	var applications []*model.JobApplication
	var total int64

	db := r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("job_id = ?", jobID).
		Preload("User")

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("created_at DESC").
		Find(&applications).Error
	return applications, total, err
}

// GetEnterpriseApplications retrieves applications for an enterprise's jobs
func (r *applicationRepository) GetEnterpriseApplications(ctx context.Context, enterpriseID uint, req *types.ApplicationQueryRequest) ([]*model.JobApplication, int64, error) {
	var applications []*model.JobApplication
	var total int64

	db := r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Joins("JOIN jobs ON job_applications.job_id = jobs.id").
		Where("jobs.enterprise_id = ?", enterpriseID).
		Preload("Job").
		Preload("User")

	// Apply additional filters
	db = r.applyApplicationFilters(db, req)

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	db = r.applyApplicationSorting(db, req)

	err := db.Scopes(model.Paginate(req.Page, req.PageSize)).Find(&applications).Error
	return applications, total, err
}

// GetApplicationsByStatus retrieves applications for a job with specific status
func (r *applicationRepository) GetApplicationsByStatus(ctx context.Context, jobID uint, status string, page, pageSize int) ([]*model.JobApplication, int64, error) {
	var applications []*model.JobApplication
	var total int64

	db := r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("job_id = ? AND status = ?", jobID, status).
		Preload("User")

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("created_at DESC").
		Find(&applications).Error
	return applications, total, err
}

// UpdateStatus updates application status with optional note
func (r *applicationRepository) UpdateStatus(ctx context.Context, id uint, status, note string) error {
	updates := map[string]interface{}{
		"status": status,
	}
	if note != "" {
		updates["recruiter_note"] = note
	}

	return r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("id = ?", id).
		Updates(updates).Error
}

// BatchUpdateStatus updates status for multiple applications
func (r *applicationRepository) BatchUpdateStatus(ctx context.Context, ids []uint, status, note string) error {
	updates := map[string]interface{}{
		"status": status,
	}
	if note != "" {
		updates["recruiter_note"] = note
	}

	return r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("id IN ?", ids).
		Updates(updates).Error
}

// ScheduleInterview schedules an interview for an application
func (r *applicationRepository) ScheduleInterview(ctx context.Context, id uint, interviewTime time.Time, address string) error {
	return r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":            constants.ApplicationStatusInterviewInvited,
			"interview_time":    &interviewTime,
			"interview_address": address,
		}).Error
}

// GetApplicationStats retrieves statistics for a specific job
func (r *applicationRepository) GetApplicationStats(ctx context.Context, jobID uint) (*model.ApplicationStatistics, error) {
	stats := &model.ApplicationStatistics{
		JobID: jobID,
	}

	// Count total applications
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("job_id = ?", jobID).
		Count(&stats.TotalApplications)

	// Count by status
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("job_id = ? AND status = ?", jobID, constants.ApplicationStatusSubmitted).
		Count(&stats.PendingApplications)

	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("job_id = ? AND status = ?", jobID, constants.ApplicationStatusViewed).
		Count(&stats.ViewedApplications)

	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("job_id = ? AND status IN ?", jobID, []string{
			constants.ApplicationStatusInterviewInvited,
			constants.ApplicationStatusInterviewing,
		}).Count(&stats.InterviewApplications)

	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("job_id = ? AND status = ?", jobID, constants.ApplicationStatusHired).
		Count(&stats.HiredApplications)

	return stats, nil
}

// GetEnterpriseApplicationStats retrieves aggregated statistics for an enterprise
func (r *applicationRepository) GetEnterpriseApplicationStats(ctx context.Context, enterpriseID uint) (*model.ApplicationStatistics, error) {
	stats := &model.ApplicationStatistics{
		EnterpriseID: enterpriseID,
	}

	// Count total applications
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Joins("JOIN jobs ON job_applications.job_id = jobs.id").
		Where("jobs.enterprise_id = ?", enterpriseID).
		Count(&stats.TotalApplications)

	// Count pending applications
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Joins("JOIN jobs ON job_applications.job_id = jobs.id").
		Where("jobs.enterprise_id = ? AND job_applications.status = ?",
			enterpriseID, constants.ApplicationStatusSubmitted).
		Count(&stats.PendingApplications)

	// Count hired applications
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Joins("JOIN jobs ON job_applications.job_id = jobs.id").
		Where("jobs.enterprise_id = ? AND job_applications.status = ?",
			enterpriseID, constants.ApplicationStatusHired).
		Count(&stats.HiredApplications)

	return stats, nil
}

// GetApplicationTrends retrieves application trends for an enterprise
func (r *applicationRepository) GetApplicationTrends(ctx context.Context, enterpriseID uint, days int) ([]*model.ApplicationTrend, error) {
	var trends []*model.ApplicationTrend
	startDate := time.Now().AddDate(0, 0, -days)

	rows, err := r.db.WithContext(ctx).Raw(`
		SELECT 
			DATE(job_applications.created_at) as date,
			COUNT(*) as count
		FROM job_applications 
		JOIN jobs ON job_applications.job_id = jobs.id
		WHERE jobs.enterprise_id = ? AND job_applications.created_at >= ?
		GROUP BY DATE(job_applications.created_at)
		ORDER BY date DESC
	`, enterpriseID, startDate).Rows()

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var trend model.ApplicationTrend
		if err := rows.Scan(&trend.Date, &trend.Count); err != nil {
			return nil, err
		}
		trends = append(trends, &trend)
	}

	return trends, nil
}

// GetPendingNotifications retrieves pending notifications for a user
func (r *applicationRepository) GetPendingNotifications(ctx context.Context, userID uint) ([]*model.JobNotification, error) {
	var notifications []*model.JobNotification
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND is_read = ?", userID, false).
		Order("created_at DESC").
		Find(&notifications).Error
	return notifications, err
}

// CreateNotification creates a new notification
func (r *applicationRepository) CreateNotification(ctx context.Context, notification *model.JobNotification) error {
	return r.db.WithContext(ctx).Create(notification).Error
}

// MarkNotificationAsRead marks a notification as read
func (r *applicationRepository) MarkNotificationAsRead(ctx context.Context, notificationID uint) error {
	return r.db.WithContext(ctx).Model(&model.JobNotification{}).
		Where("id = ?", notificationID).
		Update("is_read", true).Error
}

// CreateFavorite creates a new job favorite
func (r *applicationRepository) CreateFavorite(ctx context.Context, favorite *model.JobFavorite) error {
	return r.db.WithContext(ctx).Create(favorite).Error
}

// RemoveFavorite removes a job favorite
func (r *applicationRepository) RemoveFavorite(ctx context.Context, userID, jobID uint) error {
	return r.db.WithContext(ctx).
		Where("user_id = ? AND job_id = ?", userID, jobID).
		Delete(&model.JobFavorite{}).Error
}

// GetUserFavorites retrieves user's favorite jobs
func (r *applicationRepository) GetUserFavorites(ctx context.Context, userID uint, page, pageSize int) ([]*model.JobFavorite, int64, error) {
	var favorites []*model.JobFavorite
	var total int64

	db := r.db.WithContext(ctx).Model(&model.JobFavorite{}).
		Where("user_id = ?", userID).
		Preload("Job").
		Preload("Job.Enterprise")

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("created_at DESC").
		Find(&favorites).Error
	return favorites, total, err
}

// CheckIsFavorited checks if a job is favorited by a user
func (r *applicationRepository) CheckIsFavorited(ctx context.Context, userID, jobID uint) bool {
	var count int64
	r.db.WithContext(ctx).Model(&model.JobFavorite{}).
		Where("user_id = ? AND job_id = ?", userID, jobID).
		Count(&count)
	return count > 0
}

// CreateViewHistory creates a new view history record
func (r *applicationRepository) CreateViewHistory(ctx context.Context, viewHistory *model.JobViewHistory) error {
	return r.db.WithContext(ctx).Create(viewHistory).Error
}

// GetUserViewHistory retrieves user's job view history
func (r *applicationRepository) GetUserViewHistory(ctx context.Context, userID uint, page, pageSize int) ([]*model.JobViewHistory, int64, error) {
	var viewHistory []*model.JobViewHistory
	var total int64

	db := r.db.WithContext(ctx).Model(&model.JobViewHistory{}).
		Where("user_id = ?", userID).
		Preload("Job").
		Preload("Job.Enterprise")

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("view_time DESC").
		Find(&viewHistory).Error
	return viewHistory, total, err
}

// CreateReport creates a new job report
func (r *applicationRepository) CreateReport(ctx context.Context, report *model.JobReport) error {
	return r.db.WithContext(ctx).Create(report).Error
}

// GetReportsByStatus retrieves reports by status
func (r *applicationRepository) GetReportsByStatus(ctx context.Context, status string, page, pageSize int) ([]*model.JobReport, int64, error) {
	var reports []*model.JobReport
	var total int64

	db := r.db.WithContext(ctx).Model(&model.JobReport{}).
		Preload("Job").
		Preload("Reporter")

	if status != "" {
		db = db.Where("status = ?", status)
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("created_at DESC").
		Find(&reports).Error
	return reports, total, err
}

// UpdateReportStatus updates report status and response
func (r *applicationRepository) UpdateReportStatus(ctx context.Context, reportID uint, status, response string) error {
	updates := map[string]interface{}{
		"status": status,
	}
	if response != "" {
		updates["admin_response"] = response
	}

	return r.db.WithContext(ctx).Model(&model.JobReport{}).
		Where("id = ?", reportID).
		Updates(updates).Error
}

// Helper methods for building queries

// applyApplicationFilters applies various filters to the application query
func (r *applicationRepository) applyApplicationFilters(db *gorm.DB, req *types.ApplicationQueryRequest) *gorm.DB {
	if req.JobID > 0 {
		db = db.Where("job_applications.job_id = ?", req.JobID)
	}

	if req.UserID > 0 {
		db = db.Where("job_applications.user_id = ?", req.UserID)
	}

	if req.Status != "" {
		db = db.Where("job_applications.status = ?", req.Status)
	}

	return db
}

// applyApplicationSorting applies sorting to the application query
func (r *applicationRepository) applyApplicationSorting(db *gorm.DB, req *types.ApplicationQueryRequest) *gorm.DB {
	sortBy := req.SortBy
	sortOrder := req.SortOrder

	// Default sorting
	if sortBy == "" {
		sortBy = "created_at"
	}
	if sortOrder == "" {
		sortOrder = "desc"
	}

	switch sortBy {
	case "created_at":
		db = db.Order(fmt.Sprintf("job_applications.created_at %s", strings.ToUpper(sortOrder)))
	case "updated_at":
		db = db.Order(fmt.Sprintf("job_applications.updated_at %s", strings.ToUpper(sortOrder)))
	default:
		db = db.Order("job_applications.created_at DESC")
	}

	return db
}

package repository

import (
	"context"
	"time"

	"bdb-backend/internal/model"

	"gorm.io/gorm"
)

// MessageRepository 消息Repository接口
type MessageRepository interface {
	// 基础CRUD
	Create(ctx context.Context, message *model.Message) error
	Find(ctx context.Context, id uint) (*model.Message, error)
	GetByMsgID(ctx context.Context, msgID string) (*model.Message, error)
	Update(ctx context.Context, id uint, updates map[string]interface{}) error
	Delete(ctx context.Context, id uint) error

	// 查询方法
	ListByConversation(ctx context.Context, conversationID uint, page, pageSize int) ([]*model.Message, error)
	GetLastMessage(ctx context.Context, conversationID uint) (*model.Message, error)
	GetLastMessages(ctx context.Context, conversationIDs []uint) (map[uint]*model.Message, error)

	// 状态管理
	UpdateStatus(ctx context.Context, messageID uint, status model.ChatMessageStatus) error
	MarkAsDelivered(ctx context.Context, messageID uint) error
	MarkAsRead(ctx context.Context, messageID uint) error
	RevokeMessage(ctx context.Context, messageID uint, revokedBy uint) error

	// 统计查询
	CountUnreadMessages(ctx context.Context, userID, conversationID uint, afterMessageID uint) (int64, error)
	CountMessagesByConversation(ctx context.Context, conversationID uint) (int64, error)

	// 批量操作
	BatchCreate(ctx context.Context, messages []*model.Message) error
	BatchUpdateStatus(ctx context.Context, messageIDs []uint, status model.ChatMessageStatus) error
}

// messageRepository 消息Repository实现
type messageRepository struct {
	db *gorm.DB
}

// NewMessageRepository 创建消息Repository
func NewMessageRepository(db *gorm.DB) MessageRepository {
	return &messageRepository{db: db}
}

// Create 创建消息
func (r *messageRepository) Create(ctx context.Context, message *model.Message) error {
	return r.db.WithContext(ctx).Create(message).Error
}

// Find 根据ID获取消息
func (r *messageRepository) Find(ctx context.Context, id uint) (*model.Message, error) {
	var message model.Message
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&message).Error
	if err != nil {
		return nil, err
	}
	return &message, nil
}

// GetByMsgID 根据MsgID获取消息
func (r *messageRepository) GetByMsgID(ctx context.Context, msgID string) (*model.Message, error) {
	var message model.Message
	err := r.db.WithContext(ctx).Where("msg_id = ?", msgID).First(&message).Error
	if err != nil {
		return nil, err
	}
	return &message, nil
}

// Update 更新消息
func (r *messageRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&model.Message{}).Where("id = ?", id).Updates(updates).Error
}

// Delete 删除消息
func (r *messageRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&model.Message{}).Error
}

// ListByConversation 获取会话的消息列表
func (r *messageRepository) ListByConversation(ctx context.Context, conversationID uint, page, pageSize int) ([]*model.Message, error) {
	query := r.db.WithContext(ctx).Model(&model.Message{}).Where("conversation_id = ?", conversationID)

	var messages []*model.Message
	err := query.Order("created_at DESC").
		Scopes(model.Paginate(page, pageSize)).
		Find(&messages).Error

	return messages, err
}

// GetLastMessage 获取会话的最后一条消息
func (r *messageRepository) GetLastMessage(ctx context.Context, conversationID uint) (*model.Message, error) {
	var message model.Message
	err := r.db.WithContext(ctx).Where("conversation_id = ?", conversationID).
		Order("created_at DESC").
		First(&message).Error
	if err != nil {
		return nil, err
	}
	return &message, nil
}

// GetLastMessages 批量获取多个会话的最后一条消息
func (r *messageRepository) GetLastMessages(ctx context.Context, conversationIDs []uint) (map[uint]*model.Message, error) {
	if len(conversationIDs) == 0 {
		return make(map[uint]*model.Message), nil
	}

	var messages []*model.Message
	err := r.db.WithContext(ctx).Raw(`
		SELECT DISTINCT ON (conversation_id) *
		FROM messages 
		WHERE conversation_id IN ? 
		ORDER BY conversation_id, created_at DESC
	`, conversationIDs).Find(&messages).Error

	if err != nil {
		return nil, err
	}

	result := make(map[uint]*model.Message)
	for _, msg := range messages {
		result[msg.ConversationID] = msg
	}

	return result, nil
}

// UpdateStatus 更新消息状态
func (r *messageRepository) UpdateStatus(ctx context.Context, messageID uint, status model.ChatMessageStatus) error {
	updates := map[string]interface{}{
		"status": status,
	}

	switch status {
	case model.ChatMessageStatusDelivered:
		updates["delivered_at"] = time.Now()
	case model.ChatMessageStatusRead:
		updates["read_at"] = time.Now()
	}

	return r.db.WithContext(ctx).Model(&model.Message{}).Where("id = ?", messageID).Updates(updates).Error
}

// MarkAsDelivered 标记消息为已送达
func (r *messageRepository) MarkAsDelivered(ctx context.Context, messageID uint) error {
	return r.UpdateStatus(ctx, messageID, model.ChatMessageStatusDelivered)
}

// MarkAsRead 标记消息为已读
func (r *messageRepository) MarkAsRead(ctx context.Context, messageID uint) error {
	return r.UpdateStatus(ctx, messageID, model.ChatMessageStatusRead)
}

// RevokeMessage 撤回消息
func (r *messageRepository) RevokeMessage(ctx context.Context, messageID uint, revokedBy uint) error {
	now := time.Now()
	updates := map[string]interface{}{
		"is_revoked": true,
		"revoked_at": now,
		"status":     model.ChatMessageStatusRevoked,
	}
	return r.db.WithContext(ctx).Model(&model.Message{}).Where("id = ?", messageID).Updates(updates).Error
}

// CountUnreadMessages 统计未读消息数量
func (r *messageRepository) CountUnreadMessages(ctx context.Context, userID, conversationID uint, afterMessageID uint) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&model.Message{}).Where(
		"conversation_id = ? AND sender_id != ? AND is_revoked = false",
		conversationID, userID,
	)

	if afterMessageID > 0 {
		query = query.Where("id > ?", afterMessageID)
	}

	err := query.Count(&count).Error
	return count, err
}

// CountMessagesByConversation 统计会话的消息数量
func (r *messageRepository) CountMessagesByConversation(ctx context.Context, conversationID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.Message{}).Where("conversation_id = ?", conversationID).Count(&count).Error
	return count, err
}

// BatchCreate 批量创建消息
func (r *messageRepository) BatchCreate(ctx context.Context, messages []*model.Message) error {
	if len(messages) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).CreateInBatches(messages, 100).Error
}

// BatchUpdateStatus 批量更新消息状态
func (r *messageRepository) BatchUpdateStatus(ctx context.Context, messageIDs []uint, status model.ChatMessageStatus) error {
	if len(messageIDs) == 0 {
		return nil
	}

	updates := map[string]interface{}{
		"status": status,
	}

	switch status {
	case model.ChatMessageStatusDelivered:
		updates["delivered_at"] = time.Now()
	case model.ChatMessageStatusRead:
		updates["read_at"] = time.Now()
	}

	return r.db.WithContext(ctx).Model(&model.Message{}).Where("id IN ?", messageIDs).Updates(updates).Error
}

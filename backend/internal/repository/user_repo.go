package repository

import (
	"bdb-backend/internal/model"
	"time"

	"gorm.io/gorm"
)

type UserRepository interface {
	Create(user *model.User) error
	Find(id uint) (*model.User, error)
	FindByPhone(phone string) (*model.User, error)
	FindByOpenID(openID string) *model.User
	Update(user *model.User) error
	UpdateLastLoginAt(id uint) error
	Delete(id uint) error
	List(offset, limit int) ([]*model.User, error)
	Count() (int64, error)
}

type userRepository struct {
	db *gorm.DB
}

func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

func (r *userRepository) Create(user *model.User) error {
	return r.db.Create(user).Error
}

func (r *userRepository) Find(id uint) (*model.User, error) {
	var user model.User
	err := r.db.Where("id = ?", id).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *userRepository) FindByPhone(phone string) (*model.User, error) {
	var user model.User
	err := r.db.Where("phone = ?", phone).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *userRepository) FindByOpenID(openID string) *model.User {
	var user model.User
	err := r.db.Where("openid = ?", openID).First(&user).Error
	if err != nil {
		return nil // 用户不存在时返回nil
	}
	return &user
}

func (r *userRepository) Update(user *model.User) error {
	return r.db.Save(user).Error
}

func (r *userRepository) UpdateLastLoginAt(id uint) error {
	return r.db.Model(&model.User{}).Where("id = ?", id).Update("last_login_at", time.Now()).Error
}

func (r *userRepository) Delete(id uint) error {
	return r.db.Where("id = ?", id).Delete(&model.User{}).Error
}

func (r *userRepository) List(offset, limit int) ([]*model.User, error) {
	var users []*model.User
	err := r.db.Offset(offset).Limit(limit).Find(&users).Error
	return users, err
}

func (r *userRepository) Count() (int64, error) {
	var count int64
	err := r.db.Model(&model.User{}).Count(&count).Error
	return count, err
}

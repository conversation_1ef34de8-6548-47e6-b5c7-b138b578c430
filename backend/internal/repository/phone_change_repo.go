package repository

import (
	"bdb-backend/internal/model"
	"context"

	"gorm.io/gorm"
)

// PhoneChangeRecordRepository 手机号更换记录仓储接口
type PhoneChangeRecordRepository interface {
	Create(ctx context.Context, record *model.PhoneChangeRecord) error
	GetByID(ctx context.Context, id uint) (*model.PhoneChangeRecord, error)
	GetByUserID(ctx context.Context, userID uint, page, size int) ([]*model.PhoneChangeRecord, int64, error)
	GetRecentChangeByUserID(ctx context.Context, userID uint) (*model.PhoneChangeRecord, error)
	CountChangesByUserIDInDays(ctx context.Context, userID uint, days int) (int64, error)
}

// phoneChangeRecordRepository 手机号更换记录仓储实现
type phoneChangeRecordRepository struct {
	db *gorm.DB
}

// NewPhoneChangeRecordRepository 创建手机号更换记录仓储
func NewPhoneChangeRecordRepository(db *gorm.DB) PhoneChangeRecordRepository {
	return &phoneChangeRecordRepository{db: db}
}

func (r *phoneChangeRecordRepository) Create(ctx context.Context, record *model.PhoneChangeRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

func (r *phoneChangeRecordRepository) GetByID(ctx context.Context, id uint) (*model.PhoneChangeRecord, error) {
	var record model.PhoneChangeRecord
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

func (r *phoneChangeRecordRepository) GetByUserID(ctx context.Context, userID uint, page, size int) ([]*model.PhoneChangeRecord, int64, error) {
	var records []*model.PhoneChangeRecord
	var total int64

	offset := (page - 1) * size

	query := r.db.WithContext(ctx).Model(&model.PhoneChangeRecord{}).Where("user_id = ?", userID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Offset(offset).Limit(size).Order("created_at DESC").Find(&records).Error
	return records, total, err
}

func (r *phoneChangeRecordRepository) GetRecentChangeByUserID(ctx context.Context, userID uint) (*model.PhoneChangeRecord, error) {
	var record model.PhoneChangeRecord
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("created_at DESC").First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

func (r *phoneChangeRecordRepository) CountChangesByUserIDInDays(ctx context.Context, userID uint, days int) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.PhoneChangeRecord{}).
		Where("user_id = ? AND created_at >= NOW() - INTERVAL ? DAY", userID, days).
		Count(&count).Error
	return count, err
}

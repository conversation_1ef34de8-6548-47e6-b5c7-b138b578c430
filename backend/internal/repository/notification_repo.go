package repository

import (
	"context"
	"time"

	"bdb-backend/internal/model"

	"gorm.io/gorm"
)

// NotificationRepository 系统通知Repository接口
type NotificationRepository interface {
	// 基础CRUD
	Create(ctx context.Context, notification *model.SystemNotification) error
	GetByID(ctx context.Context, id uint) (*model.SystemNotification, error)
	Update(ctx context.Context, id uint, updates map[string]interface{}) error
	Delete(ctx context.Context, id uint) error

	// 查询方法
	ListByUser(ctx context.Context, userID uint, filter NotificationFilter) ([]*model.SystemNotification, int64, error)
	GetUnreadCount(ctx context.Context, userID uint) (int64, error)
	GetUnreadCountByType(ctx context.Context, userID uint, notificationType model.NotificationType) (int64, error)

	// 状态管理
	MarkAsRead(ctx context.Context, id uint, userID uint) error
	MarkAsDeleted(ctx context.Context, id uint, userID uint) error
	MarkAllAsRead(ctx context.Context, userID uint) error
	MarkAllAsReadByType(ctx context.Context, userID uint, notificationType model.NotificationType) error

	// 批量操作
	BatchCreate(ctx context.Context, notifications []*model.SystemNotification) error
	BatchMarkAsRead(ctx context.Context, ids []uint, userID uint) error
	BatchDelete(ctx context.Context, ids []uint, userID uint) error

	// 清理操作
	DeleteExpiredNotifications(ctx context.Context) (int64, error)
	DeleteOldNotifications(ctx context.Context, beforeDays int) (int64, error)
}

// NotificationFilter 通知查询过滤器
type NotificationFilter struct {
	Type    model.NotificationType
	Level   model.NotificationLevel
	Status  model.NotificationStatus
	IsRead  *bool // 兼容旧的查询方式
	Offset  int
	Limit   int
	OrderBy string // created_at, updated_at
	Order   string // ASC, DESC
}

// notificationRepository 系统通知Repository实现
type notificationRepository struct {
	db *gorm.DB
}

// NewNotificationRepository 创建系统通知Repository
func NewNotificationRepository(db *gorm.DB) NotificationRepository {
	return &notificationRepository{db: db}
}

// Create 创建通知
func (r *notificationRepository) Create(ctx context.Context, notification *model.SystemNotification) error {
	return r.db.WithContext(ctx).Create(notification).Error
}

// GetByID 根据ID获取通知
func (r *notificationRepository) GetByID(ctx context.Context, id uint) (*model.SystemNotification, error) {
	var notification model.SystemNotification
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&notification).Error
	if err != nil {
		return nil, err
	}
	return &notification, nil
}

// Update 更新通知
func (r *notificationRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&model.SystemNotification{}).Where("id = ?", id).Updates(updates).Error
}

// Delete 删除通知
func (r *notificationRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&model.SystemNotification{}).Error
}

// ListByUser 获取用户的通知列表
func (r *notificationRepository) ListByUser(ctx context.Context, userID uint, filter NotificationFilter) ([]*model.SystemNotification, int64, error) {
	query := r.db.WithContext(ctx).Model(&model.SystemNotification{}).Where("user_id = ?", userID)

	// 应用过滤条件
	if filter.Type != "" {
		query = query.Where("type = ?", filter.Type)
	}
	if filter.Level != "" {
		query = query.Where("level = ?", filter.Level)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.IsRead != nil {
		if *filter.IsRead {
			query = query.Where("status = ?", model.NotificationStatusRead)
		} else {
			query = query.Where("status = ?", model.NotificationStatusUnread)
		}
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 排序
	orderBy := "created_at"
	if filter.OrderBy != "" {
		orderBy = filter.OrderBy
	}
	order := "DESC"
	if filter.Order != "" {
		order = filter.Order
	}

	var notifications []*model.SystemNotification
	err := query.Order(orderBy + " " + order).
		Offset(filter.Offset).
		Limit(filter.Limit).
		Find(&notifications).Error

	return notifications, total, err
}

// GetUnreadCount 获取用户未读通知数量
func (r *notificationRepository) GetUnreadCount(ctx context.Context, userID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.SystemNotification{}).
		Where("user_id = ? AND status = ?", userID, model.NotificationStatusUnread).
		Count(&count).Error
	return count, err
}

// GetUnreadCountByType 根据类型获取用户未读通知数量
func (r *notificationRepository) GetUnreadCountByType(ctx context.Context, userID uint, notificationType model.NotificationType) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.SystemNotification{}).
		Where("user_id = ? AND type = ? AND status = ?", userID, notificationType, model.NotificationStatusUnread).
		Count(&count).Error
	return count, err
}

// MarkAsRead 标记通知为已读
func (r *notificationRepository) MarkAsRead(ctx context.Context, id uint, userID uint) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":     model.NotificationStatusRead,
		"read_at":    now,
		"updated_at": now,
	}
	return r.db.WithContext(ctx).Model(&model.SystemNotification{}).
		Where("id = ? AND user_id = ?", id, userID).
		Updates(updates).Error
}

// MarkAsDeleted 标记通知为已删除
func (r *notificationRepository) MarkAsDeleted(ctx context.Context, id uint, userID uint) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":     model.NotificationStatusDeleted,
		"deleted_at": now,
		"updated_at": now,
	}
	return r.db.WithContext(ctx).Model(&model.SystemNotification{}).
		Where("id = ? AND user_id = ?", id, userID).
		Updates(updates).Error
}

// MarkAllAsRead 标记用户所有通知为已读
func (r *notificationRepository) MarkAllAsRead(ctx context.Context, userID uint) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":     model.NotificationStatusRead,
		"read_at":    now,
		"updated_at": now,
	}
	return r.db.WithContext(ctx).Model(&model.SystemNotification{}).
		Where("user_id = ? AND status = ?", userID, model.NotificationStatusUnread).
		Updates(updates).Error
}

// MarkAllAsReadByType 根据类型标记用户所有通知为已读
func (r *notificationRepository) MarkAllAsReadByType(ctx context.Context, userID uint, notificationType model.NotificationType) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":     model.NotificationStatusRead,
		"read_at":    now,
		"updated_at": now,
	}
	return r.db.WithContext(ctx).Model(&model.SystemNotification{}).
		Where("user_id = ? AND type = ? AND status = ?", userID, notificationType, model.NotificationStatusUnread).
		Updates(updates).Error
}

// BatchCreate 批量创建通知
func (r *notificationRepository) BatchCreate(ctx context.Context, notifications []*model.SystemNotification) error {
	if len(notifications) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).CreateInBatches(notifications, 100).Error
}

// BatchMarkAsRead 批量标记通知为已读
func (r *notificationRepository) BatchMarkAsRead(ctx context.Context, ids []uint, userID uint) error {
	if len(ids) == 0 {
		return nil
	}

	now := time.Now()
	updates := map[string]interface{}{
		"status":     model.NotificationStatusRead,
		"read_at":    now,
		"updated_at": now,
	}
	return r.db.WithContext(ctx).Model(&model.SystemNotification{}).
		Where("id IN ? AND user_id = ?", ids, userID).
		Updates(updates).Error
}

// BatchDelete 批量删除通知
func (r *notificationRepository) BatchDelete(ctx context.Context, ids []uint, userID uint) error {
	if len(ids) == 0 {
		return nil
	}

	now := time.Now()
	updates := map[string]interface{}{
		"status":     model.NotificationStatusDeleted,
		"deleted_at": now,
		"updated_at": now,
	}
	return r.db.WithContext(ctx).Model(&model.SystemNotification{}).
		Where("id IN ? AND user_id = ?", ids, userID).
		Updates(updates).Error
}

// DeleteExpiredNotifications 删除过期通知
func (r *notificationRepository) DeleteExpiredNotifications(ctx context.Context) (int64, error) {
	now := time.Now()
	result := r.db.WithContext(ctx).Where("expires_at IS NOT NULL AND expires_at < ?", now).
		Delete(&model.SystemNotification{})
	return result.RowsAffected, result.Error
}

// DeleteOldNotifications 删除指定天数前的通知
func (r *notificationRepository) DeleteOldNotifications(ctx context.Context, beforeDays int) (int64, error) {
	cutoffTime := time.Now().AddDate(0, 0, -beforeDays)
	result := r.db.WithContext(ctx).Where("created_at < ?", cutoffTime).
		Delete(&model.SystemNotification{})
	return result.RowsAffected, result.Error
}

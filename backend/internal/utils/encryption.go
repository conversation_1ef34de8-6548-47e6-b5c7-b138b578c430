package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"strings"
)

// EncryptionService 加密服务
type EncryptionService struct {
	key []byte
}

// NewEncryptionService 创建新的加密服务实例
func NewEncryptionService(secretKey string) *EncryptionService {
	// 使用SHA256生成32字节的密钥，确保AES-256的安全性
	hash := sha256.Sum256([]byte(secretKey))
	return &EncryptionService{
		key: hash[:],
	}
}

// Encrypt 使用AES-256-GCM加密数据
func (e *EncryptionService) Encrypt(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}

	// 创建AES cipher
	block, err := aes.NewCipher(e.key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}

	// 创建GCM
	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}

	// 生成随机nonce
	nonce := make([]byte, aesGCM.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	// 加密数据
	ciphertext := aesGCM.Seal(nonce, nonce, []byte(plaintext), nil)

	// 返回base64编码的结果
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt 使用AES-256-GCM解密数据
func (e *EncryptionService) Decrypt(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}

	// Base64解码
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %w", err)
	}

	// 创建AES cipher
	block, err := aes.NewCipher(e.key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}

	// 创建GCM
	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}

	// 检查数据长度
	nonceSize := aesGCM.NonceSize()
	if len(data) < nonceSize {
		return "", errors.New("ciphertext too short")
	}

	// 分离nonce和密文
	nonce, cipherData := data[:nonceSize], data[nonceSize:]

	// 解密
	plaintext, err := aesGCM.Open(nil, nonce, cipherData, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt: %w", err)
	}

	return string(plaintext), nil
}

// Hash 生成数据的SHA256哈希值（用于查重和验证）
func (e *EncryptionService) Hash(data string) string {
	if data == "" {
		return ""
	}
	hash := sha256.Sum256([]byte(data))
	return fmt.Sprintf("%x", hash)
}

// MaskRealName 脱敏真实姓名
func (e *EncryptionService) MaskRealName(realName string) string {
	if realName == "" {
		return ""
	}

	runes := []rune(realName)
	if len(runes) <= 1 {
		return realName
	}

	if len(runes) == 2 {
		// 两个字符：李*
		return string(runes[0]) + "*"
	}

	// 三个或更多字符：李*明、欧阳*峰
	masked := string(runes[0])
	for i := 1; i < len(runes)-1; i++ {
		masked += "*"
	}
	if len(runes) > 2 {
		masked += string(runes[len(runes)-1])
	}

	return masked
}

// MaskIDNumber 脱敏身份证号码
func (e *EncryptionService) MaskIDNumber(idNumber string) string {
	if idNumber == "" {
		return ""
	}

	if len(idNumber) < 8 {
		return idNumber // 太短，不脱敏
	}

	// 保留前3位和后4位，中间用*替换
	// 例如：123456789012345678 -> 123**********5678
	if len(idNumber) >= 15 {
		return idNumber[:3] + strings.Repeat("*", len(idNumber)-7) + idNumber[len(idNumber)-4:]
	}

	// 较短的ID号码
	return idNumber[:2] + strings.Repeat("*", len(idNumber)-4) + idNumber[len(idNumber)-2:]
}

// MaskPhone 脱敏手机号
func (e *EncryptionService) MaskPhone(phone string) string {
	if phone == "" {
		return ""
	}

	if len(phone) != 11 {
		return phone // 不是标准手机号，不脱敏
	}

	// 保留前3位和后4位：138****5678
	return phone[:3] + "****" + phone[7:]
}

// MaskEmail 脱敏邮箱
func (e *EncryptionService) MaskEmail(email string) string {
	if email == "" {
		return ""
	}

	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return email // 不是有效邮箱，不脱敏
	}

	username := parts[0]
	domain := parts[1]

	if len(username) <= 2 {
		return email // 用户名太短，不脱敏
	}

	// 保留前2位和后1位：ab****<EMAIL>
	maskedUsername := username[:2] + strings.Repeat("*", len(username)-3) + username[len(username)-1:]
	return maskedUsername + "@" + domain
}

// VerificationDataEncryption 认证数据加密结构
type VerificationDataEncryption struct {
	service *EncryptionService
}

// NewVerificationDataEncryption 创建认证数据加密实例
func NewVerificationDataEncryption(secretKey string) *VerificationDataEncryption {
	return &VerificationDataEncryption{
		service: NewEncryptionService(secretKey),
	}
}

// EncryptUserVerificationData 加密用户认证数据
func (v *VerificationDataEncryption) EncryptUserVerificationData(realName, idNumber, idPhotos string) (map[string]string, error) {
	result := make(map[string]string)

	// 加密真实姓名
	if realName != "" {
		encrypted, err := v.service.Encrypt(realName)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt real name: %w", err)
		}
		result["encrypted_real_name"] = encrypted
		result["masked_real_name"] = v.service.MaskRealName(realName)
		result["real_name_hash"] = v.service.Hash(realName)
	}

	// 加密身份证号
	if idNumber != "" {
		encrypted, err := v.service.Encrypt(idNumber)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt id number: %w", err)
		}
		result["encrypted_id_number"] = encrypted
		result["masked_id_number"] = v.service.MaskIDNumber(idNumber)
		result["id_number_hash"] = v.service.Hash(idNumber)
	}

	// 加密身份证照片URL
	if idPhotos != "" {
		encrypted, err := v.service.Encrypt(idPhotos)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt id photos: %w", err)
		}
		result["encrypted_id_photos"] = encrypted
	}

	return result, nil
}

// EncryptEnterpriseVerificationData 加密企业认证数据
func (v *VerificationDataEncryption) EncryptEnterpriseVerificationData(creditCode, licenseURL, legalPersonName, legalPersonID, additionalDocs string) (map[string]string, error) {
	result := make(map[string]string)

	// 加密统一社会信用代码
	if creditCode != "" {
		encrypted, err := v.service.Encrypt(creditCode)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt credit code: %w", err)
		}
		result["encrypted_credit_code"] = encrypted
		result["credit_code_hash"] = v.service.Hash(creditCode)
	}

	// 加密营业执照URL
	if licenseURL != "" {
		encrypted, err := v.service.Encrypt(licenseURL)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt license url: %w", err)
		}
		result["encrypted_license_url"] = encrypted
	}

	// 加密法人姓名
	if legalPersonName != "" {
		encrypted, err := v.service.Encrypt(legalPersonName)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt legal person name: %w", err)
		}
		result["encrypted_legal_person_name"] = encrypted
		result["legal_person_name_hash"] = v.service.Hash(legalPersonName)
	}

	// 加密法人身份证号
	if legalPersonID != "" {
		encrypted, err := v.service.Encrypt(legalPersonID)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt legal person id: %w", err)
		}
		result["encrypted_legal_person_id_number"] = encrypted
		result["legal_person_id_hash"] = v.service.Hash(legalPersonID)
	}

	// 加密其他文档
	if additionalDocs != "" {
		encrypted, err := v.service.Encrypt(additionalDocs)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt additional docs: %w", err)
		}
		result["encrypted_additional_docs"] = encrypted
	}

	return result, nil
}

// DecryptUserVerificationData 解密用户认证数据
func (v *VerificationDataEncryption) DecryptUserVerificationData(encryptedRealName, encryptedIDNumber, encryptedIDPhotos string) (map[string]string, error) {
	result := make(map[string]string)

	// 解密真实姓名
	if encryptedRealName != "" {
		decrypted, err := v.service.Decrypt(encryptedRealName)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt real name: %w", err)
		}
		result["real_name"] = decrypted
	}

	// 解密身份证号
	if encryptedIDNumber != "" {
		decrypted, err := v.service.Decrypt(encryptedIDNumber)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt id number: %w", err)
		}
		result["id_number"] = decrypted
	}

	// 解密身份证照片URL
	if encryptedIDPhotos != "" {
		decrypted, err := v.service.Decrypt(encryptedIDPhotos)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt id photos: %w", err)
		}
		result["id_photos"] = decrypted
	}

	return result, nil
}

// DecryptEnterpriseVerificationData 解密企业认证数据
func (v *VerificationDataEncryption) DecryptEnterpriseVerificationData(encryptedCreditCode, encryptedLicenseURL, encryptedLegalPersonName, encryptedLegalPersonID, encryptedAdditionalDocs string) (map[string]string, error) {
	result := make(map[string]string)

	// 解密统一社会信用代码
	if encryptedCreditCode != "" {
		decrypted, err := v.service.Decrypt(encryptedCreditCode)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt credit code: %w", err)
		}
		result["credit_code"] = decrypted
	}

	// 解密营业执照URL
	if encryptedLicenseURL != "" {
		decrypted, err := v.service.Decrypt(encryptedLicenseURL)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt license url: %w", err)
		}
		result["license_url"] = decrypted
	}

	// 解密法人姓名
	if encryptedLegalPersonName != "" {
		decrypted, err := v.service.Decrypt(encryptedLegalPersonName)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt legal person name: %w", err)
		}
		result["legal_person_name"] = decrypted
	}

	// 解密法人身份证号
	if encryptedLegalPersonID != "" {
		decrypted, err := v.service.Decrypt(encryptedLegalPersonID)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt legal person id: %w", err)
		}
		result["legal_person_id"] = decrypted
	}

	// 解密其他文档
	if encryptedAdditionalDocs != "" {
		decrypted, err := v.service.Decrypt(encryptedAdditionalDocs)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt additional docs: %w", err)
		}
		result["additional_docs"] = decrypted
	}

	return result, nil
}

// ValidateIDNumber 验证身份证号码格式（简单验证）
func (v *VerificationDataEncryption) ValidateIDNumber(idNumber string) bool {
	if len(idNumber) != 18 {
		return false
	}

	// 检查前17位是否为数字
	for i := 0; i < 17; i++ {
		if idNumber[i] < '0' || idNumber[i] > '9' {
			return false
		}
	}

	// 最后一位可以是数字或X
	lastChar := idNumber[17]
	if (lastChar < '0' || lastChar > '9') && lastChar != 'X' && lastChar != 'x' {
		return false
	}

	return true
}

// ValidateCreditCode 验证统一社会信用代码格式（简单验证）
func (v *VerificationDataEncryption) ValidateCreditCode(creditCode string) bool {
	if len(creditCode) != 18 {
		return false
	}

	// 统一社会信用代码由数字和大写字母组成
	for _, char := range creditCode {
		if !((char >= '0' && char <= '9') || (char >= 'A' && char <= 'Z')) {
			return false
		}
	}

	return true
}

package utils

import (
	cryptoRand "crypto/rand"
	"fmt"
	"log"
	"math/big"
	"math/rand/v2"
	"regexp"
	"strings"
	"unicode"
)

// IsEmpty 判断字符串是否为空
func IsEmpty(str string) bool {
	return len(strings.TrimSpace(str)) == 0
}

// IsNotEmpty 判断字符串是否不为空
func IsNotEmpty(str string) bool {
	return !IsEmpty(str)
}

// TrimSpace 去除字符串首尾空格
func TrimSpace(str string) string {
	return strings.TrimSpace(str)
}

// ToLower 转换为小写
func ToLower(str string) string {
	return strings.ToLower(str)
}

// ToUpper 转换为大写
func ToUpper(str string) string {
	return strings.ToUpper(str)
}

// Contains 判断字符串是否包含子串
func Contains(str, substr string) bool {
	return strings.Contains(str, substr)
}

// StartsWith 判断字符串是否以指定前缀开始
func StartsWith(str, prefix string) bool {
	return strings.HasPrefix(str, prefix)
}

// EndsWith 判断字符串是否以指定后缀结束
func EndsWith(str, suffix string) bool {
	return strings.HasSuffix(str, suffix)
}

// ReplaceAll 替换所有匹配的子串
func ReplaceAll(str, old, new string) string {
	return strings.ReplaceAll(str, old, new)
}

// Split 分割字符串
func Split(str, sep string) []string {
	return strings.Split(str, sep)
}

// Join 连接字符串数组
func Join(strs []string, sep string) string {
	return strings.Join(strs, sep)
}

// IsValidEmail 验证邮箱格式
func IsValidEmail(email string) bool {
	pattern := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	reg := regexp.MustCompile(pattern)
	return reg.MatchString(email)
}

// IsValidPhone 验证手机号格式（中国大陆）
func IsValidPhone(phone string) bool {
	pattern := `^1[3-9]\d{9}$`
	reg := regexp.MustCompile(pattern)
	return reg.MatchString(phone)
}

// IsValidIDCard 验证身份证号格式（中国大陆）
func IsValidIDCard(idCard string) bool {
	pattern := `^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$`
	reg := regexp.MustCompile(pattern)
	return reg.MatchString(idCard)
}

// MaskEmail 邮箱脱敏
func MaskEmail(email string) string {
	if !IsValidEmail(email) {
		return email
	}
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return email
	}
	username := parts[0]
	domain := parts[1]

	if len(username) <= 2 {
		return email
	}

	masked := username[:1] + strings.Repeat("*", len(username)-2) + username[len(username)-1:]
	return masked + "@" + domain
}

// MaskPhone 手机号脱敏
func MaskPhone(phone string) string {
	if len(phone) != 11 {
		return phone
	}
	return phone[:3] + "****" + phone[7:]
}

// MaskIDCard 身份证号脱敏
func MaskIDCard(idCard string) string {
	if len(idCard) != 18 {
		return idCard
	}
	return idCard[:6] + "********" + idCard[14:]
}

// CamelToSnake 驼峰转蛇形
func CamelToSnake(str string) string {
	var result []rune
	for i, r := range str {
		if i > 0 && unicode.IsUpper(r) {
			result = append(result, '_')
		}
		result = append(result, unicode.ToLower(r))
	}
	return string(result)
}

// SnakeToCamel 蛇形转驼峰
func SnakeToCamel(str string) string {
	parts := strings.Split(str, "_")
	for i := 1; i < len(parts); i++ {
		if len(parts[i]) > 0 {
			parts[i] = strings.ToUpper(parts[i][:1]) + parts[i][1:]
		}
	}
	return strings.Join(parts, "")
}

// Truncate 截断字符串
func Truncate(str string, length int) string {
	if len(str) <= length {
		return str
	}
	return str[:length] + "..."
}

// RemoveEmptyStrings 移除字符串数组中的空字符串
func RemoveEmptyStrings(strs []string) []string {
	var result []string
	for _, str := range strs {
		if IsNotEmpty(str) {
			result = append(result, str)
		}
	}
	return result
}

// GenerateSMSCode 生成一个6位数字的短信验证码。
// 此函数是并发安全的，并优先使用密码学安全的随机数生成器。
func GenerateSMSCode() string {
	// 我们需要生成一个范围在 [100000, 999999] 内的数字。
	// 这个范围的大小是 900000。
	const codeRange = 900000
	const codeMin = 100000

	// 使用 crypto/rand.Int 来生成一个在 [0, codeRange-1] 范围内的、无偏差的随机数。
	// 这是生成安全令牌或验证码的首选方法。
	n, err := cryptoRand.Int(cryptoRand.Reader, big.NewInt(codeRange))
	if err != nil {
		// 这是一个罕见但严重的错误，通常表明操作系统的熵源存在问题。
		// 作为备用方案，我们使用非密码学安全的伪随机数生成器，以确保函数总是能返回一个值。
		log.Printf("警告: crypto/rand.Int 生成安全随机数失败: %v。降级到使用 math/rand/v2。", err)
		return fmt.Sprintf("%06d", rand.IntN(codeRange)+codeMin)
	}

	// 将生成的 *big.Int 转换为 int64，并加上范围的最小值，得到最终的验证码。
	code := n.Int64() + codeMin
	return fmt.Sprintf("%06d", code)
}

package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"math/big"
	"os"
	"strings"

	"golang.org/x/crypto/bcrypt"
)

// 🔐 敏感数据加密密钥（从环境变量获取）
var (
	encryptionKey []byte
)

// InitCrypto 初始化加密服务
func InitCrypto() error {
	key := os.Getenv("ENCRYPTION_KEY")
	if key == "" {
		// 开发环境使用默认密钥，生产环境必须设置
		key = "your-32-byte-encryption-key-change-in-production!!"
	}

	if len(key) != 32 {
		return fmt.Errorf("encryption key must be 32 bytes, got %d bytes", len(key))
	}

	encryptionKey = []byte(key)
	return nil
}

// CryptoService 加密服务接口
type CryptoService interface {
	// EncryptSensitiveData 加密敏感数据
	EncryptSensitiveData(data string) (string, error)
	// DecryptSensitiveData 解密敏感数据
	DecryptSensitiveData(encryptedData string) (string, error)
	// HashSensitiveData 生成敏感数据哈希（用于查询索引）
	HashSensitiveData(data string) string
	// GenerateIDCardMask 生成身份证掩码显示
	GenerateIDCardMask(idCard string) string
	// GenerateNameMask 生成姓名掩码显示
	GenerateNameMask(name string) string
}

// cryptoService 加密服务实现
type cryptoService struct{}

// NewCryptoService 创建加密服务实例
func NewCryptoService() CryptoService {
	return &cryptoService{}
}

// EncryptSensitiveData 使用AES-256-GCM加密敏感数据
func (c *cryptoService) EncryptSensitiveData(data string) (string, error) {
	if data == "" {
		return "", nil
	}

	if len(encryptionKey) == 0 {
		return "", fmt.Errorf("encryption key not initialized")
	}

	// 创建AES cipher
	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", fmt.Errorf("failed to create AES cipher: %w", err)
	}

	// 创建GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM mode: %w", err)
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, []byte(data), nil)

	// 返回base64编码的结果
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// DecryptSensitiveData 解密敏感数据
func (c *cryptoService) DecryptSensitiveData(encryptedData string) (string, error) {
	if encryptedData == "" {
		return "", nil
	}

	if len(encryptionKey) == 0 {
		return "", fmt.Errorf("encryption key not initialized")
	}

	// base64解码
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %w", err)
	}

	// 创建AES cipher
	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", fmt.Errorf("failed to create AES cipher: %w", err)
	}

	// 创建GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM mode: %w", err)
	}

	// 检查数据长度
	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	// 提取nonce和加密数据
	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt data: %w", err)
	}

	return string(plaintext), nil
}

// HashSensitiveData 生成敏感数据哈希（用于查询索引和查重）
func (c *cryptoService) HashSensitiveData(data string) string {
	if data == "" {
		return ""
	}

	// 使用SHA256生成哈希，加盐防止彩虹表攻击
	salt := "bdb_sensitive_data_salt_2024"
	h := sha256.New()
	h.Write([]byte(salt + data))
	return hex.EncodeToString(h.Sum(nil))
}

// GenerateIDCardMask 生成身份证掩码显示（前3位+****+后4位）
func (c *cryptoService) GenerateIDCardMask(idCard string) string {
	if len(idCard) != 18 {
		return "***************"
	}
	return idCard[:3] + "***********" + idCard[14:]
}

// GenerateNameMask 生成姓名掩码显示
func (c *cryptoService) GenerateNameMask(name string) string {
	if name == "" {
		return ""
	}
	runes := []rune(name)
	if len(runes) <= 1 {
		return name
	} else if len(runes) == 2 {
		return string(runes[0]) + "*"
	} else {
		return string(runes[0]) + "**" + string(runes[len(runes)-1])
	}
}

// HashPassword 使用bcrypt加密密码
func HashPassword(password string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hash), nil
}

// CheckPassword 验证密码
func CheckPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// GenerateRandomString 生成指定长度的随机字符串
func GenerateRandomString(length int) (string, error) {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", err
		}
		result[i] = charset[num.Int64()]
	}
	return string(result), nil
}

// GenerateRandomNumber 生成指定长度的随机数字字符串
func GenerateRandomNumber(length int) (string, error) {
	const charset = "0123456789"
	result := make([]byte, length)
	for i := range result {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", err
		}
		result[i] = charset[num.Int64()]
	}
	return string(result), nil
}

// MD5 生成MD5哈希
func MD5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// SHA256 生成SHA256哈希
func SHA256(str string) string {
	h := sha256.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// GenerateToken 生成访问令牌
func GenerateToken(prefix string, length int) (string, error) {
	randomStr, err := GenerateRandomString(length)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s_%s", prefix, strings.ToLower(randomStr)), nil
}

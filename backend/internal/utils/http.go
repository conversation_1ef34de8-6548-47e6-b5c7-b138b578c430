package utils

import (
	"net"
	"strings"

	"github.com/gin-gonic/gin"
)

// GetClientIP 获取客户端IP地址
func GetClientIP(c *gin.Context) string {
	// 首先检查 X-Forwarded-For 头
	xForwardedFor := c.<PERSON>eader("X-Forwarded-For")
	if xForwardedFor != "" {
		// X-Forwarded-For 可能包含多个IP，取第一个
		ips := strings.Split(xForwardedFor, ",")
		ip := strings.TrimSpace(ips[0])
		if ip != "" && !isPrivateIP(ip) {
			return ip
		}
	}

	// 检查 X-Real-IP 头
	xRealIP := c.GetHeader("X-Real-IP")
	if xRealIP != "" && !isPrivateIP(xRealIP) {
		return xRealIP
	}

	// 检查 X-Forwarded-Proto 头
	xForwardedProto := c.GetHeader("X-Forwarded-Proto")
	if xForwardedProto != "" {
		return c.ClientIP()
	}

	// 使用 Gin 的默认方法
	return c.ClientIP()
}

// isPrivateIP 判断是否为私有IP地址
func isPrivateIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// 检查是否为私有网络地址
	privateBlocks := []*net.IPNet{
		{IP: net.IPv4(10, 0, 0, 0), Mask: net.CIDRMask(8, 32)},     // 10.0.0.0/8
		{IP: net.IPv4(172, 16, 0, 0), Mask: net.CIDRMask(12, 32)},  // **********/12
		{IP: net.IPv4(192, 168, 0, 0), Mask: net.CIDRMask(16, 32)}, // ***********/16
		{IP: net.IPv4(127, 0, 0, 0), Mask: net.CIDRMask(8, 32)},    // *********/8
	}

	for _, block := range privateBlocks {
		if block.Contains(parsedIP) {
			return true
		}
	}

	return false
}

// GetUserAgent 获取用户代理字符串
func GetUserAgent(c *gin.Context) string {
	return c.GetHeader("User-Agent")
}

// GetReferer 获取来源页面
func GetReferer(c *gin.Context) string {
	return c.GetHeader("Referer")
}

// IsAjaxRequest 判断是否为Ajax请求
func IsAjaxRequest(c *gin.Context) bool {
	return c.GetHeader("X-Requested-With") == "XMLHttpRequest"
}

// IsJSONRequest 判断是否为JSON请求
func IsJSONRequest(c *gin.Context) bool {
	contentType := c.GetHeader("Content-Type")
	return strings.Contains(contentType, "application/json")
}

// GetRequestID 获取请求ID（如果存在）
func GetRequestID(c *gin.Context) string {
	return c.GetHeader("X-Request-ID")
}

// SetNoCacheHeaders 设置禁用缓存的响应头
func SetNoCacheHeaders(c *gin.Context) {
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")
}

// SetCORSHeaders 设置CORS响应头
func SetCORSHeaders(c *gin.Context) {
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
}

// GetQueryParam 获取查询参数，如果不存在则返回默认值
func GetQueryParam(c *gin.Context, key, defaultValue string) string {
	value := c.Query(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// GetPostParam 获取POST参数，如果不存在则返回默认值
func GetPostParam(c *gin.Context, key, defaultValue string) string {
	value := c.PostForm(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// IsHTTPS 判断是否为HTTPS请求
func IsHTTPS(c *gin.Context) bool {
	if c.Request.TLS != nil {
		return true
	}
	if proto := c.GetHeader("X-Forwarded-Proto"); proto == "https" {
		return true
	}
	return false
}

// GetHost 获取主机名
func GetHost(c *gin.Context) string {
	if host := c.GetHeader("X-Forwarded-Host"); host != "" {
		return host
	}
	return c.Request.Host
}

// GetScheme 获取协议方案
func GetScheme(c *gin.Context) string {
	if IsHTTPS(c) {
		return "https"
	}
	return "http"
}

// GetFullURL 获取完整的请求URL
func GetFullURL(c *gin.Context) string {
	scheme := GetScheme(c)
	host := GetHost(c)
	return scheme + "://" + host + c.Request.RequestURI
}

// IsMethodAllowed 检查HTTP方法是否被允许
func IsMethodAllowed(method string, allowedMethods []string) bool {
	for _, allowed := range allowedMethods {
		if method == allowed {
			return true
		}
	}
	return false
}

// SetJSONContentType 设置JSON内容类型
func SetJSONContentType(c *gin.Context) {
	c.Header("Content-Type", "application/json; charset=utf-8")
}

// SetXMLContentType 设置XML内容类型
func SetXMLContentType(c *gin.Context) {
	c.Header("Content-Type", "application/xml; charset=utf-8")
}

// SetTextContentType 设置文本内容类型
func SetTextContentType(c *gin.Context) {
	c.Header("Content-Type", "text/plain; charset=utf-8")
}

// SetHTMLContentType 设置HTML内容类型
func SetHTMLContentType(c *gin.Context) {
	c.Header("Content-Type", "text/html; charset=utf-8")
}

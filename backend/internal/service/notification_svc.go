package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"bdb-backend/internal/model"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/types"

	"gorm.io/gorm"
)

// NotificationService 系统通知服务接口
type NotificationService interface {
	// 基础通知发送
	SendSystemNotification(ctx context.Context, req *types.SendSystemNotificationRequest) (*types.SendSystemNotificationResponse, error)

	// 专业化通知发送方法
	SendAuditNotification(ctx context.Context, userID uint, data *model.AuditNotificationData) error
	SendActivityNotification(ctx context.Context, userIDs []uint, data *model.ActivityNotificationData) error
	SendSurveyNotification(ctx context.Context, userIDs []uint, data *model.SurveyNotificationData) error
	SendInteractionNotification(ctx context.Context, toUserID uint, data *model.InteractionNotificationData) error

	// 通知管理
	GetUserNotifications(ctx context.Context, userID uint, page, pageSize int, notificationType model.NotificationType) ([]*model.SystemNotification, int64, error)
	MarkAsRead(ctx context.Context, userID, notificationID uint) error
	MarkAllAsRead(ctx context.Context, userID uint) error
	DeleteNotification(ctx context.Context, userID, notificationID uint) error
	GetUnreadCount(ctx context.Context, userID uint) (int64, error)

	// 清理任务
	CleanExpiredNotifications(ctx context.Context) error
}

// notificationService 系统通知服务实现
type notificationService struct {
	notificationRepo    repository.NotificationRepository
	conversationService ConversationService
	messageRepo         repository.MessageRepository
	db                  *gorm.DB
}

// NewNotificationService 创建系统通知服务
func NewNotificationService(
	notificationRepo repository.NotificationRepository,
	conversationService ConversationService,
	messageRepo repository.MessageRepository,
	db *gorm.DB,
) NotificationService {
	return &notificationService{
		notificationRepo:    notificationRepo,
		conversationService: conversationService,
		messageRepo:         messageRepo,
		db:                  db,
	}
}

// SendSystemNotification 发送系统通知
func (s *notificationService) SendSystemNotification(ctx context.Context, req *types.SendSystemNotificationRequest) (*types.SendSystemNotificationResponse, error) {
	var successIDs []uint
	var failedCount int

	// 为每个用户发送通知
	for _, userID := range req.UserIDs {
		if err := s.sendNotificationToUser(ctx, userID, req); err != nil {
			failedCount++
			continue
		}

		// 创建系统会话中的消息
		messageID, err := s.createSystemMessage(ctx, userID, req)
		if err != nil {
			failedCount++
			continue
		}

		successIDs = append(successIDs, messageID)
	}

	return &types.SendSystemNotificationResponse{
		Success:     len(successIDs) > 0,
		MessageIDs:  successIDs,
		FailedCount: failedCount,
	}, nil
}

// sendNotificationToUser 给单个用户发送通知
func (s *notificationService) sendNotificationToUser(ctx context.Context, userID uint, req *types.SendSystemNotificationRequest) error {
	// 构建通知数据
	var extraData map[string]interface{}
	if req.Extra != nil {
		if err := json.Unmarshal([]byte(*req.Extra), &extraData); err != nil {
			extraData = nil
		}
	}

	notification := model.NewNotificationBuilder(userID).
		SetType(model.NotificationTypeSystem).
		SetTitle(req.Title).
		SetContent(req.Content).
		SetData(extraData).
		Build()

	return s.notificationRepo.Create(ctx, notification)
}

// createSystemMessage 在系统会话中创建消息
func (s *notificationService) createSystemMessage(ctx context.Context, userID uint, req *types.SendSystemNotificationRequest) (uint, error) {
	// 获取或创建系统通知会话
	conversation, err := s.conversationService.GetOrCreateSystemConversation(ctx, userID)
	if err != nil {
		return 0, fmt.Errorf("获取系统会话失败: %w", err)
	}

	// 生成消息ID
	msgID := fmt.Sprintf("sys_%d_%d", time.Now().UnixNano(), userID)

	// 创建消息
	message := &model.Message{
		MsgID:          msgID,
		ConversationID: conversation.ID,
		SenderID:       0, // 系统消息
		Content:        req.Content,
		MessageType:    model.ChatMessageTypeSystem,
		Status:         model.ChatMessageStatusSent,
		Extra:          req.Extra,
	}

	if err := s.messageRepo.Create(ctx, message); err != nil {
		return 0, fmt.Errorf("创建系统消息失败: %w", err)
	}

	return message.ID, nil
}

// SendAuditNotification 发送审核通知
func (s *notificationService) SendAuditNotification(ctx context.Context, userID uint, data *model.AuditNotificationData) error {
	var title, content string
	var level model.NotificationLevel

	switch data.AuditResult {
	case "approved":
		title = "审核通过"
		content = fmt.Sprintf("您的%s「%s」已审核通过", data.AuditType, data.ItemTitle)
		level = model.NotificationLevelSuccess
	case "rejected":
		title = "审核未通过"
		content = fmt.Sprintf("您的%s「%s」审核未通过，原因：%s", data.AuditType, data.ItemTitle, data.Reason)
		level = model.NotificationLevelWarning
	default:
		title = "审核通知"
		content = fmt.Sprintf("您的%s「%s」正在审核中", data.AuditType, data.ItemTitle)
		level = model.NotificationLevelInfo
	}

	notification := model.NewNotificationBuilder(userID).
		SetType(model.NotificationTypeAudit).
		SetLevel(level).
		SetTitle(title).
		SetContent(content).
		SetData(data).
		SetActionURL(fmt.Sprintf("/audit/detail/%d", data.ItemID)).
		Build()

	return s.notificationRepo.Create(ctx, notification)
}

// SendActivityNotification 发送活动通知
func (s *notificationService) SendActivityNotification(ctx context.Context, userIDs []uint, data *model.ActivityNotificationData) error {
	title := "新活动上线"
	content := fmt.Sprintf("精彩活动「%s」即将开始，快来参与吧！", data.ActivityType)
	if data.Reward != "" {
		content += fmt.Sprintf(" 丰厚奖励：%s", data.Reward)
	}

	notifications := make([]*model.SystemNotification, 0, len(userIDs))
	for _, userID := range userIDs {
		notification := model.NewNotificationBuilder(userID).
			SetType(model.NotificationTypeActivity).
			SetLevel(model.NotificationLevelInfo).
			SetTitle(title).
			SetContent(content).
			SetData(data).
			SetActionURL(fmt.Sprintf("/activity/%d", data.ActivityID)).
			SetActionText("立即参与").
			SetExpireAt(data.EndTime).
			Build()

		notifications = append(notifications, notification)
	}

	return s.notificationRepo.BatchCreate(ctx, notifications)
}

// SendSurveyNotification 发送调查问卷通知
func (s *notificationService) SendSurveyNotification(ctx context.Context, userIDs []uint, data *model.SurveyNotificationData) error {
	title := "问卷调查"
	content := fmt.Sprintf("邀请您参与「%s」问卷调查", data.SurveyTitle)
	if data.Reward != "" {
		content += fmt.Sprintf("，完成可获得：%s", data.Reward)
	}

	notifications := make([]*model.SystemNotification, 0, len(userIDs))
	for _, userID := range userIDs {
		notification := model.NewNotificationBuilder(userID).
			SetType(model.NotificationTypeSurvey).
			SetLevel(model.NotificationLevelInfo).
			SetTitle(title).
			SetContent(content).
			SetData(data).
			SetActionURL(fmt.Sprintf("/survey/%d", data.SurveyID)).
			SetActionText("开始答题").
			SetExpireAt(data.Deadline).
			Build()

		notifications = append(notifications, notification)
	}

	return s.notificationRepo.BatchCreate(ctx, notifications)
}

// SendInteractionNotification 发送互动通知
func (s *notificationService) SendInteractionNotification(ctx context.Context, toUserID uint, data *model.InteractionNotificationData) error {
	var title, content string
	var notificationType model.NotificationType

	switch data.TargetType {
	case "post":
		title = "帖子互动"
		content = fmt.Sprintf("%s 赞了您的帖子「%s」", data.FromUserName, data.TargetTitle)
		notificationType = model.NotificationTypeLike
	case "comment":
		title = "评论回复"
		content = fmt.Sprintf("%s 回复了您的评论", data.FromUserName)
		notificationType = model.NotificationTypeComment
	case "profile":
		title = "新关注"
		content = fmt.Sprintf("%s 关注了您", data.FromUserName)
		notificationType = model.NotificationTypeFollow
	default:
		title = "互动通知"
		content = fmt.Sprintf("%s 与您产生了互动", data.FromUserName)
		notificationType = model.NotificationTypeSystem
	}

	notification := model.NewNotificationBuilder(toUserID).
		SetType(notificationType).
		SetLevel(model.NotificationLevelInfo).
		SetTitle(title).
		SetContent(content).
		SetData(data).
		SetActionURL(fmt.Sprintf("/%s/%d", data.TargetType, data.TargetID)).
		Build()

	return s.notificationRepo.Create(ctx, notification)
}

// GetUserNotifications 获取用户通知列表
func (s *notificationService) GetUserNotifications(ctx context.Context, userID uint, page, pageSize int, notificationType model.NotificationType) ([]*model.SystemNotification, int64, error) {
	filter := repository.NotificationFilter{
		Type:    notificationType,
		Offset:  (page - 1) * pageSize,
		Limit:   pageSize,
		OrderBy: "created_at",
		Order:   "DESC",
	}

	return s.notificationRepo.ListByUser(ctx, userID, filter)
}

// MarkAsRead 标记通知为已读
func (s *notificationService) MarkAsRead(ctx context.Context, userID, notificationID uint) error {
	return s.notificationRepo.MarkAsRead(ctx, notificationID, userID)
}

// MarkAllAsRead 标记所有通知为已读
func (s *notificationService) MarkAllAsRead(ctx context.Context, userID uint) error {
	return s.notificationRepo.MarkAllAsRead(ctx, userID)
}

// DeleteNotification 删除通知
func (s *notificationService) DeleteNotification(ctx context.Context, userID, notificationID uint) error {
	return s.notificationRepo.MarkAsDeleted(ctx, notificationID, userID)
}

// GetUnreadCount 获取未读通知数量
func (s *notificationService) GetUnreadCount(ctx context.Context, userID uint) (int64, error) {
	return s.notificationRepo.GetUnreadCount(ctx, userID)
}

// CleanExpiredNotifications 清理过期通知
func (s *notificationService) CleanExpiredNotifications(ctx context.Context) error {
	_, err := s.notificationRepo.DeleteExpiredNotifications(ctx)
	return err
}

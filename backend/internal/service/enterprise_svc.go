package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"bdb-backend/internal/constants"
	"bdb-backend/internal/model"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/types"
	"bdb-backend/internal/utils"
	"bdb-backend/pkg/logger"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type EnterpriseService interface {
	// Enterprise CRUD operations
	CreateEnterprise(ctx context.Context, userID uint, req *types.CreateEnterpriseRequest) (*model.Enterprise, error)
	GetEnterpriseByID(ctx context.Context, id uint) (*types.EnterpriseInfo, error)
	UpdateEnterprise(ctx context.Context, userID uint, req *types.UpdateEnterpriseRequest) error
	DeleteEnterprise(ctx context.Context, userID uint) error
	GetMyEnterprise(ctx context.Context, userID uint) (*types.EnterpriseInfo, error)
	GetEnterpriseList(ctx context.Context, page, pageSize int) ([]*types.EnterpriseInfo, int64, error)

	// Enterprise verification
	SubmitVerification(ctx context.Context, userID uint, req *types.EnterpriseVerificationRequest) error
	GetVerificationStatus(ctx context.Context, userID uint) (*model.EnterpriseVerification, error)
	GetPendingVerifications(ctx context.Context, page, pageSize int) ([]*model.EnterpriseVerification, int64, error)
	ApproveVerification(ctx context.Context, verificationID uint, reviewNote string) error
	RejectVerification(ctx context.Context, verificationID uint, reviewNote string) error

	// Membership and subscription management
	GetMembershipPlans(ctx context.Context) ([]*types.MembershipPlanResponse, error)
	CreateSubscription(ctx context.Context, userID uint, req *types.CreateSubscriptionRequest) (*types.SubscriptionResponse, error)
	GetActiveSubscription(ctx context.Context, userID uint) (*types.SubscriptionResponse, error)
	CancelSubscription(ctx context.Context, userID uint) error
	ProcessExpiredSubscriptions(ctx context.Context) error
	CheckMembershipBenefits(ctx context.Context, userID uint, benefitType string) (bool, error)

	// Enterprise analytics
	GetEnterpriseStats(ctx context.Context, userID uint) (*types.StatisticsResponse, error)
	GetEnterpriseProfile(ctx context.Context, userID uint) (*types.EnterpriseInfo, error)

	// Search and discovery
	SearchEnterprises(ctx context.Context, keywords string, page, pageSize int) ([]*types.EnterpriseInfo, int64, error)
	GetVerifiedEnterprises(ctx context.Context, page, pageSize int) ([]*types.EnterpriseInfo, int64, error)

	// Admin operations
	GetEnterpriseAnalytics(ctx context.Context, enterpriseID uint) (*types.StatisticsResponse, error)
}

type enterpriseService struct {
	enterpriseRepo repository.EnterpriseRepository
	userRepo       repository.UserRepository
	encryption     *utils.VerificationDataEncryption
}

func NewEnterpriseService(
	enterpriseRepo repository.EnterpriseRepository,
	userRepo repository.UserRepository,
) EnterpriseService {
	// Initialize encryption service for verification data
	encryption := utils.NewVerificationDataEncryption("your-encryption-key") // TODO: Use config

	return &enterpriseService{
		enterpriseRepo: enterpriseRepo,
		userRepo:       userRepo,
		encryption:     encryption,
	}
}

// CreateEnterprise creates a new enterprise profile
func (s *enterpriseService) CreateEnterprise(ctx context.Context, userID uint, req *types.CreateEnterpriseRequest) (*model.Enterprise, error) {
	// Check if user already has an enterprise
	existing, err := s.enterpriseRepo.GetByUserID(ctx, userID)
	if err == nil && existing != nil {
		return nil, errors.New("用户已有企业账户")
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("检查企业账户失败: %w", err)
	}

	// Validate request
	if err := s.validateEnterpriseRequest(req); err != nil {
		return nil, err
	}

	// Create enterprise
	enterprise := &model.Enterprise{
		UserID:             userID,
		Name:               req.Name,
		Description:        req.Description,
		LogoURL:            req.LogoURL,
		Type:               req.Type,
		Industry:           req.Industry,
		CompanySize:        req.CompanySize,
		ContactPerson:      req.ContactPerson,
		ContactPhone:       req.ContactPhone,
		Address:            req.Address,
		Latitude:           req.Latitude,
		Longitude:          req.Longitude,
		WelfareTags:        convertStringSliceToJSON(req.WelfareTags),
		IsVerified:         false,
		VerificationStatus: constants.VerificationStatusPending,
	}

	if err := s.enterpriseRepo.Create(ctx, enterprise); err != nil {
		return nil, fmt.Errorf("创建企业失败: %w", err)
	}

	return enterprise, nil
}

// GetEnterpriseByID retrieves enterprise information by ID
func (s *enterpriseService) GetEnterpriseByID(ctx context.Context, id uint) (*types.EnterpriseInfo, error) {
	enterprise, err := s.enterpriseRepo.Find(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("企业不存在")
		}
		return nil, fmt.Errorf("获取企业信息失败: %w", err)
	}

	return s.convertEnterpriseToInfo(enterprise), nil
}

// UpdateEnterprise updates enterprise information
func (s *enterpriseService) UpdateEnterprise(ctx context.Context, userID uint, req *types.UpdateEnterpriseRequest) error {
	// Get user's enterprise
	enterprise, err := s.enterpriseRepo.GetByUserID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("企业不存在")
		}
		return fmt.Errorf("获取企业信息失败: %w", err)
	}

	// Apply updates
	s.applyEnterpriseUpdates(enterprise, req)

	// Validate updated data
	if err := s.validateUpdatedEnterprise(enterprise); err != nil {
		return err
	}

	// Update in database
	if err := s.enterpriseRepo.Update(ctx, enterprise); err != nil {
		return fmt.Errorf("更新企业信息失败: %w", err)
	}

	return nil
}

// DeleteEnterprise deletes (soft delete) an enterprise
func (s *enterpriseService) DeleteEnterprise(ctx context.Context, userID uint) error {
	enterprise, err := s.enterpriseRepo.GetByUserID(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取企业信息失败: %w", err)
	}

	// Check if enterprise has active jobs
	// TODO: Add validation for active jobs/applications

	return s.enterpriseRepo.Delete(ctx, enterprise.ID)
}

// GetMyEnterprise retrieves current user's enterprise information
func (s *enterpriseService) GetMyEnterprise(ctx context.Context, userID uint) (*types.EnterpriseInfo, error) {
	enterprise, err := s.enterpriseRepo.GetByUserID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("企业不存在")
		}
		return nil, fmt.Errorf("获取企业信息失败: %w", err)
	}

	return s.convertEnterpriseToInfo(enterprise), nil
}

// GetEnterpriseList retrieves a paginated list of enterprises
func (s *enterpriseService) GetEnterpriseList(ctx context.Context, page, pageSize int) ([]*types.EnterpriseInfo, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	enterprises, total, err := s.enterpriseRepo.GetList(ctx, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("获取企业列表失败: %w", err)
	}

	// Convert to response format
	result := make([]*types.EnterpriseInfo, 0, len(enterprises))
	for _, enterprise := range enterprises {
		result = append(result, s.convertEnterpriseToInfo(enterprise))
	}

	return result, total, nil
}

// SubmitVerification submits enterprise verification documents
func (s *enterpriseService) SubmitVerification(ctx context.Context, userID uint, req *types.EnterpriseVerificationRequest) error {
	// Get user's enterprise
	enterprise, err := s.enterpriseRepo.GetByUserID(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取企业信息失败: %w", err)
	}

	// Check if verification is already approved
	if enterprise.IsVerified {
		return errors.New("企业已认证")
	}

	// Validate verification data
	if err := s.validateVerificationRequest(req); err != nil {
		return err
	}

	// Encrypt sensitive verification data
	encryptedData, err := s.encryptVerificationData(req)
	if err != nil {
		return fmt.Errorf("加密认证数据失败: %w", err)
	}

	// Check if verification record exists
	existing, err := s.enterpriseRepo.GetVerificationByEnterpriseID(ctx, enterprise.ID)
	if err == nil {
		// Update existing verification
		existing.VerificationType = req.VerificationType
		existing.Status = constants.VerificationStatusPending
		existing.EncryptedCreditCode = encryptedData["encrypted_credit_code"]
		existing.EncryptedLicenseURL = encryptedData["encrypted_license_url"]
		existing.EncryptedLegalPersonName = encryptedData["encrypted_legal_person_name"]
		existing.EncryptedLegalPersonIDNumber = encryptedData["encrypted_legal_person_id_number"]
		existing.EncryptedAdditionalDocs = encryptedData["encrypted_additional_docs"]
		existing.RejectReason = ""

		err = s.enterpriseRepo.UpdateVerification(ctx, existing)
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		// Create new verification
		verification := &model.EnterpriseVerification{
			EnterpriseID:                 enterprise.ID,
			VerificationType:             req.VerificationType,
			Status:                       constants.VerificationStatusPending,
			EncryptedCreditCode:          encryptedData["encrypted_credit_code"],
			EncryptedLicenseURL:          encryptedData["encrypted_license_url"],
			EncryptedLegalPersonName:     encryptedData["encrypted_legal_person_name"],
			EncryptedLegalPersonIDNumber: encryptedData["encrypted_legal_person_id_number"],
			EncryptedAdditionalDocs:      encryptedData["encrypted_additional_docs"],
		}

		err = s.enterpriseRepo.CreateVerification(ctx, verification)
	}

	if err != nil {
		return fmt.Errorf("提交认证失败: %w", err)
	}

	return nil
}

// GetVerificationStatus retrieves current verification status
func (s *enterpriseService) GetVerificationStatus(ctx context.Context, userID uint) (*model.EnterpriseVerification, error) {
	enterprise, err := s.enterpriseRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取企业信息失败: %w", err)
	}

	verification, err := s.enterpriseRepo.GetVerificationByEnterpriseID(ctx, enterprise.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("暂无认证记录")
		}
		return nil, fmt.Errorf("获取认证状态失败: %w", err)
	}

	return verification, nil
}

// GetPendingVerifications retrieves pending verifications for admin review
func (s *enterpriseService) GetPendingVerifications(ctx context.Context, page, pageSize int) ([]*model.EnterpriseVerification, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	return s.enterpriseRepo.GetPendingVerifications(ctx, page, pageSize)
}

// ApproveVerification approves an enterprise verification
func (s *enterpriseService) ApproveVerification(ctx context.Context, verificationID uint, reviewNote string) error {
	return s.enterpriseRepo.ApproveVerification(ctx, verificationID, reviewNote)
}

// RejectVerification rejects an enterprise verification
func (s *enterpriseService) RejectVerification(ctx context.Context, verificationID uint, reviewNote string) error {
	return s.enterpriseRepo.RejectVerification(ctx, verificationID, reviewNote)
}

// GetMembershipPlans retrieves available membership plans
func (s *enterpriseService) GetMembershipPlans(ctx context.Context) ([]*types.MembershipPlanResponse, error) {
	plans, err := s.enterpriseRepo.GetMembershipPlans(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取会员套餐失败: %w", err)
	}

	result := make([]*types.MembershipPlanResponse, 0, len(plans))
	for _, plan := range plans {
		result = append(result, s.convertPlanToResponse(plan))
	}

	return result, nil
}

// CreateSubscription creates a new membership subscription
func (s *enterpriseService) CreateSubscription(ctx context.Context, userID uint, req *types.CreateSubscriptionRequest) (*types.SubscriptionResponse, error) {
	// Get user's enterprise
	enterprise, err := s.enterpriseRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取企业信息失败: %w", err)
	}

	// Get membership plan
	plan, err := s.enterpriseRepo.GetMembershipPlanByID(ctx, req.PlanID)
	if err != nil {
		return nil, fmt.Errorf("获取会员套餐失败: %w", err)
	}

	if !plan.IsActive {
		return nil, errors.New("该套餐已停用")
	}

	// Check if there's an active subscription
	activeSubscription, err := s.enterpriseRepo.GetActiveSubscriptionByEnterpriseID(ctx, enterprise.ID)
	if err == nil && activeSubscription != nil {
		return nil, errors.New("已有有效会员，无法重复购买")
	}

	// Create subscription
	startDate := time.Now()
	endDate := startDate.AddDate(0, 0, plan.DurationDays)

	subscription := &model.Subscription{
		EnterpriseID:        enterprise.ID,
		PlanID:              &req.PlanID,
		PlanDetailsSnapshot: plan.Benefits,
		StartDate:           startDate,
		EndDate:             endDate,
		Status:              constants.SubscriptionStatusActive,
	}

	if err := s.enterpriseRepo.CreateSubscription(ctx, subscription); err != nil {
		return nil, fmt.Errorf("创建订阅失败: %w", err)
	}

	// TODO: Integrate with payment gateway

	return s.convertSubscriptionToResponse(subscription, plan), nil
}

// GetActiveSubscription retrieves current active subscription
func (s *enterpriseService) GetActiveSubscription(ctx context.Context, userID uint) (*types.SubscriptionResponse, error) {
	enterprise, err := s.enterpriseRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取企业信息失败: %w", err)
	}

	subscription, err := s.enterpriseRepo.GetActiveSubscriptionByEnterpriseID(ctx, enterprise.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("暂无有效会员")
		}
		return nil, fmt.Errorf("获取会员信息失败: %w", err)
	}

	// Get plan details
	plan, err := s.enterpriseRepo.GetMembershipPlanByID(ctx, *subscription.PlanID)
	if err != nil {
		logger.Error("Failed to get plan details", err, "plan_id", subscription.PlanID)
		plan = nil
	}

	return s.convertSubscriptionToResponse(subscription, plan), nil
}

// GetEnterpriseStats retrieves enterprise statistics
func (s *enterpriseService) GetEnterpriseStats(ctx context.Context, userID uint) (*types.StatisticsResponse, error) {
	enterprise, err := s.enterpriseRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取企业信息失败: %w", err)
	}

	stats, err := s.enterpriseRepo.GetEnterpriseStats(ctx, enterprise.ID)
	if err != nil {
		return nil, fmt.Errorf("获取统计信息失败: %w", err)
	}

	return &types.StatisticsResponse{
		TotalJobs:           int(stats.TotalJobs),
		ActiveJobs:          int(stats.ActiveJobs),
		TotalApplications:   int(stats.TotalApplications),
		PendingApplications: int(stats.PendingApplications),
		TotalViews:          int(stats.TotalViews),
	}, nil
}

// SearchEnterprises searches enterprises by keywords
func (s *enterpriseService) SearchEnterprises(ctx context.Context, keywords string, page, pageSize int) ([]*types.EnterpriseInfo, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	enterprises, total, err := s.enterpriseRepo.SearchEnterprises(ctx, keywords, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("搜索企业失败: %w", err)
	}

	result := make([]*types.EnterpriseInfo, 0, len(enterprises))
	for _, enterprise := range enterprises {
		result = append(result, s.convertEnterpriseToInfo(enterprise))
	}

	return result, total, nil
}

// GetVerifiedEnterprises retrieves verified enterprises
func (s *enterpriseService) GetVerifiedEnterprises(ctx context.Context, page, pageSize int) ([]*types.EnterpriseInfo, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	enterprises, total, err := s.enterpriseRepo.GetVerifiedEnterprises(ctx, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("获取认证企业失败: %w", err)
	}

	result := make([]*types.EnterpriseInfo, 0, len(enterprises))
	for _, enterprise := range enterprises {
		result = append(result, s.convertEnterpriseToInfo(enterprise))
	}

	return result, total, nil
}

// ProcessExpiredSubscriptions processes expired subscriptions
func (s *enterpriseService) ProcessExpiredSubscriptions(ctx context.Context) error {
	logger.Info("Processing expired subscriptions...")

	expiredSubscriptions, err := s.enterpriseRepo.GetExpiredSubscriptions(ctx)
	if err != nil {
		logger.Error("Failed to get expired subscriptions", err)
		return err
	}

	for _, subscription := range expiredSubscriptions {
		subscription.Status = constants.SubscriptionStatusExpired
		if err := s.enterpriseRepo.UpdateSubscription(ctx, subscription); err != nil {
			logger.Error("Failed to update expired subscription", err, "subscription_id", subscription.ID)
		}
	}

	logger.Info("Expired subscriptions processed", "count", len(expiredSubscriptions))
	return nil
}

// CheckMembershipBenefits checks if user has access to specific membership benefits
func (s *enterpriseService) CheckMembershipBenefits(ctx context.Context, userID uint, benefitType string) (bool, error) {
	subscription, err := s.GetActiveSubscription(ctx, userID)
	if err != nil {
		return false, nil // No active subscription means no benefits
	}

	// TODO: Parse subscription benefits and check specific benefit type
	// This would involve parsing the PlanDetailsSnapshot JSON

	return subscription.IsActive, nil
}

// Helper methods

// convertStringSliceToJSON converts []string to datatypes.JSON
func convertStringSliceToJSON(stringSlice []string) datatypes.JSON {
	if len(stringSlice) == 0 {
		return nil
	}

	jsonData, err := json.Marshal(stringSlice)
	if err != nil {
		// If marshal fails, return nil
		return nil
	}

	return jsonData
}

// convertJSONToStringSliceEnt converts datatypes.JSON to []string (enterprise version)
func convertJSONToStringSliceEnt(jsonData datatypes.JSON) []string {
	if jsonData == nil {
		return []string{}
	}

	var stringSlice []string
	if err := json.Unmarshal(jsonData, &stringSlice); err != nil {
		// If unmarshal fails, return empty slice
		return []string{}
	}

	return stringSlice
}

// convertJSONToMap converts datatypes.JSON to map[string]interface{}
func convertJSONToMap(jsonData datatypes.JSON) map[string]interface{} {
	if jsonData == nil {
		return map[string]interface{}{}
	}

	var result map[string]interface{}
	if err := json.Unmarshal(jsonData, &result); err != nil {
		// If unmarshal fails, return empty map
		return map[string]interface{}{}
	}

	return result
}

// validateEnterpriseRequest validates enterprise creation request
func (s *enterpriseService) validateEnterpriseRequest(req *types.CreateEnterpriseRequest) error {
	if len(req.Name) < 2 || len(req.Name) > constants.MaxCompanyNameLength {
		return errors.New("企业名称长度应在2-100字符之间")
	}

	if req.Type != constants.EnterpriseTypeEnterprise &&
		req.Type != constants.EnterpriseTypeSmallBusiness &&
		req.Type != constants.EnterpriseTypeIndividual {
		return errors.New("无效的企业类型")
	}

	if len(req.ContactPerson) < 1 || len(req.ContactPerson) > constants.MaxContactPersonLength {
		return errors.New("联系人姓名长度应在1-50字符之间")
	}

	if len(req.ContactPhone) < 1 {
		return errors.New("联系电话不能为空")
	}

	return nil
}

// validateVerificationRequest validates verification submission
func (s *enterpriseService) validateVerificationRequest(req *types.EnterpriseVerificationRequest) error {
	if req.VerificationType == constants.EnterpriseTypeEnterprise {
		if req.CreditCode == "" {
			return errors.New("企业认证需要提供统一社会信用代码")
		}
		if !s.encryption.ValidateCreditCode(req.CreditCode) {
			return errors.New("统一社会信用代码格式错误")
		}
		if req.LegalPersonName == "" {
			return errors.New("企业认证需要提供法人姓名")
		}
		if req.LegalPersonIDNumber == "" {
			return errors.New("企业认证需要提供法人身份证号")
		}
		if !s.encryption.ValidateIDNumber(req.LegalPersonIDNumber) {
			return errors.New("法人身份证号格式错误")
		}
	}

	if req.LicenseURL == "" {
		return errors.New("营业执照图片不能为空")
	}

	return nil
}

// encryptVerificationData encrypts sensitive verification data
func (s *enterpriseService) encryptVerificationData(req *types.EnterpriseVerificationRequest) (map[string]string, error) {
	return s.encryption.EncryptEnterpriseVerificationData(
		req.CreditCode,
		req.LicenseURL,
		req.LegalPersonName,
		req.LegalPersonIDNumber,
		req.AdditionalDocs,
	)
}

// applyEnterpriseUpdates applies updates to enterprise model
func (s *enterpriseService) applyEnterpriseUpdates(enterprise *model.Enterprise, req *types.UpdateEnterpriseRequest) {
	if req.Name != nil {
		enterprise.Name = *req.Name
	}
	if req.Description != nil {
		enterprise.Description = *req.Description
	}
	if req.LogoURL != nil {
		enterprise.LogoURL = *req.LogoURL
	}
	if req.Industry != nil {
		enterprise.Industry = *req.Industry
	}
	if req.CompanySize != nil {
		enterprise.CompanySize = *req.CompanySize
	}
	if req.ContactPerson != nil {
		enterprise.ContactPerson = *req.ContactPerson
	}
	if req.ContactPhone != nil {
		enterprise.ContactPhone = *req.ContactPhone
	}
	if req.Address != nil {
		enterprise.Address = *req.Address
	}
	if req.Latitude != nil {
		enterprise.Latitude = *req.Latitude
	}
	if req.Longitude != nil {
		enterprise.Longitude = *req.Longitude
	}
	if req.WelfareTags != nil {
		enterprise.WelfareTags = convertStringSliceToJSON(req.WelfareTags)
	}
}

// validateUpdatedEnterprise validates enterprise after updates
func (s *enterpriseService) validateUpdatedEnterprise(enterprise *model.Enterprise) error {
	if len(enterprise.Name) < 2 || len(enterprise.Name) > constants.MaxCompanyNameLength {
		return errors.New("企业名称长度应在2-100字符之间")
	}

	if len(enterprise.ContactPerson) < 1 || len(enterprise.ContactPerson) > constants.MaxContactPersonLength {
		return errors.New("联系人姓名长度应在1-50字符之间")
	}

	return nil
}

// convertEnterpriseToInfo converts enterprise model to info response
func (s *enterpriseService) convertEnterpriseToInfo(enterprise *model.Enterprise) *types.EnterpriseInfo {
	return &types.EnterpriseInfo{
		ID:                 enterprise.ID,
		UserID:             enterprise.UserID,
		Name:               enterprise.Name,
		Description:        enterprise.Description,
		LogoURL:            enterprise.LogoURL,
		Type:               enterprise.Type,
		Industry:           enterprise.Industry,
		CompanySize:        enterprise.CompanySize,
		ContactPerson:      enterprise.ContactPerson,
		ContactPhone:       enterprise.ContactPhone,
		Address:            enterprise.Address,
		Latitude:           enterprise.Latitude,
		Longitude:          enterprise.Longitude,
		WelfareTags:        convertJSONToStringSliceEnt(enterprise.WelfareTags),
		IsVerified:         enterprise.IsVerified,
		VerificationStatus: enterprise.VerificationStatus,
		ActiveJobCount:     enterprise.ActiveJobCount,
		TotalViewCount:     enterprise.TotalViewCount,
		CreatedAt:          enterprise.CreatedAt,
		UpdatedAt:          enterprise.UpdatedAt,
	}
}

// convertPlanToResponse converts membership plan to response
func (s *enterpriseService) convertPlanToResponse(plan *model.MembershipPlan) *types.MembershipPlanResponse {
	return &types.MembershipPlanResponse{
		ID:           plan.ID,
		Key:          plan.Key,
		Name:         plan.Name,
		Description:  plan.Description,
		Price:        plan.Price,
		DurationDays: plan.DurationDays,
		Benefits:     convertJSONToMap(plan.Benefits),
		IsActive:     plan.IsActive,
		SortOrder:    plan.SortOrder,
		CreatedAt:    plan.CreatedAt,
		UpdatedAt:    plan.UpdatedAt,
	}
}

// convertSubscriptionToResponse converts subscription to response
func (s *enterpriseService) convertSubscriptionToResponse(subscription *model.Subscription, plan *model.MembershipPlan) *types.SubscriptionResponse {
	response := &types.SubscriptionResponse{
		ID:                  subscription.ID,
		EnterpriseID:        subscription.EnterpriseID,
		PlanID:              *subscription.PlanID,
		PlanDetailsSnapshot: convertJSONToMap(subscription.PlanDetailsSnapshot),
		StartDate:           subscription.StartDate,
		EndDate:             subscription.EndDate,
		Status:              subscription.Status,
		IsActive:            subscription.Status == constants.SubscriptionStatusActive && subscription.EndDate.After(time.Now()),
		DaysLeft:            int(time.Until(subscription.EndDate).Hours() / 24),
		CreatedAt:           subscription.CreatedAt,
		UpdatedAt:           subscription.UpdatedAt,
	}

	if plan != nil {
		response.Plan = s.convertPlanToResponse(plan)
	}

	return response
}

// Placeholder implementations for remaining interface methods

func (s *enterpriseService) GetEnterpriseProfile(ctx context.Context, userID uint) (*types.EnterpriseInfo, error) {
	return s.GetMyEnterprise(ctx, userID)
}

func (s *enterpriseService) CancelSubscription(ctx context.Context, userID uint) error {
	// TODO: Implement subscription cancellation logic
	return errors.New("功能暂未实现")
}

func (s *enterpriseService) GetEnterpriseAnalytics(ctx context.Context, enterpriseID uint) (*types.StatisticsResponse, error) {
	stats, err := s.enterpriseRepo.GetEnterpriseStats(ctx, enterpriseID)
	if err != nil {
		return nil, fmt.Errorf("获取企业分析数据失败: %w", err)
	}

	return &types.StatisticsResponse{
		TotalJobs:           int(stats.TotalJobs),
		ActiveJobs:          int(stats.ActiveJobs),
		TotalApplications:   int(stats.TotalApplications),
		PendingApplications: int(stats.PendingApplications),
		TotalViews:          int(stats.TotalViews),
	}, nil
}

package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"bdb-backend/internal/model"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/types"
	"bdb-backend/internal/utils"
	"bdb-backend/pkg/cache"
	"bdb-backend/pkg/config"
	auth "bdb-backend/pkg/jwt"
	"bdb-backend/pkg/wechat"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// AuthService 认证服务接口
type AuthService interface {
	// Login 用户登录
	Login(ctx context.Context, req *types.LoginRequest) (*types.LoginResponse, error)
	// WechatLogin 微信快捷登录
	WechatLogin(ctx context.Context, req *types.WechatLoginRequest) (*types.LoginResponse, error)
	// SendSmsCode 发送短信验证码
	SendSmsCode(ctx context.Context, req *types.SendSmsCodeRequest) error
	// CheckUserByCode 根据微信code检查用户是否注册
	CheckUserByCode(ctx context.Context, req *types.CheckUserByCodeRequest) (*types.CheckUserByCodeResponse, error)
	// DevLogin 开发测试登录
	DevLogin(ctx context.Context, req *types.DevLoginRequest) (*types.DevLoginResponse, error)
	// RefreshToken 刷新访问令牌
	RefreshToken(ctx context.Context, req *types.RefreshTokenRequest) (*types.RefreshTokenResponse, error)
}

type authService struct {
	userRepo      repository.UserRepository
	jwtService    auth.JWTService
	wechatService wechat.AuthService
	cacheService  cache.Cache
	config        *config.Config
}

// NewAuthService 创建认证服务
func NewAuthService(userRepo repository.UserRepository, jwtService auth.JWTService, wechatService wechat.AuthService, cacheService cache.Cache, cfg *config.Config) AuthService {
	return &authService{
		userRepo:      userRepo,
		jwtService:    jwtService,
		wechatService: wechatService,
		cacheService:  cacheService,
		config:        cfg,
	}
}

// Login 用户登录
func (s *authService) Login(ctx context.Context, req *types.LoginRequest) (*types.LoginResponse, error) {
	// 1. 验证短信验证码
	if err := s.validateSMSCode(ctx, req.Phone, req.SmsCode); err != nil {
		return nil, err
	}

	// 2. 查找或创建用户
	user, err := s.userRepo.FindByPhone(req.Phone)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户不存在，创建新用户
			user = &model.User{
				UID:      utils.GenerateUID(),
				Phone:    req.Phone,
				Nickname: "用户" + req.Phone[7:], // 使用手机号后4位作为默认昵称
				Source:   "sms",
			}
			if err := s.userRepo.Create(user); err != nil {
				log.Error().Err(err).Str("phone", req.Phone).Msg("Failed to create user")
				return nil, errors.New("创建用户失败")
			}
		} else {
			log.Error().Err(err).Str("phone", req.Phone).Msg("Failed to get user by phone")
			return nil, errors.New("获取用户信息失败")
		}
	}

	// 更新最后登录时间
	if err := s.userRepo.UpdateLastLoginAt(user.ID); err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to update user last login time")
	}

	// 生成JWT令牌
	token, err := s.jwtService.GenerateUserToken(user.ID, "")
	if err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to generate JWT token")
		return nil, errors.New("生成令牌失败")
	}

	// 生成刷新令牌
	refreshToken, err := s.jwtService.GenerateRefreshToken(user.ID, "")
	if err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to generate refresh token")
		return nil, errors.New("生成刷新令牌失败")
	}

	return &types.LoginResponse{
		AccessToken:  token,
		RefreshToken: refreshToken,
		User: &types.UserInfo{
			ID:               user.ID,
			UID:              user.UID,
			Phone:            user.Phone,
			Nickname:         user.Nickname,
			Birthday:         user.Birthday,
			Avatar:           user.Avatar,
			Source:           user.Source,
			Gender:           user.Gender,
			IsVerified:       user.IsVerified,
			PersonalVerified: user.PersonalVerificationID > 0,
			EnterpriseID:     user.EnterpriseID,
			Points:           user.Points,
		},
	}, nil
}

// WechatLogin 微信快捷登录
func (s *authService) WechatLogin(ctx context.Context, req *types.WechatLoginRequest) (*types.LoginResponse, error) {
	// 1. 验证登录code是否已使用
	if err := s.validateAndMarkCodeUsed(ctx, "wechat_login", req.LoginCode); err != nil {
		log.Error().Err(err).Str("login_code", req.LoginCode).Msg("Code validation failed")
		return nil, err
	}

	// 2. 获取微信会话信息
	sessionInfo, err := s.wechatService.Code2Session(ctx, req.LoginCode)
	if err != nil {
		log.Error().Err(err).Str("login_code", req.LoginCode).Msg("Failed to get wechat session info")
		return nil, errors.New("获取微信会话信息失败")
	}

	// 3. 根据openid查找用户
	user := s.userRepo.FindByOpenID(sessionInfo.OpenID)
	if user != nil {
		// 用户已存在，直接登录
		return s.loginExistingUser(user, sessionInfo, req.DeviceID)
	}

	// 4. 用户不存在，需要注册
	return s.registerNewUser(ctx, sessionInfo, req)
}

// loginExistingUser 登录已存在的用户
func (s *authService) loginExistingUser(user *model.User, sessionInfo *wechat.SessionInfo, deviceID string) (*types.LoginResponse, error) {
	// 更新unionid（如果有变化）
	if sessionInfo.UnionID != "" && (user.UnionID == nil || *user.UnionID != sessionInfo.UnionID) {
		user.UnionID = &sessionInfo.UnionID
		if err := s.userRepo.Update(user); err != nil {
			log.Error().Err(err).Msg("Failed to update user unionid")
		}
	}

	// 更新最后登录时间
	if err := s.userRepo.UpdateLastLoginAt(user.ID); err != nil {
		log.Error().Err(err).Msg("Failed to update user last login time")
	}

	// 生成JWT令牌
	token, err := s.jwtService.GenerateUserToken(user.ID, deviceID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to generate JWT token")
		return nil, errors.New("生成令牌失败")
	}

	// 生成刷新令牌
	refreshToken, err := s.jwtService.GenerateRefreshToken(user.ID, deviceID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to generate refresh token")
		return nil, errors.New("生成刷新令牌失败")
	}

	return &types.LoginResponse{
		AccessToken:  token,
		RefreshToken: refreshToken,
		User:         s.buildUserInfo(user),
	}, nil
}

// registerNewUser 注册新用户
func (s *authService) registerNewUser(ctx context.Context, sessionInfo *wechat.SessionInfo, req *types.WechatLoginRequest) (*types.LoginResponse, error) {
	// 新用户必须提供手机号授权码
	if req.PhoneCode == "" {
		return nil, errors.New("新用户需要授权手机号")
	}

	// 验证手机号授权码是否已使用
	if err := s.validateAndMarkCodeUsed(ctx, "wechat_phone", req.PhoneCode); err != nil {
		log.Error().Err(err).Str("phone_code", req.PhoneCode).Msg("Phone code validation failed")
		return nil, err
	}

	// 获取手机号信息
	phoneInfo, err := s.wechatService.GetPhoneNumber(ctx, req.PhoneCode)
	if err != nil {
		log.Error().Err(err).Str("phone_code", req.PhoneCode).Msg("Failed to get wechat phone number")
		return nil, errors.New("获取手机号失败")
	}

	// 检查手机号是否已被其他用户使用
	existingUser, err := s.userRepo.FindByPhone(phoneInfo.PurePhoneNumber)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error().Err(err).Str("phone", phoneInfo.PurePhoneNumber).Msg("Failed to check existing user by phone")
		return nil, errors.New("检查手机号失败")
	}

	if existingUser != nil {
		// 手机号已存在，绑定微信信息到现有用户
		return s.bindWechatToExistingUser(existingUser, sessionInfo, req.DeviceID)
	}

	// 创建新用户
	return s.createNewWechatUser(sessionInfo, phoneInfo, req.DeviceID)
}

// bindWechatToExistingUser 绑定微信信息到现有用户
func (s *authService) bindWechatToExistingUser(user *model.User, sessionInfo *wechat.SessionInfo, deviceID string) (*types.LoginResponse, error) {
	// 绑定微信信息
	user.OpenID = sessionInfo.OpenID
	if sessionInfo.UnionID != "" {
		user.UnionID = &sessionInfo.UnionID
	}
	user.Source = "mp-weixin"

	if err := s.userRepo.Update(user); err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to update existing user with wechat info")
		return nil, errors.New("绑定微信信息失败")
	}

	if err := s.userRepo.UpdateLastLoginAt(user.ID); err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to update user last login time")
	}

	// 生成JWT令牌
	token, err := s.jwtService.GenerateUserToken(user.ID, deviceID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to generate JWT token")
		return nil, errors.New("生成令牌失败")
	}

	// 生成刷新令牌
	refreshToken, err := s.jwtService.GenerateRefreshToken(user.ID, deviceID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to generate refresh token")
		return nil, errors.New("生成刷新令牌失败")
	}

	return &types.LoginResponse{
		AccessToken:  token,
		RefreshToken: refreshToken,
		User:         s.buildUserInfo(user),
	}, nil
}

// createNewWechatUser 创建新的微信用户
func (s *authService) createNewWechatUser(sessionInfo *wechat.SessionInfo, phoneInfo *wechat.PhoneInfo, deviceID string) (*types.LoginResponse, error) {
	// 创建新用户
	newUser := &model.User{
		UID:      utils.GenerateUID(),
		OpenID:   sessionInfo.OpenID,
		Phone:    phoneInfo.PurePhoneNumber,
		Nickname: "微信用户" + phoneInfo.PurePhoneNumber[7:], // 使用手机号后4位作为默认昵称
		Source:   "mp-weixin",
	}

	// 设置unionid（如果存在）
	if sessionInfo.UnionID != "" {
		newUser.UnionID = &sessionInfo.UnionID
	}

	if err := s.userRepo.Create(newUser); err != nil {
		log.Error().Err(err).Str("phone", phoneInfo.PurePhoneNumber).Str("openid", sessionInfo.OpenID).Msg("Failed to create new wechat user")
		return nil, errors.New("创建用户失败")
	}

	if err := s.userRepo.UpdateLastLoginAt(newUser.ID); err != nil {
		log.Error().Err(err).Uint("user_id", newUser.ID).Msg("Failed to update user last login time")
	}

	// 生成JWT令牌
	token, err := s.jwtService.GenerateUserToken(newUser.ID, deviceID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", newUser.ID).Msg("Failed to generate JWT token")
		return nil, errors.New("生成令牌失败")
	}

	// 生成刷新令牌
	refreshToken, err := s.jwtService.GenerateRefreshToken(newUser.ID, deviceID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", newUser.ID).Msg("Failed to generate refresh token")
		return nil, errors.New("生成刷新令牌失败")
	}

	return &types.LoginResponse{
		AccessToken:  token,
		RefreshToken: refreshToken,
		User:         s.buildUserInfo(newUser),
	}, nil
}

// buildUserInfo 构建用户信息
func (s *authService) buildUserInfo(user *model.User) *types.UserInfo {
	return &types.UserInfo{
		ID:               user.ID,
		UID:              user.UID,
		Phone:            user.Phone,
		Nickname:         user.Nickname,
		Birthday:         user.Birthday,
		Avatar:           user.Avatar,
		Source:           user.Source,
		Gender:           user.Gender,
		IsVerified:       user.IsVerified,
		PersonalVerified: user.PersonalVerificationID > 0,
		EnterpriseID:     user.EnterpriseID,
		Points:           user.Points,
	}
}

// SendSmsCode 发送短信验证码
func (s *authService) SendSmsCode(ctx context.Context, req *types.SendSmsCodeRequest) error {
	// 1. 检查频率限制（同一手机号1分钟内只能发送一次）
	rateLimitKey := fmt.Sprintf("sms_rate_limit:%s", req.Phone)
	if s.cacheService.Exists(ctx, rateLimitKey) {
		return errors.New("验证码发送过于频繁，请稍后再试")
	}

	// 2. 生成6位数字验证码
	code := utils.GenerateSMSCode()

	// 3. 存储验证码到缓存，有效期5分钟
	cacheKey := fmt.Sprintf("sms_code:%s", req.Phone)
	err := s.cacheService.Set(ctx, cacheKey, code, 5*time.Minute)
	if err != nil {
		log.Error().Err(err).Str("phone", req.Phone).Msg("Failed to store SMS code in cache")
		return errors.New("验证码生成失败，请稍后重试")
	}

	// 4. 设置频率限制，1分钟内不能重复发送
	err = s.cacheService.Set(ctx, rateLimitKey, "sent", time.Minute)
	if err != nil {
		log.Error().Err(err).Str("phone", req.Phone).Msg("Failed to set rate limit")
	}

	// 5. 发送短信验证码
	// TODO: 这里应该调用短信服务提供商的API发送验证码
	log.Info().Str("phone", req.Phone).Str("code", code).Msg("SMS code generated (TODO: integrate SMS service)")

	return nil
}

// CheckUserByCode 根据微信code检查用户是否注册
func (s *authService) CheckUserByCode(ctx context.Context, req *types.CheckUserByCodeRequest) (*types.CheckUserByCodeResponse, error) {
	// 1. 验证code是否已使用
	if err := s.validateAndMarkCodeUsed(ctx, "wechat_check", req.Code); err != nil {
		log.Error().Err(err).Str("code", req.Code).Msg("Code validation failed")
		return nil, err
	}

	// 2. 获取微信会话信息
	sessionInfo, err := s.wechatService.Code2Session(ctx, req.Code)
	if err != nil {
		log.Error().Err(err).Str("code", req.Code).Msg("Failed to get wechat session info")
		return nil, errors.New("获取微信会话信息失败")
	}

	// 3. 根据openid查找用户
	user := s.userRepo.FindByOpenID(sessionInfo.OpenID)
	if user == nil {
		return &types.CheckUserByCodeResponse{
			IsRegistered: false,
			HasPhone:     false,
			OpenID:       sessionInfo.OpenID,
			Message:      "用户未注册",
		}, nil
	}

	// 4. 检查用户是否已绑定手机号
	hasPhone := user.Phone != ""

	return &types.CheckUserByCodeResponse{
		IsRegistered: true,
		HasPhone:     hasPhone,
		OpenID:       user.OpenID,
		Message:      "用户已注册",
	}, nil
}

// validateAndMarkCodeUsed 验证并标记code为已使用
func (s *authService) validateAndMarkCodeUsed(ctx context.Context, codeType, code string) error {
	// 生成缓存key
	cacheKey := fmt.Sprintf("used_code:%s:%s", codeType, code)

	// 检查code是否已使用
	if s.cacheService.Exists(ctx, cacheKey) {
		return errors.New("验证码已被使用，请重新获取")
	}

	// 标记code为已使用，设置过期时间为30分钟
	err := s.cacheService.Set(ctx, cacheKey, "used", 30*time.Minute)
	if err != nil {
		log.Error().Err(err).Str("cache_key", cacheKey).Msg("Failed to mark code as used")
		// 不阻止登录流程，只记录错误
	}

	return nil
}

// validateSMSCode 验证短信验证码
func (s *authService) validateSMSCode(ctx context.Context, phone, inputCode string) error {
	// 生成缓存key
	cacheKey := fmt.Sprintf("sms_code:%s", phone)

	// 从缓存中获取验证码
	storedCode, err := s.cacheService.Get(ctx, cacheKey)
	if err != nil {
		log.Error().Err(err).Str("phone", phone).Msg("Failed to get SMS code from cache")
		return errors.New("验证码获取失败")
	}

	// 检查验证码是否存在
	if storedCode == "" {
		return errors.New("验证码已过期或不存在，请重新获取")
	}

	// 验证验证码是否正确
	if storedCode != inputCode {
		return errors.New("验证码错误，请重新输入")
	}

	// 验证成功后删除验证码，确保一次性使用
	err = s.cacheService.Delete(ctx, cacheKey)
	if err != nil {
		log.Error().Err(err).Str("phone", phone).Msg("Failed to delete SMS code from cache")
		// 不阻止登录流程，只记录错误
	}

	return nil
}

// DevLogin 开发测试登录
func (s *authService) DevLogin(ctx context.Context, req *types.DevLoginRequest) (*types.DevLoginResponse, error) {
	// 1. 检查开发登录是否启用
	if !s.config.DevLogin.Enabled {
		return nil, errors.New("开发登录功能未启用")
	}

	// 2. 检查手机号是否在允许列表中
	allowed := false
	for _, allowedPhone := range s.config.DevLogin.AllowedUsers {
		if allowedPhone == req.Phone {
			allowed = true
			break
		}
	}
	if !allowed {
		return nil, errors.New("该手机号不在开发登录允许列表中")
	}

	// 3. 查找或创建测试用户
	user, err := s.userRepo.FindByPhone(req.Phone)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建测试用户
			user = s.createTestUser(req.Phone)
			if err := s.userRepo.Create(user); err != nil {
				log.Error().Err(err).Str("phone", req.Phone).Msg("Failed to create test user")
				return nil, errors.New("创建测试用户失败")
			}
		} else {
			log.Error().Err(err).Str("phone", req.Phone).Msg("Failed to get user by phone")
			return nil, errors.New("获取用户信息失败")
		}
	}

	// 4. 更新最后登录时间
	if err := s.userRepo.UpdateLastLoginAt(user.ID); err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to update user last login time")
	}

	// 5. 生成JWT令牌
	token, err := s.jwtService.GenerateUserToken(user.ID, req.DeviceID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to generate JWT token")
		return nil, errors.New("生成令牌失败")
	}

	// 生成刷新令牌
	refreshToken, err := s.jwtService.GenerateRefreshToken(user.ID, req.DeviceID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to generate refresh token")
		return nil, errors.New("生成刷新令牌失败")
	}

	// 6. 记录开发登录日志
	log.Info().Str("phone", req.Phone).Uint("user_id", user.ID).Str("device_id", req.DeviceID).Msg("Dev login successful")

	return &types.DevLoginResponse{
		AccessToken:  token,
		RefreshToken: refreshToken,
		User:         s.buildUserInfo(user),
	}, nil
}

// createTestUser 创建测试用户
func (s *authService) createTestUser(phone string) *model.User {
	user := &model.User{
		UID:      utils.GenerateUID(),
		Phone:    phone,
		Nickname: s.getTestUserNickname(phone),
		Source:   "dev-test",
		Gender:   s.getTestUserGender(phone),
	}

	// 为不同的测试账号设置不同的角色和权限
	switch phone {
	case "13800138001":
		// 求职者测试账号
		user.Nickname = "测试用户-求职者"
		user.Avatar = "/static/images/avatar-male.png"
		user.Gender = 1
		user.IsVerified = false
		user.Points = 100
	case "13800138002":
		// 企业招聘方测试账号
		user.Nickname = "测试企业-招聘方"
		user.Avatar = "/static/images/avatar-female.png"
		user.Gender = 2
		user.IsVerified = true
		user.PersonalVerificationID = 1 // 模拟已认证
		user.EnterpriseID = 1           // 模拟关联企业
		user.Points = 500
	case "13800138003":
		// 管理员测试账号
		user.Nickname = "测试管理员"
		user.Avatar = "/static/images/default-avatar.png"
		user.Gender = 1
		user.IsVerified = true
		user.PersonalVerificationID = 1
		user.Points = 9999
	default:
		// 默认普通用户
		user.Nickname = "测试用户" + phone[7:]
		user.Gender = 0
		user.Points = 0
	}

	return user
}

// getTestUserNickname 获取测试用户昵称
func (s *authService) getTestUserNickname(phone string) string {
	switch phone {
	case "13800138001":
		return "测试用户-求职者"
	case "13800138002":
		return "测试企业-招聘方"
	case "13800138003":
		return "测试管理员"
	default:
		return "测试用户" + phone[7:]
	}
}

// getTestUserGender 获取测试用户性别
func (s *authService) getTestUserGender(phone string) int16 {
	switch phone {
	case "13800138001":
		return 1 // 男
	case "13800138002":
		return 2 // 女
	case "13800138003":
		return 1 // 男
	default:
		return 0 // 未知
	}
}

// RefreshToken 刷新访问令牌
func (s *authService) RefreshToken(ctx context.Context, req *types.RefreshTokenRequest) (*types.RefreshTokenResponse, error) {
	// 1. 验证刷新令牌
	claims, err := s.jwtService.ValidateRefreshToken(req.RefreshToken)
	if err != nil {
		log.Error().Err(err).Msg("Invalid refresh token")
		return nil, errors.New("刷新令牌无效")
	}

	// 2. 检查用户是否存在
	user, err := s.userRepo.Find(claims.UserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error().Uint("user_id", claims.UserID).Msg("User not found for refresh token")
			return nil, errors.New("用户不存在")
		}
		log.Error().Err(err).Uint("user_id", claims.UserID).Msg("Failed to get user by ID")
		return nil, errors.New("获取用户信息失败")
	}

	// 3. 生成新的访问令牌
	newAccessToken, err := s.jwtService.GenerateUserToken(user.ID, claims.DeviceID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to generate new access token")
		return nil, errors.New("生成访问令牌失败")
	}

	// 4. 生成新的刷新令牌
	newRefreshToken, err := s.jwtService.GenerateRefreshToken(user.ID, claims.DeviceID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", user.ID).Msg("Failed to generate new refresh token")
		return nil, errors.New("生成刷新令牌失败")
	}

	// 5. 记录刷新令牌日志
	log.Info().Uint("user_id", user.ID).Str("device_id", claims.DeviceID).Msg("Token refreshed successfully")

	return &types.RefreshTokenResponse{
		AccessToken:  newAccessToken,
		RefreshToken: newRefreshToken,
	}, nil
}

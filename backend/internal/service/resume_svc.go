package service

import (
	"context"
	"errors"
	"fmt"

	"bdb-backend/internal/constants"
	"bdb-backend/internal/model"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"

	"gorm.io/gorm"
)

type ResumeService interface {
	// Resume CRUD operations
	CreateResume(ctx context.Context, userID uint, req *types.CreateResumeRequest) (*model.Resume, error)
	GetResumeByID(ctx context.Context, id uint, viewerID *uint) (*types.ResumeResponse, error)
	UpdateResume(ctx context.Context, userID uint, req *types.UpdateResumeRequest) error
	DeleteResume(ctx context.Context, userID uint) error
	GetMyResume(ctx context.Context, userID uint) (*types.ResumeResponse, error)

	// Resume visibility and privacy
	SetResumeVisibility(ctx context.Context, userID uint, isPublic bool) error
	GetResumePreview(ctx context.Context, userID uint) (*types.ResumeResponse, error)

	// Resume search and recommendations for recruiters
	SearchResumes(ctx context.Context, req *types.ResumeQueryRequest) (*types.ResumeListResponse, error)
	GetRecommendedResumes(ctx context.Context, jobID uint, page, pageSize int) (*types.ResumeListResponse, error)
	GetResumesBySkills(ctx context.Context, skills []string, page, pageSize int) (*types.ResumeListResponse, error)
	GetHighQualityResumes(ctx context.Context, page, pageSize int) (*types.ResumeListResponse, error)

	// Resume analytics and tracking
	ViewResume(ctx context.Context, resumeID uint, viewerID uint, viewerType string) error
	GetResumeStats(ctx context.Context, userID uint) (*types.ResumeResponse, error)
	GetResumeViewHistory(ctx context.Context, userID uint, page, pageSize int) ([]*model.ResumeViewHistory, int64, error)

	// Resume quality and completion
	CalculateCompletionRate(ctx context.Context, userID uint) (float64, error)
	GetCompletionSuggestions(ctx context.Context, userID uint) ([]string, error)
	ValidateResumeForJobApplication(ctx context.Context, userID uint) error

	// Resume export and sharing
	GenerateResumeShareLink(ctx context.Context, userID uint, expiryDays int) (string, error)
	GetResumeByShareLink(ctx context.Context, shareToken string) (*types.ResumeResponse, error)

	// Admin and analysis
	GetIncompleteResumes(ctx context.Context, page, pageSize int) (*types.ResumeListResponse, error)
	GetResumeAnalytics(ctx context.Context, userID uint) (*model.ResumeStatistics, error)

	// Bulk operations
	SendResumeToRecruiter(ctx context.Context, userID uint, recruiterID uint, message string) error
	MarkResumeAsActive(ctx context.Context, userID uint) error
}

type resumeService struct {
	resumeRepo     repository.ResumeRepository
	userRepo       repository.UserRepository
	enterpriseRepo repository.EnterpriseRepository
}

func NewResumeService(
	resumeRepo repository.ResumeRepository,
	userRepo repository.UserRepository,
	enterpriseRepo repository.EnterpriseRepository,
) ResumeService {
	return &resumeService{
		resumeRepo:     resumeRepo,
		userRepo:       userRepo,
		enterpriseRepo: enterpriseRepo,
	}
}

// CreateResume creates a new resume for a user
func (s *resumeService) CreateResume(ctx context.Context, userID uint, req *types.CreateResumeRequest) (*model.Resume, error) {
	// Check if user already has a resume
	existing, err := s.resumeRepo.GetByUserID(ctx, userID)
	if err == nil && existing != nil {
		return nil, errors.New("用户已有简历，请使用更新接口")
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("检查简历失败: %w", err)
	}

	// Validate request
	if err := s.validateResumeRequest(req); err != nil {
		return nil, err
	}

	// Convert structured data to JSON
	workExperienceJSON := s.convertWorkExperienceToJSON(req.WorkExperience)
	educationHistoryJSON := s.convertEducationHistoryToJSON(req.EducationHistory)
	skillsJSON := s.convertSkillsToJSON(req.Skills)
	jobIntentionsJSON := s.convertJobIntentionsToJSON(req.JobIntentions)
	preferredLocationsJSON := s.convertLocationsToJSON(req.PreferredLocations)

	// Create resume
	resume := &model.Resume{
		UserID:             userID,
		Name:               req.Name,
		Age:                req.Age,
		Gender:             int16(req.Gender),
		Phone:              req.Phone,
		Email:              req.Email,
		AvatarURL:          req.AvatarURL,
		WorkExperience:     workExperienceJSON,
		EducationHistory:   educationHistoryJSON,
		Skills:             skillsJSON,
		JobIntentions:      jobIntentionsJSON,
		ExpectedSalaryMin:  req.ExpectedSalaryMin,
		ExpectedSalaryMax:  req.ExpectedSalaryMax,
		PreferredLocations: preferredLocationsJSON,
		WorkStatus:         req.WorkStatus,
		AvailabilityDate:   req.AvailabilityDate,
		SelfIntroduction:   req.SelfIntroduction,
	}

	if err := s.resumeRepo.Create(ctx, resume); err != nil {
		return nil, fmt.Errorf("创建简历失败: %w", err)
	}

	// Update last active time
	if err := s.resumeRepo.UpdateLastActiveTime(ctx, userID); err != nil {
		logger.Error("Failed to update last active time", err, "user_id", userID)
	}

	return resume, nil
}

// GetResumeByID retrieves a resume by ID
func (s *resumeService) GetResumeByID(ctx context.Context, id uint, viewerID *uint) (*types.ResumeResponse, error) {
	resume, err := s.resumeRepo.Find(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("简历不存在")
		}
		return nil, fmt.Errorf("获取简历失败: %w", err)
	}

	// Record view if viewer is provided and not the resume owner
	if viewerID != nil && *viewerID != resume.UserID {
		if err := s.ViewResume(ctx, id, *viewerID, "recruiter"); err != nil {
			logger.Error("Failed to record resume view", err, "resume_id", id, "viewer_id", *viewerID)
		}
	}

	return s.convertResumeToResponse(resume), nil
}

// UpdateResume updates an existing resume
func (s *resumeService) UpdateResume(ctx context.Context, userID uint, req *types.UpdateResumeRequest) error {
	// Get user's resume
	resume, err := s.resumeRepo.GetByUserID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("简历不存在，请先创建简历")
		}
		return fmt.Errorf("获取简历失败: %w", err)
	}

	// Apply updates
	s.applyResumeUpdates(resume, req)

	// Validate updated data
	if err := s.validateUpdatedResume(resume); err != nil {
		return err
	}

	// Update in database
	if err := s.resumeRepo.Update(ctx, resume); err != nil {
		return fmt.Errorf("更新简历失败: %w", err)
	}

	// Update last active time
	if err := s.resumeRepo.UpdateLastActiveTime(ctx, userID); err != nil {
		logger.Error("Failed to update last active time", err, "user_id", userID)
	}

	return nil
}

// DeleteResume deletes a user's resume
func (s *resumeService) DeleteResume(ctx context.Context, userID uint) error {
	resume, err := s.resumeRepo.GetByUserID(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取简历失败: %w", err)
	}

	// Check if resume has active applications
	// TODO: Add validation for active applications

	return s.resumeRepo.Delete(ctx, resume.ID)
}

// GetMyResume retrieves current user's resume
func (s *resumeService) GetMyResume(ctx context.Context, userID uint) (*types.ResumeResponse, error) {
	resume, err := s.resumeRepo.GetByUserID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("简历不存在")
		}
		return nil, fmt.Errorf("获取简历失败: %w", err)
	}

	return s.convertResumeToResponse(resume), nil
}

// SearchResumes performs advanced resume search for recruiters
func (s *resumeService) SearchResumes(ctx context.Context, req *types.ResumeQueryRequest) (*types.ResumeListResponse, error) {
	// Set default values
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	resumes, total, err := s.resumeRepo.SearchResumes(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("搜索简历失败: %w", err)
	}

	// Convert to response format
	resumeResponses := make([]types.ResumeResponse, 0, len(resumes))
	for _, resume := range resumes {
		response := s.convertResumeToResponse(resume)
		resumeResponses = append(resumeResponses, *response)
	}

	return &types.ResumeListResponse{
		PaginationResp: types.NewPaginationResp(resumeResponses, total, req.Page, req.PageSize),
	}, nil
}

// GetRecommendedResumes retrieves recommended resumes for a specific job
func (s *resumeService) GetRecommendedResumes(ctx context.Context, jobID uint, page, pageSize int) (*types.ResumeListResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	resumes, total, err := s.resumeRepo.GetRecommendedResumes(ctx, jobID, page, pageSize)
	if err != nil {
		return nil, fmt.Errorf("获取推荐简历失败: %w", err)
	}

	// Convert to response format
	resumeResponses := make([]types.ResumeResponse, 0, len(resumes))
	for _, resume := range resumes {
		response := s.convertResumeToResponse(resume)
		resumeResponses = append(resumeResponses, *response)
	}

	return &types.ResumeListResponse{
		PaginationResp: types.NewPaginationResp(resumeResponses, total, page, pageSize),
	}, nil
}

// GetResumesBySkills retrieves resumes containing specific skills
func (s *resumeService) GetResumesBySkills(ctx context.Context, skills []string, page, pageSize int) (*types.ResumeListResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	resumes, total, err := s.resumeRepo.GetResumesBySkills(ctx, skills, page, pageSize)
	if err != nil {
		return nil, fmt.Errorf("按技能搜索简历失败: %w", err)
	}

	// Convert to response format
	resumeResponses := make([]types.ResumeResponse, 0, len(resumes))
	for _, resume := range resumes {
		response := s.convertResumeToResponse(resume)
		resumeResponses = append(resumeResponses, *response)
	}

	return &types.ResumeListResponse{
		PaginationResp: types.NewPaginationResp(resumeResponses, total, page, pageSize),
	}, nil
}

// GetHighQualityResumes retrieves high-quality, complete resumes
func (s *resumeService) GetHighQualityResumes(ctx context.Context, page, pageSize int) (*types.ResumeListResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	resumes, total, err := s.resumeRepo.GetHighQualityResumes(ctx, page, pageSize)
	if err != nil {
		return nil, fmt.Errorf("获取优质简历失败: %w", err)
	}

	// Convert to response format
	resumeResponses := make([]types.ResumeResponse, 0, len(resumes))
	for _, resume := range resumes {
		response := s.convertResumeToResponse(resume)
		resumeResponses = append(resumeResponses, *response)
	}

	return &types.ResumeListResponse{
		PaginationResp: types.NewPaginationResp(resumeResponses, total, page, pageSize),
	}, nil
}

// ViewResume records a resume view and increments view count
func (s *resumeService) ViewResume(ctx context.Context, resumeID uint, viewerID uint, viewerType string) error {
	// Increment view count
	if err := s.resumeRepo.IncrementViewCount(ctx, resumeID); err != nil {
		logger.Error("Failed to increment resume view count", err, "resume_id", resumeID)
	}

	// Record detailed view history
	if err := s.resumeRepo.RecordResumeView(ctx, resumeID, viewerID, viewerType); err != nil {
		logger.Error("Failed to record resume view history", err, "resume_id", resumeID, "viewer_id", viewerID)
	}

	return nil
}

// GetResumeStats retrieves statistics for a user's resume
func (s *resumeService) GetResumeStats(ctx context.Context, userID uint) (*types.ResumeResponse, error) {
	resume, err := s.resumeRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取简历失败: %w", err)
	}

	response := s.convertResumeToResponse(resume)

	// Get additional stats
	stats, err := s.resumeRepo.GetResumeStats(ctx, userID)
	if err == nil {
		response.ViewCount = int(stats.ViewCount)
		response.DownloadCount = int(stats.DownloadCount)
		response.LastActiveAt = stats.LastActiveAt
	}

	return response, nil
}

// CalculateCompletionRate calculates the completion rate of a user's resume
func (s *resumeService) CalculateCompletionRate(ctx context.Context, userID uint) (float64, error) {
	resume, err := s.resumeRepo.GetByUserID(ctx, userID)
	if err != nil {
		return 0, fmt.Errorf("获取简历失败: %w", err)
	}

	return resume.GetCompletionRate(), nil
}

// GetCompletionSuggestions provides suggestions to improve resume completion
func (s *resumeService) GetCompletionSuggestions(ctx context.Context, userID uint) ([]string, error) {
	resume, err := s.resumeRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取简历失败: %w", err)
	}

	suggestions := []string{}

	if resume.Name == "" {
		suggestions = append(suggestions, "请填写您的姓名")
	}
	if resume.Age == 0 {
		suggestions = append(suggestions, "请填写您的年龄")
	}
	if resume.Phone == "" {
		suggestions = append(suggestions, "请填写联系电话")
	}
	if resume.Email == "" {
		suggestions = append(suggestions, "请填写邮箱地址")
	}
	if resume.SelfIntroduction == "" {
		suggestions = append(suggestions, "请添加自我介绍")
	}
	if resume.WorkExperience.String() == "[]" || resume.WorkExperience.String() == "" {
		suggestions = append(suggestions, "请添加工作经历")
	}
	if resume.EducationHistory.String() == "[]" || resume.EducationHistory.String() == "" {
		suggestions = append(suggestions, "请添加教育背景")
	}
	if resume.Skills.String() == "[]" || resume.Skills.String() == "" {
		suggestions = append(suggestions, "请添加技能标签")
	}
	if resume.JobIntentions.String() == "[]" || resume.JobIntentions.String() == "" {
		suggestions = append(suggestions, "请添加求职意向")
	}
	if resume.ExpectedSalaryMin == 0 {
		suggestions = append(suggestions, "请设置期望薪资")
	}
	if resume.PreferredLocations.String() == "[]" || resume.PreferredLocations.String() == "" {
		suggestions = append(suggestions, "请添加意向工作地点")
	}
	if resume.AvailabilityDate == nil {
		suggestions = append(suggestions, "请设置可到岗时间")
	}

	return suggestions, nil
}

// ValidateResumeForJobApplication validates if resume is ready for job applications
func (s *resumeService) ValidateResumeForJobApplication(ctx context.Context, userID uint) error {
	resume, err := s.resumeRepo.GetByUserID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("请先创建简历")
		}
		return fmt.Errorf("获取简历失败: %w", err)
	}

	if !resume.CanApplyJobs() {
		return errors.New("简历信息不完整，无法申请职位。请完善姓名、联系电话和求职意向等必要信息")
	}

	return nil
}

// GetIncompleteResumes retrieves resumes that need completion (admin function)
func (s *resumeService) GetIncompleteResumes(ctx context.Context, page, pageSize int) (*types.ResumeListResponse, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	resumes, total, err := s.resumeRepo.GetIncompleteResumes(ctx, page, pageSize)
	if err != nil {
		return nil, fmt.Errorf("获取不完整简历失败: %w", err)
	}

	// Convert to response format
	resumeResponses := make([]types.ResumeResponse, 0, len(resumes))
	for _, resume := range resumes {
		response := s.convertResumeToResponse(resume)
		resumeResponses = append(resumeResponses, *response)
	}

	return &types.ResumeListResponse{
		PaginationResp: types.NewPaginationResp(resumeResponses, total, page, pageSize),
	}, nil
}

// GetResumeAnalytics retrieves analytics for a user's resume
func (s *resumeService) GetResumeAnalytics(ctx context.Context, userID uint) (*model.ResumeStatistics, error) {
	return s.resumeRepo.GetResumeStats(ctx, userID)
}

// Helper methods

// validateResumeRequest validates resume creation request
func (s *resumeService) validateResumeRequest(req *types.CreateResumeRequest) error {
	if len(req.Name) < 1 || len(req.Name) > constants.MaxResumeNameLength {
		return errors.New("姓名长度应在1-20字符之间")
	}

	if req.Age < 16 || req.Age > 100 {
		return errors.New("年龄应在16-100岁之间")
	}

	if req.Gender < 0 || req.Gender > 2 {
		return errors.New("性别选择无效")
	}

	if len(req.Phone) < 1 {
		return errors.New("联系电话不能为空")
	}

	if req.Email != "" && len(req.Email) > 100 {
		return errors.New("邮箱地址过长")
	}

	if len(req.SelfIntroduction) > constants.MaxSelfIntroductionLength {
		return errors.New("自我介绍过长")
	}

	if len(req.WorkExperience) > constants.MaxWorkExperienceCount {
		return errors.New("工作经历数量过多")
	}

	if len(req.EducationHistory) > constants.MaxEducationHistoryCount {
		return errors.New("教育背景数量过多")
	}

	if len(req.Skills) > constants.MaxSkillsCount {
		return errors.New("技能标签数量过多")
	}

	if len(req.JobIntentions) > constants.MaxJobIntentionsCount {
		return errors.New("求职意向数量过多")
	}

	if len(req.PreferredLocations) > constants.MaxPreferredLocationsCount {
		return errors.New("意向地点数量过多")
	}

	return nil
}

// applyResumeUpdates applies updates to resume model
func (s *resumeService) applyResumeUpdates(resume *model.Resume, req *types.UpdateResumeRequest) {
	if req.Name != nil {
		resume.Name = *req.Name
	}
	if req.Age != nil {
		resume.Age = *req.Age
	}
	if req.Gender != nil {
		resume.Gender = int16(*req.Gender)
	}
	if req.Phone != nil {
		resume.Phone = *req.Phone
	}
	if req.Email != nil {
		resume.Email = *req.Email
	}
	if req.AvatarURL != nil {
		resume.AvatarURL = *req.AvatarURL
	}
	if req.WorkExperience != nil {
		resume.WorkExperience = s.convertWorkExperienceToJSON(req.WorkExperience)
	}
	if req.EducationHistory != nil {
		resume.EducationHistory = s.convertEducationHistoryToJSON(req.EducationHistory)
	}
	if req.Skills != nil {
		resume.Skills = s.convertSkillsToJSON(req.Skills)
	}
	if req.JobIntentions != nil {
		resume.JobIntentions = s.convertJobIntentionsToJSON(req.JobIntentions)
	}
	if req.ExpectedSalaryMin != nil {
		resume.ExpectedSalaryMin = *req.ExpectedSalaryMin
	}
	if req.ExpectedSalaryMax != nil {
		resume.ExpectedSalaryMax = *req.ExpectedSalaryMax
	}
	if req.PreferredLocations != nil {
		resume.PreferredLocations = s.convertLocationsToJSON(req.PreferredLocations)
	}
	if req.WorkStatus != nil {
		resume.WorkStatus = *req.WorkStatus
	}
	if req.AvailabilityDate != nil {
		resume.AvailabilityDate = req.AvailabilityDate
	}
	if req.SelfIntroduction != nil {
		resume.SelfIntroduction = *req.SelfIntroduction
	}
}

// validateUpdatedResume validates resume after updates
func (s *resumeService) validateUpdatedResume(resume *model.Resume) error {
	if len(resume.Name) < 1 || len(resume.Name) > constants.MaxResumeNameLength {
		return errors.New("姓名长度应在1-20字符之间")
	}

	if resume.Age < 16 || resume.Age > 100 {
		return errors.New("年龄应在16-100岁之间")
	}

	if len(resume.Phone) < 1 {
		return errors.New("联系电话不能为空")
	}

	if len(resume.SelfIntroduction) > constants.MaxSelfIntroductionLength {
		return errors.New("自我介绍过长")
	}

	return nil
}

// convertResumeToResponse converts resume model to response format
func (s *resumeService) convertResumeToResponse(resume *model.Resume) *types.ResumeResponse {
	response := &types.ResumeResponse{
		ID:                resume.ID,
		UserID:            resume.UserID,
		Name:              resume.Name,
		Age:               resume.Age,
		Gender:            resume.Gender,
		GenderLabel:       constants.GetLabelByCode(constants.GenderOptions, int16(resume.Gender)),
		Phone:             resume.Phone,
		Email:             resume.Email,
		AvatarURL:         resume.AvatarURL,
		ExpectedSalaryMin: resume.ExpectedSalaryMin,
		ExpectedSalaryMax: resume.ExpectedSalaryMax,
		WorkStatus:        resume.WorkStatus,
		WorkStatusLabel:   "", // TODO: Implement work status label mapping
		AvailabilityDate:  resume.AvailabilityDate,
		SelfIntroduction:  resume.SelfIntroduction,
		CompletionRate:    resume.GetCompletionRate(),
		IsComplete:        resume.IsComplete(),
		CanApplyJobs:      resume.CanApplyJobs(),
		ViewCount:         resume.ViewCount,
		DownloadCount:     resume.DownloadCount,
		LastActiveAt:      resume.LastActiveAt,
		CreatedAt:         resume.CreatedAt,
		UpdatedAt:         resume.UpdatedAt,
	}

	// Parse JSON fields
	response.WorkExperience = s.parseWorkExperienceFromJSON(resume.WorkExperience)
	response.EducationHistory = s.parseEducationHistoryFromJSON(resume.EducationHistory)
	response.Skills = s.parseSkillsFromJSON(resume.Skills)
	response.JobIntentions = s.parseJobIntentionsFromJSON(resume.JobIntentions)
	response.PreferredLocations = s.parseLocationsFromJSON(resume.PreferredLocations)

	return response
}

// JSON conversion helper methods
func (s *resumeService) convertWorkExperienceToJSON(items []types.WorkExperienceItem) []byte {
	// TODO: Implement JSON marshaling
	return []byte("[]")
}

func (s *resumeService) convertEducationHistoryToJSON(items []types.EducationHistoryItem) []byte {
	// TODO: Implement JSON marshaling
	return []byte("[]")
}

func (s *resumeService) convertSkillsToJSON(items []types.SkillItem) []byte {
	// TODO: Implement JSON marshaling
	return []byte("[]")
}

func (s *resumeService) convertJobIntentionsToJSON(items []types.JobIntentionItem) []byte {
	// TODO: Implement JSON marshaling
	return []byte("[]")
}

func (s *resumeService) convertLocationsToJSON(locations []string) []byte {
	// TODO: Implement JSON marshaling
	return []byte("[]")
}

func (s *resumeService) parseWorkExperienceFromJSON(data []byte) []types.WorkExperienceItem {
	// TODO: Implement JSON parsing
	return []types.WorkExperienceItem{}
}

func (s *resumeService) parseEducationHistoryFromJSON(data []byte) []types.EducationHistoryItem {
	// TODO: Implement JSON parsing
	return []types.EducationHistoryItem{}
}

func (s *resumeService) parseSkillsFromJSON(data []byte) []types.SkillItem {
	// TODO: Implement JSON parsing
	return []types.SkillItem{}
}

func (s *resumeService) parseJobIntentionsFromJSON(data []byte) []types.JobIntentionItem {
	// TODO: Implement JSON parsing
	return []types.JobIntentionItem{}
}

func (s *resumeService) parseLocationsFromJSON(data []byte) []string {
	// TODO: Implement JSON parsing
	return []string{}
}

// Placeholder implementations for remaining interface methods

func (s *resumeService) SetResumeVisibility(ctx context.Context, userID uint, isPublic bool) error {
	// TODO: Implement resume visibility setting
	return errors.New("功能暂未实现")
}

func (s *resumeService) GetResumePreview(ctx context.Context, userID uint) (*types.ResumeResponse, error) {
	return s.GetMyResume(ctx, userID)
}

func (s *resumeService) GetResumeViewHistory(ctx context.Context, userID uint, page, pageSize int) ([]*model.ResumeViewHistory, int64, error) {
	resume, err := s.resumeRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("获取简历失败: %w", err)
	}

	return s.resumeRepo.GetViewHistory(ctx, resume.ID, page, pageSize)
}

func (s *resumeService) GenerateResumeShareLink(ctx context.Context, userID uint, expiryDays int) (string, error) {
	// TODO: Implement resume sharing functionality
	return "", errors.New("功能暂未实现")
}

func (s *resumeService) GetResumeByShareLink(ctx context.Context, shareToken string) (*types.ResumeResponse, error) {
	// TODO: Implement resume sharing functionality
	return nil, errors.New("功能暂未实现")
}

func (s *resumeService) SendResumeToRecruiter(ctx context.Context, userID uint, recruiterID uint, message string) error {
	// TODO: Implement resume sending functionality
	return errors.New("功能暂未实现")
}

func (s *resumeService) MarkResumeAsActive(ctx context.Context, userID uint) error {
	return s.resumeRepo.UpdateLastActiveTime(ctx, userID)
}

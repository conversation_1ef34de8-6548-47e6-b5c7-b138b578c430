package worker

import (
	"context"

	"bdb-backend/internal/service"
	"bdb-backend/pkg/logger"
)

// GigWorker 负责处理零工相关的后台任务
type GigWorker struct {
	gigService service.GigService
}

// NewGigWorker 创建一个新的 GigWorker 实例
func NewGigWorker(gigService service.GigService) *GigWorker {
	return &GigWorker{
		gigService: gigService,
	}
}

// CheckExpiredGigs 是具体的任务执行逻辑
func (w *GigWorker) CheckExpiredGigs(ctx context.Context) error {
	logger.Info("Worker: 开始检查过期零工")
	// 核心业务逻辑依然复用 service 层的方法
	if err := w.gigService.CheckExpiredGigs(ctx); err != nil {
		logger.Error("Worker: 检查过期零工失败", err)
		return err
	}
	logger.Info("Worker: 过期零工检查完成")
	return nil
}

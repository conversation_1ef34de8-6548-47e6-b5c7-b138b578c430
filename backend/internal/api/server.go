package api

import (
	"bdb-backend/internal/api/controller"
	"bdb-backend/internal/api/router"
	"bdb-backend/internal/scheduler"
	"bdb-backend/pkg/cache"
	"bdb-backend/pkg/config"
	"bdb-backend/pkg/database"
	"bdb-backend/pkg/jwt"
	"bdb-backend/pkg/logger"
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Server struct {
	router     *gin.Engine
	config     *config.Config
	db         *gorm.DB
	cache      cache.Cache
	scheduler  scheduler.Scheduler
	httpServer *http.Server
}

// NewDB creates a new database connection.
func NewDB(cfg *config.Config) *gorm.DB {
	return database.NewPostgresClient(cfg.Database)
}

// NewCache creates a new cache instance.
func NewCache(cfg *config.Config) cache.Cache {
	return cache.NewRedisCache(cfg.Redis)
}

// NewServer creates a new server instance.
func NewServer(
	cfg *config.Config,
	db *gorm.DB,
	cache cache.Cache,
	authCtl *controller.AuthController,
	userCtl *controller.UserController,
	chatCtl *controller.ChatController,
	messageCtl *controller.MessageController,
	notificationCtl *controller.NotificationController,
	gigCtl *controller.GigController,
	commonCtl *controller.CommonController,
	scheduler scheduler.Scheduler,
	jwtService jwt.JWTService,
) *Server {
	// Controllers for features not yet fully implemented

	r := router.SetupRouter(cfg, authCtl, userCtl, gigCtl, messageCtl, chatCtl, notificationCtl, commonCtl, jwtService)

	return &Server{
		router:    r,
		config:    cfg,
		db:        db,
		cache:     cache,
		scheduler: scheduler,
	}
}

func (s *Server) Run(ctx context.Context) error {
	// 创建 HTTP 服务器
	s.httpServer = &http.Server{
		Addr:         ":" + s.config.Server.Port,
		Handler:      s.router,
		ReadTimeout:  s.config.Server.ReadTimeout,
		WriteTimeout: s.config.Server.WriteTimeout,
	}

	// 启动定时调度器
	go s.scheduler.Start()

	// 在 goroutine 中启动服务器
	errChan := make(chan error, 1)
	go func() {
		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			errChan <- err
		}
	}()

	// 等待 context 取消或服务器错误
	select {
	case <-ctx.Done():
		logger.Log.Info().Msg("收到关闭信号，开始优雅关闭...")
		return nil
	case err := <-errChan:
		return err
	}
}

func (s *Server) Shutdown(ctx context.Context) error {
	logger.Log.Info().Msg("开始关闭服务器组件...")

	// 停止调度器
	if s.scheduler != nil {
		logger.Log.Info().Msg("停止调度器...")
		s.scheduler.Stop()
	}

	// 关闭 HTTP 服务器
	if s.httpServer != nil {
		logger.Log.Info().Msg("关闭HTTP服务器...")
		if err := s.httpServer.Shutdown(ctx); err != nil {
			logger.Log.Error().Err(err).Msg("HTTP服务器关闭失败")
			return err
		}
	}

	// 关闭数据库连接
	if s.db != nil {
		logger.Log.Info().Msg("关闭数据库连接...")
		sqlDB, err := s.db.DB()
		if err != nil {
			logger.Log.Error().Err(err).Msg("获取数据库连接失败")
			return err
		}
		if err := sqlDB.Close(); err != nil {
			logger.Error("数据库连接关闭失败", err)
			return err
		}
	}

	// 关闭缓存连接
	if s.cache != nil {
		logger.Log.Info().Msg("关闭缓存连接...")
		if err := s.cache.Close(); err != nil {
			logger.Log.Error().Err(err).Msg("缓存连接关闭失败")
			return err
		}
	}

	logger.Log.Info().Msg("服务器所有组件已安全关闭")
	return nil
}

func (s *Server) Close() error {
	return s.Shutdown(context.Background())
}

package controller

import (
	"strconv"

	"bdb-backend/internal/service"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

type GigController struct {
	validator  validator.Validator
	gigService service.GigService
}

func NewGigController(validator validator.Validator, gigService service.GigService) *GigController {
	return &GigController{
		validator:  validator,
		gigService: gigService,
	}
}

// Publish 发布零工
func (c *GigController) Publish(ctx *gin.Context) {
	var req types.CreateGigReq
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	userID := GetUserID(ctx)
	if userID == 0 {
		return
	}

	gig, err := c.gigService.CreateGig(ctx, userID, &req)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, gig)
}

// Detail 获取零工详情
func (c *GigController) Detail(ctx *gin.Context) {
	gigID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(ctx, "Invalid gig ID")
		return
	}

	gig, err := c.gigService.GetGigByID(ctx, uint(gigID))
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 如果用户已登录，检查申请状态
	if userID := GetUserID(ctx); userID != 0 {
		hasApplied := c.gigService.CheckUserApplied(ctx, uint(gigID), userID)
		gig.HasApplied = hasApplied
	}

	response.OK(ctx, gig)
}

// List 获取零工列表
func (c *GigController) List(ctx *gin.Context) {
	var req types.GigListReq
	if !c.validator.CheckQuery(ctx, &req) {
		return
	}

	gigs, total, err := c.gigService.GetGigs(ctx, &req)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, response.Paginated(gigs, total, req.Page, req.PageSize))
}

// Delete 删除零工
func (c *GigController) Delete(ctx *gin.Context) {
	gigID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(ctx, "Invalid gig ID")
		return
	}

	userID := GetUserID(ctx)
	if userID == 0 {
		return
	}

	if err := c.gigService.DeleteGig(ctx, userID, uint(gigID)); err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, nil)
}

// ListManageableGigs 获取我发布的零工列表
func (c *GigController) ListManageableGigs(ctx *gin.Context) {
	userID := GetUserID(ctx)
	if userID == 0 {
		return
	}

	var req types.PaginationReq
	if !c.validator.CheckQuery(ctx, &req) {
		return
	}

	gigs, total, err := c.gigService.GetMyGigs(ctx, userID, &req)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, response.Paginated(gigs, total, req.Page, req.PageSize))
}

// ApplyForGig 申请零工
func (c *GigController) ApplyForGig(ctx *gin.Context) {
	var req types.ApplyGigReq
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	userID := GetUserID(ctx)
	if userID == 0 {
		return
	}

	application, err := c.gigService.ApplyForGig(ctx, userID, &req)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, application)
}

// ReviewApplication 审核零工申请
func (c *GigController) ReviewApplication(ctx *gin.Context) {
	var req types.ReviewApplicationReq
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	reviewerID := GetUserID(ctx)
	if reviewerID == 0 {
		return
	}

	err := c.gigService.ReviewApplication(ctx, reviewerID, &req)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, nil)
}

// GetUserApplications 获取用户的零工申请列表
func (c *GigController) GetUserApplications(ctx *gin.Context) {
	var req types.PaginationReq
	if !c.validator.CheckQuery(ctx, &req) {
		return
	}

	userID := GetUserID(ctx)
	if userID == 0 {
		return
	}

	applications, total, err := c.gigService.GetUserApplications(ctx, userID, &req)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, response.Paginated(applications, total, req.Page, req.PageSize))
}

// CheckApplicationStatus 检查用户是否已申请某个零工
func (c *GigController) CheckApplicationStatus(ctx *gin.Context) {
	gigID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(ctx, "Invalid gig ID")
		return
	}

	userID := GetUserID(ctx)

	hasApplied := c.gigService.CheckUserApplied(ctx, uint(gigID), userID)

	response.OK(ctx, map[string]interface{}{
		"has_applied": hasApplied,
		"gig_id":      gigID,
		"user_id":     userID,
	})
}

// GetGigApplications 获取某个零工的所有申请
func (c *GigController) GetGigApplications(ctx *gin.Context) {
	gigID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(ctx, "Invalid gig ID")
		return
	}

	var req types.PaginationReq
	if !c.validator.CheckQuery(ctx, &req) {
		return
	}

	applications, total, err := c.gigService.GetGigApplications(ctx, uint(gigID), &req)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, response.Paginated(applications, total, req.Page, req.PageSize))
}

// GetMonthlyStats 获取用户月度零工统计
func (c *GigController) GetMonthlyStats(ctx *gin.Context) {
	var req types.MonthlyStatsReq
	if !c.validator.CheckQuery(ctx, &req) {
		return
	}
	userID := GetUserID(ctx)
	if userID == 0 {
		return
	}

	stats, err := c.gigService.GetMonthlyStats(ctx, userID, &req)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, stats)
}

// GetDailyGigs 获取用户每日零工详情
func (c *GigController) GetDailyGigs(ctx *gin.Context) {
	var req types.DailyGigReq
	if !c.validator.CheckQuery(ctx, &req) {
		return
	}

	userID := GetUserID(ctx)
	if userID == 0 {
		return
	}

	gigs, err := c.gigService.GetDailyGigs(ctx, userID, &req)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}
	response.OK(ctx, gigs)
}

package controller

import (
	"bdb-backend/internal/service"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

// ChatController 聊天控制器
type ChatController struct {
	conversationService service.ConversationService
	validator           validator.Validator
}

// NewChatController 创建聊天控制器
func NewChatController(conversationService service.ConversationService, validator validator.Validator) *ChatController {
	return &ChatController{
		conversationService: conversationService,
		validator:           validator,
	}
}

// GetConversations 获取会话列表
func (c *ChatController) GetConversations(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.GetConversationListRequest
	if !c.validator.CheckQuery(ctx, &req) {
		return
	}

	req.UserID = userID

	resp, err := c.conversationService.GetUserConversations(ctx, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "获取会话列表失败", err, "user_id", userID)
		response.ServerError(ctx, "获取会话列表失败")
		return
	}

	response.OK(ctx, resp)
}

// GetConversationByID 获取指定会话详情
func (c *ChatController) GetConversationByID(ctx *gin.Context) {
	conversationID := ParseUintParam(ctx, "id")
	if conversationID == 0 {
		return
	}

	conversation, err := c.conversationService.GetConversationByID(ctx, conversationID)
	if err != nil {
		logger.ErrorCtx(ctx, "获取会话详情失败", err, "conversation_id", conversationID)
		response.ServerError(ctx, "获取会话详情失败")
		return
	}

	response.OK(ctx, conversation)
}

// CreateSingleConversation 创建单聊会话
func (c *ChatController) CreateSingleConversation(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req struct {
		UserID uint `json:"user_id" binding:"required" validate:"required,min=1"`
	}

	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	if userID == req.UserID {
		response.BadRequest(ctx, "不能和自己创建会话")
		return
	}

	conversation, err := c.conversationService.GetOrCreateSingleConversation(ctx, userID, req.UserID)
	if err != nil {
		logger.ErrorCtx(ctx, "创建单聊会话失败", err, "user_id", userID, "target_user_id", req.UserID)
		response.ServerError(ctx, "创建会话失败")
		return
	}

	response.OK(ctx, conversation)
}

// DeleteConversation 删除会话
func (c *ChatController) DeleteConversation(ctx *gin.Context) {
	userID := GetUserID(ctx)

	conversationID := ParseUintParam(ctx, "id")
	if conversationID == 0 {
		return
	}

	req := &types.DeleteConversationRequest{
		UserID:         userID,
		ConversationID: conversationID,
	}

	resp, err := c.conversationService.DeleteConversation(ctx, req)
	if err != nil {
		logger.ErrorCtx(ctx, "删除会话失败", err, "user_id", userID, "conversation_id", conversationID)
		response.ServerError(ctx, "删除会话失败")
		return
	}

	response.OK(ctx, resp)
}

// PinConversation 置顶/取消置顶会话
func (c *ChatController) PinConversation(ctx *gin.Context) {
	userID := GetUserID(ctx)

	conversationID := ParseUintParam(ctx, "id")
	if conversationID == 0 {
		return
	}

	var body struct {
		IsPinned bool `json:"is_pinned"`
	}

	if !c.validator.CheckJSON(ctx, &body) {
		return
	}

	req := &types.PinConversationRequest{
		UserID:         userID,
		ConversationID: conversationID,
		IsPinned:       body.IsPinned,
	}

	resp, err := c.conversationService.PinConversation(ctx, req)
	if err != nil {
		logger.ErrorCtx(ctx, "置顶会话失败", err, "user_id", userID, "conversation_id", conversationID, "is_pinned", body.IsPinned)
		response.ServerError(ctx, "置顶会话失败")
		return
	}

	response.OK(ctx, resp)
}

// MuteConversation 静音/取消静音会话
func (c *ChatController) MuteConversation(ctx *gin.Context) {
	userID := GetUserID(ctx)

	conversationID := ParseUintParam(ctx, "id")
	if conversationID == 0 {
		return
	}

	var body struct {
		IsMuted bool `json:"is_muted"`
	}

	if !c.validator.CheckJSON(ctx, &body) {
		return
	}

	req := &types.MuteConversationRequest{
		UserID:         userID,
		ConversationID: conversationID,
		IsMuted:        body.IsMuted,
	}

	resp, err := c.conversationService.MuteConversation(ctx, req)
	if err != nil {
		logger.ErrorCtx(ctx, "静音会话失败", err, "user_id", userID, "conversation_id", conversationID, "is_muted", body.IsMuted)
		response.ServerError(ctx, "静音会话失败")
		return
	}

	response.OK(ctx, resp)
}

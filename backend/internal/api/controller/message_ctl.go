package controller

import (
	"bdb-backend/internal/service"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

// MessageController 消息控制器
type MessageController struct {
	messageService service.MessageService
	validator      validator.Validator
}

// NewMessageController 创建消息控制器
func NewMessageController(messageService service.MessageService, validator validator.Validator) *MessageController {
	return &MessageController{
		messageService: messageService,
		validator:      validator,
	}
}

// SendMessage 发送消息
func (c *MessageController) SendMessage(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.SendMessageRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	req.SenderID = userID

	resp, err := c.messageService.SendMessage(ctx, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "发送消息失败", err, "user_id", userID, "receiver_id", req.ReceiverID)
		response.ServerError(ctx, "发送消息失败")
		return
	}

	response.OK(ctx, resp)
}

// GetMessages 获取消息列表
func (c *MessageController) GetMessages(ctx *gin.Context) {
	userID := GetUserID(ctx)

	conversationID := ParseUintParam(ctx, "id")
	if conversationID == 0 {
		return
	}

	var req types.GetMessageListRequest
	if !c.validator.CheckQuery(ctx, &req) {
		return
	}

	req.ConversationID = conversationID
	req.UserID = userID

	resp, err := c.messageService.GetMessageList(ctx, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "获取消息列表失败", err, "user_id", userID, "conversation_id", conversationID)
		response.ServerError(ctx, "获取消息列表失败")
		return
	}

	response.OK(ctx, resp)
}

// MarkMessagesAsRead 标记消息为已读
func (c *MessageController) MarkMessagesAsRead(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.MarkMessagesAsReadRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	req.UserID = userID

	resp, err := c.messageService.MarkMessagesAsRead(ctx, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "标记消息已读失败", err, "user_id", userID, "conversation_id", req.ConversationID)
		response.ServerError(ctx, "标记消息已读失败")
		return
	}

	response.OK(ctx, resp)
}

// RevokeMessage 撤回消息
func (c *MessageController) RevokeMessage(ctx *gin.Context) {
	userID := GetUserID(ctx)

	messageID := ParseUintParam(ctx, "id")
	if messageID == 0 {
		return
	}

	req := &types.RevokeMessageRequest{
		UserID:    userID,
		MessageID: messageID,
	}

	resp, err := c.messageService.RevokeMessage(ctx, req)
	if err != nil {
		logger.ErrorCtx(ctx, "撤回消息失败", err, "user_id", userID, "message_id", messageID)
		response.ServerError(ctx, "撤回消息失败")
		return
	}

	response.OK(ctx, resp)
}

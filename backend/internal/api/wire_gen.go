// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package api

import (
	"bdb-backend/internal/api/controller"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/scheduler"
	"bdb-backend/internal/service"
	"bdb-backend/internal/worker"
	"bdb-backend/pkg/config"
	"bdb-backend/pkg/jwt"
	"bdb-backend/pkg/storage"
	"bdb-backend/pkg/validator"
	"bdb-backend/pkg/wechat"
)

// Injectors from wire.go:

// BuildServer creates a new server with all its dependencies.
func BuildServer(cfg *config.Config) (*Server, error) {
	db := NewDB(cfg)
	cache := NewCache(cfg)
	userRepository := repository.NewUserRepository(db)
	jwtService := jwt.NewJWTService(cfg)
	client, err := wechat.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	authService := NewWechatAuthService(client)
	serviceAuthService := service.NewAuthService(userRepository, jwtService, authService, cache, cfg)
	validatorValidator := validator.NewValidator()
	authController := controller.NewAuthController(serviceAuthService, validatorValidator)
	userService := service.NewUserService(userRepository, authService)
	userController := controller.NewUserController(userService, validatorValidator)
	conversationRepository := repository.NewConversationRepository(db)
	userConversationRepository := repository.NewUserConversationRepository(db)
	messageRepository := repository.NewMessageRepository(db)
	conversationService := service.NewConversationService(conversationRepository, userConversationRepository, messageRepository, db)
	chatController := controller.NewChatController(conversationService, validatorValidator)
	messageService := service.NewMessageService(messageRepository, conversationRepository, userConversationRepository, conversationService, db)
	messageController := controller.NewMessageController(messageService, validatorValidator)
	notificationRepository := repository.NewNotificationRepository(db)
	notificationService := service.NewNotificationService(notificationRepository, conversationService, messageRepository, db)
	notificationController := controller.NewNotificationController(notificationService, validatorValidator)
	gigRepository := repository.NewGigRepository(db)
	gigService := service.NewGigService(gigRepository, userRepository)
	gigController := controller.NewGigController(validatorValidator, gigService)
	storage := NewStorage(cfg)
	commonController := controller.NewCommonController(storage)
	gigWorker := worker.NewGigWorker(gigService)
	job := scheduler.NewJob(gigWorker)
	schedulerScheduler, err := scheduler.NewScheduler(job)
	if err != nil {
		return nil, err
	}
	server := NewServer(cfg, db, cache, authController, userController, chatController, messageController, notificationController, gigController, commonController, schedulerScheduler, jwtService)
	return server, nil
}

// wire.go:

// Aliases for external constructors
var (
	// NewCentrifugoClient = centrifugo.NewClient
	NewJWTService   = jwt.NewJWTService
	NewValidator    = validator.NewValidator
	NewWechatClient = wechat.NewClient
)

func NewStorage(cfg *config.Config) storage.Storage {
	return storage.NewQiniuStorage(cfg.Storage)
}

// NewWechatAuthService 创建微信认证服务的provider
func NewWechatAuthService(client *wechat.Client) wechat.AuthService {
	return wechat.NewAuthService(client.GetMiniProgram())
}

// NewWechatPaymentService 创建微信支付服务的provider
func NewWechatPaymentService(client *wechat.Client) wechat.PaymentService {
	return wechat.NewPaymentService(client.GetMiniProgram())
}

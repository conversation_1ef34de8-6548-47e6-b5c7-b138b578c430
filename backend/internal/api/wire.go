//go:build wireinject

package api

import (
	"bdb-backend/internal/api/controller"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/scheduler"
	"bdb-backend/internal/service"
	"bdb-backend/internal/worker"
	"bdb-backend/pkg/storage"

	// "bdb-backend/pkg/centrifugo"

	"bdb-backend/pkg/config"
	"bdb-backend/pkg/jwt"
	"bdb-backend/pkg/validator"
	"bdb-backend/pkg/wechat"

	"github.com/google/wire"
)

// Aliases for external constructors
var (
	// NewCentrifugoClient = centrifugo.NewClient
	NewJWTService   = jwt.NewJWTService
	NewValidator    = validator.NewValidator
	NewWechatClient = wechat.NewClient
)

func NewStorage(cfg *config.Config) storage.Storage {
	return storage.NewQiniuStorage(cfg.Storage)
}

// NewWechatAuthService 创建微信认证服务的provider
func NewWechatAuthService(client *wechat.Client) wechat.AuthService {
	return wechat.NewAuthService(client.GetMiniProgram())
}

// NewWechatPaymentService 创建微信支付服务的provider
func NewWechatPaymentService(client *wechat.Client) wechat.PaymentService {
	return wechat.NewPaymentService(client.GetMiniProgram())
}

// BuildServer creates a new server with all its dependencies.
func BuildServer(cfg *config.Config) (*Server, error) {
	wire.Build(
		// Providers for config, database, and cache
		NewDB,
		NewCache,
		NewStorage,

		// External services
		// NewCentrifugoClient,
		NewJWTService,
		NewValidator,

		// Wechat services
		NewWechatClient,
		NewWechatAuthService,
		// NewWechatPaymentService, // 暂时注释，等有业务需要时再启用

		// Provider sets for repository, service, and controller layers
		repository.ProviderSet,
		service.ProviderSet,
		worker.ProviderSet,
		controller.ProviderSet,
		scheduler.ProviderSet,

		// The final server provider
		NewServer,
	)
	return &Server{}, nil
}

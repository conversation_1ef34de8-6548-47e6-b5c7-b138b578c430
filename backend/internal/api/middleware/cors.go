package middleware

import (
	"bdb-backend/pkg/logger"

	"github.com/gin-gonic/gin"
)

// CORS 配置跨域中间件
func CORS() gin.HandlerFunc {
	logger.Info("Initializing CORS middleware")

	return func(c *gin.Context) {
		// 记录跨域请求
		if c.Request.Method == "OPTIONS" {
			logger.Info("CORS preflight request",
				"origin", c.Request.Header.Get("Origin"),
				"method", c.Request.Header.Get("Access-Control-Request-Method"),
			)
		}

		// 允许的源
		c.Header("Access-Control-Allow-Origin", "*")

		// 允许的请求方法
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, PATCH, DELETE, HEAD, OPTIONS")

		// 允许的请求头
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Length, Content-Type, Authorization, X-Requested-With")

		// 暴露的响应头
		c.Header("Access-Control-Expose-Headers", "Content-Length")

		// 允许凭证
		c.Header("Access-Control-Allow-Credentials", "true")

		// 缓存预检请求结果
		c.Header("Access-Control-Max-Age", "43200")

		// 预检请求直接返回
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(200)
			return
		}

		c.Next()
	}
}

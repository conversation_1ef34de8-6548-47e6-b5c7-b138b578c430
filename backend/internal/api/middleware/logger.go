package middleware

import (
	"bdb-backend/pkg/logger"
	"time"

	"github.com/gin-gonic/gin"
)

// Logger 日志中间件
func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 记录请求日志
		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()

		if raw != "" {
			path = path + "?" + raw
		}

		// 记录HTTP请求日志
		logger.InfoCtx(c, "HTTP Request",
			"method", method,
			"path", path,
			"client_ip", clientIP,
			"status_code", statusCode,
			"latency", latency,
		)

		// 如果有错误，记录错误信息
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				logger.ErrorCtx(c, "Request processing error", err.Err,
					"error_type", err.Type,
				)
			}
		}
	}
}

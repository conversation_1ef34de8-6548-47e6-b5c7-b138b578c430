package middleware

import (
	"bdb-backend/pkg/jwt"
	"bdb-backend/pkg/logger"
	"bdb-backend/pkg/response"
	"strings"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware creates a new JWT authentication middleware.
func AuthMiddleware(jwtService jwt.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.Get<PERSON>eader("Authorization")
		if authHeader == "" {
			response.Unauthorized(c, "Authorization header is missing")
			c.Abort()
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			response.Unauthorized(c, "Invalid token format")
			c.Abort()
			return
		}

		tokenString := parts[1]
		claims, err := jwtService.ValidateUserToken(tokenString)
		if err != nil {
			logger.WarnCtx(c.Request.Context(), "Token validation failed",
				"error", err.Error(),
				"ip", c.<PERSON>(),
				"user_agent", c<PERSON>("User-Agent"))
			response.Unauthorized(c, "Invalid or expired token")
			c.Abort()
			return
		}

		// 验证设备ID（如果token包含设备ID）
		if claims.DeviceID != "" {
			clientDeviceID := c.GetHeader("X-Device-ID")
			if clientDeviceID == "" {
				logger.WarnCtx(c.Request.Context(), "Missing device ID header",
					"user_id", claims.UserID,
					"ip", c.ClientIP())
				response.Unauthorized(c, "Device ID header is missing")
				c.Abort()
				return
			}

			if claims.DeviceID != clientDeviceID {
				logger.WarnCtx(c.Request.Context(), "Device ID mismatch - possible token theft",
					"user_id", claims.UserID,
					"token_device_id", claims.DeviceID,
					"client_device_id", clientDeviceID,
					"ip", c.ClientIP())
				response.Unauthorized(c, "Device ID mismatch - possible token theft")
				c.Abort()
				return
			}
		}

		// Set user information in context for downstream handlers
		c.Set("user_id", claims.UserID)
		c.Set("device_id", claims.DeviceID)

		c.Next()
	}
}

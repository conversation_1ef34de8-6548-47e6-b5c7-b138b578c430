// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: api.proto

package apiproto

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CentrifugoApi_Batch_FullMethodName                = "/centrifugal.centrifugo.api.CentrifugoApi/Batch"
	CentrifugoApi_Publish_FullMethodName              = "/centrifugal.centrifugo.api.CentrifugoApi/Publish"
	CentrifugoApi_Broadcast_FullMethodName            = "/centrifugal.centrifugo.api.CentrifugoApi/Broadcast"
	CentrifugoApi_Subscribe_FullMethodName            = "/centrifugal.centrifugo.api.CentrifugoApi/Subscribe"
	CentrifugoApi_Unsubscribe_FullMethodName          = "/centrifugal.centrifugo.api.CentrifugoApi/Unsubscribe"
	CentrifugoApi_Disconnect_FullMethodName           = "/centrifugal.centrifugo.api.CentrifugoApi/Disconnect"
	CentrifugoApi_Presence_FullMethodName             = "/centrifugal.centrifugo.api.CentrifugoApi/Presence"
	CentrifugoApi_PresenceStats_FullMethodName        = "/centrifugal.centrifugo.api.CentrifugoApi/PresenceStats"
	CentrifugoApi_History_FullMethodName              = "/centrifugal.centrifugo.api.CentrifugoApi/History"
	CentrifugoApi_HistoryRemove_FullMethodName        = "/centrifugal.centrifugo.api.CentrifugoApi/HistoryRemove"
	CentrifugoApi_Info_FullMethodName                 = "/centrifugal.centrifugo.api.CentrifugoApi/Info"
	CentrifugoApi_RPC_FullMethodName                  = "/centrifugal.centrifugo.api.CentrifugoApi/RPC"
	CentrifugoApi_Refresh_FullMethodName              = "/centrifugal.centrifugo.api.CentrifugoApi/Refresh"
	CentrifugoApi_Channels_FullMethodName             = "/centrifugal.centrifugo.api.CentrifugoApi/Channels"
	CentrifugoApi_Connections_FullMethodName          = "/centrifugal.centrifugo.api.CentrifugoApi/Connections"
	CentrifugoApi_UpdateUserStatus_FullMethodName     = "/centrifugal.centrifugo.api.CentrifugoApi/UpdateUserStatus"
	CentrifugoApi_GetUserStatus_FullMethodName        = "/centrifugal.centrifugo.api.CentrifugoApi/GetUserStatus"
	CentrifugoApi_DeleteUserStatus_FullMethodName     = "/centrifugal.centrifugo.api.CentrifugoApi/DeleteUserStatus"
	CentrifugoApi_BlockUser_FullMethodName            = "/centrifugal.centrifugo.api.CentrifugoApi/BlockUser"
	CentrifugoApi_UnblockUser_FullMethodName          = "/centrifugal.centrifugo.api.CentrifugoApi/UnblockUser"
	CentrifugoApi_RevokeToken_FullMethodName          = "/centrifugal.centrifugo.api.CentrifugoApi/RevokeToken"
	CentrifugoApi_InvalidateUserTokens_FullMethodName = "/centrifugal.centrifugo.api.CentrifugoApi/InvalidateUserTokens"
	CentrifugoApi_DeviceRegister_FullMethodName       = "/centrifugal.centrifugo.api.CentrifugoApi/DeviceRegister"
	CentrifugoApi_DeviceUpdate_FullMethodName         = "/centrifugal.centrifugo.api.CentrifugoApi/DeviceUpdate"
	CentrifugoApi_DeviceRemove_FullMethodName         = "/centrifugal.centrifugo.api.CentrifugoApi/DeviceRemove"
	CentrifugoApi_DeviceList_FullMethodName           = "/centrifugal.centrifugo.api.CentrifugoApi/DeviceList"
	CentrifugoApi_DeviceTopicList_FullMethodName      = "/centrifugal.centrifugo.api.CentrifugoApi/DeviceTopicList"
	CentrifugoApi_DeviceTopicUpdate_FullMethodName    = "/centrifugal.centrifugo.api.CentrifugoApi/DeviceTopicUpdate"
	CentrifugoApi_UserTopicList_FullMethodName        = "/centrifugal.centrifugo.api.CentrifugoApi/UserTopicList"
	CentrifugoApi_UserTopicUpdate_FullMethodName      = "/centrifugal.centrifugo.api.CentrifugoApi/UserTopicUpdate"
	CentrifugoApi_SendPushNotification_FullMethodName = "/centrifugal.centrifugo.api.CentrifugoApi/SendPushNotification"
	CentrifugoApi_UpdatePushStatus_FullMethodName     = "/centrifugal.centrifugo.api.CentrifugoApi/UpdatePushStatus"
	CentrifugoApi_CancelPush_FullMethodName           = "/centrifugal.centrifugo.api.CentrifugoApi/CancelPush"
)

// CentrifugoApiClient is the client API for CentrifugoApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CentrifugoApiClient interface {
	Batch(ctx context.Context, in *BatchRequest, opts ...grpc.CallOption) (*BatchResponse, error)
	Publish(ctx context.Context, in *PublishRequest, opts ...grpc.CallOption) (*PublishResponse, error)
	Broadcast(ctx context.Context, in *BroadcastRequest, opts ...grpc.CallOption) (*BroadcastResponse, error)
	Subscribe(ctx context.Context, in *SubscribeRequest, opts ...grpc.CallOption) (*SubscribeResponse, error)
	Unsubscribe(ctx context.Context, in *UnsubscribeRequest, opts ...grpc.CallOption) (*UnsubscribeResponse, error)
	Disconnect(ctx context.Context, in *DisconnectRequest, opts ...grpc.CallOption) (*DisconnectResponse, error)
	Presence(ctx context.Context, in *PresenceRequest, opts ...grpc.CallOption) (*PresenceResponse, error)
	PresenceStats(ctx context.Context, in *PresenceStatsRequest, opts ...grpc.CallOption) (*PresenceStatsResponse, error)
	History(ctx context.Context, in *HistoryRequest, opts ...grpc.CallOption) (*HistoryResponse, error)
	HistoryRemove(ctx context.Context, in *HistoryRemoveRequest, opts ...grpc.CallOption) (*HistoryRemoveResponse, error)
	Info(ctx context.Context, in *InfoRequest, opts ...grpc.CallOption) (*InfoResponse, error)
	RPC(ctx context.Context, in *RPCRequest, opts ...grpc.CallOption) (*RPCResponse, error)
	Refresh(ctx context.Context, in *RefreshRequest, opts ...grpc.CallOption) (*RefreshResponse, error)
	Channels(ctx context.Context, in *ChannelsRequest, opts ...grpc.CallOption) (*ChannelsResponse, error)
	Connections(ctx context.Context, in *ConnectionsRequest, opts ...grpc.CallOption) (*ConnectionsResponse, error)
	UpdateUserStatus(ctx context.Context, in *UpdateUserStatusRequest, opts ...grpc.CallOption) (*UpdateUserStatusResponse, error)
	GetUserStatus(ctx context.Context, in *GetUserStatusRequest, opts ...grpc.CallOption) (*GetUserStatusResponse, error)
	DeleteUserStatus(ctx context.Context, in *DeleteUserStatusRequest, opts ...grpc.CallOption) (*DeleteUserStatusResponse, error)
	BlockUser(ctx context.Context, in *BlockUserRequest, opts ...grpc.CallOption) (*BlockUserResponse, error)
	UnblockUser(ctx context.Context, in *UnblockUserRequest, opts ...grpc.CallOption) (*UnblockUserResponse, error)
	RevokeToken(ctx context.Context, in *RevokeTokenRequest, opts ...grpc.CallOption) (*RevokeTokenResponse, error)
	InvalidateUserTokens(ctx context.Context, in *InvalidateUserTokensRequest, opts ...grpc.CallOption) (*InvalidateUserTokensResponse, error)
	DeviceRegister(ctx context.Context, in *DeviceRegisterRequest, opts ...grpc.CallOption) (*DeviceRegisterResponse, error)
	DeviceUpdate(ctx context.Context, in *DeviceUpdateRequest, opts ...grpc.CallOption) (*DeviceUpdateResponse, error)
	DeviceRemove(ctx context.Context, in *DeviceRemoveRequest, opts ...grpc.CallOption) (*DeviceRemoveResponse, error)
	DeviceList(ctx context.Context, in *DeviceListRequest, opts ...grpc.CallOption) (*DeviceListResponse, error)
	DeviceTopicList(ctx context.Context, in *DeviceTopicListRequest, opts ...grpc.CallOption) (*DeviceTopicListResponse, error)
	DeviceTopicUpdate(ctx context.Context, in *DeviceTopicUpdateRequest, opts ...grpc.CallOption) (*DeviceTopicUpdateResponse, error)
	UserTopicList(ctx context.Context, in *UserTopicListRequest, opts ...grpc.CallOption) (*UserTopicListResponse, error)
	UserTopicUpdate(ctx context.Context, in *UserTopicUpdateRequest, opts ...grpc.CallOption) (*UserTopicUpdateResponse, error)
	SendPushNotification(ctx context.Context, in *SendPushNotificationRequest, opts ...grpc.CallOption) (*SendPushNotificationResponse, error)
	UpdatePushStatus(ctx context.Context, in *UpdatePushStatusRequest, opts ...grpc.CallOption) (*UpdatePushStatusResponse, error)
	CancelPush(ctx context.Context, in *CancelPushRequest, opts ...grpc.CallOption) (*CancelPushResponse, error)
}

type centrifugoApiClient struct {
	cc grpc.ClientConnInterface
}

func NewCentrifugoApiClient(cc grpc.ClientConnInterface) CentrifugoApiClient {
	return &centrifugoApiClient{cc}
}

func (c *centrifugoApiClient) Batch(ctx context.Context, in *BatchRequest, opts ...grpc.CallOption) (*BatchResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_Batch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) Publish(ctx context.Context, in *PublishRequest, opts ...grpc.CallOption) (*PublishResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PublishResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_Publish_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) Broadcast(ctx context.Context, in *BroadcastRequest, opts ...grpc.CallOption) (*BroadcastResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BroadcastResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_Broadcast_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) Subscribe(ctx context.Context, in *SubscribeRequest, opts ...grpc.CallOption) (*SubscribeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SubscribeResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_Subscribe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) Unsubscribe(ctx context.Context, in *UnsubscribeRequest, opts ...grpc.CallOption) (*UnsubscribeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UnsubscribeResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_Unsubscribe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) Disconnect(ctx context.Context, in *DisconnectRequest, opts ...grpc.CallOption) (*DisconnectResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DisconnectResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_Disconnect_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) Presence(ctx context.Context, in *PresenceRequest, opts ...grpc.CallOption) (*PresenceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PresenceResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_Presence_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) PresenceStats(ctx context.Context, in *PresenceStatsRequest, opts ...grpc.CallOption) (*PresenceStatsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PresenceStatsResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_PresenceStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) History(ctx context.Context, in *HistoryRequest, opts ...grpc.CallOption) (*HistoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HistoryResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_History_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) HistoryRemove(ctx context.Context, in *HistoryRemoveRequest, opts ...grpc.CallOption) (*HistoryRemoveResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HistoryRemoveResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_HistoryRemove_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) Info(ctx context.Context, in *InfoRequest, opts ...grpc.CallOption) (*InfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InfoResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_Info_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) RPC(ctx context.Context, in *RPCRequest, opts ...grpc.CallOption) (*RPCResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RPCResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_RPC_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) Refresh(ctx context.Context, in *RefreshRequest, opts ...grpc.CallOption) (*RefreshResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefreshResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_Refresh_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) Channels(ctx context.Context, in *ChannelsRequest, opts ...grpc.CallOption) (*ChannelsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChannelsResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_Channels_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) Connections(ctx context.Context, in *ConnectionsRequest, opts ...grpc.CallOption) (*ConnectionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConnectionsResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_Connections_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) UpdateUserStatus(ctx context.Context, in *UpdateUserStatusRequest, opts ...grpc.CallOption) (*UpdateUserStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateUserStatusResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_UpdateUserStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) GetUserStatus(ctx context.Context, in *GetUserStatusRequest, opts ...grpc.CallOption) (*GetUserStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserStatusResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_GetUserStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) DeleteUserStatus(ctx context.Context, in *DeleteUserStatusRequest, opts ...grpc.CallOption) (*DeleteUserStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteUserStatusResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_DeleteUserStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) BlockUser(ctx context.Context, in *BlockUserRequest, opts ...grpc.CallOption) (*BlockUserResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlockUserResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_BlockUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) UnblockUser(ctx context.Context, in *UnblockUserRequest, opts ...grpc.CallOption) (*UnblockUserResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UnblockUserResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_UnblockUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) RevokeToken(ctx context.Context, in *RevokeTokenRequest, opts ...grpc.CallOption) (*RevokeTokenResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RevokeTokenResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_RevokeToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) InvalidateUserTokens(ctx context.Context, in *InvalidateUserTokensRequest, opts ...grpc.CallOption) (*InvalidateUserTokensResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InvalidateUserTokensResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_InvalidateUserTokens_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) DeviceRegister(ctx context.Context, in *DeviceRegisterRequest, opts ...grpc.CallOption) (*DeviceRegisterResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceRegisterResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_DeviceRegister_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) DeviceUpdate(ctx context.Context, in *DeviceUpdateRequest, opts ...grpc.CallOption) (*DeviceUpdateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceUpdateResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_DeviceUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) DeviceRemove(ctx context.Context, in *DeviceRemoveRequest, opts ...grpc.CallOption) (*DeviceRemoveResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceRemoveResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_DeviceRemove_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) DeviceList(ctx context.Context, in *DeviceListRequest, opts ...grpc.CallOption) (*DeviceListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceListResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_DeviceList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) DeviceTopicList(ctx context.Context, in *DeviceTopicListRequest, opts ...grpc.CallOption) (*DeviceTopicListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceTopicListResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_DeviceTopicList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) DeviceTopicUpdate(ctx context.Context, in *DeviceTopicUpdateRequest, opts ...grpc.CallOption) (*DeviceTopicUpdateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceTopicUpdateResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_DeviceTopicUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) UserTopicList(ctx context.Context, in *UserTopicListRequest, opts ...grpc.CallOption) (*UserTopicListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserTopicListResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_UserTopicList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) UserTopicUpdate(ctx context.Context, in *UserTopicUpdateRequest, opts ...grpc.CallOption) (*UserTopicUpdateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserTopicUpdateResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_UserTopicUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) SendPushNotification(ctx context.Context, in *SendPushNotificationRequest, opts ...grpc.CallOption) (*SendPushNotificationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendPushNotificationResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_SendPushNotification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) UpdatePushStatus(ctx context.Context, in *UpdatePushStatusRequest, opts ...grpc.CallOption) (*UpdatePushStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdatePushStatusResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_UpdatePushStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *centrifugoApiClient) CancelPush(ctx context.Context, in *CancelPushRequest, opts ...grpc.CallOption) (*CancelPushResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelPushResponse)
	err := c.cc.Invoke(ctx, CentrifugoApi_CancelPush_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CentrifugoApiServer is the server API for CentrifugoApi service.
// All implementations must embed UnimplementedCentrifugoApiServer
// for forward compatibility.
type CentrifugoApiServer interface {
	Batch(context.Context, *BatchRequest) (*BatchResponse, error)
	Publish(context.Context, *PublishRequest) (*PublishResponse, error)
	Broadcast(context.Context, *BroadcastRequest) (*BroadcastResponse, error)
	Subscribe(context.Context, *SubscribeRequest) (*SubscribeResponse, error)
	Unsubscribe(context.Context, *UnsubscribeRequest) (*UnsubscribeResponse, error)
	Disconnect(context.Context, *DisconnectRequest) (*DisconnectResponse, error)
	Presence(context.Context, *PresenceRequest) (*PresenceResponse, error)
	PresenceStats(context.Context, *PresenceStatsRequest) (*PresenceStatsResponse, error)
	History(context.Context, *HistoryRequest) (*HistoryResponse, error)
	HistoryRemove(context.Context, *HistoryRemoveRequest) (*HistoryRemoveResponse, error)
	Info(context.Context, *InfoRequest) (*InfoResponse, error)
	RPC(context.Context, *RPCRequest) (*RPCResponse, error)
	Refresh(context.Context, *RefreshRequest) (*RefreshResponse, error)
	Channels(context.Context, *ChannelsRequest) (*ChannelsResponse, error)
	Connections(context.Context, *ConnectionsRequest) (*ConnectionsResponse, error)
	UpdateUserStatus(context.Context, *UpdateUserStatusRequest) (*UpdateUserStatusResponse, error)
	GetUserStatus(context.Context, *GetUserStatusRequest) (*GetUserStatusResponse, error)
	DeleteUserStatus(context.Context, *DeleteUserStatusRequest) (*DeleteUserStatusResponse, error)
	BlockUser(context.Context, *BlockUserRequest) (*BlockUserResponse, error)
	UnblockUser(context.Context, *UnblockUserRequest) (*UnblockUserResponse, error)
	RevokeToken(context.Context, *RevokeTokenRequest) (*RevokeTokenResponse, error)
	InvalidateUserTokens(context.Context, *InvalidateUserTokensRequest) (*InvalidateUserTokensResponse, error)
	DeviceRegister(context.Context, *DeviceRegisterRequest) (*DeviceRegisterResponse, error)
	DeviceUpdate(context.Context, *DeviceUpdateRequest) (*DeviceUpdateResponse, error)
	DeviceRemove(context.Context, *DeviceRemoveRequest) (*DeviceRemoveResponse, error)
	DeviceList(context.Context, *DeviceListRequest) (*DeviceListResponse, error)
	DeviceTopicList(context.Context, *DeviceTopicListRequest) (*DeviceTopicListResponse, error)
	DeviceTopicUpdate(context.Context, *DeviceTopicUpdateRequest) (*DeviceTopicUpdateResponse, error)
	UserTopicList(context.Context, *UserTopicListRequest) (*UserTopicListResponse, error)
	UserTopicUpdate(context.Context, *UserTopicUpdateRequest) (*UserTopicUpdateResponse, error)
	SendPushNotification(context.Context, *SendPushNotificationRequest) (*SendPushNotificationResponse, error)
	UpdatePushStatus(context.Context, *UpdatePushStatusRequest) (*UpdatePushStatusResponse, error)
	CancelPush(context.Context, *CancelPushRequest) (*CancelPushResponse, error)
	mustEmbedUnimplementedCentrifugoApiServer()
}

// UnimplementedCentrifugoApiServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCentrifugoApiServer struct{}

func (UnimplementedCentrifugoApiServer) Batch(context.Context, *BatchRequest) (*BatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Batch not implemented")
}
func (UnimplementedCentrifugoApiServer) Publish(context.Context, *PublishRequest) (*PublishResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Publish not implemented")
}
func (UnimplementedCentrifugoApiServer) Broadcast(context.Context, *BroadcastRequest) (*BroadcastResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Broadcast not implemented")
}
func (UnimplementedCentrifugoApiServer) Subscribe(context.Context, *SubscribeRequest) (*SubscribeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Subscribe not implemented")
}
func (UnimplementedCentrifugoApiServer) Unsubscribe(context.Context, *UnsubscribeRequest) (*UnsubscribeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Unsubscribe not implemented")
}
func (UnimplementedCentrifugoApiServer) Disconnect(context.Context, *DisconnectRequest) (*DisconnectResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Disconnect not implemented")
}
func (UnimplementedCentrifugoApiServer) Presence(context.Context, *PresenceRequest) (*PresenceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Presence not implemented")
}
func (UnimplementedCentrifugoApiServer) PresenceStats(context.Context, *PresenceStatsRequest) (*PresenceStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PresenceStats not implemented")
}
func (UnimplementedCentrifugoApiServer) History(context.Context, *HistoryRequest) (*HistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method History not implemented")
}
func (UnimplementedCentrifugoApiServer) HistoryRemove(context.Context, *HistoryRemoveRequest) (*HistoryRemoveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HistoryRemove not implemented")
}
func (UnimplementedCentrifugoApiServer) Info(context.Context, *InfoRequest) (*InfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Info not implemented")
}
func (UnimplementedCentrifugoApiServer) RPC(context.Context, *RPCRequest) (*RPCResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RPC not implemented")
}
func (UnimplementedCentrifugoApiServer) Refresh(context.Context, *RefreshRequest) (*RefreshResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Refresh not implemented")
}
func (UnimplementedCentrifugoApiServer) Channels(context.Context, *ChannelsRequest) (*ChannelsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Channels not implemented")
}
func (UnimplementedCentrifugoApiServer) Connections(context.Context, *ConnectionsRequest) (*ConnectionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Connections not implemented")
}
func (UnimplementedCentrifugoApiServer) UpdateUserStatus(context.Context, *UpdateUserStatusRequest) (*UpdateUserStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserStatus not implemented")
}
func (UnimplementedCentrifugoApiServer) GetUserStatus(context.Context, *GetUserStatusRequest) (*GetUserStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserStatus not implemented")
}
func (UnimplementedCentrifugoApiServer) DeleteUserStatus(context.Context, *DeleteUserStatusRequest) (*DeleteUserStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserStatus not implemented")
}
func (UnimplementedCentrifugoApiServer) BlockUser(context.Context, *BlockUserRequest) (*BlockUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BlockUser not implemented")
}
func (UnimplementedCentrifugoApiServer) UnblockUser(context.Context, *UnblockUserRequest) (*UnblockUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnblockUser not implemented")
}
func (UnimplementedCentrifugoApiServer) RevokeToken(context.Context, *RevokeTokenRequest) (*RevokeTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RevokeToken not implemented")
}
func (UnimplementedCentrifugoApiServer) InvalidateUserTokens(context.Context, *InvalidateUserTokensRequest) (*InvalidateUserTokensResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvalidateUserTokens not implemented")
}
func (UnimplementedCentrifugoApiServer) DeviceRegister(context.Context, *DeviceRegisterRequest) (*DeviceRegisterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceRegister not implemented")
}
func (UnimplementedCentrifugoApiServer) DeviceUpdate(context.Context, *DeviceUpdateRequest) (*DeviceUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceUpdate not implemented")
}
func (UnimplementedCentrifugoApiServer) DeviceRemove(context.Context, *DeviceRemoveRequest) (*DeviceRemoveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceRemove not implemented")
}
func (UnimplementedCentrifugoApiServer) DeviceList(context.Context, *DeviceListRequest) (*DeviceListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceList not implemented")
}
func (UnimplementedCentrifugoApiServer) DeviceTopicList(context.Context, *DeviceTopicListRequest) (*DeviceTopicListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceTopicList not implemented")
}
func (UnimplementedCentrifugoApiServer) DeviceTopicUpdate(context.Context, *DeviceTopicUpdateRequest) (*DeviceTopicUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceTopicUpdate not implemented")
}
func (UnimplementedCentrifugoApiServer) UserTopicList(context.Context, *UserTopicListRequest) (*UserTopicListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserTopicList not implemented")
}
func (UnimplementedCentrifugoApiServer) UserTopicUpdate(context.Context, *UserTopicUpdateRequest) (*UserTopicUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserTopicUpdate not implemented")
}
func (UnimplementedCentrifugoApiServer) SendPushNotification(context.Context, *SendPushNotificationRequest) (*SendPushNotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendPushNotification not implemented")
}
func (UnimplementedCentrifugoApiServer) UpdatePushStatus(context.Context, *UpdatePushStatusRequest) (*UpdatePushStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePushStatus not implemented")
}
func (UnimplementedCentrifugoApiServer) CancelPush(context.Context, *CancelPushRequest) (*CancelPushResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelPush not implemented")
}
func (UnimplementedCentrifugoApiServer) mustEmbedUnimplementedCentrifugoApiServer() {}
func (UnimplementedCentrifugoApiServer) testEmbeddedByValue()                       {}

// UnsafeCentrifugoApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CentrifugoApiServer will
// result in compilation errors.
type UnsafeCentrifugoApiServer interface {
	mustEmbedUnimplementedCentrifugoApiServer()
}

func RegisterCentrifugoApiServer(s grpc.ServiceRegistrar, srv CentrifugoApiServer) {
	// If the following call pancis, it indicates UnimplementedCentrifugoApiServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CentrifugoApi_ServiceDesc, srv)
}

func _CentrifugoApi_Batch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).Batch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_Batch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).Batch(ctx, req.(*BatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_Publish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).Publish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_Publish_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).Publish(ctx, req.(*PublishRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_Broadcast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BroadcastRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).Broadcast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_Broadcast_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).Broadcast(ctx, req.(*BroadcastRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_Subscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).Subscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_Subscribe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).Subscribe(ctx, req.(*SubscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_Unsubscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnsubscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).Unsubscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_Unsubscribe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).Unsubscribe(ctx, req.(*UnsubscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_Disconnect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisconnectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).Disconnect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_Disconnect_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).Disconnect(ctx, req.(*DisconnectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_Presence_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PresenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).Presence(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_Presence_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).Presence(ctx, req.(*PresenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_PresenceStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PresenceStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).PresenceStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_PresenceStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).PresenceStats(ctx, req.(*PresenceStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_History_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).History(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_History_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).History(ctx, req.(*HistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_HistoryRemove_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HistoryRemoveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).HistoryRemove(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_HistoryRemove_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).HistoryRemove(ctx, req.(*HistoryRemoveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_Info_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).Info(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_Info_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).Info(ctx, req.(*InfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_RPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RPCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).RPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_RPC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).RPC(ctx, req.(*RPCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_Refresh_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).Refresh(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_Refresh_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).Refresh(ctx, req.(*RefreshRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_Channels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).Channels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_Channels_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).Channels(ctx, req.(*ChannelsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_Connections_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConnectionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).Connections(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_Connections_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).Connections(ctx, req.(*ConnectionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_UpdateUserStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).UpdateUserStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_UpdateUserStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).UpdateUserStatus(ctx, req.(*UpdateUserStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_GetUserStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).GetUserStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_GetUserStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).GetUserStatus(ctx, req.(*GetUserStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_DeleteUserStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).DeleteUserStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_DeleteUserStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).DeleteUserStatus(ctx, req.(*DeleteUserStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_BlockUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).BlockUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_BlockUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).BlockUser(ctx, req.(*BlockUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_UnblockUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnblockUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).UnblockUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_UnblockUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).UnblockUser(ctx, req.(*UnblockUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_RevokeToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).RevokeToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_RevokeToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).RevokeToken(ctx, req.(*RevokeTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_InvalidateUserTokens_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvalidateUserTokensRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).InvalidateUserTokens(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_InvalidateUserTokens_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).InvalidateUserTokens(ctx, req.(*InvalidateUserTokensRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_DeviceRegister_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRegisterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).DeviceRegister(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_DeviceRegister_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).DeviceRegister(ctx, req.(*DeviceRegisterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_DeviceUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).DeviceUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_DeviceUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).DeviceUpdate(ctx, req.(*DeviceUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_DeviceRemove_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRemoveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).DeviceRemove(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_DeviceRemove_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).DeviceRemove(ctx, req.(*DeviceRemoveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_DeviceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).DeviceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_DeviceList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).DeviceList(ctx, req.(*DeviceListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_DeviceTopicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceTopicListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).DeviceTopicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_DeviceTopicList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).DeviceTopicList(ctx, req.(*DeviceTopicListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_DeviceTopicUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceTopicUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).DeviceTopicUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_DeviceTopicUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).DeviceTopicUpdate(ctx, req.(*DeviceTopicUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_UserTopicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserTopicListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).UserTopicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_UserTopicList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).UserTopicList(ctx, req.(*UserTopicListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_UserTopicUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserTopicUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).UserTopicUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_UserTopicUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).UserTopicUpdate(ctx, req.(*UserTopicUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_SendPushNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPushNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).SendPushNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_SendPushNotification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).SendPushNotification(ctx, req.(*SendPushNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_UpdatePushStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePushStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).UpdatePushStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_UpdatePushStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).UpdatePushStatus(ctx, req.(*UpdatePushStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CentrifugoApi_CancelPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelPushRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CentrifugoApiServer).CancelPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CentrifugoApi_CancelPush_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CentrifugoApiServer).CancelPush(ctx, req.(*CancelPushRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CentrifugoApi_ServiceDesc is the grpc.ServiceDesc for CentrifugoApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CentrifugoApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "centrifugal.centrifugo.api.CentrifugoApi",
	HandlerType: (*CentrifugoApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Batch",
			Handler:    _CentrifugoApi_Batch_Handler,
		},
		{
			MethodName: "Publish",
			Handler:    _CentrifugoApi_Publish_Handler,
		},
		{
			MethodName: "Broadcast",
			Handler:    _CentrifugoApi_Broadcast_Handler,
		},
		{
			MethodName: "Subscribe",
			Handler:    _CentrifugoApi_Subscribe_Handler,
		},
		{
			MethodName: "Unsubscribe",
			Handler:    _CentrifugoApi_Unsubscribe_Handler,
		},
		{
			MethodName: "Disconnect",
			Handler:    _CentrifugoApi_Disconnect_Handler,
		},
		{
			MethodName: "Presence",
			Handler:    _CentrifugoApi_Presence_Handler,
		},
		{
			MethodName: "PresenceStats",
			Handler:    _CentrifugoApi_PresenceStats_Handler,
		},
		{
			MethodName: "History",
			Handler:    _CentrifugoApi_History_Handler,
		},
		{
			MethodName: "HistoryRemove",
			Handler:    _CentrifugoApi_HistoryRemove_Handler,
		},
		{
			MethodName: "Info",
			Handler:    _CentrifugoApi_Info_Handler,
		},
		{
			MethodName: "RPC",
			Handler:    _CentrifugoApi_RPC_Handler,
		},
		{
			MethodName: "Refresh",
			Handler:    _CentrifugoApi_Refresh_Handler,
		},
		{
			MethodName: "Channels",
			Handler:    _CentrifugoApi_Channels_Handler,
		},
		{
			MethodName: "Connections",
			Handler:    _CentrifugoApi_Connections_Handler,
		},
		{
			MethodName: "UpdateUserStatus",
			Handler:    _CentrifugoApi_UpdateUserStatus_Handler,
		},
		{
			MethodName: "GetUserStatus",
			Handler:    _CentrifugoApi_GetUserStatus_Handler,
		},
		{
			MethodName: "DeleteUserStatus",
			Handler:    _CentrifugoApi_DeleteUserStatus_Handler,
		},
		{
			MethodName: "BlockUser",
			Handler:    _CentrifugoApi_BlockUser_Handler,
		},
		{
			MethodName: "UnblockUser",
			Handler:    _CentrifugoApi_UnblockUser_Handler,
		},
		{
			MethodName: "RevokeToken",
			Handler:    _CentrifugoApi_RevokeToken_Handler,
		},
		{
			MethodName: "InvalidateUserTokens",
			Handler:    _CentrifugoApi_InvalidateUserTokens_Handler,
		},
		{
			MethodName: "DeviceRegister",
			Handler:    _CentrifugoApi_DeviceRegister_Handler,
		},
		{
			MethodName: "DeviceUpdate",
			Handler:    _CentrifugoApi_DeviceUpdate_Handler,
		},
		{
			MethodName: "DeviceRemove",
			Handler:    _CentrifugoApi_DeviceRemove_Handler,
		},
		{
			MethodName: "DeviceList",
			Handler:    _CentrifugoApi_DeviceList_Handler,
		},
		{
			MethodName: "DeviceTopicList",
			Handler:    _CentrifugoApi_DeviceTopicList_Handler,
		},
		{
			MethodName: "DeviceTopicUpdate",
			Handler:    _CentrifugoApi_DeviceTopicUpdate_Handler,
		},
		{
			MethodName: "UserTopicList",
			Handler:    _CentrifugoApi_UserTopicList_Handler,
		},
		{
			MethodName: "UserTopicUpdate",
			Handler:    _CentrifugoApi_UserTopicUpdate_Handler,
		},
		{
			MethodName: "SendPushNotification",
			Handler:    _CentrifugoApi_SendPushNotification_Handler,
		},
		{
			MethodName: "UpdatePushStatus",
			Handler:    _CentrifugoApi_UpdatePushStatus_Handler,
		},
		{
			MethodName: "CancelPush",
			Handler:    _CentrifugoApi_CancelPush_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: api.proto

package apiproto

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Command struct {
	state                protoimpl.MessageState       `protogen:"open.v1"`
	Publish              *PublishRequest              `protobuf:"bytes,4,opt,name=publish,proto3" json:"publish,omitempty"`
	Broadcast            *BroadcastRequest            `protobuf:"bytes,5,opt,name=broadcast,proto3" json:"broadcast,omitempty"`
	Subscribe            *SubscribeRequest            `protobuf:"bytes,6,opt,name=subscribe,proto3" json:"subscribe,omitempty"`
	Unsubscribe          *UnsubscribeRequest          `protobuf:"bytes,7,opt,name=unsubscribe,proto3" json:"unsubscribe,omitempty"`
	Disconnect           *DisconnectRequest           `protobuf:"bytes,8,opt,name=disconnect,proto3" json:"disconnect,omitempty"`
	Presence             *PresenceRequest             `protobuf:"bytes,9,opt,name=presence,proto3" json:"presence,omitempty"`
	PresenceStats        *PresenceStatsRequest        `protobuf:"bytes,10,opt,name=presence_stats,json=presenceStats,proto3" json:"presence_stats,omitempty"`
	History              *HistoryRequest              `protobuf:"bytes,11,opt,name=history,proto3" json:"history,omitempty"`
	HistoryRemove        *HistoryRemoveRequest        `protobuf:"bytes,12,opt,name=history_remove,json=historyRemove,proto3" json:"history_remove,omitempty"`
	Info                 *InfoRequest                 `protobuf:"bytes,13,opt,name=info,proto3" json:"info,omitempty"`
	Rpc                  *RPCRequest                  `protobuf:"bytes,14,opt,name=rpc,proto3" json:"rpc,omitempty"`
	Refresh              *RefreshRequest              `protobuf:"bytes,15,opt,name=refresh,proto3" json:"refresh,omitempty"`
	Channels             *ChannelsRequest             `protobuf:"bytes,16,opt,name=channels,proto3" json:"channels,omitempty"`
	Connections          *ConnectionsRequest          `protobuf:"bytes,17,opt,name=connections,proto3" json:"connections,omitempty"`
	UpdateUserStatus     *UpdateUserStatusRequest     `protobuf:"bytes,18,opt,name=update_user_status,json=updateUserStatus,proto3" json:"update_user_status,omitempty"`
	GetUserStatus        *GetUserStatusRequest        `protobuf:"bytes,19,opt,name=get_user_status,json=getUserStatus,proto3" json:"get_user_status,omitempty"`
	DeleteUserStatus     *DeleteUserStatusRequest     `protobuf:"bytes,20,opt,name=delete_user_status,json=deleteUserStatus,proto3" json:"delete_user_status,omitempty"`
	BlockUser            *BlockUserRequest            `protobuf:"bytes,21,opt,name=block_user,json=blockUser,proto3" json:"block_user,omitempty"`
	UnblockUser          *UnblockUserRequest          `protobuf:"bytes,22,opt,name=unblock_user,json=unblockUser,proto3" json:"unblock_user,omitempty"`
	RevokeToken          *RevokeTokenRequest          `protobuf:"bytes,23,opt,name=revoke_token,json=revokeToken,proto3" json:"revoke_token,omitempty"`
	InvalidateUserTokens *InvalidateUserTokensRequest `protobuf:"bytes,24,opt,name=invalidate_user_tokens,json=invalidateUserTokens,proto3" json:"invalidate_user_tokens,omitempty"`
	DeviceRegister       *DeviceRegisterRequest       `protobuf:"bytes,25,opt,name=device_register,json=deviceRegister,proto3" json:"device_register,omitempty"`
	DeviceUpdate         *DeviceUpdateRequest         `protobuf:"bytes,26,opt,name=device_update,json=deviceUpdate,proto3" json:"device_update,omitempty"`
	DeviceRemove         *DeviceRemoveRequest         `protobuf:"bytes,27,opt,name=device_remove,json=deviceRemove,proto3" json:"device_remove,omitempty"`
	DeviceList           *DeviceListRequest           `protobuf:"bytes,28,opt,name=device_list,json=deviceList,proto3" json:"device_list,omitempty"`
	DeviceTopicList      *DeviceTopicListRequest      `protobuf:"bytes,29,opt,name=device_topic_list,json=deviceTopicList,proto3" json:"device_topic_list,omitempty"`
	DeviceTopicUpdate    *DeviceTopicUpdateRequest    `protobuf:"bytes,30,opt,name=device_topic_update,json=deviceTopicUpdate,proto3" json:"device_topic_update,omitempty"`
	UserTopicList        *UserTopicListRequest        `protobuf:"bytes,31,opt,name=user_topic_list,json=userTopicList,proto3" json:"user_topic_list,omitempty"`
	UserTopicUpdate      *UserTopicUpdateRequest      `protobuf:"bytes,32,opt,name=user_topic_update,json=userTopicUpdate,proto3" json:"user_topic_update,omitempty"`
	SendPushNotification *SendPushNotificationRequest `protobuf:"bytes,33,opt,name=send_push_notification,json=sendPushNotification,proto3" json:"send_push_notification,omitempty"`
	UpdatePushStatus     *UpdatePushStatusRequest     `protobuf:"bytes,34,opt,name=update_push_status,json=updatePushStatus,proto3" json:"update_push_status,omitempty"`
	CancelPush           *CancelPushRequest           `protobuf:"bytes,35,opt,name=cancel_push,json=cancelPush,proto3" json:"cancel_push,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *Command) Reset() {
	*x = Command{}
	mi := &file_api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Command) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Command) ProtoMessage() {}

func (x *Command) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Command.ProtoReflect.Descriptor instead.
func (*Command) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{0}
}

func (x *Command) GetPublish() *PublishRequest {
	if x != nil {
		return x.Publish
	}
	return nil
}

func (x *Command) GetBroadcast() *BroadcastRequest {
	if x != nil {
		return x.Broadcast
	}
	return nil
}

func (x *Command) GetSubscribe() *SubscribeRequest {
	if x != nil {
		return x.Subscribe
	}
	return nil
}

func (x *Command) GetUnsubscribe() *UnsubscribeRequest {
	if x != nil {
		return x.Unsubscribe
	}
	return nil
}

func (x *Command) GetDisconnect() *DisconnectRequest {
	if x != nil {
		return x.Disconnect
	}
	return nil
}

func (x *Command) GetPresence() *PresenceRequest {
	if x != nil {
		return x.Presence
	}
	return nil
}

func (x *Command) GetPresenceStats() *PresenceStatsRequest {
	if x != nil {
		return x.PresenceStats
	}
	return nil
}

func (x *Command) GetHistory() *HistoryRequest {
	if x != nil {
		return x.History
	}
	return nil
}

func (x *Command) GetHistoryRemove() *HistoryRemoveRequest {
	if x != nil {
		return x.HistoryRemove
	}
	return nil
}

func (x *Command) GetInfo() *InfoRequest {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *Command) GetRpc() *RPCRequest {
	if x != nil {
		return x.Rpc
	}
	return nil
}

func (x *Command) GetRefresh() *RefreshRequest {
	if x != nil {
		return x.Refresh
	}
	return nil
}

func (x *Command) GetChannels() *ChannelsRequest {
	if x != nil {
		return x.Channels
	}
	return nil
}

func (x *Command) GetConnections() *ConnectionsRequest {
	if x != nil {
		return x.Connections
	}
	return nil
}

func (x *Command) GetUpdateUserStatus() *UpdateUserStatusRequest {
	if x != nil {
		return x.UpdateUserStatus
	}
	return nil
}

func (x *Command) GetGetUserStatus() *GetUserStatusRequest {
	if x != nil {
		return x.GetUserStatus
	}
	return nil
}

func (x *Command) GetDeleteUserStatus() *DeleteUserStatusRequest {
	if x != nil {
		return x.DeleteUserStatus
	}
	return nil
}

func (x *Command) GetBlockUser() *BlockUserRequest {
	if x != nil {
		return x.BlockUser
	}
	return nil
}

func (x *Command) GetUnblockUser() *UnblockUserRequest {
	if x != nil {
		return x.UnblockUser
	}
	return nil
}

func (x *Command) GetRevokeToken() *RevokeTokenRequest {
	if x != nil {
		return x.RevokeToken
	}
	return nil
}

func (x *Command) GetInvalidateUserTokens() *InvalidateUserTokensRequest {
	if x != nil {
		return x.InvalidateUserTokens
	}
	return nil
}

func (x *Command) GetDeviceRegister() *DeviceRegisterRequest {
	if x != nil {
		return x.DeviceRegister
	}
	return nil
}

func (x *Command) GetDeviceUpdate() *DeviceUpdateRequest {
	if x != nil {
		return x.DeviceUpdate
	}
	return nil
}

func (x *Command) GetDeviceRemove() *DeviceRemoveRequest {
	if x != nil {
		return x.DeviceRemove
	}
	return nil
}

func (x *Command) GetDeviceList() *DeviceListRequest {
	if x != nil {
		return x.DeviceList
	}
	return nil
}

func (x *Command) GetDeviceTopicList() *DeviceTopicListRequest {
	if x != nil {
		return x.DeviceTopicList
	}
	return nil
}

func (x *Command) GetDeviceTopicUpdate() *DeviceTopicUpdateRequest {
	if x != nil {
		return x.DeviceTopicUpdate
	}
	return nil
}

func (x *Command) GetUserTopicList() *UserTopicListRequest {
	if x != nil {
		return x.UserTopicList
	}
	return nil
}

func (x *Command) GetUserTopicUpdate() *UserTopicUpdateRequest {
	if x != nil {
		return x.UserTopicUpdate
	}
	return nil
}

func (x *Command) GetSendPushNotification() *SendPushNotificationRequest {
	if x != nil {
		return x.SendPushNotification
	}
	return nil
}

func (x *Command) GetUpdatePushStatus() *UpdatePushStatusRequest {
	if x != nil {
		return x.UpdatePushStatus
	}
	return nil
}

func (x *Command) GetCancelPush() *CancelPushRequest {
	if x != nil {
		return x.CancelPush
	}
	return nil
}

type Error struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Error) Reset() {
	*x = Error{}
	mi := &file_api_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Error) ProtoMessage() {}

func (x *Error) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Error.ProtoReflect.Descriptor instead.
func (*Error) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{1}
}

func (x *Error) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Error) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type Reply struct {
	state                protoimpl.MessageState      `protogen:"open.v1"`
	Error                *Error                      `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Publish              *PublishResult              `protobuf:"bytes,4,opt,name=publish,proto3" json:"publish,omitempty"`
	Broadcast            *BroadcastResult            `protobuf:"bytes,5,opt,name=broadcast,proto3" json:"broadcast,omitempty"`
	Subscribe            *SubscribeResult            `protobuf:"bytes,6,opt,name=subscribe,proto3" json:"subscribe,omitempty"`
	Unsubscribe          *UnsubscribeResult          `protobuf:"bytes,7,opt,name=unsubscribe,proto3" json:"unsubscribe,omitempty"`
	Disconnect           *DisconnectResult           `protobuf:"bytes,8,opt,name=disconnect,proto3" json:"disconnect,omitempty"`
	Presence             *PresenceResult             `protobuf:"bytes,9,opt,name=presence,proto3" json:"presence,omitempty"`
	PresenceStats        *PresenceStatsResult        `protobuf:"bytes,10,opt,name=presence_stats,json=presenceStats,proto3" json:"presence_stats,omitempty"`
	History              *HistoryResult              `protobuf:"bytes,11,opt,name=history,proto3" json:"history,omitempty"`
	HistoryRemove        *HistoryRemoveResult        `protobuf:"bytes,12,opt,name=history_remove,json=historyRemove,proto3" json:"history_remove,omitempty"`
	Info                 *InfoResult                 `protobuf:"bytes,13,opt,name=info,proto3" json:"info,omitempty"`
	Rpc                  *RPCResult                  `protobuf:"bytes,14,opt,name=rpc,proto3" json:"rpc,omitempty"`
	Refresh              *RefreshResult              `protobuf:"bytes,15,opt,name=refresh,proto3" json:"refresh,omitempty"`
	Channels             *ChannelsResult             `protobuf:"bytes,16,opt,name=channels,proto3" json:"channels,omitempty"`
	Connections          *ConnectionsResult          `protobuf:"bytes,17,opt,name=connections,proto3" json:"connections,omitempty"`
	UpdateUserStatus     *UpdateUserStatusResult     `protobuf:"bytes,18,opt,name=update_user_status,json=updateUserStatus,proto3" json:"update_user_status,omitempty"`
	GetUserStatus        *GetUserStatusResult        `protobuf:"bytes,19,opt,name=get_user_status,json=getUserStatus,proto3" json:"get_user_status,omitempty"`
	DeleteUserStatus     *DeleteUserStatusResult     `protobuf:"bytes,20,opt,name=delete_user_status,json=deleteUserStatus,proto3" json:"delete_user_status,omitempty"`
	BlockUser            *BlockUserResult            `protobuf:"bytes,21,opt,name=block_user,json=blockUser,proto3" json:"block_user,omitempty"`
	UnblockUser          *UnblockUserResult          `protobuf:"bytes,22,opt,name=unblock_user,json=unblockUser,proto3" json:"unblock_user,omitempty"`
	RevokeToken          *RevokeTokenResult          `protobuf:"bytes,23,opt,name=revoke_token,json=revokeToken,proto3" json:"revoke_token,omitempty"`
	InvalidateUserTokens *InvalidateUserTokensResult `protobuf:"bytes,24,opt,name=invalidate_user_tokens,json=invalidateUserTokens,proto3" json:"invalidate_user_tokens,omitempty"`
	DeviceRegister       *DeviceRegisterResult       `protobuf:"bytes,25,opt,name=device_register,json=deviceRegister,proto3" json:"device_register,omitempty"`
	DeviceUpdate         *DeviceUpdateResult         `protobuf:"bytes,26,opt,name=device_update,json=deviceUpdate,proto3" json:"device_update,omitempty"`
	DeviceRemove         *DeviceRemoveResult         `protobuf:"bytes,27,opt,name=device_remove,json=deviceRemove,proto3" json:"device_remove,omitempty"`
	DeviceList           *DeviceListResult           `protobuf:"bytes,28,opt,name=device_list,json=deviceList,proto3" json:"device_list,omitempty"`
	DeviceTopicList      *DeviceTopicListResult      `protobuf:"bytes,29,opt,name=device_topic_list,json=deviceTopicList,proto3" json:"device_topic_list,omitempty"`
	DeviceTopicUpdate    *DeviceTopicUpdateResult    `protobuf:"bytes,30,opt,name=device_topic_update,json=deviceTopicUpdate,proto3" json:"device_topic_update,omitempty"`
	UserTopicList        *UserTopicListResult        `protobuf:"bytes,31,opt,name=user_topic_list,json=userTopicList,proto3" json:"user_topic_list,omitempty"`
	UserTopicUpdate      *UserTopicUpdateResult      `protobuf:"bytes,32,opt,name=user_topic_update,json=userTopicUpdate,proto3" json:"user_topic_update,omitempty"`
	SendPushNotification *SendPushNotificationResult `protobuf:"bytes,33,opt,name=send_push_notification,json=sendPushNotification,proto3" json:"send_push_notification,omitempty"`
	UpdatePushStatus     *UpdatePushStatusResult     `protobuf:"bytes,34,opt,name=update_push_status,json=updatePushStatus,proto3" json:"update_push_status,omitempty"`
	CancelPush           *CancelPushResult           `protobuf:"bytes,35,opt,name=cancel_push,json=cancelPush,proto3" json:"cancel_push,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *Reply) Reset() {
	*x = Reply{}
	mi := &file_api_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{2}
}

func (x *Reply) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *Reply) GetPublish() *PublishResult {
	if x != nil {
		return x.Publish
	}
	return nil
}

func (x *Reply) GetBroadcast() *BroadcastResult {
	if x != nil {
		return x.Broadcast
	}
	return nil
}

func (x *Reply) GetSubscribe() *SubscribeResult {
	if x != nil {
		return x.Subscribe
	}
	return nil
}

func (x *Reply) GetUnsubscribe() *UnsubscribeResult {
	if x != nil {
		return x.Unsubscribe
	}
	return nil
}

func (x *Reply) GetDisconnect() *DisconnectResult {
	if x != nil {
		return x.Disconnect
	}
	return nil
}

func (x *Reply) GetPresence() *PresenceResult {
	if x != nil {
		return x.Presence
	}
	return nil
}

func (x *Reply) GetPresenceStats() *PresenceStatsResult {
	if x != nil {
		return x.PresenceStats
	}
	return nil
}

func (x *Reply) GetHistory() *HistoryResult {
	if x != nil {
		return x.History
	}
	return nil
}

func (x *Reply) GetHistoryRemove() *HistoryRemoveResult {
	if x != nil {
		return x.HistoryRemove
	}
	return nil
}

func (x *Reply) GetInfo() *InfoResult {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *Reply) GetRpc() *RPCResult {
	if x != nil {
		return x.Rpc
	}
	return nil
}

func (x *Reply) GetRefresh() *RefreshResult {
	if x != nil {
		return x.Refresh
	}
	return nil
}

func (x *Reply) GetChannels() *ChannelsResult {
	if x != nil {
		return x.Channels
	}
	return nil
}

func (x *Reply) GetConnections() *ConnectionsResult {
	if x != nil {
		return x.Connections
	}
	return nil
}

func (x *Reply) GetUpdateUserStatus() *UpdateUserStatusResult {
	if x != nil {
		return x.UpdateUserStatus
	}
	return nil
}

func (x *Reply) GetGetUserStatus() *GetUserStatusResult {
	if x != nil {
		return x.GetUserStatus
	}
	return nil
}

func (x *Reply) GetDeleteUserStatus() *DeleteUserStatusResult {
	if x != nil {
		return x.DeleteUserStatus
	}
	return nil
}

func (x *Reply) GetBlockUser() *BlockUserResult {
	if x != nil {
		return x.BlockUser
	}
	return nil
}

func (x *Reply) GetUnblockUser() *UnblockUserResult {
	if x != nil {
		return x.UnblockUser
	}
	return nil
}

func (x *Reply) GetRevokeToken() *RevokeTokenResult {
	if x != nil {
		return x.RevokeToken
	}
	return nil
}

func (x *Reply) GetInvalidateUserTokens() *InvalidateUserTokensResult {
	if x != nil {
		return x.InvalidateUserTokens
	}
	return nil
}

func (x *Reply) GetDeviceRegister() *DeviceRegisterResult {
	if x != nil {
		return x.DeviceRegister
	}
	return nil
}

func (x *Reply) GetDeviceUpdate() *DeviceUpdateResult {
	if x != nil {
		return x.DeviceUpdate
	}
	return nil
}

func (x *Reply) GetDeviceRemove() *DeviceRemoveResult {
	if x != nil {
		return x.DeviceRemove
	}
	return nil
}

func (x *Reply) GetDeviceList() *DeviceListResult {
	if x != nil {
		return x.DeviceList
	}
	return nil
}

func (x *Reply) GetDeviceTopicList() *DeviceTopicListResult {
	if x != nil {
		return x.DeviceTopicList
	}
	return nil
}

func (x *Reply) GetDeviceTopicUpdate() *DeviceTopicUpdateResult {
	if x != nil {
		return x.DeviceTopicUpdate
	}
	return nil
}

func (x *Reply) GetUserTopicList() *UserTopicListResult {
	if x != nil {
		return x.UserTopicList
	}
	return nil
}

func (x *Reply) GetUserTopicUpdate() *UserTopicUpdateResult {
	if x != nil {
		return x.UserTopicUpdate
	}
	return nil
}

func (x *Reply) GetSendPushNotification() *SendPushNotificationResult {
	if x != nil {
		return x.SendPushNotification
	}
	return nil
}

func (x *Reply) GetUpdatePushStatus() *UpdatePushStatusResult {
	if x != nil {
		return x.UpdatePushStatus
	}
	return nil
}

func (x *Reply) GetCancelPush() *CancelPushResult {
	if x != nil {
		return x.CancelPush
	}
	return nil
}

type BatchRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Commands      []*Command             `protobuf:"bytes,1,rep,name=commands,proto3" json:"commands,omitempty"`
	Parallel      bool                   `protobuf:"varint,2,opt,name=parallel,proto3" json:"parallel,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchRequest) Reset() {
	*x = BatchRequest{}
	mi := &file_api_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRequest) ProtoMessage() {}

func (x *BatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRequest.ProtoReflect.Descriptor instead.
func (*BatchRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{3}
}

func (x *BatchRequest) GetCommands() []*Command {
	if x != nil {
		return x.Commands
	}
	return nil
}

func (x *BatchRequest) GetParallel() bool {
	if x != nil {
		return x.Parallel
	}
	return false
}

type BatchResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Replies       []*Reply               `protobuf:"bytes,1,rep,name=replies,proto3" json:"replies,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchResponse) Reset() {
	*x = BatchResponse{}
	mi := &file_api_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchResponse) ProtoMessage() {}

func (x *BatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchResponse.ProtoReflect.Descriptor instead.
func (*BatchResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{4}
}

func (x *BatchResponse) GetReplies() []*Reply {
	if x != nil {
		return x.Replies
	}
	return nil
}

type PublishRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Channel        string                 `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
	Data           []byte                 `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	B64Data        string                 `protobuf:"bytes,3,opt,name=b64data,proto3" json:"b64data,omitempty"`
	SkipHistory    bool                   `protobuf:"varint,4,opt,name=skip_history,json=skipHistory,proto3" json:"skip_history,omitempty"`
	Tags           map[string]string      `protobuf:"bytes,5,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	IdempotencyKey string                 `protobuf:"bytes,6,opt,name=idempotency_key,json=idempotencyKey,proto3" json:"idempotency_key,omitempty"`
	Delta          bool                   `protobuf:"varint,7,opt,name=delta,proto3" json:"delta,omitempty"`
	Version        uint64                 `protobuf:"varint,8,opt,name=version,proto3" json:"version,omitempty"`
	VersionEpoch   string                 `protobuf:"bytes,9,opt,name=version_epoch,json=versionEpoch,proto3" json:"version_epoch,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PublishRequest) Reset() {
	*x = PublishRequest{}
	mi := &file_api_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublishRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishRequest) ProtoMessage() {}

func (x *PublishRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishRequest.ProtoReflect.Descriptor instead.
func (*PublishRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{5}
}

func (x *PublishRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *PublishRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *PublishRequest) GetB64Data() string {
	if x != nil {
		return x.B64Data
	}
	return ""
}

func (x *PublishRequest) GetSkipHistory() bool {
	if x != nil {
		return x.SkipHistory
	}
	return false
}

func (x *PublishRequest) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *PublishRequest) GetIdempotencyKey() string {
	if x != nil {
		return x.IdempotencyKey
	}
	return ""
}

func (x *PublishRequest) GetDelta() bool {
	if x != nil {
		return x.Delta
	}
	return false
}

func (x *PublishRequest) GetVersion() uint64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *PublishRequest) GetVersionEpoch() string {
	if x != nil {
		return x.VersionEpoch
	}
	return ""
}

type PublishResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *PublishResult         `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublishResponse) Reset() {
	*x = PublishResponse{}
	mi := &file_api_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublishResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishResponse) ProtoMessage() {}

func (x *PublishResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishResponse.ProtoReflect.Descriptor instead.
func (*PublishResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{6}
}

func (x *PublishResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *PublishResponse) GetResult() *PublishResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type PublishResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Offset        uint64                 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Epoch         string                 `protobuf:"bytes,2,opt,name=epoch,proto3" json:"epoch,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublishResult) Reset() {
	*x = PublishResult{}
	mi := &file_api_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublishResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishResult) ProtoMessage() {}

func (x *PublishResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishResult.ProtoReflect.Descriptor instead.
func (*PublishResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{7}
}

func (x *PublishResult) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *PublishResult) GetEpoch() string {
	if x != nil {
		return x.Epoch
	}
	return ""
}

type BroadcastRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Channels       []string               `protobuf:"bytes,1,rep,name=channels,proto3" json:"channels,omitempty"`
	Data           []byte                 `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	B64Data        string                 `protobuf:"bytes,3,opt,name=b64data,proto3" json:"b64data,omitempty"`
	SkipHistory    bool                   `protobuf:"varint,4,opt,name=skip_history,json=skipHistory,proto3" json:"skip_history,omitempty"`
	Tags           map[string]string      `protobuf:"bytes,5,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	IdempotencyKey string                 `protobuf:"bytes,6,opt,name=idempotency_key,json=idempotencyKey,proto3" json:"idempotency_key,omitempty"`
	Delta          bool                   `protobuf:"varint,7,opt,name=delta,proto3" json:"delta,omitempty"`
	Version        uint64                 `protobuf:"varint,8,opt,name=version,proto3" json:"version,omitempty"`
	VersionEpoch   string                 `protobuf:"bytes,9,opt,name=version_epoch,json=versionEpoch,proto3" json:"version_epoch,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BroadcastRequest) Reset() {
	*x = BroadcastRequest{}
	mi := &file_api_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BroadcastRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastRequest) ProtoMessage() {}

func (x *BroadcastRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastRequest.ProtoReflect.Descriptor instead.
func (*BroadcastRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{8}
}

func (x *BroadcastRequest) GetChannels() []string {
	if x != nil {
		return x.Channels
	}
	return nil
}

func (x *BroadcastRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *BroadcastRequest) GetB64Data() string {
	if x != nil {
		return x.B64Data
	}
	return ""
}

func (x *BroadcastRequest) GetSkipHistory() bool {
	if x != nil {
		return x.SkipHistory
	}
	return false
}

func (x *BroadcastRequest) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *BroadcastRequest) GetIdempotencyKey() string {
	if x != nil {
		return x.IdempotencyKey
	}
	return ""
}

func (x *BroadcastRequest) GetDelta() bool {
	if x != nil {
		return x.Delta
	}
	return false
}

func (x *BroadcastRequest) GetVersion() uint64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *BroadcastRequest) GetVersionEpoch() string {
	if x != nil {
		return x.VersionEpoch
	}
	return ""
}

type BroadcastResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *BroadcastResult       `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BroadcastResponse) Reset() {
	*x = BroadcastResponse{}
	mi := &file_api_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BroadcastResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastResponse) ProtoMessage() {}

func (x *BroadcastResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastResponse.ProtoReflect.Descriptor instead.
func (*BroadcastResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{9}
}

func (x *BroadcastResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BroadcastResponse) GetResult() *BroadcastResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type BroadcastResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Responses     []*PublishResponse     `protobuf:"bytes,1,rep,name=responses,proto3" json:"responses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BroadcastResult) Reset() {
	*x = BroadcastResult{}
	mi := &file_api_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BroadcastResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastResult) ProtoMessage() {}

func (x *BroadcastResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastResult.ProtoReflect.Descriptor instead.
func (*BroadcastResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{10}
}

func (x *BroadcastResult) GetResponses() []*PublishResponse {
	if x != nil {
		return x.Responses
	}
	return nil
}

type SubscribeRequest struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Channel       string                   `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
	User          string                   `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	ExpireAt      int64                    `protobuf:"varint,3,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	Info          []byte                   `protobuf:"bytes,4,opt,name=info,proto3" json:"info,omitempty"`
	B64Info       string                   `protobuf:"bytes,5,opt,name=b64info,proto3" json:"b64info,omitempty"`
	Client        string                   `protobuf:"bytes,6,opt,name=client,proto3" json:"client,omitempty"`
	Data          []byte                   `protobuf:"bytes,7,opt,name=data,proto3" json:"data,omitempty"`
	B64Data       string                   `protobuf:"bytes,8,opt,name=b64data,proto3" json:"b64data,omitempty"`
	RecoverSince  *StreamPosition          `protobuf:"bytes,9,opt,name=recover_since,json=recoverSince,proto3" json:"recover_since,omitempty"`
	Override      *SubscribeOptionOverride `protobuf:"bytes,10,opt,name=override,proto3" json:"override,omitempty"`
	Session       string                   `protobuf:"bytes,11,opt,name=session,proto3" json:"session,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscribeRequest) Reset() {
	*x = SubscribeRequest{}
	mi := &file_api_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequest) ProtoMessage() {}

func (x *SubscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequest.ProtoReflect.Descriptor instead.
func (*SubscribeRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{11}
}

func (x *SubscribeRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *SubscribeRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *SubscribeRequest) GetExpireAt() int64 {
	if x != nil {
		return x.ExpireAt
	}
	return 0
}

func (x *SubscribeRequest) GetInfo() []byte {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *SubscribeRequest) GetB64Info() string {
	if x != nil {
		return x.B64Info
	}
	return ""
}

func (x *SubscribeRequest) GetClient() string {
	if x != nil {
		return x.Client
	}
	return ""
}

func (x *SubscribeRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SubscribeRequest) GetB64Data() string {
	if x != nil {
		return x.B64Data
	}
	return ""
}

func (x *SubscribeRequest) GetRecoverSince() *StreamPosition {
	if x != nil {
		return x.RecoverSince
	}
	return nil
}

func (x *SubscribeRequest) GetOverride() *SubscribeOptionOverride {
	if x != nil {
		return x.Override
	}
	return nil
}

func (x *SubscribeRequest) GetSession() string {
	if x != nil {
		return x.Session
	}
	return ""
}

type SubscribeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *SubscribeResult       `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscribeResponse) Reset() {
	*x = SubscribeResponse{}
	mi := &file_api_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeResponse) ProtoMessage() {}

func (x *SubscribeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeResponse.ProtoReflect.Descriptor instead.
func (*SubscribeResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{12}
}

func (x *SubscribeResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SubscribeResponse) GetResult() *SubscribeResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type BoolValue struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         bool                   `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BoolValue) Reset() {
	*x = BoolValue{}
	mi := &file_api_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BoolValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoolValue) ProtoMessage() {}

func (x *BoolValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoolValue.ProtoReflect.Descriptor instead.
func (*BoolValue) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{13}
}

func (x *BoolValue) GetValue() bool {
	if x != nil {
		return x.Value
	}
	return false
}

type Int32Value struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         int32                  `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Int32Value) Reset() {
	*x = Int32Value{}
	mi := &file_api_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Int32Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32Value) ProtoMessage() {}

func (x *Int32Value) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32Value.ProtoReflect.Descriptor instead.
func (*Int32Value) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{14}
}

func (x *Int32Value) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type SubscribeOptionOverride struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Presence           *BoolValue             `protobuf:"bytes,1,opt,name=presence,proto3" json:"presence,omitempty"`
	JoinLeave          *BoolValue             `protobuf:"bytes,2,opt,name=join_leave,json=joinLeave,proto3" json:"join_leave,omitempty"`
	ForceRecovery      *BoolValue             `protobuf:"bytes,3,opt,name=force_recovery,json=forceRecovery,proto3" json:"force_recovery,omitempty"`
	ForcePositioning   *BoolValue             `protobuf:"bytes,4,opt,name=force_positioning,json=forcePositioning,proto3" json:"force_positioning,omitempty"`
	ForcePushJoinLeave *BoolValue             `protobuf:"bytes,5,opt,name=force_push_join_leave,json=forcePushJoinLeave,proto3" json:"force_push_join_leave,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *SubscribeOptionOverride) Reset() {
	*x = SubscribeOptionOverride{}
	mi := &file_api_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeOptionOverride) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeOptionOverride) ProtoMessage() {}

func (x *SubscribeOptionOverride) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeOptionOverride.ProtoReflect.Descriptor instead.
func (*SubscribeOptionOverride) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{15}
}

func (x *SubscribeOptionOverride) GetPresence() *BoolValue {
	if x != nil {
		return x.Presence
	}
	return nil
}

func (x *SubscribeOptionOverride) GetJoinLeave() *BoolValue {
	if x != nil {
		return x.JoinLeave
	}
	return nil
}

func (x *SubscribeOptionOverride) GetForceRecovery() *BoolValue {
	if x != nil {
		return x.ForceRecovery
	}
	return nil
}

func (x *SubscribeOptionOverride) GetForcePositioning() *BoolValue {
	if x != nil {
		return x.ForcePositioning
	}
	return nil
}

func (x *SubscribeOptionOverride) GetForcePushJoinLeave() *BoolValue {
	if x != nil {
		return x.ForcePushJoinLeave
	}
	return nil
}

type SubscribeResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscribeResult) Reset() {
	*x = SubscribeResult{}
	mi := &file_api_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeResult) ProtoMessage() {}

func (x *SubscribeResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeResult.ProtoReflect.Descriptor instead.
func (*SubscribeResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{16}
}

type UnsubscribeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Channel       string                 `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
	User          string                 `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Client        string                 `protobuf:"bytes,3,opt,name=client,proto3" json:"client,omitempty"`
	Session       string                 `protobuf:"bytes,4,opt,name=session,proto3" json:"session,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnsubscribeRequest) Reset() {
	*x = UnsubscribeRequest{}
	mi := &file_api_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnsubscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsubscribeRequest) ProtoMessage() {}

func (x *UnsubscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsubscribeRequest.ProtoReflect.Descriptor instead.
func (*UnsubscribeRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{17}
}

func (x *UnsubscribeRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *UnsubscribeRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *UnsubscribeRequest) GetClient() string {
	if x != nil {
		return x.Client
	}
	return ""
}

func (x *UnsubscribeRequest) GetSession() string {
	if x != nil {
		return x.Session
	}
	return ""
}

type UnsubscribeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *UnsubscribeResult     `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnsubscribeResponse) Reset() {
	*x = UnsubscribeResponse{}
	mi := &file_api_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnsubscribeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsubscribeResponse) ProtoMessage() {}

func (x *UnsubscribeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsubscribeResponse.ProtoReflect.Descriptor instead.
func (*UnsubscribeResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{18}
}

func (x *UnsubscribeResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UnsubscribeResponse) GetResult() *UnsubscribeResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type UnsubscribeResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnsubscribeResult) Reset() {
	*x = UnsubscribeResult{}
	mi := &file_api_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnsubscribeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsubscribeResult) ProtoMessage() {}

func (x *UnsubscribeResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsubscribeResult.ProtoReflect.Descriptor instead.
func (*UnsubscribeResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{19}
}

type Disconnect struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Disconnect) Reset() {
	*x = Disconnect{}
	mi := &file_api_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Disconnect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Disconnect) ProtoMessage() {}

func (x *Disconnect) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Disconnect.ProtoReflect.Descriptor instead.
func (*Disconnect) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{20}
}

func (x *Disconnect) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Disconnect) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type DisconnectRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Disconnect    *Disconnect            `protobuf:"bytes,2,opt,name=disconnect,proto3" json:"disconnect,omitempty"`
	Client        string                 `protobuf:"bytes,3,opt,name=client,proto3" json:"client,omitempty"`
	Whitelist     []string               `protobuf:"bytes,4,rep,name=whitelist,proto3" json:"whitelist,omitempty"`
	Session       string                 `protobuf:"bytes,5,opt,name=session,proto3" json:"session,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DisconnectRequest) Reset() {
	*x = DisconnectRequest{}
	mi := &file_api_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DisconnectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisconnectRequest) ProtoMessage() {}

func (x *DisconnectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisconnectRequest.ProtoReflect.Descriptor instead.
func (*DisconnectRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{21}
}

func (x *DisconnectRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *DisconnectRequest) GetDisconnect() *Disconnect {
	if x != nil {
		return x.Disconnect
	}
	return nil
}

func (x *DisconnectRequest) GetClient() string {
	if x != nil {
		return x.Client
	}
	return ""
}

func (x *DisconnectRequest) GetWhitelist() []string {
	if x != nil {
		return x.Whitelist
	}
	return nil
}

func (x *DisconnectRequest) GetSession() string {
	if x != nil {
		return x.Session
	}
	return ""
}

type DisconnectResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *DisconnectResult      `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DisconnectResponse) Reset() {
	*x = DisconnectResponse{}
	mi := &file_api_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DisconnectResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisconnectResponse) ProtoMessage() {}

func (x *DisconnectResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisconnectResponse.ProtoReflect.Descriptor instead.
func (*DisconnectResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{22}
}

func (x *DisconnectResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DisconnectResponse) GetResult() *DisconnectResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type DisconnectResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DisconnectResult) Reset() {
	*x = DisconnectResult{}
	mi := &file_api_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DisconnectResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisconnectResult) ProtoMessage() {}

func (x *DisconnectResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisconnectResult.ProtoReflect.Descriptor instead.
func (*DisconnectResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{23}
}

type PresenceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Channel       string                 `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresenceRequest) Reset() {
	*x = PresenceRequest{}
	mi := &file_api_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresenceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresenceRequest) ProtoMessage() {}

func (x *PresenceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresenceRequest.ProtoReflect.Descriptor instead.
func (*PresenceRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{24}
}

func (x *PresenceRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

type PresenceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *PresenceResult        `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresenceResponse) Reset() {
	*x = PresenceResponse{}
	mi := &file_api_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresenceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresenceResponse) ProtoMessage() {}

func (x *PresenceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresenceResponse.ProtoReflect.Descriptor instead.
func (*PresenceResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{25}
}

func (x *PresenceResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *PresenceResponse) GetResult() *PresenceResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type ClientInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Client        string                 `protobuf:"bytes,2,opt,name=client,proto3" json:"client,omitempty"`
	ConnInfo      []byte                 `protobuf:"bytes,3,opt,name=conn_info,json=connInfo,proto3" json:"conn_info,omitempty"`
	ChanInfo      []byte                 `protobuf:"bytes,4,opt,name=chan_info,json=chanInfo,proto3" json:"chan_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientInfo) Reset() {
	*x = ClientInfo{}
	mi := &file_api_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientInfo) ProtoMessage() {}

func (x *ClientInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientInfo.ProtoReflect.Descriptor instead.
func (*ClientInfo) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{26}
}

func (x *ClientInfo) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *ClientInfo) GetClient() string {
	if x != nil {
		return x.Client
	}
	return ""
}

func (x *ClientInfo) GetConnInfo() []byte {
	if x != nil {
		return x.ConnInfo
	}
	return nil
}

func (x *ClientInfo) GetChanInfo() []byte {
	if x != nil {
		return x.ChanInfo
	}
	return nil
}

type PresenceResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Presence      map[string]*ClientInfo `protobuf:"bytes,1,rep,name=presence,proto3" json:"presence,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresenceResult) Reset() {
	*x = PresenceResult{}
	mi := &file_api_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresenceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresenceResult) ProtoMessage() {}

func (x *PresenceResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresenceResult.ProtoReflect.Descriptor instead.
func (*PresenceResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{27}
}

func (x *PresenceResult) GetPresence() map[string]*ClientInfo {
	if x != nil {
		return x.Presence
	}
	return nil
}

type PresenceStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Channel       string                 `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresenceStatsRequest) Reset() {
	*x = PresenceStatsRequest{}
	mi := &file_api_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresenceStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresenceStatsRequest) ProtoMessage() {}

func (x *PresenceStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresenceStatsRequest.ProtoReflect.Descriptor instead.
func (*PresenceStatsRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{28}
}

func (x *PresenceStatsRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

type PresenceStatsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *PresenceStatsResult   `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresenceStatsResponse) Reset() {
	*x = PresenceStatsResponse{}
	mi := &file_api_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresenceStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresenceStatsResponse) ProtoMessage() {}

func (x *PresenceStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresenceStatsResponse.ProtoReflect.Descriptor instead.
func (*PresenceStatsResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{29}
}

func (x *PresenceStatsResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *PresenceStatsResponse) GetResult() *PresenceStatsResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type PresenceStatsResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NumClients    uint32                 `protobuf:"varint,1,opt,name=num_clients,json=numClients,proto3" json:"num_clients,omitempty"`
	NumUsers      uint32                 `protobuf:"varint,2,opt,name=num_users,json=numUsers,proto3" json:"num_users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresenceStatsResult) Reset() {
	*x = PresenceStatsResult{}
	mi := &file_api_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresenceStatsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresenceStatsResult) ProtoMessage() {}

func (x *PresenceStatsResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresenceStatsResult.ProtoReflect.Descriptor instead.
func (*PresenceStatsResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{30}
}

func (x *PresenceStatsResult) GetNumClients() uint32 {
	if x != nil {
		return x.NumClients
	}
	return 0
}

func (x *PresenceStatsResult) GetNumUsers() uint32 {
	if x != nil {
		return x.NumUsers
	}
	return 0
}

type StreamPosition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Offset        uint64                 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Epoch         string                 `protobuf:"bytes,2,opt,name=epoch,proto3" json:"epoch,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamPosition) Reset() {
	*x = StreamPosition{}
	mi := &file_api_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamPosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamPosition) ProtoMessage() {}

func (x *StreamPosition) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamPosition.ProtoReflect.Descriptor instead.
func (*StreamPosition) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{31}
}

func (x *StreamPosition) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *StreamPosition) GetEpoch() string {
	if x != nil {
		return x.Epoch
	}
	return ""
}

type HistoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Channel       string                 `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Since         *StreamPosition        `protobuf:"bytes,3,opt,name=since,proto3" json:"since,omitempty"`
	Reverse       bool                   `protobuf:"varint,4,opt,name=reverse,proto3" json:"reverse,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryRequest) Reset() {
	*x = HistoryRequest{}
	mi := &file_api_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryRequest) ProtoMessage() {}

func (x *HistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryRequest.ProtoReflect.Descriptor instead.
func (*HistoryRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{32}
}

func (x *HistoryRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *HistoryRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *HistoryRequest) GetSince() *StreamPosition {
	if x != nil {
		return x.Since
	}
	return nil
}

func (x *HistoryRequest) GetReverse() bool {
	if x != nil {
		return x.Reverse
	}
	return false
}

type HistoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *HistoryResult         `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryResponse) Reset() {
	*x = HistoryResponse{}
	mi := &file_api_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryResponse) ProtoMessage() {}

func (x *HistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryResponse.ProtoReflect.Descriptor instead.
func (*HistoryResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{33}
}

func (x *HistoryResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HistoryResponse) GetResult() *HistoryResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type Publication struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Removed: string uid = 1;
	Data          []byte            `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Info          *ClientInfo       `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	Offset        uint64            `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Tags          map[string]string `protobuf:"bytes,5,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Publication) Reset() {
	*x = Publication{}
	mi := &file_api_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Publication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Publication) ProtoMessage() {}

func (x *Publication) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Publication.ProtoReflect.Descriptor instead.
func (*Publication) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{34}
}

func (x *Publication) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Publication) GetInfo() *ClientInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *Publication) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *Publication) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

type HistoryResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Publications  []*Publication         `protobuf:"bytes,1,rep,name=publications,proto3" json:"publications,omitempty"`
	Epoch         string                 `protobuf:"bytes,2,opt,name=epoch,proto3" json:"epoch,omitempty"`
	Offset        uint64                 `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryResult) Reset() {
	*x = HistoryResult{}
	mi := &file_api_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryResult) ProtoMessage() {}

func (x *HistoryResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryResult.ProtoReflect.Descriptor instead.
func (*HistoryResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{35}
}

func (x *HistoryResult) GetPublications() []*Publication {
	if x != nil {
		return x.Publications
	}
	return nil
}

func (x *HistoryResult) GetEpoch() string {
	if x != nil {
		return x.Epoch
	}
	return ""
}

func (x *HistoryResult) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

type HistoryRemoveRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Channel       string                 `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryRemoveRequest) Reset() {
	*x = HistoryRemoveRequest{}
	mi := &file_api_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryRemoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryRemoveRequest) ProtoMessage() {}

func (x *HistoryRemoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryRemoveRequest.ProtoReflect.Descriptor instead.
func (*HistoryRemoveRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{36}
}

func (x *HistoryRemoveRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

type HistoryRemoveResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *HistoryRemoveResult   `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryRemoveResponse) Reset() {
	*x = HistoryRemoveResponse{}
	mi := &file_api_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryRemoveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryRemoveResponse) ProtoMessage() {}

func (x *HistoryRemoveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryRemoveResponse.ProtoReflect.Descriptor instead.
func (*HistoryRemoveResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{37}
}

func (x *HistoryRemoveResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HistoryRemoveResponse) GetResult() *HistoryRemoveResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type HistoryRemoveResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistoryRemoveResult) Reset() {
	*x = HistoryRemoveResult{}
	mi := &file_api_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryRemoveResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryRemoveResult) ProtoMessage() {}

func (x *HistoryRemoveResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryRemoveResult.ProtoReflect.Descriptor instead.
func (*HistoryRemoveResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{38}
}

type InfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InfoRequest) Reset() {
	*x = InfoRequest{}
	mi := &file_api_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoRequest) ProtoMessage() {}

func (x *InfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoRequest.ProtoReflect.Descriptor instead.
func (*InfoRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{39}
}

type InfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *InfoResult            `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InfoResponse) Reset() {
	*x = InfoResponse{}
	mi := &file_api_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoResponse) ProtoMessage() {}

func (x *InfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoResponse.ProtoReflect.Descriptor instead.
func (*InfoResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{40}
}

func (x *InfoResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *InfoResponse) GetResult() *InfoResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type InfoResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nodes         []*NodeResult          `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InfoResult) Reset() {
	*x = InfoResult{}
	mi := &file_api_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InfoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoResult) ProtoMessage() {}

func (x *InfoResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoResult.ProtoReflect.Descriptor instead.
func (*InfoResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{41}
}

func (x *InfoResult) GetNodes() []*NodeResult {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type RPCRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Method        string                 `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
	Params        []byte                 `protobuf:"bytes,2,opt,name=params,proto3" json:"params,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RPCRequest) Reset() {
	*x = RPCRequest{}
	mi := &file_api_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RPCRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RPCRequest) ProtoMessage() {}

func (x *RPCRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RPCRequest.ProtoReflect.Descriptor instead.
func (*RPCRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{42}
}

func (x *RPCRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *RPCRequest) GetParams() []byte {
	if x != nil {
		return x.Params
	}
	return nil
}

type RPCResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *RPCResult             `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RPCResponse) Reset() {
	*x = RPCResponse{}
	mi := &file_api_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RPCResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RPCResponse) ProtoMessage() {}

func (x *RPCResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RPCResponse.ProtoReflect.Descriptor instead.
func (*RPCResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{43}
}

func (x *RPCResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *RPCResponse) GetResult() *RPCResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type RPCResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []byte                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RPCResult) Reset() {
	*x = RPCResult{}
	mi := &file_api_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RPCResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RPCResult) ProtoMessage() {}

func (x *RPCResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RPCResult.ProtoReflect.Descriptor instead.
func (*RPCResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{44}
}

func (x *RPCResult) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type RefreshRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Client        string                 `protobuf:"bytes,2,opt,name=client,proto3" json:"client,omitempty"`
	Expired       bool                   `protobuf:"varint,3,opt,name=expired,proto3" json:"expired,omitempty"`
	ExpireAt      int64                  `protobuf:"varint,4,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	Info          []byte                 `protobuf:"bytes,5,opt,name=info,proto3" json:"info,omitempty"`
	Session       string                 `protobuf:"bytes,6,opt,name=session,proto3" json:"session,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshRequest) Reset() {
	*x = RefreshRequest{}
	mi := &file_api_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshRequest) ProtoMessage() {}

func (x *RefreshRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshRequest.ProtoReflect.Descriptor instead.
func (*RefreshRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{45}
}

func (x *RefreshRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *RefreshRequest) GetClient() string {
	if x != nil {
		return x.Client
	}
	return ""
}

func (x *RefreshRequest) GetExpired() bool {
	if x != nil {
		return x.Expired
	}
	return false
}

func (x *RefreshRequest) GetExpireAt() int64 {
	if x != nil {
		return x.ExpireAt
	}
	return 0
}

func (x *RefreshRequest) GetInfo() []byte {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *RefreshRequest) GetSession() string {
	if x != nil {
		return x.Session
	}
	return ""
}

type RefreshResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *RefreshResult         `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshResponse) Reset() {
	*x = RefreshResponse{}
	mi := &file_api_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshResponse) ProtoMessage() {}

func (x *RefreshResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshResponse.ProtoReflect.Descriptor instead.
func (*RefreshResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{46}
}

func (x *RefreshResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *RefreshResponse) GetResult() *RefreshResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type RefreshResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshResult) Reset() {
	*x = RefreshResult{}
	mi := &file_api_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshResult) ProtoMessage() {}

func (x *RefreshResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshResult.ProtoReflect.Descriptor instead.
func (*RefreshResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{47}
}

type NodeResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uid           string                 `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Version       string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	NumClients    uint32                 `protobuf:"varint,4,opt,name=num_clients,json=numClients,proto3" json:"num_clients,omitempty"`
	NumUsers      uint32                 `protobuf:"varint,5,opt,name=num_users,json=numUsers,proto3" json:"num_users,omitempty"`
	NumChannels   uint32                 `protobuf:"varint,6,opt,name=num_channels,json=numChannels,proto3" json:"num_channels,omitempty"`
	Uptime        uint32                 `protobuf:"varint,7,opt,name=uptime,proto3" json:"uptime,omitempty"`
	Metrics       *Metrics               `protobuf:"bytes,8,opt,name=metrics,proto3" json:"metrics,omitempty"`
	Process       *Process               `protobuf:"bytes,9,opt,name=process,proto3" json:"process,omitempty"`
	NumSubs       uint32                 `protobuf:"varint,10,opt,name=num_subs,json=numSubs,proto3" json:"num_subs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeResult) Reset() {
	*x = NodeResult{}
	mi := &file_api_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeResult) ProtoMessage() {}

func (x *NodeResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeResult.ProtoReflect.Descriptor instead.
func (*NodeResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{48}
}

func (x *NodeResult) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *NodeResult) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeResult) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *NodeResult) GetNumClients() uint32 {
	if x != nil {
		return x.NumClients
	}
	return 0
}

func (x *NodeResult) GetNumUsers() uint32 {
	if x != nil {
		return x.NumUsers
	}
	return 0
}

func (x *NodeResult) GetNumChannels() uint32 {
	if x != nil {
		return x.NumChannels
	}
	return 0
}

func (x *NodeResult) GetUptime() uint32 {
	if x != nil {
		return x.Uptime
	}
	return 0
}

func (x *NodeResult) GetMetrics() *Metrics {
	if x != nil {
		return x.Metrics
	}
	return nil
}

func (x *NodeResult) GetProcess() *Process {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *NodeResult) GetNumSubs() uint32 {
	if x != nil {
		return x.NumSubs
	}
	return 0
}

type Metrics struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Interval      float64                `protobuf:"fixed64,1,opt,name=interval,proto3" json:"interval,omitempty"`
	Items         map[string]float64     `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Metrics) Reset() {
	*x = Metrics{}
	mi := &file_api_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Metrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metrics) ProtoMessage() {}

func (x *Metrics) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metrics.ProtoReflect.Descriptor instead.
func (*Metrics) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{49}
}

func (x *Metrics) GetInterval() float64 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (x *Metrics) GetItems() map[string]float64 {
	if x != nil {
		return x.Items
	}
	return nil
}

type Process struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cpu           float64                `protobuf:"fixed64,1,opt,name=cpu,proto3" json:"cpu,omitempty"`
	Rss           int64                  `protobuf:"varint,2,opt,name=rss,proto3" json:"rss,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Process) Reset() {
	*x = Process{}
	mi := &file_api_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Process) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Process) ProtoMessage() {}

func (x *Process) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Process.ProtoReflect.Descriptor instead.
func (*Process) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{50}
}

func (x *Process) GetCpu() float64 {
	if x != nil {
		return x.Cpu
	}
	return 0
}

func (x *Process) GetRss() int64 {
	if x != nil {
		return x.Rss
	}
	return 0
}

type ChannelsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pattern       string                 `protobuf:"bytes,1,opt,name=pattern,proto3" json:"pattern,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChannelsRequest) Reset() {
	*x = ChannelsRequest{}
	mi := &file_api_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChannelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelsRequest) ProtoMessage() {}

func (x *ChannelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelsRequest.ProtoReflect.Descriptor instead.
func (*ChannelsRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{51}
}

func (x *ChannelsRequest) GetPattern() string {
	if x != nil {
		return x.Pattern
	}
	return ""
}

type ChannelsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *ChannelsResult        `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChannelsResponse) Reset() {
	*x = ChannelsResponse{}
	mi := &file_api_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChannelsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelsResponse) ProtoMessage() {}

func (x *ChannelsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelsResponse.ProtoReflect.Descriptor instead.
func (*ChannelsResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{52}
}

func (x *ChannelsResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *ChannelsResponse) GetResult() *ChannelsResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type ChannelsResult struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Channels      map[string]*ChannelInfo `protobuf:"bytes,1,rep,name=channels,proto3" json:"channels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChannelsResult) Reset() {
	*x = ChannelsResult{}
	mi := &file_api_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChannelsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelsResult) ProtoMessage() {}

func (x *ChannelsResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelsResult.ProtoReflect.Descriptor instead.
func (*ChannelsResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{53}
}

func (x *ChannelsResult) GetChannels() map[string]*ChannelInfo {
	if x != nil {
		return x.Channels
	}
	return nil
}

type ChannelInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NumClients    uint32                 `protobuf:"varint,1,opt,name=num_clients,json=numClients,proto3" json:"num_clients,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChannelInfo) Reset() {
	*x = ChannelInfo{}
	mi := &file_api_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChannelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelInfo) ProtoMessage() {}

func (x *ChannelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelInfo.ProtoReflect.Descriptor instead.
func (*ChannelInfo) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{54}
}

func (x *ChannelInfo) GetNumClients() uint32 {
	if x != nil {
		return x.NumClients
	}
	return 0
}

type ConnectionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Expression    string                 `protobuf:"bytes,2,opt,name=expression,proto3" json:"expression,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConnectionsRequest) Reset() {
	*x = ConnectionsRequest{}
	mi := &file_api_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionsRequest) ProtoMessage() {}

func (x *ConnectionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionsRequest.ProtoReflect.Descriptor instead.
func (*ConnectionsRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{55}
}

func (x *ConnectionsRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *ConnectionsRequest) GetExpression() string {
	if x != nil {
		return x.Expression
	}
	return ""
}

type ConnectionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *ConnectionsResult     `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConnectionsResponse) Reset() {
	*x = ConnectionsResponse{}
	mi := &file_api_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionsResponse) ProtoMessage() {}

func (x *ConnectionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionsResponse.ProtoReflect.Descriptor instead.
func (*ConnectionsResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{56}
}

func (x *ConnectionsResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *ConnectionsResponse) GetResult() *ConnectionsResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type ConnectionsResult struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Connections   map[string]*ConnectionInfo `protobuf:"bytes,1,rep,name=connections,proto3" json:"connections,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConnectionsResult) Reset() {
	*x = ConnectionsResult{}
	mi := &file_api_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionsResult) ProtoMessage() {}

func (x *ConnectionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionsResult.ProtoReflect.Descriptor instead.
func (*ConnectionsResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{57}
}

func (x *ConnectionsResult) GetConnections() map[string]*ConnectionInfo {
	if x != nil {
		return x.Connections
	}
	return nil
}

type ConnectionInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppName       string                 `protobuf:"bytes,1,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	AppVersion    string                 `protobuf:"bytes,2,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	Transport     string                 `protobuf:"bytes,3,opt,name=transport,proto3" json:"transport,omitempty"`
	Protocol      string                 `protobuf:"bytes,4,opt,name=protocol,proto3" json:"protocol,omitempty"`
	User          string                 `protobuf:"bytes,8,opt,name=user,proto3" json:"user,omitempty"`
	State         *ConnectionState       `protobuf:"bytes,9,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConnectionInfo) Reset() {
	*x = ConnectionInfo{}
	mi := &file_api_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionInfo) ProtoMessage() {}

func (x *ConnectionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionInfo.ProtoReflect.Descriptor instead.
func (*ConnectionInfo) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{58}
}

func (x *ConnectionInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *ConnectionInfo) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *ConnectionInfo) GetTransport() string {
	if x != nil {
		return x.Transport
	}
	return ""
}

func (x *ConnectionInfo) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *ConnectionInfo) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *ConnectionInfo) GetState() *ConnectionState {
	if x != nil {
		return x.State
	}
	return nil
}

type ConnectionState struct {
	state              protoimpl.MessageState            `protogen:"open.v1"`
	Channels           map[string]*ChannelContext        `protobuf:"bytes,1,rep,name=channels,proto3" json:"channels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	ConnectionToken    *ConnectionTokenInfo              `protobuf:"bytes,2,opt,name=connection_token,json=connectionToken,proto3" json:"connection_token,omitempty"`
	SubscriptionTokens map[string]*SubscriptionTokenInfo `protobuf:"bytes,3,rep,name=subscription_tokens,json=subscriptionTokens,proto3" json:"subscription_tokens,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Meta               []byte                            `protobuf:"bytes,4,opt,name=meta,proto3" json:"meta,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ConnectionState) Reset() {
	*x = ConnectionState{}
	mi := &file_api_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectionState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionState) ProtoMessage() {}

func (x *ConnectionState) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionState.ProtoReflect.Descriptor instead.
func (*ConnectionState) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{59}
}

func (x *ConnectionState) GetChannels() map[string]*ChannelContext {
	if x != nil {
		return x.Channels
	}
	return nil
}

func (x *ConnectionState) GetConnectionToken() *ConnectionTokenInfo {
	if x != nil {
		return x.ConnectionToken
	}
	return nil
}

func (x *ConnectionState) GetSubscriptionTokens() map[string]*SubscriptionTokenInfo {
	if x != nil {
		return x.SubscriptionTokens
	}
	return nil
}

func (x *ConnectionState) GetMeta() []byte {
	if x != nil {
		return x.Meta
	}
	return nil
}

type ChannelContext struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        uint32                 `protobuf:"varint,1,opt,name=source,proto3" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChannelContext) Reset() {
	*x = ChannelContext{}
	mi := &file_api_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChannelContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelContext) ProtoMessage() {}

func (x *ChannelContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelContext.ProtoReflect.Descriptor instead.
func (*ChannelContext) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{60}
}

func (x *ChannelContext) GetSource() uint32 {
	if x != nil {
		return x.Source
	}
	return 0
}

type ConnectionTokenInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uid           string                 `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IssuedAt      int64                  `protobuf:"varint,2,opt,name=issued_at,json=issuedAt,proto3" json:"issued_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConnectionTokenInfo) Reset() {
	*x = ConnectionTokenInfo{}
	mi := &file_api_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectionTokenInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionTokenInfo) ProtoMessage() {}

func (x *ConnectionTokenInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionTokenInfo.ProtoReflect.Descriptor instead.
func (*ConnectionTokenInfo) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{61}
}

func (x *ConnectionTokenInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ConnectionTokenInfo) GetIssuedAt() int64 {
	if x != nil {
		return x.IssuedAt
	}
	return 0
}

type SubscriptionTokenInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uid           string                 `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IssuedAt      int64                  `protobuf:"varint,2,opt,name=issued_at,json=issuedAt,proto3" json:"issued_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscriptionTokenInfo) Reset() {
	*x = SubscriptionTokenInfo{}
	mi := &file_api_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscriptionTokenInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionTokenInfo) ProtoMessage() {}

func (x *SubscriptionTokenInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionTokenInfo.ProtoReflect.Descriptor instead.
func (*SubscriptionTokenInfo) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{62}
}

func (x *SubscriptionTokenInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SubscriptionTokenInfo) GetIssuedAt() int64 {
	if x != nil {
		return x.IssuedAt
	}
	return 0
}

type UpdateUserStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []string               `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	State         string                 `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserStatusRequest) Reset() {
	*x = UpdateUserStatusRequest{}
	mi := &file_api_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserStatusRequest) ProtoMessage() {}

func (x *UpdateUserStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{63}
}

func (x *UpdateUserStatusRequest) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *UpdateUserStatusRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

type UpdateUserStatusResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *Error                  `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *UpdateUserStatusResult `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserStatusResponse) Reset() {
	*x = UpdateUserStatusResponse{}
	mi := &file_api_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserStatusResponse) ProtoMessage() {}

func (x *UpdateUserStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateUserStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{64}
}

func (x *UpdateUserStatusResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UpdateUserStatusResponse) GetResult() *UpdateUserStatusResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type UpdateUserStatusResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserStatusResult) Reset() {
	*x = UpdateUserStatusResult{}
	mi := &file_api_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserStatusResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserStatusResult) ProtoMessage() {}

func (x *UpdateUserStatusResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserStatusResult.ProtoReflect.Descriptor instead.
func (*UpdateUserStatusResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{65}
}

type GetUserStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []string               `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserStatusRequest) Reset() {
	*x = GetUserStatusRequest{}
	mi := &file_api_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserStatusRequest) ProtoMessage() {}

func (x *GetUserStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserStatusRequest.ProtoReflect.Descriptor instead.
func (*GetUserStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{66}
}

func (x *GetUserStatusRequest) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

type GetUserStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *GetUserStatusResult   `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserStatusResponse) Reset() {
	*x = GetUserStatusResponse{}
	mi := &file_api_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserStatusResponse) ProtoMessage() {}

func (x *GetUserStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserStatusResponse.ProtoReflect.Descriptor instead.
func (*GetUserStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{67}
}

func (x *GetUserStatusResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetUserStatusResponse) GetResult() *GetUserStatusResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type GetUserStatusResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Statuses      []*UserStatus          `protobuf:"bytes,1,rep,name=statuses,proto3" json:"statuses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserStatusResult) Reset() {
	*x = GetUserStatusResult{}
	mi := &file_api_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserStatusResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserStatusResult) ProtoMessage() {}

func (x *GetUserStatusResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserStatusResult.ProtoReflect.Descriptor instead.
func (*GetUserStatusResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{68}
}

func (x *GetUserStatusResult) GetStatuses() []*UserStatus {
	if x != nil {
		return x.Statuses
	}
	return nil
}

type UserStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Active        int64                  `protobuf:"varint,2,opt,name=active,proto3" json:"active,omitempty"`
	Online        int64                  `protobuf:"varint,3,opt,name=online,proto3" json:"online,omitempty"`
	State         string                 `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserStatus) Reset() {
	*x = UserStatus{}
	mi := &file_api_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStatus) ProtoMessage() {}

func (x *UserStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStatus.ProtoReflect.Descriptor instead.
func (*UserStatus) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{69}
}

func (x *UserStatus) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *UserStatus) GetActive() int64 {
	if x != nil {
		return x.Active
	}
	return 0
}

func (x *UserStatus) GetOnline() int64 {
	if x != nil {
		return x.Online
	}
	return 0
}

func (x *UserStatus) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

type DeleteUserStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []string               `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserStatusRequest) Reset() {
	*x = DeleteUserStatusRequest{}
	mi := &file_api_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserStatusRequest) ProtoMessage() {}

func (x *DeleteUserStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserStatusRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{70}
}

func (x *DeleteUserStatusRequest) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

type DeleteUserStatusResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *Error                  `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *DeleteUserStatusResult `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserStatusResponse) Reset() {
	*x = DeleteUserStatusResponse{}
	mi := &file_api_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserStatusResponse) ProtoMessage() {}

func (x *DeleteUserStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserStatusResponse.ProtoReflect.Descriptor instead.
func (*DeleteUserStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{71}
}

func (x *DeleteUserStatusResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DeleteUserStatusResponse) GetResult() *DeleteUserStatusResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type DeleteUserStatusResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserStatusResult) Reset() {
	*x = DeleteUserStatusResult{}
	mi := &file_api_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserStatusResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserStatusResult) ProtoMessage() {}

func (x *DeleteUserStatusResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserStatusResult.ProtoReflect.Descriptor instead.
func (*DeleteUserStatusResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{72}
}

type BlockUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ExpireAt      int64                  `protobuf:"varint,1,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	User          string                 `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockUserRequest) Reset() {
	*x = BlockUserRequest{}
	mi := &file_api_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockUserRequest) ProtoMessage() {}

func (x *BlockUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockUserRequest.ProtoReflect.Descriptor instead.
func (*BlockUserRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{73}
}

func (x *BlockUserRequest) GetExpireAt() int64 {
	if x != nil {
		return x.ExpireAt
	}
	return 0
}

func (x *BlockUserRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

type BlockUserResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockUserResult) Reset() {
	*x = BlockUserResult{}
	mi := &file_api_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockUserResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockUserResult) ProtoMessage() {}

func (x *BlockUserResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockUserResult.ProtoReflect.Descriptor instead.
func (*BlockUserResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{74}
}

type BlockUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *BlockUserResult       `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockUserResponse) Reset() {
	*x = BlockUserResponse{}
	mi := &file_api_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockUserResponse) ProtoMessage() {}

func (x *BlockUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockUserResponse.ProtoReflect.Descriptor instead.
func (*BlockUserResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{75}
}

func (x *BlockUserResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BlockUserResponse) GetResult() *BlockUserResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type UnblockUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnblockUserRequest) Reset() {
	*x = UnblockUserRequest{}
	mi := &file_api_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnblockUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnblockUserRequest) ProtoMessage() {}

func (x *UnblockUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnblockUserRequest.ProtoReflect.Descriptor instead.
func (*UnblockUserRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{76}
}

func (x *UnblockUserRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

type UnblockUserResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnblockUserResult) Reset() {
	*x = UnblockUserResult{}
	mi := &file_api_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnblockUserResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnblockUserResult) ProtoMessage() {}

func (x *UnblockUserResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnblockUserResult.ProtoReflect.Descriptor instead.
func (*UnblockUserResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{77}
}

type UnblockUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *UnblockUserResult     `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnblockUserResponse) Reset() {
	*x = UnblockUserResponse{}
	mi := &file_api_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnblockUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnblockUserResponse) ProtoMessage() {}

func (x *UnblockUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnblockUserResponse.ProtoReflect.Descriptor instead.
func (*UnblockUserResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{78}
}

func (x *UnblockUserResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UnblockUserResponse) GetResult() *UnblockUserResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type RevokeTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ExpireAt      int64                  `protobuf:"varint,1,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	Uid           string                 `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeTokenRequest) Reset() {
	*x = RevokeTokenRequest{}
	mi := &file_api_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeTokenRequest) ProtoMessage() {}

func (x *RevokeTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeTokenRequest.ProtoReflect.Descriptor instead.
func (*RevokeTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{79}
}

func (x *RevokeTokenRequest) GetExpireAt() int64 {
	if x != nil {
		return x.ExpireAt
	}
	return 0
}

func (x *RevokeTokenRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type RevokeTokenResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeTokenResult) Reset() {
	*x = RevokeTokenResult{}
	mi := &file_api_proto_msgTypes[80]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeTokenResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeTokenResult) ProtoMessage() {}

func (x *RevokeTokenResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[80]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeTokenResult.ProtoReflect.Descriptor instead.
func (*RevokeTokenResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{80}
}

type RevokeTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *RevokeTokenResult     `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeTokenResponse) Reset() {
	*x = RevokeTokenResponse{}
	mi := &file_api_proto_msgTypes[81]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeTokenResponse) ProtoMessage() {}

func (x *RevokeTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[81]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeTokenResponse.ProtoReflect.Descriptor instead.
func (*RevokeTokenResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{81}
}

func (x *RevokeTokenResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *RevokeTokenResponse) GetResult() *RevokeTokenResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type InvalidateUserTokensRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ExpireAt      int64                  `protobuf:"varint,1,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	User          string                 `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	IssuedBefore  int64                  `protobuf:"varint,3,opt,name=issued_before,json=issuedBefore,proto3" json:"issued_before,omitempty"`
	Channel       string                 `protobuf:"bytes,4,opt,name=channel,proto3" json:"channel,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InvalidateUserTokensRequest) Reset() {
	*x = InvalidateUserTokensRequest{}
	mi := &file_api_proto_msgTypes[82]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InvalidateUserTokensRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvalidateUserTokensRequest) ProtoMessage() {}

func (x *InvalidateUserTokensRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[82]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvalidateUserTokensRequest.ProtoReflect.Descriptor instead.
func (*InvalidateUserTokensRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{82}
}

func (x *InvalidateUserTokensRequest) GetExpireAt() int64 {
	if x != nil {
		return x.ExpireAt
	}
	return 0
}

func (x *InvalidateUserTokensRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *InvalidateUserTokensRequest) GetIssuedBefore() int64 {
	if x != nil {
		return x.IssuedBefore
	}
	return 0
}

func (x *InvalidateUserTokensRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

type InvalidateUserTokensResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InvalidateUserTokensResult) Reset() {
	*x = InvalidateUserTokensResult{}
	mi := &file_api_proto_msgTypes[83]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InvalidateUserTokensResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvalidateUserTokensResult) ProtoMessage() {}

func (x *InvalidateUserTokensResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[83]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvalidateUserTokensResult.ProtoReflect.Descriptor instead.
func (*InvalidateUserTokensResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{83}
}

type InvalidateUserTokensResponse struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Error         *Error                      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *InvalidateUserTokensResult `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InvalidateUserTokensResponse) Reset() {
	*x = InvalidateUserTokensResponse{}
	mi := &file_api_proto_msgTypes[84]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InvalidateUserTokensResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvalidateUserTokensResponse) ProtoMessage() {}

func (x *InvalidateUserTokensResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[84]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvalidateUserTokensResponse.ProtoReflect.Descriptor instead.
func (*InvalidateUserTokensResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{84}
}

func (x *InvalidateUserTokensResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *InvalidateUserTokensResponse) GetResult() *InvalidateUserTokensResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type DeviceRegisterRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Provider      string                 `protobuf:"bytes,2,opt,name=provider,proto3" json:"provider,omitempty"`
	Token         string                 `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	Platform      string                 `protobuf:"bytes,4,opt,name=platform,proto3" json:"platform,omitempty"`
	User          string                 `protobuf:"bytes,5,opt,name=user,proto3" json:"user,omitempty"`
	Meta          map[string]string      `protobuf:"bytes,6,rep,name=meta,proto3" json:"meta,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Topics        []string               `protobuf:"bytes,7,rep,name=topics,proto3" json:"topics,omitempty"`
	Timezone      string                 `protobuf:"bytes,8,opt,name=timezone,proto3" json:"timezone,omitempty"`
	Locale        string                 `protobuf:"bytes,9,opt,name=locale,proto3" json:"locale,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceRegisterRequest) Reset() {
	*x = DeviceRegisterRequest{}
	mi := &file_api_proto_msgTypes[85]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceRegisterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRegisterRequest) ProtoMessage() {}

func (x *DeviceRegisterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[85]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRegisterRequest.ProtoReflect.Descriptor instead.
func (*DeviceRegisterRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{85}
}

func (x *DeviceRegisterRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeviceRegisterRequest) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *DeviceRegisterRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *DeviceRegisterRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *DeviceRegisterRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *DeviceRegisterRequest) GetMeta() map[string]string {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *DeviceRegisterRequest) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

func (x *DeviceRegisterRequest) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *DeviceRegisterRequest) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

type DeviceUpdateRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Ids            []string               `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	Users          []string               `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
	UserUpdate     *DeviceUserUpdate      `protobuf:"bytes,4,opt,name=user_update,json=userUpdate,proto3" json:"user_update,omitempty"`
	MetaUpdate     *DeviceMetaUpdate      `protobuf:"bytes,5,opt,name=meta_update,json=metaUpdate,proto3" json:"meta_update,omitempty"`
	TopicsUpdate   *DeviceTopicsUpdate    `protobuf:"bytes,6,opt,name=topics_update,json=topicsUpdate,proto3" json:"topics_update,omitempty"`
	TimezoneUpdate *DeviceTimezoneUpdate  `protobuf:"bytes,7,opt,name=timezone_update,json=timezoneUpdate,proto3" json:"timezone_update,omitempty"`
	LocaleUpdate   *DeviceLocaleUpdate    `protobuf:"bytes,8,opt,name=locale_update,json=localeUpdate,proto3" json:"locale_update,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DeviceUpdateRequest) Reset() {
	*x = DeviceUpdateRequest{}
	mi := &file_api_proto_msgTypes[86]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceUpdateRequest) ProtoMessage() {}

func (x *DeviceUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[86]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceUpdateRequest.ProtoReflect.Descriptor instead.
func (*DeviceUpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{86}
}

func (x *DeviceUpdateRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *DeviceUpdateRequest) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *DeviceUpdateRequest) GetUserUpdate() *DeviceUserUpdate {
	if x != nil {
		return x.UserUpdate
	}
	return nil
}

func (x *DeviceUpdateRequest) GetMetaUpdate() *DeviceMetaUpdate {
	if x != nil {
		return x.MetaUpdate
	}
	return nil
}

func (x *DeviceUpdateRequest) GetTopicsUpdate() *DeviceTopicsUpdate {
	if x != nil {
		return x.TopicsUpdate
	}
	return nil
}

func (x *DeviceUpdateRequest) GetTimezoneUpdate() *DeviceTimezoneUpdate {
	if x != nil {
		return x.TimezoneUpdate
	}
	return nil
}

func (x *DeviceUpdateRequest) GetLocaleUpdate() *DeviceLocaleUpdate {
	if x != nil {
		return x.LocaleUpdate
	}
	return nil
}

type DeviceRemoveRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []string               `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	Users         []string               `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceRemoveRequest) Reset() {
	*x = DeviceRemoveRequest{}
	mi := &file_api_proto_msgTypes[87]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceRemoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRemoveRequest) ProtoMessage() {}

func (x *DeviceRemoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[87]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRemoveRequest.ProtoReflect.Descriptor instead.
func (*DeviceRemoveRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{87}
}

func (x *DeviceRemoveRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *DeviceRemoveRequest) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

type DeviceUserUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceUserUpdate) Reset() {
	*x = DeviceUserUpdate{}
	mi := &file_api_proto_msgTypes[88]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceUserUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceUserUpdate) ProtoMessage() {}

func (x *DeviceUserUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[88]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceUserUpdate.ProtoReflect.Descriptor instead.
func (*DeviceUserUpdate) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{88}
}

func (x *DeviceUserUpdate) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

type DeviceTimezoneUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Timezone      string                 `protobuf:"bytes,1,opt,name=timezone,proto3" json:"timezone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceTimezoneUpdate) Reset() {
	*x = DeviceTimezoneUpdate{}
	mi := &file_api_proto_msgTypes[89]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceTimezoneUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTimezoneUpdate) ProtoMessage() {}

func (x *DeviceTimezoneUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[89]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTimezoneUpdate.ProtoReflect.Descriptor instead.
func (*DeviceTimezoneUpdate) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{89}
}

func (x *DeviceTimezoneUpdate) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

type DeviceLocaleUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Locale        string                 `protobuf:"bytes,1,opt,name=locale,proto3" json:"locale,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceLocaleUpdate) Reset() {
	*x = DeviceLocaleUpdate{}
	mi := &file_api_proto_msgTypes[90]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceLocaleUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceLocaleUpdate) ProtoMessage() {}

func (x *DeviceLocaleUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[90]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceLocaleUpdate.ProtoReflect.Descriptor instead.
func (*DeviceLocaleUpdate) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{90}
}

func (x *DeviceLocaleUpdate) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

type DeviceMetaUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Meta          map[string]string      `protobuf:"bytes,1,rep,name=meta,proto3" json:"meta,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceMetaUpdate) Reset() {
	*x = DeviceMetaUpdate{}
	mi := &file_api_proto_msgTypes[91]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceMetaUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceMetaUpdate) ProtoMessage() {}

func (x *DeviceMetaUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[91]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceMetaUpdate.ProtoReflect.Descriptor instead.
func (*DeviceMetaUpdate) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{91}
}

func (x *DeviceMetaUpdate) GetMeta() map[string]string {
	if x != nil {
		return x.Meta
	}
	return nil
}

type DeviceTopicsUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Op            string                 `protobuf:"bytes,1,opt,name=op,proto3" json:"op,omitempty"` // add | remove | set
	Topics        []string               `protobuf:"bytes,2,rep,name=topics,proto3" json:"topics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceTopicsUpdate) Reset() {
	*x = DeviceTopicsUpdate{}
	mi := &file_api_proto_msgTypes[92]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceTopicsUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTopicsUpdate) ProtoMessage() {}

func (x *DeviceTopicsUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[92]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTopicsUpdate.ProtoReflect.Descriptor instead.
func (*DeviceTopicsUpdate) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{92}
}

func (x *DeviceTopicsUpdate) GetOp() string {
	if x != nil {
		return x.Op
	}
	return ""
}

func (x *DeviceTopicsUpdate) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

type DeviceFilter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []string               `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	Users         []string               `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
	Topics        []string               `protobuf:"bytes,3,rep,name=topics,proto3" json:"topics,omitempty"`
	Providers     []string               `protobuf:"bytes,4,rep,name=providers,proto3" json:"providers,omitempty"`
	Platforms     []string               `protobuf:"bytes,5,rep,name=platforms,proto3" json:"platforms,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceFilter) Reset() {
	*x = DeviceFilter{}
	mi := &file_api_proto_msgTypes[93]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceFilter) ProtoMessage() {}

func (x *DeviceFilter) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[93]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceFilter.ProtoReflect.Descriptor instead.
func (*DeviceFilter) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{93}
}

func (x *DeviceFilter) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *DeviceFilter) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *DeviceFilter) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

func (x *DeviceFilter) GetProviders() []string {
	if x != nil {
		return x.Providers
	}
	return nil
}

func (x *DeviceFilter) GetPlatforms() []string {
	if x != nil {
		return x.Platforms
	}
	return nil
}

type DeviceListRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Filter            *DeviceFilter          `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	IncludeTotalCount bool                   `protobuf:"varint,2,opt,name=include_total_count,json=includeTotalCount,proto3" json:"include_total_count,omitempty"`
	IncludeMeta       bool                   `protobuf:"varint,3,opt,name=include_meta,json=includeMeta,proto3" json:"include_meta,omitempty"`
	IncludeTopics     bool                   `protobuf:"varint,4,opt,name=include_topics,json=includeTopics,proto3" json:"include_topics,omitempty"`
	Cursor            string                 `protobuf:"bytes,10,opt,name=cursor,proto3" json:"cursor,omitempty"`
	Limit             int32                  `protobuf:"varint,11,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *DeviceListRequest) Reset() {
	*x = DeviceListRequest{}
	mi := &file_api_proto_msgTypes[94]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceListRequest) ProtoMessage() {}

func (x *DeviceListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[94]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceListRequest.ProtoReflect.Descriptor instead.
func (*DeviceListRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{94}
}

func (x *DeviceListRequest) GetFilter() *DeviceFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *DeviceListRequest) GetIncludeTotalCount() bool {
	if x != nil {
		return x.IncludeTotalCount
	}
	return false
}

func (x *DeviceListRequest) GetIncludeMeta() bool {
	if x != nil {
		return x.IncludeMeta
	}
	return false
}

func (x *DeviceListRequest) GetIncludeTopics() bool {
	if x != nil {
		return x.IncludeTopics
	}
	return false
}

func (x *DeviceListRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *DeviceListRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type DeviceTopicFilter struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	DeviceIds       []string               `protobuf:"bytes,1,rep,name=device_ids,json=deviceIds,proto3" json:"device_ids,omitempty"`
	DeviceProviders []string               `protobuf:"bytes,2,rep,name=device_providers,json=deviceProviders,proto3" json:"device_providers,omitempty"`
	DevicePlatforms []string               `protobuf:"bytes,3,rep,name=device_platforms,json=devicePlatforms,proto3" json:"device_platforms,omitempty"`
	DeviceUsers     []string               `protobuf:"bytes,4,rep,name=device_users,json=deviceUsers,proto3" json:"device_users,omitempty"`
	Topics          []string               `protobuf:"bytes,5,rep,name=topics,proto3" json:"topics,omitempty"`
	TopicPrefix     string                 `protobuf:"bytes,6,opt,name=topic_prefix,json=topicPrefix,proto3" json:"topic_prefix,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *DeviceTopicFilter) Reset() {
	*x = DeviceTopicFilter{}
	mi := &file_api_proto_msgTypes[95]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceTopicFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTopicFilter) ProtoMessage() {}

func (x *DeviceTopicFilter) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[95]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTopicFilter.ProtoReflect.Descriptor instead.
func (*DeviceTopicFilter) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{95}
}

func (x *DeviceTopicFilter) GetDeviceIds() []string {
	if x != nil {
		return x.DeviceIds
	}
	return nil
}

func (x *DeviceTopicFilter) GetDeviceProviders() []string {
	if x != nil {
		return x.DeviceProviders
	}
	return nil
}

func (x *DeviceTopicFilter) GetDevicePlatforms() []string {
	if x != nil {
		return x.DevicePlatforms
	}
	return nil
}

func (x *DeviceTopicFilter) GetDeviceUsers() []string {
	if x != nil {
		return x.DeviceUsers
	}
	return nil
}

func (x *DeviceTopicFilter) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

func (x *DeviceTopicFilter) GetTopicPrefix() string {
	if x != nil {
		return x.TopicPrefix
	}
	return ""
}

type DeviceTopicListRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Filter            *DeviceTopicFilter     `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	IncludeTotalCount bool                   `protobuf:"varint,2,opt,name=include_total_count,json=includeTotalCount,proto3" json:"include_total_count,omitempty"`
	IncludeDevice     bool                   `protobuf:"varint,3,opt,name=include_device,json=includeDevice,proto3" json:"include_device,omitempty"`
	Cursor            string                 `protobuf:"bytes,10,opt,name=cursor,proto3" json:"cursor,omitempty"`
	Limit             int32                  `protobuf:"varint,11,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *DeviceTopicListRequest) Reset() {
	*x = DeviceTopicListRequest{}
	mi := &file_api_proto_msgTypes[96]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceTopicListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTopicListRequest) ProtoMessage() {}

func (x *DeviceTopicListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[96]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTopicListRequest.ProtoReflect.Descriptor instead.
func (*DeviceTopicListRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{96}
}

func (x *DeviceTopicListRequest) GetFilter() *DeviceTopicFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *DeviceTopicListRequest) GetIncludeTotalCount() bool {
	if x != nil {
		return x.IncludeTotalCount
	}
	return false
}

func (x *DeviceTopicListRequest) GetIncludeDevice() bool {
	if x != nil {
		return x.IncludeDevice
	}
	return false
}

func (x *DeviceTopicListRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *DeviceTopicListRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type UserTopicFilter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []string               `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	Topics        []string               `protobuf:"bytes,2,rep,name=topics,proto3" json:"topics,omitempty"`
	TopicPrefix   string                 `protobuf:"bytes,3,opt,name=topic_prefix,json=topicPrefix,proto3" json:"topic_prefix,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserTopicFilter) Reset() {
	*x = UserTopicFilter{}
	mi := &file_api_proto_msgTypes[97]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserTopicFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTopicFilter) ProtoMessage() {}

func (x *UserTopicFilter) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[97]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTopicFilter.ProtoReflect.Descriptor instead.
func (*UserTopicFilter) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{97}
}

func (x *UserTopicFilter) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *UserTopicFilter) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

func (x *UserTopicFilter) GetTopicPrefix() string {
	if x != nil {
		return x.TopicPrefix
	}
	return ""
}

type UserTopicListRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Filter            *UserTopicFilter       `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	IncludeTotalCount bool                   `protobuf:"varint,2,opt,name=include_total_count,json=includeTotalCount,proto3" json:"include_total_count,omitempty"`
	Cursor            string                 `protobuf:"bytes,10,opt,name=cursor,proto3" json:"cursor,omitempty"`
	Limit             int32                  `protobuf:"varint,11,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UserTopicListRequest) Reset() {
	*x = UserTopicListRequest{}
	mi := &file_api_proto_msgTypes[98]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserTopicListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTopicListRequest) ProtoMessage() {}

func (x *UserTopicListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[98]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTopicListRequest.ProtoReflect.Descriptor instead.
func (*UserTopicListRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{98}
}

func (x *UserTopicListRequest) GetFilter() *UserTopicFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *UserTopicListRequest) GetIncludeTotalCount() bool {
	if x != nil {
		return x.IncludeTotalCount
	}
	return false
}

func (x *UserTopicListRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *UserTopicListRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type DeviceTopicUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Op            string                 `protobuf:"bytes,2,opt,name=op,proto3" json:"op,omitempty"` // add | remove | set
	Topics        []string               `protobuf:"bytes,3,rep,name=topics,proto3" json:"topics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceTopicUpdateRequest) Reset() {
	*x = DeviceTopicUpdateRequest{}
	mi := &file_api_proto_msgTypes[99]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceTopicUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTopicUpdateRequest) ProtoMessage() {}

func (x *DeviceTopicUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[99]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTopicUpdateRequest.ProtoReflect.Descriptor instead.
func (*DeviceTopicUpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{99}
}

func (x *DeviceTopicUpdateRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *DeviceTopicUpdateRequest) GetOp() string {
	if x != nil {
		return x.Op
	}
	return ""
}

func (x *DeviceTopicUpdateRequest) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

type UserTopicUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Op            string                 `protobuf:"bytes,2,opt,name=op,proto3" json:"op,omitempty"` // add | remove | set
	Topics        []string               `protobuf:"bytes,3,rep,name=topics,proto3" json:"topics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserTopicUpdateRequest) Reset() {
	*x = UserTopicUpdateRequest{}
	mi := &file_api_proto_msgTypes[100]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserTopicUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTopicUpdateRequest) ProtoMessage() {}

func (x *UserTopicUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[100]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTopicUpdateRequest.ProtoReflect.Descriptor instead.
func (*UserTopicUpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{100}
}

func (x *UserTopicUpdateRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *UserTopicUpdateRequest) GetOp() string {
	if x != nil {
		return x.Op
	}
	return ""
}

func (x *UserTopicUpdateRequest) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

type DeviceRegisterResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *DeviceRegisterResult  `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceRegisterResponse) Reset() {
	*x = DeviceRegisterResponse{}
	mi := &file_api_proto_msgTypes[101]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceRegisterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRegisterResponse) ProtoMessage() {}

func (x *DeviceRegisterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[101]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRegisterResponse.ProtoReflect.Descriptor instead.
func (*DeviceRegisterResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{101}
}

func (x *DeviceRegisterResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DeviceRegisterResponse) GetResult() *DeviceRegisterResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type DeviceUpdateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *DeviceUpdateResult    `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceUpdateResponse) Reset() {
	*x = DeviceUpdateResponse{}
	mi := &file_api_proto_msgTypes[102]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceUpdateResponse) ProtoMessage() {}

func (x *DeviceUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[102]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceUpdateResponse.ProtoReflect.Descriptor instead.
func (*DeviceUpdateResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{102}
}

func (x *DeviceUpdateResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DeviceUpdateResponse) GetResult() *DeviceUpdateResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type DeviceRemoveResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *DeviceRemoveResult    `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceRemoveResponse) Reset() {
	*x = DeviceRemoveResponse{}
	mi := &file_api_proto_msgTypes[103]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceRemoveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRemoveResponse) ProtoMessage() {}

func (x *DeviceRemoveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[103]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRemoveResponse.ProtoReflect.Descriptor instead.
func (*DeviceRemoveResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{103}
}

func (x *DeviceRemoveResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DeviceRemoveResponse) GetResult() *DeviceRemoveResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type DeviceListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *DeviceListResult      `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceListResponse) Reset() {
	*x = DeviceListResponse{}
	mi := &file_api_proto_msgTypes[104]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceListResponse) ProtoMessage() {}

func (x *DeviceListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[104]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceListResponse.ProtoReflect.Descriptor instead.
func (*DeviceListResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{104}
}

func (x *DeviceListResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DeviceListResponse) GetResult() *DeviceListResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type DeviceTopicListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *DeviceTopicListResult `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceTopicListResponse) Reset() {
	*x = DeviceTopicListResponse{}
	mi := &file_api_proto_msgTypes[105]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceTopicListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTopicListResponse) ProtoMessage() {}

func (x *DeviceTopicListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[105]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTopicListResponse.ProtoReflect.Descriptor instead.
func (*DeviceTopicListResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{105}
}

func (x *DeviceTopicListResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DeviceTopicListResponse) GetResult() *DeviceTopicListResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type UserTopicListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *UserTopicListResult   `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserTopicListResponse) Reset() {
	*x = UserTopicListResponse{}
	mi := &file_api_proto_msgTypes[106]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserTopicListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTopicListResponse) ProtoMessage() {}

func (x *UserTopicListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[106]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTopicListResponse.ProtoReflect.Descriptor instead.
func (*UserTopicListResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{106}
}

func (x *UserTopicListResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UserTopicListResponse) GetResult() *UserTopicListResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type DeviceTopicUpdateResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Error         *Error                   `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *DeviceTopicUpdateResult `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceTopicUpdateResponse) Reset() {
	*x = DeviceTopicUpdateResponse{}
	mi := &file_api_proto_msgTypes[107]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceTopicUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTopicUpdateResponse) ProtoMessage() {}

func (x *DeviceTopicUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[107]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTopicUpdateResponse.ProtoReflect.Descriptor instead.
func (*DeviceTopicUpdateResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{107}
}

func (x *DeviceTopicUpdateResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DeviceTopicUpdateResponse) GetResult() *DeviceTopicUpdateResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type UserTopicUpdateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *UserTopicUpdateResult `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserTopicUpdateResponse) Reset() {
	*x = UserTopicUpdateResponse{}
	mi := &file_api_proto_msgTypes[108]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserTopicUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTopicUpdateResponse) ProtoMessage() {}

func (x *UserTopicUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[108]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTopicUpdateResponse.ProtoReflect.Descriptor instead.
func (*UserTopicUpdateResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{108}
}

func (x *UserTopicUpdateResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UserTopicUpdateResponse) GetResult() *UserTopicUpdateResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type DeviceRegisterResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceRegisterResult) Reset() {
	*x = DeviceRegisterResult{}
	mi := &file_api_proto_msgTypes[109]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceRegisterResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRegisterResult) ProtoMessage() {}

func (x *DeviceRegisterResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[109]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRegisterResult.ProtoReflect.Descriptor instead.
func (*DeviceRegisterResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{109}
}

func (x *DeviceRegisterResult) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeviceUpdateResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceUpdateResult) Reset() {
	*x = DeviceUpdateResult{}
	mi := &file_api_proto_msgTypes[110]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceUpdateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceUpdateResult) ProtoMessage() {}

func (x *DeviceUpdateResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[110]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceUpdateResult.ProtoReflect.Descriptor instead.
func (*DeviceUpdateResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{110}
}

type DeviceRemoveResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceRemoveResult) Reset() {
	*x = DeviceRemoveResult{}
	mi := &file_api_proto_msgTypes[111]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceRemoveResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRemoveResult) ProtoMessage() {}

func (x *DeviceRemoveResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[111]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRemoveResult.ProtoReflect.Descriptor instead.
func (*DeviceRemoveResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{111}
}

type DeviceListResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Device              `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	NextCursor    string                 `protobuf:"bytes,2,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	TotalCount    int64                  `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceListResult) Reset() {
	*x = DeviceListResult{}
	mi := &file_api_proto_msgTypes[112]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceListResult) ProtoMessage() {}

func (x *DeviceListResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[112]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceListResult.ProtoReflect.Descriptor instead.
func (*DeviceListResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{112}
}

func (x *DeviceListResult) GetItems() []*Device {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *DeviceListResult) GetNextCursor() string {
	if x != nil {
		return x.NextCursor
	}
	return ""
}

func (x *DeviceListResult) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type Device struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Platform      string                 `protobuf:"bytes,2,opt,name=platform,proto3" json:"platform,omitempty"`
	Provider      string                 `protobuf:"bytes,3,opt,name=provider,proto3" json:"provider,omitempty"`
	Token         string                 `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	User          string                 `protobuf:"bytes,5,opt,name=user,proto3" json:"user,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Meta          map[string]string      `protobuf:"bytes,10,rep,name=meta,proto3" json:"meta,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Topics        []string               `protobuf:"bytes,11,rep,name=topics,proto3" json:"topics,omitempty"`
	Timezone      string                 `protobuf:"bytes,12,opt,name=timezone,proto3" json:"timezone,omitempty"`
	Locale        string                 `protobuf:"bytes,13,opt,name=locale,proto3" json:"locale,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Device) Reset() {
	*x = Device{}
	mi := &file_api_proto_msgTypes[113]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[113]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{113}
}

func (x *Device) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Device) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *Device) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *Device) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *Device) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *Device) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Device) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *Device) GetMeta() map[string]string {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *Device) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

func (x *Device) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *Device) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

type DeviceTopicListResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*DeviceTopic         `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	NextCursor    string                 `protobuf:"bytes,2,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	TotalCount    int64                  `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceTopicListResult) Reset() {
	*x = DeviceTopicListResult{}
	mi := &file_api_proto_msgTypes[114]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceTopicListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTopicListResult) ProtoMessage() {}

func (x *DeviceTopicListResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[114]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTopicListResult.ProtoReflect.Descriptor instead.
func (*DeviceTopicListResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{114}
}

func (x *DeviceTopicListResult) GetItems() []*DeviceTopic {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *DeviceTopicListResult) GetNextCursor() string {
	if x != nil {
		return x.NextCursor
	}
	return ""
}

func (x *DeviceTopicListResult) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type DeviceTopic struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Topic         string                 `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	Device        *Device                `protobuf:"bytes,3,opt,name=device,proto3" json:"device,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceTopic) Reset() {
	*x = DeviceTopic{}
	mi := &file_api_proto_msgTypes[115]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceTopic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTopic) ProtoMessage() {}

func (x *DeviceTopic) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[115]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTopic.ProtoReflect.Descriptor instead.
func (*DeviceTopic) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{115}
}

func (x *DeviceTopic) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeviceTopic) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *DeviceTopic) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type UserTopicListResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*UserTopic           `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	NextCursor    string                 `protobuf:"bytes,2,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	TotalCount    int64                  `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserTopicListResult) Reset() {
	*x = UserTopicListResult{}
	mi := &file_api_proto_msgTypes[116]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserTopicListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTopicListResult) ProtoMessage() {}

func (x *UserTopicListResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[116]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTopicListResult.ProtoReflect.Descriptor instead.
func (*UserTopicListResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{116}
}

func (x *UserTopicListResult) GetItems() []*UserTopic {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *UserTopicListResult) GetNextCursor() string {
	if x != nil {
		return x.NextCursor
	}
	return ""
}

func (x *UserTopicListResult) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type DeviceTopicUpdateResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceTopicUpdateResult) Reset() {
	*x = DeviceTopicUpdateResult{}
	mi := &file_api_proto_msgTypes[117]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceTopicUpdateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTopicUpdateResult) ProtoMessage() {}

func (x *DeviceTopicUpdateResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[117]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTopicUpdateResult.ProtoReflect.Descriptor instead.
func (*DeviceTopicUpdateResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{117}
}

type UserTopicUpdateResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserTopicUpdateResult) Reset() {
	*x = UserTopicUpdateResult{}
	mi := &file_api_proto_msgTypes[118]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserTopicUpdateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTopicUpdateResult) ProtoMessage() {}

func (x *UserTopicUpdateResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[118]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTopicUpdateResult.ProtoReflect.Descriptor instead.
func (*UserTopicUpdateResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{118}
}

type UserTopic struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	User          string                 `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Topic         string                 `protobuf:"bytes,3,opt,name=topic,proto3" json:"topic,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserTopic) Reset() {
	*x = UserTopic{}
	mi := &file_api_proto_msgTypes[119]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserTopic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTopic) ProtoMessage() {}

func (x *UserTopic) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[119]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTopic.ProtoReflect.Descriptor instead.
func (*UserTopic) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{119}
}

func (x *UserTopic) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UserTopic) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *UserTopic) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type PushRecipient struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Filter        *DeviceFilter          `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	FcmTokens     []string               `protobuf:"bytes,2,rep,name=fcm_tokens,json=fcmTokens,proto3" json:"fcm_tokens,omitempty"`
	FcmTopic      string                 `protobuf:"bytes,3,opt,name=fcm_topic,json=fcmTopic,proto3" json:"fcm_topic,omitempty"`
	FcmCondition  string                 `protobuf:"bytes,4,opt,name=fcm_condition,json=fcmCondition,proto3" json:"fcm_condition,omitempty"`
	HmsTokens     []string               `protobuf:"bytes,5,rep,name=hms_tokens,json=hmsTokens,proto3" json:"hms_tokens,omitempty"`
	HmsTopic      string                 `protobuf:"bytes,6,opt,name=hms_topic,json=hmsTopic,proto3" json:"hms_topic,omitempty"`
	HmsCondition  string                 `protobuf:"bytes,7,opt,name=hms_condition,json=hmsCondition,proto3" json:"hms_condition,omitempty"`
	ApnsTokens    []string               `protobuf:"bytes,8,rep,name=apns_tokens,json=apnsTokens,proto3" json:"apns_tokens,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushRecipient) Reset() {
	*x = PushRecipient{}
	mi := &file_api_proto_msgTypes[120]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushRecipient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushRecipient) ProtoMessage() {}

func (x *PushRecipient) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[120]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushRecipient.ProtoReflect.Descriptor instead.
func (*PushRecipient) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{120}
}

func (x *PushRecipient) GetFilter() *DeviceFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *PushRecipient) GetFcmTokens() []string {
	if x != nil {
		return x.FcmTokens
	}
	return nil
}

func (x *PushRecipient) GetFcmTopic() string {
	if x != nil {
		return x.FcmTopic
	}
	return ""
}

func (x *PushRecipient) GetFcmCondition() string {
	if x != nil {
		return x.FcmCondition
	}
	return ""
}

func (x *PushRecipient) GetHmsTokens() []string {
	if x != nil {
		return x.HmsTokens
	}
	return nil
}

func (x *PushRecipient) GetHmsTopic() string {
	if x != nil {
		return x.HmsTopic
	}
	return ""
}

func (x *PushRecipient) GetHmsCondition() string {
	if x != nil {
		return x.HmsCondition
	}
	return ""
}

func (x *PushRecipient) GetApnsTokens() []string {
	if x != nil {
		return x.ApnsTokens
	}
	return nil
}

type PushNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Fcm           *FcmPushNotification   `protobuf:"bytes,1,opt,name=fcm,proto3" json:"fcm,omitempty"`
	Hms           *HmsPushNotification   `protobuf:"bytes,2,opt,name=hms,proto3" json:"hms,omitempty"`
	Apns          *ApnsPushNotification  `protobuf:"bytes,3,opt,name=apns,proto3" json:"apns,omitempty"`
	ExpireAt      int64                  `protobuf:"varint,5,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"` // timestamp in the future when Centrifugo should stop trying to send push notification.
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushNotification) Reset() {
	*x = PushNotification{}
	mi := &file_api_proto_msgTypes[121]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushNotification) ProtoMessage() {}

func (x *PushNotification) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[121]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushNotification.ProtoReflect.Descriptor instead.
func (*PushNotification) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{121}
}

func (x *PushNotification) GetFcm() *FcmPushNotification {
	if x != nil {
		return x.Fcm
	}
	return nil
}

func (x *PushNotification) GetHms() *HmsPushNotification {
	if x != nil {
		return x.Hms
	}
	return nil
}

func (x *PushNotification) GetApns() *ApnsPushNotification {
	if x != nil {
		return x.Apns
	}
	return nil
}

func (x *PushNotification) GetExpireAt() int64 {
	if x != nil {
		return x.ExpireAt
	}
	return 0
}

type FcmPushNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       []byte                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FcmPushNotification) Reset() {
	*x = FcmPushNotification{}
	mi := &file_api_proto_msgTypes[122]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FcmPushNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FcmPushNotification) ProtoMessage() {}

func (x *FcmPushNotification) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[122]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FcmPushNotification.ProtoReflect.Descriptor instead.
func (*FcmPushNotification) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{122}
}

func (x *FcmPushNotification) GetMessage() []byte {
	if x != nil {
		return x.Message
	}
	return nil
}

type HmsPushNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       []byte                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HmsPushNotification) Reset() {
	*x = HmsPushNotification{}
	mi := &file_api_proto_msgTypes[123]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HmsPushNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HmsPushNotification) ProtoMessage() {}

func (x *HmsPushNotification) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[123]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HmsPushNotification.ProtoReflect.Descriptor instead.
func (*HmsPushNotification) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{123}
}

func (x *HmsPushNotification) GetMessage() []byte {
	if x != nil {
		return x.Message
	}
	return nil
}

type ApnsPushNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Headers       map[string]string      `protobuf:"bytes,1,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Payload       []byte                 `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApnsPushNotification) Reset() {
	*x = ApnsPushNotification{}
	mi := &file_api_proto_msgTypes[124]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApnsPushNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApnsPushNotification) ProtoMessage() {}

func (x *ApnsPushNotification) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[124]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApnsPushNotification.ProtoReflect.Descriptor instead.
func (*ApnsPushNotification) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{124}
}

func (x *ApnsPushNotification) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *ApnsPushNotification) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

type SendPushNotificationRequest struct {
	state                  protoimpl.MessageState       `protogen:"open.v1"`
	Recipient              *PushRecipient               `protobuf:"bytes,1,opt,name=recipient,proto3" json:"recipient,omitempty"`
	Notification           *PushNotification            `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
	Uid                    string                       `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,omitempty"`                                                                                               // unique identifier for each push notification request, can be used to cancel push.
	SendAt                 int64                        `protobuf:"varint,4,opt,name=send_at,json=sendAt,proto3" json:"send_at,omitempty"`                                                                          // Unix seconds, if set - push will be sent at this time, if not set - immediately.
	OptimizeForReliability bool                         `protobuf:"varint,5,opt,name=optimize_for_reliability,json=optimizeForReliability,proto3" json:"optimize_for_reliability,omitempty"`                        // makes processing heavier, but tolerates edge cases, like not loosing inflight pushes due to temporary queue unavailability.
	LimitStrategy          *PushLimitStrategy           `protobuf:"bytes,6,opt,name=limit_strategy,json=limitStrategy,proto3" json:"limit_strategy,omitempty"`                                                      // strategy for sending push notifications. Applicable only for pushes with filter recipient. When using this field Centrifugo processes devices one by one.
	AnalyticsUid           string                       `protobuf:"bytes,7,opt,name=analytics_uid,json=analyticsUid,proto3" json:"analytics_uid,omitempty"`                                                         // uid for push notification analytics, if not set - Centrifugo will use uid field.
	Localizations          map[string]*PushLocalization `protobuf:"bytes,8,rep,name=localizations,proto3" json:"localizations,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // optional per language/locale localizations for push notification.
	UseTemplating          bool                         `protobuf:"varint,9,opt,name=use_templating,json=useTemplating,proto3" json:"use_templating,omitempty"`                                                     // if set - Centrifugo will use templating for push notification. Note that setting localizations enables templating automatically.
	UseMeta                bool                         `protobuf:"varint,10,opt,name=use_meta,json=useMeta,proto3" json:"use_meta,omitempty"`                                                                      // if set - Centrifugo will additionally load device meta during push sending, this meta becomes available in templating.
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *SendPushNotificationRequest) Reset() {
	*x = SendPushNotificationRequest{}
	mi := &file_api_proto_msgTypes[125]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPushNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPushNotificationRequest) ProtoMessage() {}

func (x *SendPushNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[125]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPushNotificationRequest.ProtoReflect.Descriptor instead.
func (*SendPushNotificationRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{125}
}

func (x *SendPushNotificationRequest) GetRecipient() *PushRecipient {
	if x != nil {
		return x.Recipient
	}
	return nil
}

func (x *SendPushNotificationRequest) GetNotification() *PushNotification {
	if x != nil {
		return x.Notification
	}
	return nil
}

func (x *SendPushNotificationRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SendPushNotificationRequest) GetSendAt() int64 {
	if x != nil {
		return x.SendAt
	}
	return 0
}

func (x *SendPushNotificationRequest) GetOptimizeForReliability() bool {
	if x != nil {
		return x.OptimizeForReliability
	}
	return false
}

func (x *SendPushNotificationRequest) GetLimitStrategy() *PushLimitStrategy {
	if x != nil {
		return x.LimitStrategy
	}
	return nil
}

func (x *SendPushNotificationRequest) GetAnalyticsUid() string {
	if x != nil {
		return x.AnalyticsUid
	}
	return ""
}

func (x *SendPushNotificationRequest) GetLocalizations() map[string]*PushLocalization {
	if x != nil {
		return x.Localizations
	}
	return nil
}

func (x *SendPushNotificationRequest) GetUseTemplating() bool {
	if x != nil {
		return x.UseTemplating
	}
	return false
}

func (x *SendPushNotificationRequest) GetUseMeta() bool {
	if x != nil {
		return x.UseMeta
	}
	return false
}

type PushLocalization struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Translations  map[string]string      `protobuf:"bytes,1,rep,name=translations,proto3" json:"translations,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // variable name to value for the specific language/locale.
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushLocalization) Reset() {
	*x = PushLocalization{}
	mi := &file_api_proto_msgTypes[126]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushLocalization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushLocalization) ProtoMessage() {}

func (x *PushLocalization) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[126]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushLocalization.ProtoReflect.Descriptor instead.
func (*PushLocalization) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{126}
}

func (x *PushLocalization) GetTranslations() map[string]string {
	if x != nil {
		return x.Translations
	}
	return nil
}

type PushLimitStrategy struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RateLimit     *PushRateLimitStrategy `protobuf:"bytes,1,opt,name=rate_limit,json=rateLimit,proto3" json:"rate_limit,omitempty"`
	TimeLimit     *PushTimeLimitStrategy `protobuf:"bytes,2,opt,name=time_limit,json=timeLimit,proto3" json:"time_limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushLimitStrategy) Reset() {
	*x = PushLimitStrategy{}
	mi := &file_api_proto_msgTypes[127]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushLimitStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushLimitStrategy) ProtoMessage() {}

func (x *PushLimitStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[127]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushLimitStrategy.ProtoReflect.Descriptor instead.
func (*PushLimitStrategy) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{127}
}

func (x *PushLimitStrategy) GetRateLimit() *PushRateLimitStrategy {
	if x != nil {
		return x.RateLimit
	}
	return nil
}

func (x *PushLimitStrategy) GetTimeLimit() *PushTimeLimitStrategy {
	if x != nil {
		return x.TimeLimit
	}
	return nil
}

type PushTimeLimitStrategy struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	SendAfterTime  string                 `protobuf:"bytes,1,opt,name=send_after_time,json=sendAfterTime,proto3" json:"send_after_time,omitempty"`    // HH:MM:SS
	SendBeforeTime string                 `protobuf:"bytes,2,opt,name=send_before_time,json=sendBeforeTime,proto3" json:"send_before_time,omitempty"` // HH:MM:SS
	NoTzSendNow    bool                   `protobuf:"varint,3,opt,name=no_tz_send_now,json=noTzSendNow,proto3" json:"no_tz_send_now,omitempty"`       // If device timezone is not set - send push now, by default will be dropped.
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PushTimeLimitStrategy) Reset() {
	*x = PushTimeLimitStrategy{}
	mi := &file_api_proto_msgTypes[128]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushTimeLimitStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushTimeLimitStrategy) ProtoMessage() {}

func (x *PushTimeLimitStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[128]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushTimeLimitStrategy.ProtoReflect.Descriptor instead.
func (*PushTimeLimitStrategy) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{128}
}

func (x *PushTimeLimitStrategy) GetSendAfterTime() string {
	if x != nil {
		return x.SendAfterTime
	}
	return ""
}

func (x *PushTimeLimitStrategy) GetSendBeforeTime() string {
	if x != nil {
		return x.SendBeforeTime
	}
	return ""
}

func (x *PushTimeLimitStrategy) GetNoTzSendNow() bool {
	if x != nil {
		return x.NoTzSendNow
	}
	return false
}

type PushRateLimitStrategy struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Key               string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"` // optional key for rate limit policy, supports variables.
	Policies          []*RateLimitPolicy     `protobuf:"bytes,2,rep,name=policies,proto3" json:"policies,omitempty"`
	DropIfRateLimited bool                   `protobuf:"varint,3,opt,name=drop_if_rate_limited,json=dropIfRateLimited,proto3" json:"drop_if_rate_limited,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PushRateLimitStrategy) Reset() {
	*x = PushRateLimitStrategy{}
	mi := &file_api_proto_msgTypes[129]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushRateLimitStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushRateLimitStrategy) ProtoMessage() {}

func (x *PushRateLimitStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[129]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushRateLimitStrategy.ProtoReflect.Descriptor instead.
func (*PushRateLimitStrategy) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{129}
}

func (x *PushRateLimitStrategy) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *PushRateLimitStrategy) GetPolicies() []*RateLimitPolicy {
	if x != nil {
		return x.Policies
	}
	return nil
}

func (x *PushRateLimitStrategy) GetDropIfRateLimited() bool {
	if x != nil {
		return x.DropIfRateLimited
	}
	return false
}

type RateLimitPolicy struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Rate          int64                  `protobuf:"varint,1,opt,name=rate,proto3" json:"rate,omitempty"`
	IntervalMs    int32                  `protobuf:"varint,2,opt,name=interval_ms,json=intervalMs,proto3" json:"interval_ms,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RateLimitPolicy) Reset() {
	*x = RateLimitPolicy{}
	mi := &file_api_proto_msgTypes[130]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RateLimitPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimitPolicy) ProtoMessage() {}

func (x *RateLimitPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[130]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimitPolicy.ProtoReflect.Descriptor instead.
func (*RateLimitPolicy) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{130}
}

func (x *RateLimitPolicy) GetRate() int64 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *RateLimitPolicy) GetIntervalMs() int32 {
	if x != nil {
		return x.IntervalMs
	}
	return 0
}

type SendPushNotificationResponse struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Error         *Error                      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *SendPushNotificationResult `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPushNotificationResponse) Reset() {
	*x = SendPushNotificationResponse{}
	mi := &file_api_proto_msgTypes[131]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPushNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPushNotificationResponse) ProtoMessage() {}

func (x *SendPushNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[131]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPushNotificationResponse.ProtoReflect.Descriptor instead.
func (*SendPushNotificationResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{131}
}

func (x *SendPushNotificationResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SendPushNotificationResponse) GetResult() *SendPushNotificationResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type SendPushNotificationResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uid           string                 `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"` // Unique identifier of notification send request (it's not a FCM message id).
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPushNotificationResult) Reset() {
	*x = SendPushNotificationResult{}
	mi := &file_api_proto_msgTypes[132]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPushNotificationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPushNotificationResult) ProtoMessage() {}

func (x *SendPushNotificationResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[132]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPushNotificationResult.ProtoReflect.Descriptor instead.
func (*SendPushNotificationResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{132}
}

func (x *SendPushNotificationResult) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type UpdatePushStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AnalyticsUid  string                 `protobuf:"bytes,1,opt,name=analytics_uid,json=analyticsUid,proto3" json:"analytics_uid,omitempty"` // analytics uid of push notification (should match SendPushNotificationRequest.analytics_uid)
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`                                 // delivered | interacted
	DeviceId      string                 `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`             // Centrifugo device id.
	MsgId         string                 `protobuf:"bytes,4,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`                      // Provider issued message id.
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePushStatusRequest) Reset() {
	*x = UpdatePushStatusRequest{}
	mi := &file_api_proto_msgTypes[133]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePushStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePushStatusRequest) ProtoMessage() {}

func (x *UpdatePushStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[133]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePushStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdatePushStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{133}
}

func (x *UpdatePushStatusRequest) GetAnalyticsUid() string {
	if x != nil {
		return x.AnalyticsUid
	}
	return ""
}

func (x *UpdatePushStatusRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UpdatePushStatusRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *UpdatePushStatusRequest) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

type UpdatePushStatusResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *Error                  `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *UpdatePushStatusResult `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePushStatusResponse) Reset() {
	*x = UpdatePushStatusResponse{}
	mi := &file_api_proto_msgTypes[134]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePushStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePushStatusResponse) ProtoMessage() {}

func (x *UpdatePushStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[134]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePushStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdatePushStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{134}
}

func (x *UpdatePushStatusResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UpdatePushStatusResponse) GetResult() *UpdatePushStatusResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type UpdatePushStatusResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePushStatusResult) Reset() {
	*x = UpdatePushStatusResult{}
	mi := &file_api_proto_msgTypes[135]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePushStatusResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePushStatusResult) ProtoMessage() {}

func (x *UpdatePushStatusResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[135]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePushStatusResult.ProtoReflect.Descriptor instead.
func (*UpdatePushStatusResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{135}
}

type CancelPushRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uid           string                 `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelPushRequest) Reset() {
	*x = CancelPushRequest{}
	mi := &file_api_proto_msgTypes[136]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelPushRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPushRequest) ProtoMessage() {}

func (x *CancelPushRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[136]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPushRequest.ProtoReflect.Descriptor instead.
func (*CancelPushRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{136}
}

func (x *CancelPushRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type CancelPushResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Result        *CancelPushResult      `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelPushResponse) Reset() {
	*x = CancelPushResponse{}
	mi := &file_api_proto_msgTypes[137]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelPushResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPushResponse) ProtoMessage() {}

func (x *CancelPushResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[137]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPushResponse.ProtoReflect.Descriptor instead.
func (*CancelPushResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{137}
}

func (x *CancelPushResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CancelPushResponse) GetResult() *CancelPushResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type CancelPushResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelPushResult) Reset() {
	*x = CancelPushResult{}
	mi := &file_api_proto_msgTypes[138]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelPushResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPushResult) ProtoMessage() {}

func (x *CancelPushResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_msgTypes[138]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPushResult.ProtoReflect.Descriptor instead.
func (*CancelPushResult) Descriptor() ([]byte, []int) {
	return file_api_proto_rawDescGZIP(), []int{138}
}

var File_api_proto protoreflect.FileDescriptor

var file_api_proto_rawDesc = string([]byte{
	0x0a, 0x09, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x22, 0xaf, 0x15, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x12, 0x44, 0x0a, 0x07, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x07, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x12, 0x4a, 0x0a, 0x09, 0x62, 0x72, 0x6f,
	0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63,
	0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x09, 0x62, 0x72, 0x6f, 0x61,
	0x64, 0x63, 0x61, 0x73, 0x74, 0x12, 0x4a, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x09, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x12, 0x50, 0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x55, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x75, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x12, 0x47, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x08, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x57, 0x0a, 0x0e, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61,
	0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x12, 0x44, 0x0a, 0x07, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x07, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x57, 0x0a, 0x0e, 0x68, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x0d, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x12, 0x3b, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f,
	0x12, 0x38, 0x0a, 0x03, 0x72, 0x70, 0x63, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x50, 0x43, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x03, 0x72, 0x70, 0x63, 0x12, 0x44, 0x0a, 0x07, 0x72, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x12, 0x47, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x08, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12, 0x50, 0x0a, 0x0b, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x61, 0x0a, 0x12, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x10, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x58,
	0x0a, 0x0f, 0x67, 0x65, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0d, 0x67, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x61, 0x0a, 0x12, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x10, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x0a, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x09, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x12, 0x51, 0x0a, 0x0c, 0x75, 0x6e, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x6e, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b,
	0x75, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x12, 0x51, 0x0a, 0x0c, 0x72,
	0x65, 0x76, 0x6f, 0x6b, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52,
	0x65, 0x76, 0x6f, 0x6b, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x0b, 0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x6d,
	0x0a, 0x16, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x49, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x14, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x5a, 0x0a,
	0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x54, 0x0a, 0x0d, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x54, 0x0a, 0x0d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x4e, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x5e, 0x0a, 0x11, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69,
	0x63, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x64, 0x0a, 0x13, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x11, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x58, 0x0a, 0x0f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x1f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69,
	0x63, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x5e, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x6d, 0x0a, 0x16, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x70, 0x75,
	0x73, 0x68, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x14,
	0x73, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x61, 0x0a, 0x12, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x70,
	0x75, 0x73, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x33, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x10, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73,
	0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4e, 0x0a, 0x0b, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x63, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x50, 0x75, 0x73, 0x68, 0x4a, 0x04, 0x08, 0x01, 0x10, 0x02, 0x4a, 0x04, 0x08,
	0x02, 0x10, 0x03, 0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x22, 0x35, 0x0a, 0x05, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0xc0, 0x15, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x43, 0x0a, 0x07, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x07, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x12, 0x49, 0x0a, 0x09, 0x62, 0x72, 0x6f, 0x61,
	0x64, 0x63, 0x61, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x09, 0x62, 0x72, 0x6f, 0x61, 0x64, 0x63,
	0x61, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x09, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x4f,
	0x0a, 0x0b, 0x75, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61,
	0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x55, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x0b, 0x75, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12,
	0x4c, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61,
	0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x0a, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x12, 0x46, 0x0a,
	0x08, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x08, 0x70, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x56, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65,
	0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0d,
	0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x43, 0x0a,
	0x07, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x68, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x12, 0x56, 0x0a, 0x0e, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x72, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0d, 0x68, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x3a, 0x0a, 0x04, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x37, 0x0a, 0x03, 0x72, 0x70, 0x63, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61,
	0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x52, 0x50, 0x43, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x70, 0x63, 0x12,
	0x43, 0x0a, 0x07, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x12, 0x46, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x08, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12, 0x4f, 0x0a, 0x0b,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x60, 0x0a,
	0x12, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x10, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x57, 0x0a, 0x0f, 0x67, 0x65, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0d, 0x67, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x60, 0x0a, 0x12, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x10, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a, 0x0a, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x09, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x0c, 0x75, 0x6e, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0b, 0x75, 0x6e, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x0c, 0x72, 0x65, 0x76, 0x6f,
	0x6b, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x76, 0x6f,
	0x6b, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0b, 0x72,
	0x65, 0x76, 0x6f, 0x6b, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x6c, 0x0a, 0x16, 0x69, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x14, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x59, 0x0a, 0x0f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x12, 0x53, 0x0a, 0x0d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0c, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x53, 0x0a, 0x0d, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x4d, 0x0a,
	0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x5d, 0x0a, 0x11,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x63, 0x0a, 0x13, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69,
	0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x11, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x57, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x5d, 0x0a, 0x11, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x20,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x6c, 0x0a, 0x16, 0x73, 0x65, 0x6e, 0x64,
	0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x14, 0x73, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x60, 0x0a, 0x12, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x22, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x10, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4d, 0x0a, 0x0b, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0a, 0x63, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x50, 0x75, 0x73, 0x68, 0x4a, 0x04, 0x08, 0x01, 0x10, 0x02, 0x4a, 0x04, 0x08,
	0x03, 0x10, 0x04, 0x22, 0x6b, 0x0a, 0x0c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c,
	0x22, 0x4c, 0x0a, 0x0d, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x3b, 0x0a, 0x07, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x07, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x65, 0x73, 0x22, 0xfc,
	0x02, 0x0a, 0x0e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x18, 0x0a, 0x07, 0x62, 0x36, 0x34, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x62, 0x36, 0x34, 0x64, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x6b, 0x69,
	0x70, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x73, 0x6b, 0x69, 0x70, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x48, 0x0a, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x64, 0x65, 0x6d, 0x70, 0x6f,
	0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x69, 0x64, 0x65, 0x6d, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x4b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x64, 0x65, 0x6c, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x23, 0x0a, 0x0d, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x70, 0x6f, 0x63, 0x68,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x45,
	0x70, 0x6f, 0x63, 0x68, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8d, 0x01,
	0x0a, 0x0f, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x41, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x3d, 0x0a,
	0x0d, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06,
	0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x22, 0x82, 0x03, 0x0a,
	0x10, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x36, 0x34, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x62, 0x36, 0x34, 0x64, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x6b, 0x69, 0x70, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x73, 0x6b, 0x69, 0x70, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x4a,
	0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63,
	0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x64,
	0x65, 0x6d, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x64, 0x65, 0x6d, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x63, 0x79,
	0x4b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65,
	0x70, 0x6f, 0x63, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x45, 0x70, 0x6f, 0x63, 0x68, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x91, 0x01, 0x0a, 0x11, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x43, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x72,
	0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x5c, 0x0a, 0x0f, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x49, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x73, 0x22, 0x8d, 0x03, 0x0a, 0x10, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x41, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x36, 0x34, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x36, 0x34, 0x69, 0x6e, 0x66,
	0x6f, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a,
	0x07, 0x62, 0x36, 0x34, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x62, 0x36, 0x34, 0x64, 0x61, 0x74, 0x61, 0x12, 0x4f, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x4f, 0x0a, 0x08, 0x6f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52,
	0x08, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0x91, 0x01, 0x0a, 0x11, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x43, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x21, 0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x22, 0x0a, 0x0a, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x9e,
	0x03, 0x0a, 0x17, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x41, 0x0a, 0x08, 0x70, 0x72,
	0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x08, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x44, 0x0a,
	0x0a, 0x6a, 0x6f, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42,
	0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6a, 0x6f, 0x69, 0x6e, 0x4c, 0x65,
	0x61, 0x76, 0x65, 0x12, 0x4c, 0x0a, 0x0e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x0d, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x79, 0x12, 0x52, 0x0a, 0x11, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x10, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x58, 0x0a, 0x15, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x70,
	0x75, 0x73, 0x68, 0x5f, 0x6a, 0x6f, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12, 0x66, 0x6f, 0x72,
	0x63, 0x65, 0x50, 0x75, 0x73, 0x68, 0x4a, 0x6f, 0x69, 0x6e, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x22,
	0x11, 0x0a, 0x0f, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x74, 0x0a, 0x12, 0x55, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x95, 0x01, 0x0a, 0x13, 0x55, 0x6e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x45, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x13, 0x0a, 0x11, 0x55, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x3e, 0x0a, 0x0a, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x4a,
	0x04, 0x08, 0x03, 0x10, 0x04, 0x22, 0xbf, 0x01, 0x0a, 0x11, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12,
	0x46, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61,
	0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52, 0x0a, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x93, 0x01, 0x0a, 0x12, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x44, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x12, 0x0a,
	0x10, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x2b, 0x0a, 0x0f, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x8f,
	0x01, 0x0a, 0x10, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x42, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x72, 0x0a, 0x0a, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f,
	0x6e, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x63,
	0x6f, 0x6e, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x63, 0x68, 0x61, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0xcb, 0x01, 0x0a, 0x0e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x54, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x73, 0x65,
	0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x08, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x1a, 0x63, 0x0a,
	0x0d, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x30, 0x0a, 0x14, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x99, 0x01, 0x0a, 0x15, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x47, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x53, 0x0a, 0x13, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x5f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6e, 0x75,
	0x6d, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x75, 0x6d, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6e, 0x75, 0x6d,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x22, 0x3e, 0x0a, 0x0e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x70, 0x6f, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x70, 0x6f, 0x63, 0x68, 0x22, 0x9c, 0x01, 0x0a, 0x0e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x40, 0x0a, 0x05, 0x73, 0x69, 0x6e, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x05, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65,
	0x76, 0x65, 0x72, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x65, 0x76,
	0x65, 0x72, 0x73, 0x65, 0x22, 0x8d, 0x01, 0x0a, 0x0f, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x41, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0xf5, 0x01, 0x0a, 0x0b, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3a, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04,
	0x69, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x45, 0x0a, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8a, 0x01, 0x0a,
	0x0d, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4b,
	0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x70, 0x6f, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x70, 0x6f, 0x63,
	0x68, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x30, 0x0a, 0x14, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x99, 0x01, 0x0a, 0x15,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x47,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x15, 0x0a, 0x13, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x0d,
	0x0a, 0x0b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x87, 0x01,
	0x0a, 0x0c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x3e, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x4a, 0x0a, 0x0a, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x05, 0x6e, 0x6f,
	0x64, 0x65, 0x73, 0x22, 0x3c, 0x0a, 0x0a, 0x52, 0x50, 0x43, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x22, 0x85, 0x01, 0x0a, 0x0b, 0x52, 0x50, 0x43, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x3d, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x50, 0x43, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1f, 0x0a, 0x09, 0x52, 0x50, 0x43,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa1, 0x01, 0x0a, 0x0e, 0x52,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x41, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04,
	0x69, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x8d,
	0x01, 0x0a, 0x0f, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x41, 0x0a, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x0f,
	0x0a, 0x0d, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0xde, 0x02, 0x0a, 0x0a, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x6e, 0x75, 0x6d, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x6e, 0x75, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x21, 0x0a, 0x0c,
	0x6e, 0x75, 0x6d, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x07, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x3d, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x07, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x75, 0x62,
	0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x6e, 0x75, 0x6d, 0x53, 0x75, 0x62, 0x73,
	0x22, 0xa5, 0x01, 0x0a, 0x07, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x44, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0x38,
	0x0a, 0x0a, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x2d, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x03, 0x63, 0x70, 0x75, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x72, 0x73, 0x73, 0x22, 0x2b, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61,
	0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x74,
	0x74, 0x65, 0x72, 0x6e, 0x22, 0x8f, 0x01, 0x0a, 0x10, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x42, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xcc, 0x01, 0x0a, 0x0e, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x54, 0x0a, 0x08, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x1a,
	0x64, 0x0a, 0x0d, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x3d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x2e, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x5f, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x48, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12,
	0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0x95, 0x01, 0x0a, 0x13, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x45, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xe1, 0x01, 0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x60, 0x0a,
	0x0b, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a,
	0x6a, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x40, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xe9, 0x01, 0x0a, 0x0e,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x4a, 0x04, 0x08, 0x05, 0x10,
	0x06, 0x4a, 0x04, 0x08, 0x07, 0x10, 0x08, 0x22, 0xb1, 0x04, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x55, 0x0a, 0x08, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x73, 0x12, 0x5a, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x74,
	0x0a, 0x13, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x12, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x1a, 0x67, 0x0a, 0x0d, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x40, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x78, 0x0a, 0x17, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x47,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x28, 0x0a, 0x0e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x44, 0x0a, 0x13, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x69, 0x73, 0x73, 0x75, 0x65, 0x64, 0x41, 0x74, 0x22, 0x46, 0x0a, 0x15, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x73, 0x75, 0x65,
	0x64, 0x41, 0x74, 0x22, 0x45, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x9f, 0x01, 0x0a, 0x18, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x4a, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x18, 0x0a, 0x16,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2c, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x22, 0x99, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x47, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x59, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x42, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x22, 0x66, 0x0a, 0x0a, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x22, 0x2f, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x22, 0x9f, 0x01, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x4a, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x18, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x43, 0x0a, 0x10, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x41,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x22, 0x11, 0x0a, 0x0f, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x91, 0x01, 0x0a, 0x11, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x43, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x28, 0x0a, 0x12,
	0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x22, 0x13, 0x0a, 0x11, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x95, 0x01, 0x0a, 0x13,
	0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x45, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x43, 0x0a, 0x12, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x41, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x52, 0x65, 0x76, 0x6f,
	0x6b, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x95, 0x01,
	0x0a, 0x13, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x45,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x76, 0x6f,
	0x6b, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x8d, 0x01, 0x0a, 0x1b, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f,
	0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x41, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x73, 0x75, 0x65, 0x64,
	0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x69,
	0x73, 0x73, 0x75, 0x65, 0x64, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x1c, 0x0a, 0x1a, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0xa7, 0x01, 0x0a, 0x1c, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61,
	0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x4e, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x49, 0x6e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xdf, 0x02,
	0x0a, 0x15, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x4f, 0x0a, 0x04, 0x6d, 0x65, 0x74,
	0x61, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x70, 0x69,
	0x63, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x4d, 0x65, 0x74, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xe0, 0x03, 0x0a, 0x13, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12,
	0x4d, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x4d,
	0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61,
	0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x61, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x53, 0x0a,
	0x0d, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x59, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x74,
	0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x53, 0x0a,
	0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x22, 0x3d, 0x0a, 0x13, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x22, 0x26, 0x0a, 0x10, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x22, 0x32, 0x0a, 0x14, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x22, 0x2c, 0x0a,
	0x12, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x22, 0x97, 0x01, 0x0a, 0x10,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x4a, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x4d, 0x65, 0x74,
	0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x1a, 0x37, 0x0a, 0x09,
	0x4d, 0x65, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3c, 0x0a, 0x12, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x6f, 0x70, 0x69, 0x63, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x74,
	0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x70,
	0x69, 0x63, 0x73, 0x22, 0x8a, 0x01, 0x0a, 0x0c, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x73,
	0x22, 0xfd, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x69,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x54, 0x6f, 0x70, 0x69,
	0x63, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x22, 0xe6, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73,
	0x12, 0x29, 0x0a, 0x10, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f,
	0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x22, 0xe4, 0x01, 0x0a, 0x16, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x45, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x69,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x69,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x22, 0x62, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x70,
	0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69,
	0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x50, 0x72,
	0x65, 0x66, 0x69, 0x78, 0x22, 0xb9, 0x01, 0x0a, 0x14, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54,
	0x6f, 0x70, 0x69, 0x63, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x11, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x22, 0x5f, 0x0a, 0x18, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x70,
	0x69, 0x63, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x73, 0x22, 0x54, 0x0a, 0x16, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12,
	0x0e, 0x0a, 0x02, 0x6f, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x70, 0x12,
	0x16, 0x0a, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x22, 0x9b, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x48, 0x0a, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x97, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x46, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x97, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x46, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x93, 0x01, 0x0a, 0x12, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x44, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x9d, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x49, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x99, 0x01, 0x0a, 0x15, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x47, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xa1, 0x01, 0x0a, 0x19,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x4b, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x9d, 0x01, 0x0a, 0x17, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x49, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x26, 0x0a, 0x14, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x14, 0x0a,
	0x12, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x8e, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x38, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x43, 0x75, 0x72,
	0x73, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0xff, 0x02, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x40, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x6d, 0x65, 0x74,
	0x61, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d,
	0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d,
	0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x1a, 0x37, 0x0a,
	0x09, 0x4d, 0x65, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x98, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x3d, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x6f, 0x0a, 0x0b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x3a, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x22, 0x94, 0x01, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3b, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x65,
	0x78, 0x74, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x19, 0x0a, 0x17, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x17, 0x0a, 0x15, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69,
	0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x45, 0x0a,
	0x09, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x70, 0x69, 0x63, 0x22, 0xb4, 0x02, 0x0a, 0x0d, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x63,
	0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x40, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x63, 0x6d, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x66, 0x63,
	0x6d, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x63, 0x6d, 0x5f, 0x74,
	0x6f, 0x70, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x63, 0x6d, 0x54,
	0x6f, 0x70, 0x69, 0x63, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x63, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x63, 0x6d,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x6d, 0x73,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x68,
	0x6d, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6d, 0x73, 0x5f,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6d, 0x73,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x6d, 0x73, 0x5f, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x68, 0x6d,
	0x73, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70,
	0x6e, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x70, 0x6e, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x22, 0xfb, 0x01, 0x0a, 0x10,
	0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x41, 0x0a, 0x03, 0x66, 0x63, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x63, 0x6d, 0x50, 0x75,
	0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03,
	0x66, 0x63, 0x6d, 0x12, 0x41, 0x0a, 0x03, 0x68, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x48, 0x6d,
	0x73, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x03, 0x68, 0x6d, 0x73, 0x12, 0x44, 0x0a, 0x04, 0x61, 0x70, 0x6e, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x41, 0x70, 0x6e, 0x73, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x61, 0x70, 0x6e, 0x73, 0x12, 0x1b, 0x0a, 0x09,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x41, 0x74, 0x22, 0x2f, 0x0a, 0x13, 0x46, 0x63, 0x6d,
	0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x2f, 0x0a, 0x13, 0x48, 0x6d,
	0x73, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xc5, 0x01, 0x0a, 0x14,
	0x41, 0x70, 0x6e, 0x73, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x57, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x41, 0x70, 0x6e, 0x73, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x1a, 0x3a, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xbc, 0x05, 0x0a, 0x1b, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x47, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x50, 0x0a, 0x0c,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x41, 0x74, 0x12, 0x38, 0x0a, 0x18, 0x6f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x6c, 0x69, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x6f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x12, 0x54, 0x0a, 0x0e, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x0d, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x55, 0x69, 0x64, 0x12, 0x70,
	0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c,
	0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x25, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x75, 0x73, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x1a, 0x6e, 0x0a, 0x12, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x42, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x6f, 0x63, 0x61, 0x6c,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xb7, 0x01, 0x0a, 0x10, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x6f, 0x63, 0x61, 0x6c,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x62, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x4c,
	0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x3f, 0x0a, 0x11, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb7, 0x01, 0x0a,
	0x11, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x12, 0x50, 0x0a, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x09, 0x72, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x50, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x8e, 0x01, 0x0a, 0x15, 0x50, 0x75, 0x73, 0x68, 0x54,
	0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x12, 0x26, 0x0a, 0x0f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x6e, 0x64, 0x41,
	0x66, 0x74, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x65, 0x6e, 0x64,
	0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x6e, 0x64, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0e, 0x6e, 0x6f, 0x5f, 0x74, 0x7a, 0x5f, 0x73, 0x65, 0x6e, 0x64,
	0x5f, 0x6e, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6e, 0x6f, 0x54, 0x7a,
	0x53, 0x65, 0x6e, 0x64, 0x4e, 0x6f, 0x77, 0x22, 0xa3, 0x01, 0x0a, 0x15, 0x50, 0x75, 0x73, 0x68,
	0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x08, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x52, 0x08, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x14,
	0x64, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x66, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x64, 0x72, 0x6f, 0x70,
	0x49, 0x66, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x22, 0x46, 0x0a,
	0x0f, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04,
	0x72, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x4d, 0x73, 0x22, 0xa7, 0x01, 0x0a, 0x1c, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75,
	0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12,
	0x4e, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x2e, 0x0a, 0x1a, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22,
	0x8a, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x55, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6d, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x22, 0x9f, 0x01, 0x0a,
	0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x4a, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x18,
	0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x25, 0x0a, 0x11, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22,
	0x93, 0x01, 0x0a, 0x12, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12,
	0x44, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x12, 0x0a, 0x10, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50,
	0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xe8, 0x1d, 0x0a, 0x0d, 0x43, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x41, 0x70, 0x69, 0x12, 0x5e, 0x0a, 0x05, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x12, 0x28, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x07, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x12, 0x2a, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x6a, 0x0a, 0x09, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x12, 0x2c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x72, 0x6f, 0x61,
	0x64, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63,
	0x61, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6a, 0x0a,
	0x09, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x2c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x0b, 0x55, 0x6e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x2e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6d, 0x0a, 0x0a, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x12, 0x2d, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x08, 0x50, 0x72,
	0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x2b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61,
	0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x76, 0x0a, 0x0d, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x12, 0x30, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x07, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x2a, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x76, 0x0a, 0x0d, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x12, 0x30, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x04, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x27, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x58, 0x0a, 0x03, 0x52, 0x50, 0x43, 0x12, 0x26, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x50, 0x43, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x52, 0x50, 0x43, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x64, 0x0a, 0x07, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x12, 0x2a, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x08, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x73, 0x12, 0x2b, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x70, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x7f, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x76, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x30, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61,
	0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7f, 0x0a, 0x10, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61,
	0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6a, 0x0a, 0x09, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x0b, 0x55, 0x6e, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x0b, 0x52, 0x65, 0x76,
	0x6f, 0x6b, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8b, 0x01, 0x0a, 0x14,
	0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x73, 0x12, 0x37, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x49, 0x6e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x79, 0x0a, 0x0e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x31, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x73, 0x0a, 0x0c, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x73, 0x0a, 0x0c, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x2f, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66,
	0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6d,
	0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2d, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7c, 0x0a,
	0x0f, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x32, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x82, 0x01, 0x0a, 0x11,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x34, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x76, 0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x30, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61,
	0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7c, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x32, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x33, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8b, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x6e, 0x64, 0x50,
	0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x37, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x7f, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e,
	0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6d, 0x0a, 0x0a, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50,
	0x75, 0x73, 0x68, 0x12, 0x2d, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61,
	0x6c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x61, 0x6c,
	0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x66, 0x75, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x42, 0x0d, 0x5a, 0x0b, 0x2e, 0x2f, 0x3b, 0x61, 0x70, 0x69, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_api_proto_rawDescOnce sync.Once
	file_api_proto_rawDescData []byte
)

func file_api_proto_rawDescGZIP() []byte {
	file_api_proto_rawDescOnce.Do(func() {
		file_api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_proto_rawDesc), len(file_api_proto_rawDesc)))
	})
	return file_api_proto_rawDescData
}

var file_api_proto_msgTypes = make([]protoimpl.MessageInfo, 154)
var file_api_proto_goTypes = []any{
	(*Command)(nil),                      // 0: centrifugal.centrifugo.api.Command
	(*Error)(nil),                        // 1: centrifugal.centrifugo.api.Error
	(*Reply)(nil),                        // 2: centrifugal.centrifugo.api.Reply
	(*BatchRequest)(nil),                 // 3: centrifugal.centrifugo.api.BatchRequest
	(*BatchResponse)(nil),                // 4: centrifugal.centrifugo.api.BatchResponse
	(*PublishRequest)(nil),               // 5: centrifugal.centrifugo.api.PublishRequest
	(*PublishResponse)(nil),              // 6: centrifugal.centrifugo.api.PublishResponse
	(*PublishResult)(nil),                // 7: centrifugal.centrifugo.api.PublishResult
	(*BroadcastRequest)(nil),             // 8: centrifugal.centrifugo.api.BroadcastRequest
	(*BroadcastResponse)(nil),            // 9: centrifugal.centrifugo.api.BroadcastResponse
	(*BroadcastResult)(nil),              // 10: centrifugal.centrifugo.api.BroadcastResult
	(*SubscribeRequest)(nil),             // 11: centrifugal.centrifugo.api.SubscribeRequest
	(*SubscribeResponse)(nil),            // 12: centrifugal.centrifugo.api.SubscribeResponse
	(*BoolValue)(nil),                    // 13: centrifugal.centrifugo.api.BoolValue
	(*Int32Value)(nil),                   // 14: centrifugal.centrifugo.api.Int32Value
	(*SubscribeOptionOverride)(nil),      // 15: centrifugal.centrifugo.api.SubscribeOptionOverride
	(*SubscribeResult)(nil),              // 16: centrifugal.centrifugo.api.SubscribeResult
	(*UnsubscribeRequest)(nil),           // 17: centrifugal.centrifugo.api.UnsubscribeRequest
	(*UnsubscribeResponse)(nil),          // 18: centrifugal.centrifugo.api.UnsubscribeResponse
	(*UnsubscribeResult)(nil),            // 19: centrifugal.centrifugo.api.UnsubscribeResult
	(*Disconnect)(nil),                   // 20: centrifugal.centrifugo.api.Disconnect
	(*DisconnectRequest)(nil),            // 21: centrifugal.centrifugo.api.DisconnectRequest
	(*DisconnectResponse)(nil),           // 22: centrifugal.centrifugo.api.DisconnectResponse
	(*DisconnectResult)(nil),             // 23: centrifugal.centrifugo.api.DisconnectResult
	(*PresenceRequest)(nil),              // 24: centrifugal.centrifugo.api.PresenceRequest
	(*PresenceResponse)(nil),             // 25: centrifugal.centrifugo.api.PresenceResponse
	(*ClientInfo)(nil),                   // 26: centrifugal.centrifugo.api.ClientInfo
	(*PresenceResult)(nil),               // 27: centrifugal.centrifugo.api.PresenceResult
	(*PresenceStatsRequest)(nil),         // 28: centrifugal.centrifugo.api.PresenceStatsRequest
	(*PresenceStatsResponse)(nil),        // 29: centrifugal.centrifugo.api.PresenceStatsResponse
	(*PresenceStatsResult)(nil),          // 30: centrifugal.centrifugo.api.PresenceStatsResult
	(*StreamPosition)(nil),               // 31: centrifugal.centrifugo.api.StreamPosition
	(*HistoryRequest)(nil),               // 32: centrifugal.centrifugo.api.HistoryRequest
	(*HistoryResponse)(nil),              // 33: centrifugal.centrifugo.api.HistoryResponse
	(*Publication)(nil),                  // 34: centrifugal.centrifugo.api.Publication
	(*HistoryResult)(nil),                // 35: centrifugal.centrifugo.api.HistoryResult
	(*HistoryRemoveRequest)(nil),         // 36: centrifugal.centrifugo.api.HistoryRemoveRequest
	(*HistoryRemoveResponse)(nil),        // 37: centrifugal.centrifugo.api.HistoryRemoveResponse
	(*HistoryRemoveResult)(nil),          // 38: centrifugal.centrifugo.api.HistoryRemoveResult
	(*InfoRequest)(nil),                  // 39: centrifugal.centrifugo.api.InfoRequest
	(*InfoResponse)(nil),                 // 40: centrifugal.centrifugo.api.InfoResponse
	(*InfoResult)(nil),                   // 41: centrifugal.centrifugo.api.InfoResult
	(*RPCRequest)(nil),                   // 42: centrifugal.centrifugo.api.RPCRequest
	(*RPCResponse)(nil),                  // 43: centrifugal.centrifugo.api.RPCResponse
	(*RPCResult)(nil),                    // 44: centrifugal.centrifugo.api.RPCResult
	(*RefreshRequest)(nil),               // 45: centrifugal.centrifugo.api.RefreshRequest
	(*RefreshResponse)(nil),              // 46: centrifugal.centrifugo.api.RefreshResponse
	(*RefreshResult)(nil),                // 47: centrifugal.centrifugo.api.RefreshResult
	(*NodeResult)(nil),                   // 48: centrifugal.centrifugo.api.NodeResult
	(*Metrics)(nil),                      // 49: centrifugal.centrifugo.api.Metrics
	(*Process)(nil),                      // 50: centrifugal.centrifugo.api.Process
	(*ChannelsRequest)(nil),              // 51: centrifugal.centrifugo.api.ChannelsRequest
	(*ChannelsResponse)(nil),             // 52: centrifugal.centrifugo.api.ChannelsResponse
	(*ChannelsResult)(nil),               // 53: centrifugal.centrifugo.api.ChannelsResult
	(*ChannelInfo)(nil),                  // 54: centrifugal.centrifugo.api.ChannelInfo
	(*ConnectionsRequest)(nil),           // 55: centrifugal.centrifugo.api.ConnectionsRequest
	(*ConnectionsResponse)(nil),          // 56: centrifugal.centrifugo.api.ConnectionsResponse
	(*ConnectionsResult)(nil),            // 57: centrifugal.centrifugo.api.ConnectionsResult
	(*ConnectionInfo)(nil),               // 58: centrifugal.centrifugo.api.ConnectionInfo
	(*ConnectionState)(nil),              // 59: centrifugal.centrifugo.api.ConnectionState
	(*ChannelContext)(nil),               // 60: centrifugal.centrifugo.api.ChannelContext
	(*ConnectionTokenInfo)(nil),          // 61: centrifugal.centrifugo.api.ConnectionTokenInfo
	(*SubscriptionTokenInfo)(nil),        // 62: centrifugal.centrifugo.api.SubscriptionTokenInfo
	(*UpdateUserStatusRequest)(nil),      // 63: centrifugal.centrifugo.api.UpdateUserStatusRequest
	(*UpdateUserStatusResponse)(nil),     // 64: centrifugal.centrifugo.api.UpdateUserStatusResponse
	(*UpdateUserStatusResult)(nil),       // 65: centrifugal.centrifugo.api.UpdateUserStatusResult
	(*GetUserStatusRequest)(nil),         // 66: centrifugal.centrifugo.api.GetUserStatusRequest
	(*GetUserStatusResponse)(nil),        // 67: centrifugal.centrifugo.api.GetUserStatusResponse
	(*GetUserStatusResult)(nil),          // 68: centrifugal.centrifugo.api.GetUserStatusResult
	(*UserStatus)(nil),                   // 69: centrifugal.centrifugo.api.UserStatus
	(*DeleteUserStatusRequest)(nil),      // 70: centrifugal.centrifugo.api.DeleteUserStatusRequest
	(*DeleteUserStatusResponse)(nil),     // 71: centrifugal.centrifugo.api.DeleteUserStatusResponse
	(*DeleteUserStatusResult)(nil),       // 72: centrifugal.centrifugo.api.DeleteUserStatusResult
	(*BlockUserRequest)(nil),             // 73: centrifugal.centrifugo.api.BlockUserRequest
	(*BlockUserResult)(nil),              // 74: centrifugal.centrifugo.api.BlockUserResult
	(*BlockUserResponse)(nil),            // 75: centrifugal.centrifugo.api.BlockUserResponse
	(*UnblockUserRequest)(nil),           // 76: centrifugal.centrifugo.api.UnblockUserRequest
	(*UnblockUserResult)(nil),            // 77: centrifugal.centrifugo.api.UnblockUserResult
	(*UnblockUserResponse)(nil),          // 78: centrifugal.centrifugo.api.UnblockUserResponse
	(*RevokeTokenRequest)(nil),           // 79: centrifugal.centrifugo.api.RevokeTokenRequest
	(*RevokeTokenResult)(nil),            // 80: centrifugal.centrifugo.api.RevokeTokenResult
	(*RevokeTokenResponse)(nil),          // 81: centrifugal.centrifugo.api.RevokeTokenResponse
	(*InvalidateUserTokensRequest)(nil),  // 82: centrifugal.centrifugo.api.InvalidateUserTokensRequest
	(*InvalidateUserTokensResult)(nil),   // 83: centrifugal.centrifugo.api.InvalidateUserTokensResult
	(*InvalidateUserTokensResponse)(nil), // 84: centrifugal.centrifugo.api.InvalidateUserTokensResponse
	(*DeviceRegisterRequest)(nil),        // 85: centrifugal.centrifugo.api.DeviceRegisterRequest
	(*DeviceUpdateRequest)(nil),          // 86: centrifugal.centrifugo.api.DeviceUpdateRequest
	(*DeviceRemoveRequest)(nil),          // 87: centrifugal.centrifugo.api.DeviceRemoveRequest
	(*DeviceUserUpdate)(nil),             // 88: centrifugal.centrifugo.api.DeviceUserUpdate
	(*DeviceTimezoneUpdate)(nil),         // 89: centrifugal.centrifugo.api.DeviceTimezoneUpdate
	(*DeviceLocaleUpdate)(nil),           // 90: centrifugal.centrifugo.api.DeviceLocaleUpdate
	(*DeviceMetaUpdate)(nil),             // 91: centrifugal.centrifugo.api.DeviceMetaUpdate
	(*DeviceTopicsUpdate)(nil),           // 92: centrifugal.centrifugo.api.DeviceTopicsUpdate
	(*DeviceFilter)(nil),                 // 93: centrifugal.centrifugo.api.DeviceFilter
	(*DeviceListRequest)(nil),            // 94: centrifugal.centrifugo.api.DeviceListRequest
	(*DeviceTopicFilter)(nil),            // 95: centrifugal.centrifugo.api.DeviceTopicFilter
	(*DeviceTopicListRequest)(nil),       // 96: centrifugal.centrifugo.api.DeviceTopicListRequest
	(*UserTopicFilter)(nil),              // 97: centrifugal.centrifugo.api.UserTopicFilter
	(*UserTopicListRequest)(nil),         // 98: centrifugal.centrifugo.api.UserTopicListRequest
	(*DeviceTopicUpdateRequest)(nil),     // 99: centrifugal.centrifugo.api.DeviceTopicUpdateRequest
	(*UserTopicUpdateRequest)(nil),       // 100: centrifugal.centrifugo.api.UserTopicUpdateRequest
	(*DeviceRegisterResponse)(nil),       // 101: centrifugal.centrifugo.api.DeviceRegisterResponse
	(*DeviceUpdateResponse)(nil),         // 102: centrifugal.centrifugo.api.DeviceUpdateResponse
	(*DeviceRemoveResponse)(nil),         // 103: centrifugal.centrifugo.api.DeviceRemoveResponse
	(*DeviceListResponse)(nil),           // 104: centrifugal.centrifugo.api.DeviceListResponse
	(*DeviceTopicListResponse)(nil),      // 105: centrifugal.centrifugo.api.DeviceTopicListResponse
	(*UserTopicListResponse)(nil),        // 106: centrifugal.centrifugo.api.UserTopicListResponse
	(*DeviceTopicUpdateResponse)(nil),    // 107: centrifugal.centrifugo.api.DeviceTopicUpdateResponse
	(*UserTopicUpdateResponse)(nil),      // 108: centrifugal.centrifugo.api.UserTopicUpdateResponse
	(*DeviceRegisterResult)(nil),         // 109: centrifugal.centrifugo.api.DeviceRegisterResult
	(*DeviceUpdateResult)(nil),           // 110: centrifugal.centrifugo.api.DeviceUpdateResult
	(*DeviceRemoveResult)(nil),           // 111: centrifugal.centrifugo.api.DeviceRemoveResult
	(*DeviceListResult)(nil),             // 112: centrifugal.centrifugo.api.DeviceListResult
	(*Device)(nil),                       // 113: centrifugal.centrifugo.api.Device
	(*DeviceTopicListResult)(nil),        // 114: centrifugal.centrifugo.api.DeviceTopicListResult
	(*DeviceTopic)(nil),                  // 115: centrifugal.centrifugo.api.DeviceTopic
	(*UserTopicListResult)(nil),          // 116: centrifugal.centrifugo.api.UserTopicListResult
	(*DeviceTopicUpdateResult)(nil),      // 117: centrifugal.centrifugo.api.DeviceTopicUpdateResult
	(*UserTopicUpdateResult)(nil),        // 118: centrifugal.centrifugo.api.UserTopicUpdateResult
	(*UserTopic)(nil),                    // 119: centrifugal.centrifugo.api.UserTopic
	(*PushRecipient)(nil),                // 120: centrifugal.centrifugo.api.PushRecipient
	(*PushNotification)(nil),             // 121: centrifugal.centrifugo.api.PushNotification
	(*FcmPushNotification)(nil),          // 122: centrifugal.centrifugo.api.FcmPushNotification
	(*HmsPushNotification)(nil),          // 123: centrifugal.centrifugo.api.HmsPushNotification
	(*ApnsPushNotification)(nil),         // 124: centrifugal.centrifugo.api.ApnsPushNotification
	(*SendPushNotificationRequest)(nil),  // 125: centrifugal.centrifugo.api.SendPushNotificationRequest
	(*PushLocalization)(nil),             // 126: centrifugal.centrifugo.api.PushLocalization
	(*PushLimitStrategy)(nil),            // 127: centrifugal.centrifugo.api.PushLimitStrategy
	(*PushTimeLimitStrategy)(nil),        // 128: centrifugal.centrifugo.api.PushTimeLimitStrategy
	(*PushRateLimitStrategy)(nil),        // 129: centrifugal.centrifugo.api.PushRateLimitStrategy
	(*RateLimitPolicy)(nil),              // 130: centrifugal.centrifugo.api.RateLimitPolicy
	(*SendPushNotificationResponse)(nil), // 131: centrifugal.centrifugo.api.SendPushNotificationResponse
	(*SendPushNotificationResult)(nil),   // 132: centrifugal.centrifugo.api.SendPushNotificationResult
	(*UpdatePushStatusRequest)(nil),      // 133: centrifugal.centrifugo.api.UpdatePushStatusRequest
	(*UpdatePushStatusResponse)(nil),     // 134: centrifugal.centrifugo.api.UpdatePushStatusResponse
	(*UpdatePushStatusResult)(nil),       // 135: centrifugal.centrifugo.api.UpdatePushStatusResult
	(*CancelPushRequest)(nil),            // 136: centrifugal.centrifugo.api.CancelPushRequest
	(*CancelPushResponse)(nil),           // 137: centrifugal.centrifugo.api.CancelPushResponse
	(*CancelPushResult)(nil),             // 138: centrifugal.centrifugo.api.CancelPushResult
	nil,                                  // 139: centrifugal.centrifugo.api.PublishRequest.TagsEntry
	nil,                                  // 140: centrifugal.centrifugo.api.BroadcastRequest.TagsEntry
	nil,                                  // 141: centrifugal.centrifugo.api.PresenceResult.PresenceEntry
	nil,                                  // 142: centrifugal.centrifugo.api.Publication.TagsEntry
	nil,                                  // 143: centrifugal.centrifugo.api.Metrics.ItemsEntry
	nil,                                  // 144: centrifugal.centrifugo.api.ChannelsResult.ChannelsEntry
	nil,                                  // 145: centrifugal.centrifugo.api.ConnectionsResult.ConnectionsEntry
	nil,                                  // 146: centrifugal.centrifugo.api.ConnectionState.ChannelsEntry
	nil,                                  // 147: centrifugal.centrifugo.api.ConnectionState.SubscriptionTokensEntry
	nil,                                  // 148: centrifugal.centrifugo.api.DeviceRegisterRequest.MetaEntry
	nil,                                  // 149: centrifugal.centrifugo.api.DeviceMetaUpdate.MetaEntry
	nil,                                  // 150: centrifugal.centrifugo.api.Device.MetaEntry
	nil,                                  // 151: centrifugal.centrifugo.api.ApnsPushNotification.HeadersEntry
	nil,                                  // 152: centrifugal.centrifugo.api.SendPushNotificationRequest.LocalizationsEntry
	nil,                                  // 153: centrifugal.centrifugo.api.PushLocalization.TranslationsEntry
}
var file_api_proto_depIdxs = []int32{
	5,   // 0: centrifugal.centrifugo.api.Command.publish:type_name -> centrifugal.centrifugo.api.PublishRequest
	8,   // 1: centrifugal.centrifugo.api.Command.broadcast:type_name -> centrifugal.centrifugo.api.BroadcastRequest
	11,  // 2: centrifugal.centrifugo.api.Command.subscribe:type_name -> centrifugal.centrifugo.api.SubscribeRequest
	17,  // 3: centrifugal.centrifugo.api.Command.unsubscribe:type_name -> centrifugal.centrifugo.api.UnsubscribeRequest
	21,  // 4: centrifugal.centrifugo.api.Command.disconnect:type_name -> centrifugal.centrifugo.api.DisconnectRequest
	24,  // 5: centrifugal.centrifugo.api.Command.presence:type_name -> centrifugal.centrifugo.api.PresenceRequest
	28,  // 6: centrifugal.centrifugo.api.Command.presence_stats:type_name -> centrifugal.centrifugo.api.PresenceStatsRequest
	32,  // 7: centrifugal.centrifugo.api.Command.history:type_name -> centrifugal.centrifugo.api.HistoryRequest
	36,  // 8: centrifugal.centrifugo.api.Command.history_remove:type_name -> centrifugal.centrifugo.api.HistoryRemoveRequest
	39,  // 9: centrifugal.centrifugo.api.Command.info:type_name -> centrifugal.centrifugo.api.InfoRequest
	42,  // 10: centrifugal.centrifugo.api.Command.rpc:type_name -> centrifugal.centrifugo.api.RPCRequest
	45,  // 11: centrifugal.centrifugo.api.Command.refresh:type_name -> centrifugal.centrifugo.api.RefreshRequest
	51,  // 12: centrifugal.centrifugo.api.Command.channels:type_name -> centrifugal.centrifugo.api.ChannelsRequest
	55,  // 13: centrifugal.centrifugo.api.Command.connections:type_name -> centrifugal.centrifugo.api.ConnectionsRequest
	63,  // 14: centrifugal.centrifugo.api.Command.update_user_status:type_name -> centrifugal.centrifugo.api.UpdateUserStatusRequest
	66,  // 15: centrifugal.centrifugo.api.Command.get_user_status:type_name -> centrifugal.centrifugo.api.GetUserStatusRequest
	70,  // 16: centrifugal.centrifugo.api.Command.delete_user_status:type_name -> centrifugal.centrifugo.api.DeleteUserStatusRequest
	73,  // 17: centrifugal.centrifugo.api.Command.block_user:type_name -> centrifugal.centrifugo.api.BlockUserRequest
	76,  // 18: centrifugal.centrifugo.api.Command.unblock_user:type_name -> centrifugal.centrifugo.api.UnblockUserRequest
	79,  // 19: centrifugal.centrifugo.api.Command.revoke_token:type_name -> centrifugal.centrifugo.api.RevokeTokenRequest
	82,  // 20: centrifugal.centrifugo.api.Command.invalidate_user_tokens:type_name -> centrifugal.centrifugo.api.InvalidateUserTokensRequest
	85,  // 21: centrifugal.centrifugo.api.Command.device_register:type_name -> centrifugal.centrifugo.api.DeviceRegisterRequest
	86,  // 22: centrifugal.centrifugo.api.Command.device_update:type_name -> centrifugal.centrifugo.api.DeviceUpdateRequest
	87,  // 23: centrifugal.centrifugo.api.Command.device_remove:type_name -> centrifugal.centrifugo.api.DeviceRemoveRequest
	94,  // 24: centrifugal.centrifugo.api.Command.device_list:type_name -> centrifugal.centrifugo.api.DeviceListRequest
	96,  // 25: centrifugal.centrifugo.api.Command.device_topic_list:type_name -> centrifugal.centrifugo.api.DeviceTopicListRequest
	99,  // 26: centrifugal.centrifugo.api.Command.device_topic_update:type_name -> centrifugal.centrifugo.api.DeviceTopicUpdateRequest
	98,  // 27: centrifugal.centrifugo.api.Command.user_topic_list:type_name -> centrifugal.centrifugo.api.UserTopicListRequest
	100, // 28: centrifugal.centrifugo.api.Command.user_topic_update:type_name -> centrifugal.centrifugo.api.UserTopicUpdateRequest
	125, // 29: centrifugal.centrifugo.api.Command.send_push_notification:type_name -> centrifugal.centrifugo.api.SendPushNotificationRequest
	133, // 30: centrifugal.centrifugo.api.Command.update_push_status:type_name -> centrifugal.centrifugo.api.UpdatePushStatusRequest
	136, // 31: centrifugal.centrifugo.api.Command.cancel_push:type_name -> centrifugal.centrifugo.api.CancelPushRequest
	1,   // 32: centrifugal.centrifugo.api.Reply.error:type_name -> centrifugal.centrifugo.api.Error
	7,   // 33: centrifugal.centrifugo.api.Reply.publish:type_name -> centrifugal.centrifugo.api.PublishResult
	10,  // 34: centrifugal.centrifugo.api.Reply.broadcast:type_name -> centrifugal.centrifugo.api.BroadcastResult
	16,  // 35: centrifugal.centrifugo.api.Reply.subscribe:type_name -> centrifugal.centrifugo.api.SubscribeResult
	19,  // 36: centrifugal.centrifugo.api.Reply.unsubscribe:type_name -> centrifugal.centrifugo.api.UnsubscribeResult
	23,  // 37: centrifugal.centrifugo.api.Reply.disconnect:type_name -> centrifugal.centrifugo.api.DisconnectResult
	27,  // 38: centrifugal.centrifugo.api.Reply.presence:type_name -> centrifugal.centrifugo.api.PresenceResult
	30,  // 39: centrifugal.centrifugo.api.Reply.presence_stats:type_name -> centrifugal.centrifugo.api.PresenceStatsResult
	35,  // 40: centrifugal.centrifugo.api.Reply.history:type_name -> centrifugal.centrifugo.api.HistoryResult
	38,  // 41: centrifugal.centrifugo.api.Reply.history_remove:type_name -> centrifugal.centrifugo.api.HistoryRemoveResult
	41,  // 42: centrifugal.centrifugo.api.Reply.info:type_name -> centrifugal.centrifugo.api.InfoResult
	44,  // 43: centrifugal.centrifugo.api.Reply.rpc:type_name -> centrifugal.centrifugo.api.RPCResult
	47,  // 44: centrifugal.centrifugo.api.Reply.refresh:type_name -> centrifugal.centrifugo.api.RefreshResult
	53,  // 45: centrifugal.centrifugo.api.Reply.channels:type_name -> centrifugal.centrifugo.api.ChannelsResult
	57,  // 46: centrifugal.centrifugo.api.Reply.connections:type_name -> centrifugal.centrifugo.api.ConnectionsResult
	65,  // 47: centrifugal.centrifugo.api.Reply.update_user_status:type_name -> centrifugal.centrifugo.api.UpdateUserStatusResult
	68,  // 48: centrifugal.centrifugo.api.Reply.get_user_status:type_name -> centrifugal.centrifugo.api.GetUserStatusResult
	72,  // 49: centrifugal.centrifugo.api.Reply.delete_user_status:type_name -> centrifugal.centrifugo.api.DeleteUserStatusResult
	74,  // 50: centrifugal.centrifugo.api.Reply.block_user:type_name -> centrifugal.centrifugo.api.BlockUserResult
	77,  // 51: centrifugal.centrifugo.api.Reply.unblock_user:type_name -> centrifugal.centrifugo.api.UnblockUserResult
	80,  // 52: centrifugal.centrifugo.api.Reply.revoke_token:type_name -> centrifugal.centrifugo.api.RevokeTokenResult
	83,  // 53: centrifugal.centrifugo.api.Reply.invalidate_user_tokens:type_name -> centrifugal.centrifugo.api.InvalidateUserTokensResult
	109, // 54: centrifugal.centrifugo.api.Reply.device_register:type_name -> centrifugal.centrifugo.api.DeviceRegisterResult
	110, // 55: centrifugal.centrifugo.api.Reply.device_update:type_name -> centrifugal.centrifugo.api.DeviceUpdateResult
	111, // 56: centrifugal.centrifugo.api.Reply.device_remove:type_name -> centrifugal.centrifugo.api.DeviceRemoveResult
	112, // 57: centrifugal.centrifugo.api.Reply.device_list:type_name -> centrifugal.centrifugo.api.DeviceListResult
	114, // 58: centrifugal.centrifugo.api.Reply.device_topic_list:type_name -> centrifugal.centrifugo.api.DeviceTopicListResult
	117, // 59: centrifugal.centrifugo.api.Reply.device_topic_update:type_name -> centrifugal.centrifugo.api.DeviceTopicUpdateResult
	116, // 60: centrifugal.centrifugo.api.Reply.user_topic_list:type_name -> centrifugal.centrifugo.api.UserTopicListResult
	118, // 61: centrifugal.centrifugo.api.Reply.user_topic_update:type_name -> centrifugal.centrifugo.api.UserTopicUpdateResult
	132, // 62: centrifugal.centrifugo.api.Reply.send_push_notification:type_name -> centrifugal.centrifugo.api.SendPushNotificationResult
	135, // 63: centrifugal.centrifugo.api.Reply.update_push_status:type_name -> centrifugal.centrifugo.api.UpdatePushStatusResult
	138, // 64: centrifugal.centrifugo.api.Reply.cancel_push:type_name -> centrifugal.centrifugo.api.CancelPushResult
	0,   // 65: centrifugal.centrifugo.api.BatchRequest.commands:type_name -> centrifugal.centrifugo.api.Command
	2,   // 66: centrifugal.centrifugo.api.BatchResponse.replies:type_name -> centrifugal.centrifugo.api.Reply
	139, // 67: centrifugal.centrifugo.api.PublishRequest.tags:type_name -> centrifugal.centrifugo.api.PublishRequest.TagsEntry
	1,   // 68: centrifugal.centrifugo.api.PublishResponse.error:type_name -> centrifugal.centrifugo.api.Error
	7,   // 69: centrifugal.centrifugo.api.PublishResponse.result:type_name -> centrifugal.centrifugo.api.PublishResult
	140, // 70: centrifugal.centrifugo.api.BroadcastRequest.tags:type_name -> centrifugal.centrifugo.api.BroadcastRequest.TagsEntry
	1,   // 71: centrifugal.centrifugo.api.BroadcastResponse.error:type_name -> centrifugal.centrifugo.api.Error
	10,  // 72: centrifugal.centrifugo.api.BroadcastResponse.result:type_name -> centrifugal.centrifugo.api.BroadcastResult
	6,   // 73: centrifugal.centrifugo.api.BroadcastResult.responses:type_name -> centrifugal.centrifugo.api.PublishResponse
	31,  // 74: centrifugal.centrifugo.api.SubscribeRequest.recover_since:type_name -> centrifugal.centrifugo.api.StreamPosition
	15,  // 75: centrifugal.centrifugo.api.SubscribeRequest.override:type_name -> centrifugal.centrifugo.api.SubscribeOptionOverride
	1,   // 76: centrifugal.centrifugo.api.SubscribeResponse.error:type_name -> centrifugal.centrifugo.api.Error
	16,  // 77: centrifugal.centrifugo.api.SubscribeResponse.result:type_name -> centrifugal.centrifugo.api.SubscribeResult
	13,  // 78: centrifugal.centrifugo.api.SubscribeOptionOverride.presence:type_name -> centrifugal.centrifugo.api.BoolValue
	13,  // 79: centrifugal.centrifugo.api.SubscribeOptionOverride.join_leave:type_name -> centrifugal.centrifugo.api.BoolValue
	13,  // 80: centrifugal.centrifugo.api.SubscribeOptionOverride.force_recovery:type_name -> centrifugal.centrifugo.api.BoolValue
	13,  // 81: centrifugal.centrifugo.api.SubscribeOptionOverride.force_positioning:type_name -> centrifugal.centrifugo.api.BoolValue
	13,  // 82: centrifugal.centrifugo.api.SubscribeOptionOverride.force_push_join_leave:type_name -> centrifugal.centrifugo.api.BoolValue
	1,   // 83: centrifugal.centrifugo.api.UnsubscribeResponse.error:type_name -> centrifugal.centrifugo.api.Error
	19,  // 84: centrifugal.centrifugo.api.UnsubscribeResponse.result:type_name -> centrifugal.centrifugo.api.UnsubscribeResult
	20,  // 85: centrifugal.centrifugo.api.DisconnectRequest.disconnect:type_name -> centrifugal.centrifugo.api.Disconnect
	1,   // 86: centrifugal.centrifugo.api.DisconnectResponse.error:type_name -> centrifugal.centrifugo.api.Error
	23,  // 87: centrifugal.centrifugo.api.DisconnectResponse.result:type_name -> centrifugal.centrifugo.api.DisconnectResult
	1,   // 88: centrifugal.centrifugo.api.PresenceResponse.error:type_name -> centrifugal.centrifugo.api.Error
	27,  // 89: centrifugal.centrifugo.api.PresenceResponse.result:type_name -> centrifugal.centrifugo.api.PresenceResult
	141, // 90: centrifugal.centrifugo.api.PresenceResult.presence:type_name -> centrifugal.centrifugo.api.PresenceResult.PresenceEntry
	1,   // 91: centrifugal.centrifugo.api.PresenceStatsResponse.error:type_name -> centrifugal.centrifugo.api.Error
	30,  // 92: centrifugal.centrifugo.api.PresenceStatsResponse.result:type_name -> centrifugal.centrifugo.api.PresenceStatsResult
	31,  // 93: centrifugal.centrifugo.api.HistoryRequest.since:type_name -> centrifugal.centrifugo.api.StreamPosition
	1,   // 94: centrifugal.centrifugo.api.HistoryResponse.error:type_name -> centrifugal.centrifugo.api.Error
	35,  // 95: centrifugal.centrifugo.api.HistoryResponse.result:type_name -> centrifugal.centrifugo.api.HistoryResult
	26,  // 96: centrifugal.centrifugo.api.Publication.info:type_name -> centrifugal.centrifugo.api.ClientInfo
	142, // 97: centrifugal.centrifugo.api.Publication.tags:type_name -> centrifugal.centrifugo.api.Publication.TagsEntry
	34,  // 98: centrifugal.centrifugo.api.HistoryResult.publications:type_name -> centrifugal.centrifugo.api.Publication
	1,   // 99: centrifugal.centrifugo.api.HistoryRemoveResponse.error:type_name -> centrifugal.centrifugo.api.Error
	38,  // 100: centrifugal.centrifugo.api.HistoryRemoveResponse.result:type_name -> centrifugal.centrifugo.api.HistoryRemoveResult
	1,   // 101: centrifugal.centrifugo.api.InfoResponse.error:type_name -> centrifugal.centrifugo.api.Error
	41,  // 102: centrifugal.centrifugo.api.InfoResponse.result:type_name -> centrifugal.centrifugo.api.InfoResult
	48,  // 103: centrifugal.centrifugo.api.InfoResult.nodes:type_name -> centrifugal.centrifugo.api.NodeResult
	1,   // 104: centrifugal.centrifugo.api.RPCResponse.error:type_name -> centrifugal.centrifugo.api.Error
	44,  // 105: centrifugal.centrifugo.api.RPCResponse.result:type_name -> centrifugal.centrifugo.api.RPCResult
	1,   // 106: centrifugal.centrifugo.api.RefreshResponse.error:type_name -> centrifugal.centrifugo.api.Error
	47,  // 107: centrifugal.centrifugo.api.RefreshResponse.result:type_name -> centrifugal.centrifugo.api.RefreshResult
	49,  // 108: centrifugal.centrifugo.api.NodeResult.metrics:type_name -> centrifugal.centrifugo.api.Metrics
	50,  // 109: centrifugal.centrifugo.api.NodeResult.process:type_name -> centrifugal.centrifugo.api.Process
	143, // 110: centrifugal.centrifugo.api.Metrics.items:type_name -> centrifugal.centrifugo.api.Metrics.ItemsEntry
	1,   // 111: centrifugal.centrifugo.api.ChannelsResponse.error:type_name -> centrifugal.centrifugo.api.Error
	53,  // 112: centrifugal.centrifugo.api.ChannelsResponse.result:type_name -> centrifugal.centrifugo.api.ChannelsResult
	144, // 113: centrifugal.centrifugo.api.ChannelsResult.channels:type_name -> centrifugal.centrifugo.api.ChannelsResult.ChannelsEntry
	1,   // 114: centrifugal.centrifugo.api.ConnectionsResponse.error:type_name -> centrifugal.centrifugo.api.Error
	57,  // 115: centrifugal.centrifugo.api.ConnectionsResponse.result:type_name -> centrifugal.centrifugo.api.ConnectionsResult
	145, // 116: centrifugal.centrifugo.api.ConnectionsResult.connections:type_name -> centrifugal.centrifugo.api.ConnectionsResult.ConnectionsEntry
	59,  // 117: centrifugal.centrifugo.api.ConnectionInfo.state:type_name -> centrifugal.centrifugo.api.ConnectionState
	146, // 118: centrifugal.centrifugo.api.ConnectionState.channels:type_name -> centrifugal.centrifugo.api.ConnectionState.ChannelsEntry
	61,  // 119: centrifugal.centrifugo.api.ConnectionState.connection_token:type_name -> centrifugal.centrifugo.api.ConnectionTokenInfo
	147, // 120: centrifugal.centrifugo.api.ConnectionState.subscription_tokens:type_name -> centrifugal.centrifugo.api.ConnectionState.SubscriptionTokensEntry
	1,   // 121: centrifugal.centrifugo.api.UpdateUserStatusResponse.error:type_name -> centrifugal.centrifugo.api.Error
	65,  // 122: centrifugal.centrifugo.api.UpdateUserStatusResponse.result:type_name -> centrifugal.centrifugo.api.UpdateUserStatusResult
	1,   // 123: centrifugal.centrifugo.api.GetUserStatusResponse.error:type_name -> centrifugal.centrifugo.api.Error
	68,  // 124: centrifugal.centrifugo.api.GetUserStatusResponse.result:type_name -> centrifugal.centrifugo.api.GetUserStatusResult
	69,  // 125: centrifugal.centrifugo.api.GetUserStatusResult.statuses:type_name -> centrifugal.centrifugo.api.UserStatus
	1,   // 126: centrifugal.centrifugo.api.DeleteUserStatusResponse.error:type_name -> centrifugal.centrifugo.api.Error
	72,  // 127: centrifugal.centrifugo.api.DeleteUserStatusResponse.result:type_name -> centrifugal.centrifugo.api.DeleteUserStatusResult
	1,   // 128: centrifugal.centrifugo.api.BlockUserResponse.error:type_name -> centrifugal.centrifugo.api.Error
	74,  // 129: centrifugal.centrifugo.api.BlockUserResponse.result:type_name -> centrifugal.centrifugo.api.BlockUserResult
	1,   // 130: centrifugal.centrifugo.api.UnblockUserResponse.error:type_name -> centrifugal.centrifugo.api.Error
	77,  // 131: centrifugal.centrifugo.api.UnblockUserResponse.result:type_name -> centrifugal.centrifugo.api.UnblockUserResult
	1,   // 132: centrifugal.centrifugo.api.RevokeTokenResponse.error:type_name -> centrifugal.centrifugo.api.Error
	80,  // 133: centrifugal.centrifugo.api.RevokeTokenResponse.result:type_name -> centrifugal.centrifugo.api.RevokeTokenResult
	1,   // 134: centrifugal.centrifugo.api.InvalidateUserTokensResponse.error:type_name -> centrifugal.centrifugo.api.Error
	83,  // 135: centrifugal.centrifugo.api.InvalidateUserTokensResponse.result:type_name -> centrifugal.centrifugo.api.InvalidateUserTokensResult
	148, // 136: centrifugal.centrifugo.api.DeviceRegisterRequest.meta:type_name -> centrifugal.centrifugo.api.DeviceRegisterRequest.MetaEntry
	88,  // 137: centrifugal.centrifugo.api.DeviceUpdateRequest.user_update:type_name -> centrifugal.centrifugo.api.DeviceUserUpdate
	91,  // 138: centrifugal.centrifugo.api.DeviceUpdateRequest.meta_update:type_name -> centrifugal.centrifugo.api.DeviceMetaUpdate
	92,  // 139: centrifugal.centrifugo.api.DeviceUpdateRequest.topics_update:type_name -> centrifugal.centrifugo.api.DeviceTopicsUpdate
	89,  // 140: centrifugal.centrifugo.api.DeviceUpdateRequest.timezone_update:type_name -> centrifugal.centrifugo.api.DeviceTimezoneUpdate
	90,  // 141: centrifugal.centrifugo.api.DeviceUpdateRequest.locale_update:type_name -> centrifugal.centrifugo.api.DeviceLocaleUpdate
	149, // 142: centrifugal.centrifugo.api.DeviceMetaUpdate.meta:type_name -> centrifugal.centrifugo.api.DeviceMetaUpdate.MetaEntry
	93,  // 143: centrifugal.centrifugo.api.DeviceListRequest.filter:type_name -> centrifugal.centrifugo.api.DeviceFilter
	95,  // 144: centrifugal.centrifugo.api.DeviceTopicListRequest.filter:type_name -> centrifugal.centrifugo.api.DeviceTopicFilter
	97,  // 145: centrifugal.centrifugo.api.UserTopicListRequest.filter:type_name -> centrifugal.centrifugo.api.UserTopicFilter
	1,   // 146: centrifugal.centrifugo.api.DeviceRegisterResponse.error:type_name -> centrifugal.centrifugo.api.Error
	109, // 147: centrifugal.centrifugo.api.DeviceRegisterResponse.result:type_name -> centrifugal.centrifugo.api.DeviceRegisterResult
	1,   // 148: centrifugal.centrifugo.api.DeviceUpdateResponse.error:type_name -> centrifugal.centrifugo.api.Error
	110, // 149: centrifugal.centrifugo.api.DeviceUpdateResponse.result:type_name -> centrifugal.centrifugo.api.DeviceUpdateResult
	1,   // 150: centrifugal.centrifugo.api.DeviceRemoveResponse.error:type_name -> centrifugal.centrifugo.api.Error
	111, // 151: centrifugal.centrifugo.api.DeviceRemoveResponse.result:type_name -> centrifugal.centrifugo.api.DeviceRemoveResult
	1,   // 152: centrifugal.centrifugo.api.DeviceListResponse.error:type_name -> centrifugal.centrifugo.api.Error
	112, // 153: centrifugal.centrifugo.api.DeviceListResponse.result:type_name -> centrifugal.centrifugo.api.DeviceListResult
	1,   // 154: centrifugal.centrifugo.api.DeviceTopicListResponse.error:type_name -> centrifugal.centrifugo.api.Error
	114, // 155: centrifugal.centrifugo.api.DeviceTopicListResponse.result:type_name -> centrifugal.centrifugo.api.DeviceTopicListResult
	1,   // 156: centrifugal.centrifugo.api.UserTopicListResponse.error:type_name -> centrifugal.centrifugo.api.Error
	116, // 157: centrifugal.centrifugo.api.UserTopicListResponse.result:type_name -> centrifugal.centrifugo.api.UserTopicListResult
	1,   // 158: centrifugal.centrifugo.api.DeviceTopicUpdateResponse.error:type_name -> centrifugal.centrifugo.api.Error
	117, // 159: centrifugal.centrifugo.api.DeviceTopicUpdateResponse.result:type_name -> centrifugal.centrifugo.api.DeviceTopicUpdateResult
	1,   // 160: centrifugal.centrifugo.api.UserTopicUpdateResponse.error:type_name -> centrifugal.centrifugo.api.Error
	118, // 161: centrifugal.centrifugo.api.UserTopicUpdateResponse.result:type_name -> centrifugal.centrifugo.api.UserTopicUpdateResult
	113, // 162: centrifugal.centrifugo.api.DeviceListResult.items:type_name -> centrifugal.centrifugo.api.Device
	150, // 163: centrifugal.centrifugo.api.Device.meta:type_name -> centrifugal.centrifugo.api.Device.MetaEntry
	115, // 164: centrifugal.centrifugo.api.DeviceTopicListResult.items:type_name -> centrifugal.centrifugo.api.DeviceTopic
	113, // 165: centrifugal.centrifugo.api.DeviceTopic.device:type_name -> centrifugal.centrifugo.api.Device
	119, // 166: centrifugal.centrifugo.api.UserTopicListResult.items:type_name -> centrifugal.centrifugo.api.UserTopic
	93,  // 167: centrifugal.centrifugo.api.PushRecipient.filter:type_name -> centrifugal.centrifugo.api.DeviceFilter
	122, // 168: centrifugal.centrifugo.api.PushNotification.fcm:type_name -> centrifugal.centrifugo.api.FcmPushNotification
	123, // 169: centrifugal.centrifugo.api.PushNotification.hms:type_name -> centrifugal.centrifugo.api.HmsPushNotification
	124, // 170: centrifugal.centrifugo.api.PushNotification.apns:type_name -> centrifugal.centrifugo.api.ApnsPushNotification
	151, // 171: centrifugal.centrifugo.api.ApnsPushNotification.headers:type_name -> centrifugal.centrifugo.api.ApnsPushNotification.HeadersEntry
	120, // 172: centrifugal.centrifugo.api.SendPushNotificationRequest.recipient:type_name -> centrifugal.centrifugo.api.PushRecipient
	121, // 173: centrifugal.centrifugo.api.SendPushNotificationRequest.notification:type_name -> centrifugal.centrifugo.api.PushNotification
	127, // 174: centrifugal.centrifugo.api.SendPushNotificationRequest.limit_strategy:type_name -> centrifugal.centrifugo.api.PushLimitStrategy
	152, // 175: centrifugal.centrifugo.api.SendPushNotificationRequest.localizations:type_name -> centrifugal.centrifugo.api.SendPushNotificationRequest.LocalizationsEntry
	153, // 176: centrifugal.centrifugo.api.PushLocalization.translations:type_name -> centrifugal.centrifugo.api.PushLocalization.TranslationsEntry
	129, // 177: centrifugal.centrifugo.api.PushLimitStrategy.rate_limit:type_name -> centrifugal.centrifugo.api.PushRateLimitStrategy
	128, // 178: centrifugal.centrifugo.api.PushLimitStrategy.time_limit:type_name -> centrifugal.centrifugo.api.PushTimeLimitStrategy
	130, // 179: centrifugal.centrifugo.api.PushRateLimitStrategy.policies:type_name -> centrifugal.centrifugo.api.RateLimitPolicy
	1,   // 180: centrifugal.centrifugo.api.SendPushNotificationResponse.error:type_name -> centrifugal.centrifugo.api.Error
	132, // 181: centrifugal.centrifugo.api.SendPushNotificationResponse.result:type_name -> centrifugal.centrifugo.api.SendPushNotificationResult
	1,   // 182: centrifugal.centrifugo.api.UpdatePushStatusResponse.error:type_name -> centrifugal.centrifugo.api.Error
	135, // 183: centrifugal.centrifugo.api.UpdatePushStatusResponse.result:type_name -> centrifugal.centrifugo.api.UpdatePushStatusResult
	1,   // 184: centrifugal.centrifugo.api.CancelPushResponse.error:type_name -> centrifugal.centrifugo.api.Error
	138, // 185: centrifugal.centrifugo.api.CancelPushResponse.result:type_name -> centrifugal.centrifugo.api.CancelPushResult
	26,  // 186: centrifugal.centrifugo.api.PresenceResult.PresenceEntry.value:type_name -> centrifugal.centrifugo.api.ClientInfo
	54,  // 187: centrifugal.centrifugo.api.ChannelsResult.ChannelsEntry.value:type_name -> centrifugal.centrifugo.api.ChannelInfo
	58,  // 188: centrifugal.centrifugo.api.ConnectionsResult.ConnectionsEntry.value:type_name -> centrifugal.centrifugo.api.ConnectionInfo
	60,  // 189: centrifugal.centrifugo.api.ConnectionState.ChannelsEntry.value:type_name -> centrifugal.centrifugo.api.ChannelContext
	62,  // 190: centrifugal.centrifugo.api.ConnectionState.SubscriptionTokensEntry.value:type_name -> centrifugal.centrifugo.api.SubscriptionTokenInfo
	126, // 191: centrifugal.centrifugo.api.SendPushNotificationRequest.LocalizationsEntry.value:type_name -> centrifugal.centrifugo.api.PushLocalization
	3,   // 192: centrifugal.centrifugo.api.CentrifugoApi.Batch:input_type -> centrifugal.centrifugo.api.BatchRequest
	5,   // 193: centrifugal.centrifugo.api.CentrifugoApi.Publish:input_type -> centrifugal.centrifugo.api.PublishRequest
	8,   // 194: centrifugal.centrifugo.api.CentrifugoApi.Broadcast:input_type -> centrifugal.centrifugo.api.BroadcastRequest
	11,  // 195: centrifugal.centrifugo.api.CentrifugoApi.Subscribe:input_type -> centrifugal.centrifugo.api.SubscribeRequest
	17,  // 196: centrifugal.centrifugo.api.CentrifugoApi.Unsubscribe:input_type -> centrifugal.centrifugo.api.UnsubscribeRequest
	21,  // 197: centrifugal.centrifugo.api.CentrifugoApi.Disconnect:input_type -> centrifugal.centrifugo.api.DisconnectRequest
	24,  // 198: centrifugal.centrifugo.api.CentrifugoApi.Presence:input_type -> centrifugal.centrifugo.api.PresenceRequest
	28,  // 199: centrifugal.centrifugo.api.CentrifugoApi.PresenceStats:input_type -> centrifugal.centrifugo.api.PresenceStatsRequest
	32,  // 200: centrifugal.centrifugo.api.CentrifugoApi.History:input_type -> centrifugal.centrifugo.api.HistoryRequest
	36,  // 201: centrifugal.centrifugo.api.CentrifugoApi.HistoryRemove:input_type -> centrifugal.centrifugo.api.HistoryRemoveRequest
	39,  // 202: centrifugal.centrifugo.api.CentrifugoApi.Info:input_type -> centrifugal.centrifugo.api.InfoRequest
	42,  // 203: centrifugal.centrifugo.api.CentrifugoApi.RPC:input_type -> centrifugal.centrifugo.api.RPCRequest
	45,  // 204: centrifugal.centrifugo.api.CentrifugoApi.Refresh:input_type -> centrifugal.centrifugo.api.RefreshRequest
	51,  // 205: centrifugal.centrifugo.api.CentrifugoApi.Channels:input_type -> centrifugal.centrifugo.api.ChannelsRequest
	55,  // 206: centrifugal.centrifugo.api.CentrifugoApi.Connections:input_type -> centrifugal.centrifugo.api.ConnectionsRequest
	63,  // 207: centrifugal.centrifugo.api.CentrifugoApi.UpdateUserStatus:input_type -> centrifugal.centrifugo.api.UpdateUserStatusRequest
	66,  // 208: centrifugal.centrifugo.api.CentrifugoApi.GetUserStatus:input_type -> centrifugal.centrifugo.api.GetUserStatusRequest
	70,  // 209: centrifugal.centrifugo.api.CentrifugoApi.DeleteUserStatus:input_type -> centrifugal.centrifugo.api.DeleteUserStatusRequest
	73,  // 210: centrifugal.centrifugo.api.CentrifugoApi.BlockUser:input_type -> centrifugal.centrifugo.api.BlockUserRequest
	76,  // 211: centrifugal.centrifugo.api.CentrifugoApi.UnblockUser:input_type -> centrifugal.centrifugo.api.UnblockUserRequest
	79,  // 212: centrifugal.centrifugo.api.CentrifugoApi.RevokeToken:input_type -> centrifugal.centrifugo.api.RevokeTokenRequest
	82,  // 213: centrifugal.centrifugo.api.CentrifugoApi.InvalidateUserTokens:input_type -> centrifugal.centrifugo.api.InvalidateUserTokensRequest
	85,  // 214: centrifugal.centrifugo.api.CentrifugoApi.DeviceRegister:input_type -> centrifugal.centrifugo.api.DeviceRegisterRequest
	86,  // 215: centrifugal.centrifugo.api.CentrifugoApi.DeviceUpdate:input_type -> centrifugal.centrifugo.api.DeviceUpdateRequest
	87,  // 216: centrifugal.centrifugo.api.CentrifugoApi.DeviceRemove:input_type -> centrifugal.centrifugo.api.DeviceRemoveRequest
	94,  // 217: centrifugal.centrifugo.api.CentrifugoApi.DeviceList:input_type -> centrifugal.centrifugo.api.DeviceListRequest
	96,  // 218: centrifugal.centrifugo.api.CentrifugoApi.DeviceTopicList:input_type -> centrifugal.centrifugo.api.DeviceTopicListRequest
	99,  // 219: centrifugal.centrifugo.api.CentrifugoApi.DeviceTopicUpdate:input_type -> centrifugal.centrifugo.api.DeviceTopicUpdateRequest
	98,  // 220: centrifugal.centrifugo.api.CentrifugoApi.UserTopicList:input_type -> centrifugal.centrifugo.api.UserTopicListRequest
	100, // 221: centrifugal.centrifugo.api.CentrifugoApi.UserTopicUpdate:input_type -> centrifugal.centrifugo.api.UserTopicUpdateRequest
	125, // 222: centrifugal.centrifugo.api.CentrifugoApi.SendPushNotification:input_type -> centrifugal.centrifugo.api.SendPushNotificationRequest
	133, // 223: centrifugal.centrifugo.api.CentrifugoApi.UpdatePushStatus:input_type -> centrifugal.centrifugo.api.UpdatePushStatusRequest
	136, // 224: centrifugal.centrifugo.api.CentrifugoApi.CancelPush:input_type -> centrifugal.centrifugo.api.CancelPushRequest
	4,   // 225: centrifugal.centrifugo.api.CentrifugoApi.Batch:output_type -> centrifugal.centrifugo.api.BatchResponse
	6,   // 226: centrifugal.centrifugo.api.CentrifugoApi.Publish:output_type -> centrifugal.centrifugo.api.PublishResponse
	9,   // 227: centrifugal.centrifugo.api.CentrifugoApi.Broadcast:output_type -> centrifugal.centrifugo.api.BroadcastResponse
	12,  // 228: centrifugal.centrifugo.api.CentrifugoApi.Subscribe:output_type -> centrifugal.centrifugo.api.SubscribeResponse
	18,  // 229: centrifugal.centrifugo.api.CentrifugoApi.Unsubscribe:output_type -> centrifugal.centrifugo.api.UnsubscribeResponse
	22,  // 230: centrifugal.centrifugo.api.CentrifugoApi.Disconnect:output_type -> centrifugal.centrifugo.api.DisconnectResponse
	25,  // 231: centrifugal.centrifugo.api.CentrifugoApi.Presence:output_type -> centrifugal.centrifugo.api.PresenceResponse
	29,  // 232: centrifugal.centrifugo.api.CentrifugoApi.PresenceStats:output_type -> centrifugal.centrifugo.api.PresenceStatsResponse
	33,  // 233: centrifugal.centrifugo.api.CentrifugoApi.History:output_type -> centrifugal.centrifugo.api.HistoryResponse
	37,  // 234: centrifugal.centrifugo.api.CentrifugoApi.HistoryRemove:output_type -> centrifugal.centrifugo.api.HistoryRemoveResponse
	40,  // 235: centrifugal.centrifugo.api.CentrifugoApi.Info:output_type -> centrifugal.centrifugo.api.InfoResponse
	43,  // 236: centrifugal.centrifugo.api.CentrifugoApi.RPC:output_type -> centrifugal.centrifugo.api.RPCResponse
	46,  // 237: centrifugal.centrifugo.api.CentrifugoApi.Refresh:output_type -> centrifugal.centrifugo.api.RefreshResponse
	52,  // 238: centrifugal.centrifugo.api.CentrifugoApi.Channels:output_type -> centrifugal.centrifugo.api.ChannelsResponse
	56,  // 239: centrifugal.centrifugo.api.CentrifugoApi.Connections:output_type -> centrifugal.centrifugo.api.ConnectionsResponse
	64,  // 240: centrifugal.centrifugo.api.CentrifugoApi.UpdateUserStatus:output_type -> centrifugal.centrifugo.api.UpdateUserStatusResponse
	67,  // 241: centrifugal.centrifugo.api.CentrifugoApi.GetUserStatus:output_type -> centrifugal.centrifugo.api.GetUserStatusResponse
	71,  // 242: centrifugal.centrifugo.api.CentrifugoApi.DeleteUserStatus:output_type -> centrifugal.centrifugo.api.DeleteUserStatusResponse
	75,  // 243: centrifugal.centrifugo.api.CentrifugoApi.BlockUser:output_type -> centrifugal.centrifugo.api.BlockUserResponse
	78,  // 244: centrifugal.centrifugo.api.CentrifugoApi.UnblockUser:output_type -> centrifugal.centrifugo.api.UnblockUserResponse
	81,  // 245: centrifugal.centrifugo.api.CentrifugoApi.RevokeToken:output_type -> centrifugal.centrifugo.api.RevokeTokenResponse
	84,  // 246: centrifugal.centrifugo.api.CentrifugoApi.InvalidateUserTokens:output_type -> centrifugal.centrifugo.api.InvalidateUserTokensResponse
	101, // 247: centrifugal.centrifugo.api.CentrifugoApi.DeviceRegister:output_type -> centrifugal.centrifugo.api.DeviceRegisterResponse
	102, // 248: centrifugal.centrifugo.api.CentrifugoApi.DeviceUpdate:output_type -> centrifugal.centrifugo.api.DeviceUpdateResponse
	103, // 249: centrifugal.centrifugo.api.CentrifugoApi.DeviceRemove:output_type -> centrifugal.centrifugo.api.DeviceRemoveResponse
	104, // 250: centrifugal.centrifugo.api.CentrifugoApi.DeviceList:output_type -> centrifugal.centrifugo.api.DeviceListResponse
	105, // 251: centrifugal.centrifugo.api.CentrifugoApi.DeviceTopicList:output_type -> centrifugal.centrifugo.api.DeviceTopicListResponse
	107, // 252: centrifugal.centrifugo.api.CentrifugoApi.DeviceTopicUpdate:output_type -> centrifugal.centrifugo.api.DeviceTopicUpdateResponse
	106, // 253: centrifugal.centrifugo.api.CentrifugoApi.UserTopicList:output_type -> centrifugal.centrifugo.api.UserTopicListResponse
	108, // 254: centrifugal.centrifugo.api.CentrifugoApi.UserTopicUpdate:output_type -> centrifugal.centrifugo.api.UserTopicUpdateResponse
	131, // 255: centrifugal.centrifugo.api.CentrifugoApi.SendPushNotification:output_type -> centrifugal.centrifugo.api.SendPushNotificationResponse
	134, // 256: centrifugal.centrifugo.api.CentrifugoApi.UpdatePushStatus:output_type -> centrifugal.centrifugo.api.UpdatePushStatusResponse
	137, // 257: centrifugal.centrifugo.api.CentrifugoApi.CancelPush:output_type -> centrifugal.centrifugo.api.CancelPushResponse
	225, // [225:258] is the sub-list for method output_type
	192, // [192:225] is the sub-list for method input_type
	192, // [192:192] is the sub-list for extension type_name
	192, // [192:192] is the sub-list for extension extendee
	0,   // [0:192] is the sub-list for field type_name
}

func init() { file_api_proto_init() }
func file_api_proto_init() {
	if File_api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_proto_rawDesc), len(file_api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   154,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_proto_goTypes,
		DependencyIndexes: file_api_proto_depIdxs,
		MessageInfos:      file_api_proto_msgTypes,
	}.Build()
	File_api_proto = out.File
	file_api_proto_goTypes = nil
	file_api_proto_depIdxs = nil
}

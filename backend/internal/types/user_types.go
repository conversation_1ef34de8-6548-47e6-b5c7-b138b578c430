package types

// ============================ 用户信息相关 ============================

// GetUserProfileRequest 获取用户资料请求
type GetUserProfileRequest struct {
	UID string `json:"uid" validate:"required" comment:"用户UID"`
}

// UpdateUserProfileRequest 更新用户资料请求
type UpdateUserProfileRequest struct {
	Nickname  string `json:"nickname" validate:"max=50" comment:"昵称"`
	Avatar    string `json:"avatar" comment:"头像URL"`
	Gender    int16  `json:"gender" validate:"min=0,max=2" comment:"性别:0-未知,1-男,2-女"`
	Birthday  *Date  `json:"birthday"`
	SelfIntro string `json:"self_intro" validate:"max=1000" comment:"个人简介"`
}

// UserProfileResponse 用户资料响应
type UserProfileResponse struct {
	UserInfo     UserBasicInfo        `json:"user_info"`
	Verification UserVerificationInfo `json:"verification"`
	MemberInfo   UserMemberInfo       `json:"member_info"`
	Settings     UserSettingsInfo     `json:"settings"`
}

// UserBasicInfo 用户基本信息
type UserBasicInfo struct {
	UID       string   `json:"uid"`
	Nickname  string   `json:"nickname"`
	Avatar    string   `json:"avatar"`
	Gender    int16    `json:"gender"`
	Birthday  *Date    `json:"birthday"`
	Phone     string   `json:"phone"`
	Status    int      `json:"status"`
	CreatedAt DateTime `json:"created_at"`
}

// UserVerificationInfo 用户认证信息
type UserVerificationInfo struct {
	IsVerified             bool                        `json:"is_verified"`
	PersonalVerification   *PersonalVerificationInfo   `json:"personal_verification"`
	EnterpriseVerification *EnterpriseVerificationInfo `json:"enterprise_verification"`
}

// PersonalVerificationInfo 个人认证信息
type PersonalVerificationInfo struct {
	RealName     string   `json:"real_name"` // 掩码后的姓名
	IDCard       string   `json:"id_card"`   // 掩码后的身份证
	Status       int      `json:"status"`
	RejectReason string   `json:"reject_reason"`
	CreatedAt    DateTime `json:"created_at"`
	UpdatedAt    DateTime `json:"updated_at"`
}

// EnterpriseVerificationInfo 企业认证信息
type EnterpriseVerificationInfo struct {
	EnterpriseName            string   `json:"enterprise_name"`
	UnifiedSocialCreditCode   string   `json:"unified_social_credit_code"`   // 掩码后的信用代码
	LegalRepresentative       string   `json:"legal_representative"`         // 掩码后的法人姓名
	LegalRepresentativeIDCard string   `json:"legal_representative_id_card"` // 掩码后的法人身份证
	Status                    int      `json:"status"`
	RejectReason              string   `json:"reject_reason"`
	CreatedAt                 DateTime `json:"created_at"`
	UpdatedAt                 DateTime `json:"updated_at"`
}

// UserMemberInfo 用户会员信息
type UserMemberInfo struct {
	MemberLevel    int       `json:"member_level"`
	MemberExpireAt *DateTime `json:"member_expire_at"`
	Points         int       `json:"points"`
}

// UserSettingsInfo 用户设置信息
type UserSettingsInfo struct {
	AllowMessageFrom    int  `json:"allow_message_from"`
	AllowLocationShare  bool `json:"allow_location_share"`
	AllowJobRecommend   bool `json:"allow_job_recommend"`
	AllowHouseRecommend bool `json:"allow_house_recommend"`
}

// ============================ 个人实名认证相关 ============================

// SubmitPersonalVerificationRequest 提交个人实名认证请求
type SubmitPersonalVerificationRequest struct {
	RealName         string `json:"real_name" validate:"required,max=20" comment:"真实姓名"`
	IDCard           string `json:"id_card" validate:"required,len=18" comment:"身份证号"`
	IdCardFrontImage string `json:"id_card_front_image" validate:"required,url" comment:"身份证正面照片"`
	IdCardBackImage  string `json:"id_card_back_image" validate:"required,url" comment:"身份证背面照片"`
	HandHeldImage    string `json:"hand_held_image" validate:"url" comment:"手持身份证照片"`
}

// PersonalVerificationResponse 个人认证响应
type PersonalVerificationResponse struct {
	ID        uint     `json:"id"`
	Status    int      `json:"status"`
	CreatedAt DateTime `json:"created_at"`
}

// GetPersonalVerificationRequest 获取个人认证状态请求
type GetPersonalVerificationRequest struct {
	// 通过JWT token获取用户信息，无需额外参数
}

// GetPersonalVerificationResponse 获取个人认证状态响应
type GetPersonalVerificationResponse struct {
	HasVerification bool                      `json:"has_verification"`
	Verification    *PersonalVerificationInfo `json:"verification"`
}

// ============================ 企业认证相关 ============================

// SubmitEnterpriseVerificationRequest 提交企业认证请求
type SubmitEnterpriseVerificationRequest struct {
	EnterpriseName                      string `json:"enterprise_name" validate:"required,max=100" comment:"企业名称"`
	UnifiedSocialCreditCode             string `json:"unified_social_credit_code" validate:"required,len=18" comment:"统一社会信用代码"`
	LegalRepresentative                 string `json:"legal_representative" validate:"required,max=20" comment:"法定代表人姓名"`
	LegalRepresentativeIDCard           string `json:"legal_representative_id_card" validate:"required,len=18" comment:"法定代表人身份证号"`
	BusinessLicenseImage                string `json:"business_license_image" validate:"required,url" comment:"营业执照照片"`
	LegalRepresentativeIDCardFrontImage string `json:"legal_representative_id_card_front_image" validate:"required,url" comment:"法人身份证正面照片"`
	LegalRepresentativeIDCardBackImage  string `json:"legal_representative_id_card_back_image" validate:"required,url" comment:"法人身份证背面照片"`
	AuthorizationLetter                 string `json:"authorization_letter" validate:"url" comment:"授权委托书"`
}

// EnterpriseVerificationResponse 企业认证响应
type EnterpriseVerificationResponse struct {
	ID        uint     `json:"id"`
	Status    int      `json:"status"`
	CreatedAt DateTime `json:"created_at"`
}

// GetEnterpriseVerificationRequest 获取企业认证状态请求
type GetEnterpriseVerificationRequest struct {
	// 通过JWT token获取用户信息，无需额外参数
}

// GetEnterpriseVerificationResponse 获取企业认证状态响应
type GetEnterpriseVerificationResponse struct {
	HasVerification bool                        `json:"has_verification"`
	Verification    *EnterpriseVerificationInfo `json:"verification"`
}

// ============================ 手机号绑定和更换相关 ============================

// BindPhoneRequest 绑定手机号请求
type BindPhoneRequest struct {
	PhoneCode string `json:"phone_code" validate:"required" comment:"微信获取手机号的code"`
}

// BindPhoneResponse 绑定手机号响应
type BindPhoneResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Phone   string `json:"phone"`
}

// CheckPhoneChangeEligibilityRequest 检查手机号更换资格请求
type CheckPhoneChangeEligibilityRequest struct {
	// 通过JWT token获取用户信息，无需额外参数
}

// CheckPhoneChangeEligibilityResponse 检查手机号更换资格响应
type CheckPhoneChangeEligibilityResponse struct {
	Eligible         bool      `json:"eligible"`
	Reason           string    `json:"reason"`
	LastChangedAt    *DateTime `json:"last_changed_at"`
	NextEligibleTime *DateTime `json:"next_eligible_time"`
}

// SendSMSVerificationRequest 发送短信验证码请求（用于手机号更换）
type SendSMSVerificationRequest struct {
	NewPhone string `json:"new_phone" validate:"required,len=11" comment:"新手机号"`
	Type     string `json:"type" validate:"required,oneof=change_phone" comment:"验证码类型"`
}

// SendSMSVerificationResponse 发送短信验证码响应
type SendSMSVerificationResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	CountDown int    `json:"count_down"` // 下次发送倒计时（秒）
}

// ChangePhoneBySMSRequest 通过短信验证码更换手机号请求
type ChangePhoneBySMSRequest struct {
	NewPhone         string `json:"new_phone" validate:"required,len=11" comment:"新手机号"`
	VerificationCode string `json:"verification_code" validate:"required,len=6" comment:"短信验证码"`
}

// ChangePhoneByWechatRequest 通过微信快速更换手机号请求
type ChangePhoneByWechatRequest struct {
	Code          string `json:"code" validate:"required" comment:"微信授权码"`
	EncryptedData string `json:"encrypted_data" validate:"required" comment:"加密数据"`
	IV            string `json:"iv" validate:"required" comment:"初始向量"`
}

// ChangePhoneResponse 更换手机号响应
type ChangePhoneResponse struct {
	Success   bool     `json:"success"`
	Message   string   `json:"message"`
	NewPhone  string   `json:"new_phone"`
	ChangedAt DateTime `json:"changed_at"`
}

// GetPhoneChangeHistoryRequest 获取手机号更换历史请求
type GetPhoneChangeHistoryRequest struct {
	Page int `json:"page" validate:"min=1" comment:"页码"`
	Size int `json:"size" validate:"min=1,max=100" comment:"每页数量"`
}

// PhoneChangeRecord 手机号更换记录
type PhoneChangeRecord struct {
	ID         uint     `json:"id"`
	OldPhone   string   `json:"old_phone"`
	NewPhone   string   `json:"new_phone"`
	ChangeType string   `json:"change_type"`
	Status     int      `json:"status"`
	FailReason string   `json:"fail_reason"`
	CreatedAt  DateTime `json:"created_at"`
}

// GetPhoneChangeHistoryResponse 获取手机号更换历史响应
type GetPhoneChangeHistoryResponse struct {
	Records []PhoneChangeRecord `json:"records"`
	Total   int64               `json:"total"`
	Page    int                 `json:"page"`
	Size    int                 `json:"size"`
}

// ============================ 系统通知相关 ============================

// GetNotificationsRequest 获取系统通知列表请求
type GetNotificationsRequest struct {
	Type   string `json:"type" comment:"通知类型筛选"`
	Status string `json:"status" comment:"通知状态筛选"`
	Page   int    `json:"page" validate:"min=1" comment:"页码"`
	Size   int    `json:"size" validate:"min=1,max=100" comment:"每页数量"`
}

// SystemNotification 系统通知
type SystemNotification struct {
	ID         uint      `json:"id"`
	Type       string    `json:"type"`
	Level      string    `json:"level"`
	Status     string    `json:"status"`
	Title      string    `json:"title"`
	Summary    string    `json:"summary"`
	ActionURL  string    `json:"action_url"`
	ActionText string    `json:"action_text"`
	ImageURL   string    `json:"image_url"`
	IconURL    string    `json:"icon_url"`
	ReadAt     *DateTime `json:"read_at"`
	CreatedAt  DateTime  `json:"created_at"`
}

// GetNotificationsResponse 获取系统通知列表响应
type GetNotificationsResponse struct {
	Notifications []SystemNotification `json:"notifications"`
	Total         int64                `json:"total"`
	UnreadCount   int64                `json:"unread_count"`
	Page          int                  `json:"page"`
	Size          int                  `json:"size"`
}

// MarkNotificationReadRequest 标记通知已读请求
type MarkNotificationReadRequest struct {
	IDs []uint `json:"ids" validate:"required" comment:"通知ID列表"`
}

// MarkNotificationReadResponse 标记通知已读响应
type MarkNotificationReadResponse struct {
	Success     bool `json:"success"`
	MarkedCount int  `json:"marked_count"`
}

// GetNotificationDetailRequest 获取通知详情请求
type GetNotificationDetailRequest struct {
	ID uint `json:"id" validate:"required" comment:"通知ID"`
}

// GetNotificationDetailResponse 获取通知详情响应
type GetNotificationDetailResponse struct {
	ID         uint      `json:"id"`
	Type       string    `json:"type"`
	Level      string    `json:"level"`
	Status     string    `json:"status"`
	Title      string    `json:"title"`
	Content    string    `json:"content"`
	Summary    string    `json:"summary"`
	Data       any       `json:"data"`
	ActionURL  string    `json:"action_url"`
	ActionText string    `json:"action_text"`
	ImageURL   string    `json:"image_url"`
	IconURL    string    `json:"icon_url"`
	ReadAt     *DateTime `json:"read_at"`
	CreatedAt  DateTime  `json:"created_at"`
}

// ============================ 用户设置相关 ============================

// UpdateUserSettingsRequest 更新用户设置请求
type UpdateUserSettingsRequest struct {
	AllowMessageFrom    *int  `json:"allow_message_from" validate:"omitempty,min=1,max=3" comment:"允许消息来源"`
	AllowLocationShare  *bool `json:"allow_location_share" comment:"允许位置共享"`
	AllowJobRecommend   *bool `json:"allow_job_recommend" comment:"允许工作推荐"`
	AllowHouseRecommend *bool `json:"allow_house_recommend" comment:"允许房源推荐"`
}

// UpdateUserSettingsResponse 更新用户设置响应
type UpdateUserSettingsResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// GetUserSettingsRequest 获取用户设置请求
type GetUserSettingsRequest struct {
	// 通过JWT token获取用户信息，无需额外参数
}

// GetUserSettingsResponse 获取用户设置响应
type GetUserSettingsResponse struct {
	Settings UserSettingsInfo `json:"settings"`
}

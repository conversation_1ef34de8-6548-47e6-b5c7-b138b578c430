package types

// PaginationReq 分页请求参数
type PaginationReq struct {
	Page     int `form:"page,default=1"`
	PageSize int `form:"page_size,default=20"`
}

// PaginationResp 通用分页响应结构
type PaginationResp struct {
	List       interface{} `json:"list"`        // 数据列表
	Total      int64       `json:"total"`       // 总记录数
	Page       int         `json:"page"`        // 当前页码
	PageSize   int         `json:"page_size"`   // 每页大小
	TotalPages int         `json:"total_pages"` // 总页数
	HasNext    bool        `json:"has_next"`    // 是否有下一页
	HasPrev    bool        `json:"has_prev"`    // 是否有上一页
}

// NewPaginationResp 创建分页响应
func NewPaginationResp(list interface{}, total int64, page, pageSize int) *PaginationResp {
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	if totalPages == 0 {
		totalPages = 1
	}

	return &PaginationResp{
		List:       list,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}

// Sanitize 修正分页参数, 设置默认值和最大值
func (p *PaginationReq) GetPageNum() int {
	return p.Page
}

func (p *PaginationReq) GetPageSize() int {
	return p.PageSize
}

// UploadTokenResponse represents the response for getting an upload token
type UploadTokenResponse struct {
	Token  string `json:"token"`
	Domain string `json:"domain"`
}

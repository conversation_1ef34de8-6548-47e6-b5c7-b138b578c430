package types

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

// Date 自定义日期类型，支持 "YYYY-MM-DD" 格式的JSON序列化和反序列化
// 专门用于处理生日等只需要日期部分的字段
type Date struct {
	time.Time
}

// UnmarshalJSON 自定义JSON反序列化，支持多种日期格式
func (d *Date) UnmarshalJSON(data []byte) error {
	str := strings.Trim(string(data), `"`)
	if str == "null" || str == "" {
		d.Time = time.Time{}
		return nil
	}

	// 优先尝试日期格式 "2006-01-02" (前端发送的格式)
	if t, err := time.Parse("2006-01-02", str); err == nil {
		d.Time = t
		return nil
	}

	// 兼容ISO 8601完整格式 "2006-01-02T15:04:05Z07:00"
	if t, err := time.Parse(time.RFC3339, str); err == nil {
		d.Time = t
		return nil
	}

	// 兼容其他常见日期时间格式
	if t, err := time.Parse("2006-01-02 15:04:05", str); err == nil {
		d.Time = t
		return nil
	}

	return fmt.Errorf("无法解析日期格式: %s", str)
}

// MarshalJSON 自定义JSON序列化，统一输出 "YYYY-MM-DD" 格式
func (d Date) MarshalJSON() ([]byte, error) {
	if d.Time.IsZero() {
		return []byte("null"), nil
	}
	return json.Marshal(d.Time.Format("2006-01-02"))
}

// Value 实现 driver.Valuer 接口，用于数据库存储
func (d Date) Value() (driver.Value, error) {
	if d.Time.IsZero() {
		return nil, nil
	}
	return d.Time.Format("2006-01-02"), nil
}

// Scan 实现 sql.Scanner 接口，用于数据库读取
func (d *Date) Scan(value interface{}) error {
	if value == nil {
		d.Time = time.Time{}
		return nil
	}

	switch v := value.(type) {
	case time.Time:
		d.Time = v
	case string:
		t, err := time.Parse("2006-01-02", v)
		if err != nil {
			// 尝试解析完整的时间戳格式
			t, err = time.Parse("2006-01-02 15:04:05", v)
			if err != nil {
				return fmt.Errorf("无法解析数据库日期值: %v", v)
			}
		}
		d.Time = t
	case []byte:
		return d.Scan(string(v))
	default:
		return fmt.Errorf("无法将 %T 类型转换为 Date", value)
	}

	return nil
}

// String 返回格式化的日期字符串
func (d Date) String() string {
	if d.Time.IsZero() {
		return ""
	}
	return d.Time.Format("2006-01-02")
}

// IsZero 检查是否为零值
func (d Date) IsZero() bool {
	return d.Time.IsZero()
}

// DateTime 自定义日期时间类型，用于解决时间戳格式问题
// 支持国内标准的 "2006-01-02 15:04:05" 格式输出
type DateTime struct {
	time.Time
}

// UnmarshalJSON 自定义JSON反序列化
func (dt *DateTime) UnmarshalJSON(data []byte) error {
	str := strings.Trim(string(data), `"`)
	if str == "null" || str == "" {
		dt.Time = time.Time{}
		return nil
	}

	// 尝试多种时间格式
	formats := []string{
		time.RFC3339,           // "2006-01-02T15:04:05Z07:00"
		"2006-01-02 15:04:05",  // 国内标准格式
		"2006-01-02T15:04:05Z", // UTC格式
		"2006-01-02T15:04:05",  // 无时区格式
	}

	for _, format := range formats {
		if t, err := time.Parse(format, str); err == nil {
			dt.Time = t
			return nil
		}
	}

	return fmt.Errorf("无法解析时间格式: %s", str)
}

// MarshalJSON 自定义JSON序列化，输出国内标准格式
func (dt DateTime) MarshalJSON() ([]byte, error) {
	if dt.Time.IsZero() {
		return []byte("null"), nil
	}
	// 转换为本地时间并格式化为国内标准格式
	localTime := dt.Time.In(time.Local)
	return json.Marshal(localTime.Format("2006-01-02 15:04:05"))
}

// Value 实现 driver.Valuer 接口
func (dt DateTime) Value() (driver.Value, error) {
	if dt.Time.IsZero() {
		return nil, nil
	}
	return dt.Time, nil
}

// Scan 实现 sql.Scanner 接口
func (dt *DateTime) Scan(value interface{}) error {
	if value == nil {
		dt.Time = time.Time{}
		return nil
	}

	switch v := value.(type) {
	case time.Time:
		dt.Time = v
	case string:
		t, err := time.Parse("2006-01-02 15:04:05", v)
		if err != nil {
			t, err = time.Parse(time.RFC3339, v)
			if err != nil {
				return fmt.Errorf("无法解析数据库时间值: %v", v)
			}
		}
		dt.Time = t
	case []byte:
		return dt.Scan(string(v))
	default:
		return fmt.Errorf("无法将 %T 类型转换为 DateTime", value)
	}

	return nil
}

// String 返回格式化的时间字符串
func (dt DateTime) String() string {
	if dt.Time.IsZero() {
		return ""
	}
	return dt.Time.In(time.Local).Format("2006-01-02 15:04:05")
}

// IsZero 检查是否为零值
func (dt DateTime) IsZero() bool {
	return dt.Time.IsZero()
}

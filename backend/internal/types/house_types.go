package types

// CreateHouseRequest 创建房源请求
type CreateHouseRequest struct {
	Title       string `json:"title" validate:"required,min=5,max=50"`
	Description string `json:"description" validate:"required,min=20"`
	Address     string `json:"address" validate:"required"`
	Price       int    `json:"price" validate:"required,gt=0"`
	Area        int    `json:"area" validate:"required,gt=0"`
	Type        string `json:"type" validate:"required"` // e.g., "公寓", "别墅"
}

// UpdateHouseRequest 更新房源请求
type UpdateHouseRequest struct {
	Title       *string `json:"title" validate:"omitempty,min=5,max=50"`
	Description *string `json:"description" validate:"omitempty,min=20"`
	Address     *string `json:"address" validate:"omitempty"`
	Price       *int    `json:"price" validate:"omitempty,gt=0"`
	Area        *int    `json:"area" validate:"omitempty,gt=0"`
	Type        *string `json:"type" validate:"omitempty"`
}

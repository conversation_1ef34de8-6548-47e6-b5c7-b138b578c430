package types

import (
	"time"

	"gorm.io/datatypes"
)

// =====================================================
// 职位相关请求/响应类型
// =====================================================

// CreateJobRequest 创建职位请求
type CreateJobRequest struct {
	EnterpriseID      uint     `json:"enterprise_id" validate:"required"`
	Title             string   `json:"title" validate:"required,min=2,max=50"`
	Description       string   `json:"description" validate:"required,min=10,max=5000"`
	SalaryMin         int      `json:"salary_min" validate:"required,min=0"`
	SalaryMax         int      `json:"salary_max" validate:"required,min=0"`
	ExperienceReq     int16    `json:"experience_req" validate:"min=0,max=6"`
	EducationReq      int16    `json:"education_req" validate:"min=0,max=5"`
	WorkType          int16    `json:"work_type" validate:"required,min=1,max=4"`
	WorkLocation      string   `json:"work_location" validate:"required,max=200"`
	Latitude          float64  `json:"latitude" validate:"min=-90,max=90"`
	Longitude         float64  `json:"longitude" validate:"min=-180,max=180"`
	RemoteWorkSupport bool     `json:"remote_work_support"`
	Benefits          []string `json:"benefits" validate:"max=15"`
	JobHighlights     []string `json:"job_highlights" validate:"max=10"`
	Requirements      []string `json:"requirements" validate:"max=20"`
	ContactMethod     string   `json:"contact_method" validate:"required,oneof=phone wechat email"`
	IsUrgent          bool     `json:"is_urgent"`
}

// UpdateJobRequest 更新职位请求
type UpdateJobRequest struct {
	Title             *string  `json:"title" validate:"omitempty,min=2,max=50"`
	Description       *string  `json:"description" validate:"omitempty,min=10,max=5000"`
	SalaryMin         *int     `json:"salary_min" validate:"omitempty,min=0"`
	SalaryMax         *int     `json:"salary_max" validate:"omitempty,min=0"`
	ExperienceReq     *int16   `json:"experience_req" validate:"omitempty,min=0,max=6"`
	EducationReq      *int16   `json:"education_req" validate:"omitempty,min=0,max=5"`
	WorkType          *int16   `json:"work_type" validate:"omitempty,min=1,max=4"`
	WorkLocation      *string  `json:"work_location" validate:"omitempty,max=200"`
	Latitude          *float64 `json:"latitude" validate:"omitempty,min=-90,max=90"`
	Longitude         *float64 `json:"longitude" validate:"omitempty,min=-180,max=180"`
	RemoteWorkSupport *bool    `json:"remote_work_support"`
	Benefits          []string `json:"benefits" validate:"omitempty,max=15"`
	JobHighlights     []string `json:"job_highlights" validate:"omitempty,max=10"`
	Requirements      []string `json:"requirements" validate:"omitempty,max=20"`
	ContactMethod     *string  `json:"contact_method" validate:"omitempty,oneof=phone wechat email"`
	IsUrgent          *bool    `json:"is_urgent"`
}

// JobQueryRequest 职位查询请求
type JobQueryRequest struct {
	Page           int      `json:"page" validate:"min=1"`
	PageSize       int      `json:"page_size" validate:"min=1,max=100"`
	Keywords       string   `json:"keywords"`
	SalaryMin      int      `json:"salary_min" validate:"min=0"`
	SalaryMax      int      `json:"salary_max" validate:"min=0"`
	ExperienceReq  int16    `json:"experience_req" validate:"min=0,max=6"`
	EducationReq   int16    `json:"education_req" validate:"min=0,max=5"`
	WorkType       int16    `json:"work_type" validate:"min=0,max=4"`
	Latitude       float64  `json:"latitude" validate:"min=-90,max=90"`
	Longitude      float64  `json:"longitude" validate:"min=-180,max=180"`
	Radius         int      `json:"radius" validate:"min=0,max=100"` // 搜索半径(km)
	Benefits       []string `json:"benefits"`
	SortBy         string   `json:"sort_by" validate:"oneof=created_at last_refreshed_at salary_min distance"`
	SortOrder      string   `json:"sort_order" validate:"oneof=asc desc"`
	IncludeExpired bool     `json:"include_expired"`
}

// JobResponse 职位响应
type JobResponse struct {
	ID                 uint            `json:"id"`
	EnterpriseID       uint            `json:"enterprise_id"`
	UserID             uint            `json:"user_id"`
	Title              string          `json:"title"`
	Description        string          `json:"description"`
	Status             string          `json:"status"`
	StatusLabel        string          `json:"status_label"`
	StatusColor        string          `json:"status_color"`
	SalaryMin          int             `json:"salary_min"`
	SalaryMax          int             `json:"salary_max"`
	SalaryText         string          `json:"salary_text"`
	ExperienceReq      int16           `json:"experience_req"`
	ExperienceLabel    string          `json:"experience_label"`
	EducationReq       int16           `json:"education_req"`
	EducationLabel     string          `json:"education_label"`
	WorkType           int16           `json:"work_type"`
	WorkTypeLabel      string          `json:"work_type_label"`
	WorkLocation       string          `json:"work_location"`
	Latitude           float64         `json:"latitude"`
	Longitude          float64         `json:"longitude"`
	RemoteWorkSupport  bool            `json:"remote_work_support"`
	Benefits           []string        `json:"benefits"`
	JobHighlights      []string        `json:"job_highlights"`
	Requirements       []string        `json:"requirements"`
	ContactMethod      string          `json:"contact_method"`
	ContactMethodLabel string          `json:"contact_method_label"`
	IsUrgent           bool            `json:"is_urgent"`
	UrgentExpiresAt    *time.Time      `json:"urgent_expires_at"`
	LastRefreshedAt    time.Time       `json:"last_refreshed_at"`
	TodayRefreshCount  int             `json:"today_refresh_count"`
	ViewCount          int             `json:"view_count"`
	ApplicationCount   int             `json:"application_count"`
	CreatedAt          time.Time       `json:"created_at"`
	UpdatedAt          time.Time       `json:"updated_at"`
	Distance           *float64        `json:"distance,omitempty"`
	HasApplied         bool            `json:"has_applied"`
	IsFavorited        bool            `json:"is_favorited"`
	Enterprise         *EnterpriseInfo `json:"enterprise,omitempty"`
}

// JobListResponse 职位列表响应
type JobListResponse struct {
	*PaginationResp
}

// JobRefreshRequest 职位刷新请求
type JobRefreshRequest struct {
	JobID uint `json:"job_id" validate:"required"`
}

// JobStatusUpdateRequest 职位状态更新请求
type JobStatusUpdateRequest struct {
	JobID  uint   `json:"job_id" validate:"required"`
	Status string `json:"status" validate:"required,oneof=active paused closed"`
}

// =====================================================
// 企业相关请求/响应类型
// =====================================================

// CreateEnterpriseRequest 创建企业请求
type CreateEnterpriseRequest struct {
	Name          string   `json:"name" validate:"required,min=2,max=100"`
	Description   string   `json:"description" validate:"max=1000"`
	LogoURL       string   `json:"logo_url" validate:"omitempty,url"`
	Type          string   `json:"type" validate:"required,oneof=enterprise small_business individual"`
	Industry      string   `json:"industry" validate:"max=100"`
	CompanySize   int16    `json:"company_size" validate:"min=0,max=5"`
	ContactPerson string   `json:"contact_person" validate:"required,max=100"`
	ContactPhone  string   `json:"contact_phone" validate:"required,max=50"`
	Address       string   `json:"address" validate:"max=500"`
	Latitude      float64  `json:"latitude" validate:"min=-90,max=90"`
	Longitude     float64  `json:"longitude" validate:"min=-180,max=180"`
	WelfareTags   []string `json:"welfare_tags" validate:"max=20"`
}

// UpdateEnterpriseRequest 更新企业请求
type UpdateEnterpriseRequest struct {
	Name          *string  `json:"name" validate:"omitempty,min=2,max=100"`
	Description   *string  `json:"description" validate:"omitempty,max=1000"`
	LogoURL       *string  `json:"logo_url" validate:"omitempty,url"`
	Industry      *string  `json:"industry" validate:"omitempty,max=100"`
	CompanySize   *int16   `json:"company_size" validate:"omitempty,min=0,max=5"`
	ContactPerson *string  `json:"contact_person" validate:"omitempty,max=100"`
	ContactPhone  *string  `json:"contact_phone" validate:"omitempty,max=50"`
	Address       *string  `json:"address" validate:"omitempty,max=500"`
	Latitude      *float64 `json:"latitude" validate:"omitempty,min=-90,max=90"`
	Longitude     *float64 `json:"longitude" validate:"omitempty,min=-180,max=180"`
	WelfareTags   []string `json:"welfare_tags" validate:"omitempty,max=20"`
}

// EnterpriseVerificationRequest 企业认证请求
type EnterpriseVerificationRequest struct {
	EnterpriseID        uint   `json:"enterprise_id" validate:"required"`
	VerificationType    string `json:"verification_type" validate:"required,oneof=enterprise small_business individual"`
	CreditCode          string `json:"credit_code" validate:"required_if=VerificationType enterprise"`
	LicenseURL          string `json:"license_url" validate:"required,url"`
	LegalPersonName     string `json:"legal_person_name" validate:"required_if=VerificationType enterprise"`
	LegalPersonIDNumber string `json:"legal_person_id_number" validate:"required_if=VerificationType enterprise"`
	AdditionalDocs      string `json:"additional_docs"`
}

// EnterpriseInfo 企业信息
type EnterpriseInfo struct {
	ID                 uint      `json:"id"`
	UserID             uint      `json:"user_id"`
	Name               string    `json:"name"`
	Description        string    `json:"description"`
	LogoURL            string    `json:"logo_url"`
	Type               string    `json:"type"`
	TypeLabel          string    `json:"type_label"`
	Industry           string    `json:"industry"`
	CompanySize        int16     `json:"company_size"`
	ContactPerson      string    `json:"contact_person"`
	ContactPhone       string    `json:"contact_phone"`
	Address            string    `json:"address"`
	Latitude           float64   `json:"latitude"`
	Longitude          float64   `json:"longitude"`
	WelfareTags        []string  `json:"welfare_tags"`
	IsVerified         bool      `json:"is_verified"`
	VerificationStatus string    `json:"verification_status"`
	ActiveJobCount     int       `json:"active_job_count"`
	TotalViewCount     int       `json:"total_view_count"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// =====================================================
// 简历相关请求/响应类型
// =====================================================

// CreateResumeRequest 创建简历请求
type CreateResumeRequest struct {
	Name               string                 `json:"name" validate:"required,max=20"`
	Age                int                    `json:"age" validate:"min=16,max=100"`
	Gender             int                    `json:"gender" validate:"min=0,max=2"`
	Phone              string                 `json:"phone" validate:"required,max=20"`
	Email              string                 `json:"email" validate:"omitempty,email,max=100"`
	AvatarURL          string                 `json:"avatar_url" validate:"omitempty,url"`
	WorkExperience     []WorkExperienceItem   `json:"work_experience" validate:"max=10"`
	EducationHistory   []EducationHistoryItem `json:"education_history" validate:"max=5"`
	Skills             []SkillItem            `json:"skills" validate:"max=30"`
	JobIntentions      []JobIntentionItem     `json:"job_intentions" validate:"required,max=5"`
	ExpectedSalaryMin  int                    `json:"expected_salary_min" validate:"min=0"`
	ExpectedSalaryMax  int                    `json:"expected_salary_max" validate:"min=0"`
	PreferredLocations []string               `json:"preferred_locations" validate:"max=10"`
	WorkStatus         string                 `json:"work_status" validate:"required,oneof=currently_employed unemployed student"`
	AvailabilityDate   *time.Time             `json:"availability_date"`
	SelfIntroduction   string                 `json:"self_introduction" validate:"max=1000"`
}

// UpdateResumeRequest 更新简历请求
type UpdateResumeRequest struct {
	Name               *string                `json:"name" validate:"omitempty,max=20"`
	Age                *int                   `json:"age" validate:"omitempty,min=16,max=100"`
	Gender             *int                   `json:"gender" validate:"omitempty,min=0,max=2"`
	Phone              *string                `json:"phone" validate:"omitempty,max=20"`
	Email              *string                `json:"email" validate:"omitempty,email,max=100"`
	AvatarURL          *string                `json:"avatar_url" validate:"omitempty,url"`
	WorkExperience     []WorkExperienceItem   `json:"work_experience" validate:"omitempty,max=10"`
	EducationHistory   []EducationHistoryItem `json:"education_history" validate:"omitempty,max=5"`
	Skills             []SkillItem            `json:"skills" validate:"omitempty,max=30"`
	JobIntentions      []JobIntentionItem     `json:"job_intentions" validate:"omitempty,max=5"`
	ExpectedSalaryMin  *int                   `json:"expected_salary_min" validate:"omitempty,min=0"`
	ExpectedSalaryMax  *int                   `json:"expected_salary_max" validate:"omitempty,min=0"`
	PreferredLocations []string               `json:"preferred_locations" validate:"omitempty,max=10"`
	WorkStatus         *string                `json:"work_status" validate:"omitempty,oneof=currently_employed unemployed student"`
	AvailabilityDate   *time.Time             `json:"availability_date"`
	SelfIntroduction   *string                `json:"self_introduction" validate:"omitempty,max=1000"`
}

// WorkExperienceItem 工作经历项
type WorkExperienceItem struct {
	CompanyName string `json:"company_name" validate:"required,max=100"`
	Position    string `json:"position" validate:"required,max=50"`
	StartDate   string `json:"start_date" validate:"required"` // YYYY-MM format
	EndDate     string `json:"end_date" validate:"required"`   // YYYY-MM format or "present"
	Description string `json:"description" validate:"max=500"`
	Industry    string `json:"industry" validate:"max=50"`
	IsCurrent   bool   `json:"is_current"`
}

// EducationHistoryItem 教育背景项
type EducationHistoryItem struct {
	School      string `json:"school" validate:"required,max=100"`
	Major       string `json:"major" validate:"required,max=50"`
	Degree      string `json:"degree" validate:"required,max=20"`
	StartDate   string `json:"start_date" validate:"required"` // YYYY-MM format
	EndDate     string `json:"end_date" validate:"required"`   // YYYY-MM format or "present"
	Description string `json:"description" validate:"max=300"`
	IsGraduated bool   `json:"is_graduated"`
}

// SkillItem 技能项
type SkillItem struct {
	Name        string `json:"name" validate:"required,max=30"`
	Level       string `json:"level" validate:"required,oneof=beginner intermediate advanced expert"`
	Category    string `json:"category" validate:"max=20"`
	Description string `json:"description" validate:"max=200"`
}

// JobIntentionItem 求职意向项
type JobIntentionItem struct {
	Position  string   `json:"position" validate:"required,max=50"`
	Industry  string   `json:"industry" validate:"max=50"`
	SalaryMin int      `json:"salary_min" validate:"min=0"`
	SalaryMax int      `json:"salary_max" validate:"min=0"`
	WorkType  string   `json:"work_type" validate:"required,oneof=full_time part_time internship contract"`
	Locations []string `json:"locations" validate:"max=10"`
	Priority  int      `json:"priority" validate:"min=1,max=5"`
}

// ResumeResponse 简历响应
type ResumeResponse struct {
	ID                 uint                   `json:"id"`
	UserID             uint                   `json:"user_id"`
	Name               string                 `json:"name"`
	Age                int                    `json:"age"`
	Gender             int16                  `json:"gender"`
	GenderLabel        string                 `json:"gender_label"`
	Phone              string                 `json:"phone"`
	Email              string                 `json:"email"`
	AvatarURL          string                 `json:"avatar_url"`
	WorkExperience     []WorkExperienceItem   `json:"work_experience"`
	EducationHistory   []EducationHistoryItem `json:"education_history"`
	Skills             []SkillItem            `json:"skills"`
	JobIntentions      []JobIntentionItem     `json:"job_intentions"`
	ExpectedSalaryMin  int                    `json:"expected_salary_min"`
	ExpectedSalaryMax  int                    `json:"expected_salary_max"`
	PreferredLocations []string               `json:"preferred_locations"`
	WorkStatus         string                 `json:"work_status"`
	WorkStatusLabel    string                 `json:"work_status_label"`
	AvailabilityDate   *time.Time             `json:"availability_date"`
	SelfIntroduction   string                 `json:"self_introduction"`
	CompletionRate     float64                `json:"completion_rate"`
	IsComplete         bool                   `json:"is_complete"`
	CanApplyJobs       bool                   `json:"can_apply_jobs"`
	ViewCount          int                    `json:"view_count"`
	DownloadCount      int                    `json:"download_count"`
	LastActiveAt       time.Time              `json:"last_active_at"`
	CreatedAt          time.Time              `json:"created_at"`
	UpdatedAt          time.Time              `json:"updated_at"`
}

// ResumeQueryRequest 简历查询请求
type ResumeQueryRequest struct {
	Page       int      `json:"page" validate:"min=1"`
	PageSize   int      `json:"page_size" validate:"min=1,max=100"`
	Keywords   string   `json:"keywords"`
	MinAge     int      `json:"min_age" validate:"min=0,max=100"`
	MaxAge     int      `json:"max_age" validate:"min=0,max=100"`
	Gender     int16    `json:"gender" validate:"min=0,max=2"`
	Education  int16    `json:"education" validate:"min=0,max=5"`
	Experience int16    `json:"experience" validate:"min=0,max=6"`
	Skills     []string `json:"skills"`
	SalaryMin  int      `json:"salary_min" validate:"min=0"`
	SalaryMax  int      `json:"salary_max" validate:"min=0"`
	WorkStatus string   `json:"work_status" validate:"omitempty,oneof=currently_employed unemployed student"`
	Locations  []string `json:"locations"`
	SortBy     string   `json:"sort_by" validate:"oneof=created_at updated_at expected_salary_min"`
	SortOrder  string   `json:"sort_order" validate:"oneof=asc desc"`
}

// ResumeListResponse 简历列表响应
type ResumeListResponse struct {
	*PaginationResp
}

// =====================================================
// 申请相关请求/响应类型
// =====================================================

// CreateApplicationRequest 创建申请请求
type CreateApplicationRequest struct {
	JobID uint `json:"job_id" validate:"required"`
}

// UpdateApplicationStatusRequest 更新申请状态请求
type UpdateApplicationStatusRequest struct {
	ApplicationID    uint       `json:"application_id" validate:"required"`
	Status           string     `json:"status" validate:"required"`
	RecruiterNote    string     `json:"recruiter_note" validate:"max=500"`
	InterviewTime    *time.Time `json:"interview_time"`
	InterviewAddress string     `json:"interview_address" validate:"max=200"`
}

// ApplicationResponse 申请响应
type ApplicationResponse struct {
	ID               uint           `json:"id"`
	JobID            uint           `json:"job_id"`
	UserID           uint           `json:"user_id"`
	Status           string         `json:"status"`
	StatusLabel      string         `json:"status_label"`
	StatusColor      string         `json:"status_color"`
	ResumeSnapshot   datatypes.JSON `json:"resume_snapshot"`
	RecruiterNote    string         `json:"recruiter_note"`
	InterviewTime    *time.Time     `json:"interview_time"`
	InterviewAddress string         `json:"interview_address"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	Job              *JobResponse   `json:"job,omitempty"`
	User             *UserInfo      `json:"user,omitempty"`
}

// ApplicationListResponse 申请列表响应
type ApplicationListResponse struct {
	*PaginationResp
}

// ApplicationQueryRequest 申请查询请求
type ApplicationQueryRequest struct {
	Page      int    `json:"page" validate:"min=1"`
	PageSize  int    `json:"page_size" validate:"min=1,max=100"`
	JobID     uint   `json:"job_id"`
	UserID    uint   `json:"user_id"`
	Status    string `json:"status"`
	SortBy    string `json:"sort_by" validate:"oneof=created_at updated_at"`
	SortOrder string `json:"sort_order" validate:"oneof=asc desc"`
}

// =====================================================
// 会员相关请求/响应类型
// =====================================================

// MembershipPlanResponse 会员套餐响应
type MembershipPlanResponse struct {
	ID           uint                   `json:"id"`
	Key          string                 `json:"key"`
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	Price        float64                `json:"price"`
	DurationDays int                    `json:"duration_days"`
	Benefits     map[string]interface{} `json:"benefits"`
	IsActive     bool                   `json:"is_active"`
	SortOrder    int                    `json:"sort_order"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
}

// CreateSubscriptionRequest 创建订阅请求
type CreateSubscriptionRequest struct {
	EnterpriseID  uint   `json:"enterprise_id" validate:"required"`
	PlanID        uint   `json:"plan_id" validate:"required"`
	PaymentMethod string `json:"payment_method" validate:"required,oneof=wechat alipay"`
}

// SubscriptionResponse 订阅响应
type SubscriptionResponse struct {
	ID                  uint                    `json:"id"`
	EnterpriseID        uint                    `json:"enterprise_id"`
	PlanID              uint                    `json:"plan_id"`
	PlanDetailsSnapshot map[string]interface{}  `json:"plan_details_snapshot"`
	StartDate           time.Time               `json:"start_date"`
	EndDate             time.Time               `json:"end_date"`
	Status              string                  `json:"status"`
	IsActive            bool                    `json:"is_active"`
	DaysLeft            int                     `json:"days_left"`
	CreatedAt           time.Time               `json:"created_at"`
	UpdatedAt           time.Time               `json:"updated_at"`
	Plan                *MembershipPlanResponse `json:"plan,omitempty"`
}

// =====================================================
// 收藏和浏览历史类型
// =====================================================

// CreateFavoriteRequest 创建收藏请求
type CreateFavoriteRequest struct {
	JobID uint `json:"job_id" validate:"required"`
}

// FavoriteResponse 收藏响应
type FavoriteResponse struct {
	ID        uint         `json:"id"`
	UserID    uint         `json:"user_id"`
	JobID     uint         `json:"job_id"`
	CreatedAt time.Time    `json:"created_at"`
	Job       *JobResponse `json:"job,omitempty"`
}

// ViewHistoryResponse 浏览历史响应
type ViewHistoryResponse struct {
	ID       uint         `json:"id"`
	UserID   uint         `json:"user_id"`
	JobID    uint         `json:"job_id"`
	ViewTime time.Time    `json:"view_time"`
	Job      *JobResponse `json:"job,omitempty"`
}

// =====================================================
// 通用类型
// =====================================================

// LocationPoint 地理位置点
type LocationPoint struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// SearchFilters 搜索过滤器
type SearchFilters struct {
	Keywords    string        `json:"keywords"`
	SalaryRange [2]int        `json:"salary_range"` // [min, max]
	Experience  int16         `json:"experience"`
	Education   int16         `json:"education"`
	WorkType    int16         `json:"work_type"`
	Location    LocationPoint `json:"location"`
	Radius      int           `json:"radius"`
	Benefits    []string      `json:"benefits"`
	RemoteWork  bool          `json:"remote_work"`
	UrgentOnly  bool          `json:"urgent_only"`
}

// StatisticsResponse 统计信息响应
type StatisticsResponse struct {
	TotalJobs           int `json:"total_jobs"`
	ActiveJobs          int `json:"active_jobs"`
	TotalApplications   int `json:"total_applications"`
	PendingApplications int `json:"pending_applications"`
	TotalViews          int `json:"total_views"`
	TotalFavorites      int `json:"total_favorites"`
}

// NotificationResponse 通知响应
type NotificationResponse struct {
	ID        uint      `json:"id"`
	UserID    uint      `json:"user_id"`
	Title     string    `json:"title"`
	Content   string    `json:"content"`
	Type      string    `json:"type"`
	TypeLabel string    `json:"type_label"`
	RelatedID *uint     `json:"related_id"`
	IsRead    bool      `json:"is_read"`
	CreatedAt time.Time `json:"created_at"`
}

// BulkActionRequest 批量操作请求
type BulkActionRequest struct {
	IDs    []uint `json:"ids" validate:"required,min=1"`
	Action string `json:"action" validate:"required"`
}

// JobAnalyticsResponse 职位分析响应
type JobAnalyticsResponse struct {
	JobID              uint     `json:"job_id"`
	Views              int      `json:"views"`
	Applications       int      `json:"applications"`
	ConversionRate     float64  `json:"conversion_rate"`
	AvgResponseTime    int      `json:"avg_response_time"` // 平均响应时间（小时）
	PopularKeywords    []string `json:"popular_keywords"`
	SimilarJobsSalary  [2]int   `json:"similar_jobs_salary"` // [min, max]
	RecommendedActions []string `json:"recommended_actions"`
}

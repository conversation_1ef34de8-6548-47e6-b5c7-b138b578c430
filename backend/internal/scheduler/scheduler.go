package scheduler

import (
	"github.com/go-co-op/gocron/v2"
	"github.com/google/wire"
	"github.com/rs/zerolog/log"
)

// Scheduler 定义了调度器接口
type Scheduler interface {
	Start()
	Stop()
}

// scheduler 实现了 Scheduler 接口
type scheduler struct {
	s   gocron.Scheduler
	job *Job
}

// NewScheduler 创建一个新的调度器实例
// 使用了 wire 进行依赖注入
func NewScheduler(job *Job) (Scheduler, error) {
	s, err := gocron.NewScheduler()
	if err != nil {
		return nil, err
	}

	return &scheduler{
		s:   s,
		job: job,
	}, nil
}

// Start 启动调度器并注册所有任务
func (s *scheduler) Start() {
	log.Info().Msg("Starting scheduler...")

	// 注册 "检查过期零工" 任务, 使用 cron 表达式, 每小时执行一次
	_, err := s.s.NewJob(
		gocron.CronJob("@hourly", false),
		gocron.NewTask(s.job.CheckExpiredGigs),
		gocron.WithName("CheckExpiredGigs"),
		gocron.WithSingletonMode(gocron.LimitModeReschedule),
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to schedule CheckExpiredGigs job")
	}

	// 可以在这里注册更多的任务...
	// 例如：
	// _, err = s.s.NewJob(...)

	s.s.Start()
	log.Info().Msg("Scheduler started.")
}

// Stop 优雅地停止调度器
func (s *scheduler) Stop() {
	log.Info().Msg("Stopping scheduler...")
	err := s.s.Shutdown()
	if err != nil {
		log.Error().Err(err).Msg("Failed to shutdown scheduler gracefully")
	}
	log.Info().Msg("Scheduler stopped.")
}

// ProviderSet 是为 wire 准备的 provider 集合
var ProviderSet = wire.NewSet(NewScheduler, NewJob)

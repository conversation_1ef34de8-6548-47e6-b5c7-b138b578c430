package scheduler

import (
	"context"

	"bdb-backend/internal/worker" // 引入 worker
	"bdb-backend/pkg/logger"
)

// Job 包含所有定时任务的处理器
// 它作为调度器(gocron)和业务逻辑(worker)之间的适配器
type Job struct {
	gigWorker *worker.GigWorker
	// 未来可以注入更多的 worker
	// userWorker *worker.UserWorker
}

// NewJob 创建一个新的 Job 实例
func NewJob(gigWorker *worker.GigWorker) *Job {
	return &Job{
		gigWorker: gigWorker,
	}
}

// CheckExpiredGigs 是一个调度入口，它调用 worker 来执行实际的业务逻辑
func (j *Job) CheckExpiredGigs() {
	logger.Info("Scheduler: 触发 [检查过期零工] 任务")
	// 调用 worker 执行
	// 这里的错误我们已经在 worker 层记录了, 在调度层可以选择忽略或再次记录
	_ = j.gigWorker.CheckExpiredGigs(context.Background())
}

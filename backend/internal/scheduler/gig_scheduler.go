package scheduler

import (
	"context"
	"time"

	"bdb-backend/internal/service"
	"bdb-backend/pkg/logger"

	"github.com/rs/zerolog/log"
)

// GigScheduler 零工定时任务调度器
type GigScheduler struct {
	gigService service.GigService
	ctx        context.Context
	cancel     context.CancelFunc
}

// NewGigScheduler 创建零工调度器
func NewGigScheduler(gigService service.GigService) *GigScheduler {
	ctx, cancel := context.WithCancel(context.Background())
	return &GigScheduler{
		gigService: gigService,
		ctx:        ctx,
		cancel:     cancel,
	}
}

// Start 启动定时任务
func (s *GigScheduler) Start() {
	log.Info().Msg("Starting gig scheduler...")

	// 立即执行一次检查
	s.checkExpiredGigs()

	// 定时执行：每小时检查一次过期零工
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.checkExpiredGigs()
		case <-s.ctx.Done():
			log.Info().Msg("Gig scheduler stopped")
			return
		}
	}
}

// Stop 停止定时任务
func (s *GigScheduler) Stop() {
	log.Info().Msg("Stopping gig scheduler...")
	s.cancel()
}

// checkExpiredGigs 检查过期零工
func (s *GigScheduler) checkExpiredGigs() {
	logger.Info("开始检查过期零工")

	if err := s.gigService.CheckExpiredGigs(s.ctx); err != nil {
		logger.Error("检查过期零工失败", err)
	} else {
		logger.Info("过期零工检查完成")
	}
}

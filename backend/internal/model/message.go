package model

import (
	"time"

	"gorm.io/datatypes"
)

// ChatMessageType 消息类型
type ChatMessageType string

const (
	ChatMessageTypeText     ChatMessageType = "text"     // 文本消息
	ChatMessageTypeImage    ChatMessageType = "image"    // 图片消息
	ChatMessageTypeVoice    ChatMessageType = "voice"    // 语音消息
	ChatMessageTypeVideo    ChatMessageType = "video"    // 视频消息
	ChatMessageTypeFile     ChatMessageType = "file"     // 文件消息
	ChatMessageTypeLocation ChatMessageType = "location" // 位置消息
	ChatMessageTypeSystem   ChatMessageType = "system"   // 系统消息
	ChatMessageTypeEmoji    ChatMessageType = "emoji"    // 表情消息
	ChatMessageTypeCard     ChatMessageType = "card"     // 名片消息
)

// ChatMessageStatus 消息状态
type ChatMessageStatus string

const (
	ChatMessageStatusSent      ChatMessageStatus = "sent"      // 已发送到服务器
	ChatMessageStatusDelivered ChatMessageStatus = "delivered" // 已送达接收方
	ChatMessageStatusRead      ChatMessageStatus = "read"      // 已被阅读
	ChatMessageStatusRevoked   ChatMessageStatus = "revoked"   // 已撤回
	ChatMessageStatusFailed    ChatMessageStatus = "failed"    // 发送失败
)

// Message 消息模型
type Message struct {
	ID             uint   `json:"id" gorm:"primarykey"`
	MsgID          string `json:"msg_id" gorm:"type:varchar(36);uniqueIndex;not null;comment:全局唯一消息ID，客户端生成"`
	ConversationID uint   `json:"conversation_id" gorm:"not null;index:idx_msg_conversation;comment:会话ID"`
	SenderID       uint   `json:"sender_id" gorm:"not null;index;comment:发送者ID，0表示系统消息"`

	// 消息内容
	Content     string          `json:"content" gorm:"type:text;comment:消息内容"`
	MessageType ChatMessageType `json:"message_type" gorm:"type:varchar(20);comment:消息类型"`
	Extra       *datatypes.JSON `json:"extra,omitempty" gorm:"type:json;comment:扩展字段，存储图片尺寸、语音时长等"`

	// 消息状态
	Status    ChatMessageStatus `json:"status" gorm:"type:varchar(20);default:'sent';index;comment:消息状态"`
	IsRevoked bool              `json:"is_revoked" gorm:"default:false;index;comment:是否已撤回"`
	RevokedAt *time.Time        `json:"revoked_at" gorm:"comment:撤回时间"`

	// 时间信息
	CreatedAt   time.Time  `json:"created_at" gorm:"type:timestamp(0);not null;default:CURRENT_TIMESTAMP;index:idx_msg_time"`
	DeliveredAt *time.Time `json:"delivered_at" gorm:"comment:送达时间"`
	ReadAt      *time.Time `json:"read_at" gorm:"comment:已读时间"`
}

// TableName 设置表名
func (Message) TableName() string {
	return "messages"
}

// CanRevoke 判断消息是否可以撤回（发送后2分钟内）
func (m *Message) CanRevoke() bool {
	if m.IsRevoked {
		return false
	}
	return time.Since(m.CreatedAt) <= 2*time.Minute
}

// IsSystemMessage 判断是否为系统消息
func (m *Message) IsSystemMessage() bool {
	return m.SenderID == 0 || m.MessageType == ChatMessageTypeSystem
}

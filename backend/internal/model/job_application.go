package model

import (
	"time"

	"gorm.io/datatypes"
)

// JobApplication represents a job application.
type JobApplication struct {
	ID               uint           `gorm:"primaryKey;type:bigserial" json:"id"`
	JobID            uint           `gorm:"not null;comment:职位ID" json:"job_id"`
	UserID           uint           `gorm:"not null;comment:申请者ID" json:"user_id"`
	Status           string         `gorm:"type:varchar(30);not null;default:'submitted';comment:申请状态" json:"status"`
	ResumeSnapshot   datatypes.JSON `gorm:"comment:简历快照" json:"resume_snapshot"`
	RecruiterNote    string         `gorm:"type:text;comment:招聘者备注" json:"recruiter_note"`
	InterviewTime    *time.Time     `gorm:"comment:面试时间" json:"interview_time"`
	InterviewAddress string         `gorm:"type:text;comment:面试地点" json:"interview_address"`
	CreatedAt        time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 关联字段
	Job  *Job  `gorm:"foreignKey:JobID" json:"job,omitempty"`
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the JobApplication model.
func (JobApplication) TableName() string {
	return "job_applications"
}

// ApplicationStatistics represents application statistics data
type ApplicationStatistics struct {
	UserID                uint  `json:"user_id"`
	JobID                 uint  `json:"job_id,omitempty"`
	EnterpriseID          uint  `json:"enterprise_id,omitempty"`
	TotalApplications     int64 `json:"total_applications"`
	PendingApplications   int64 `json:"pending_applications"`
	ViewedApplications    int64 `json:"viewed_applications"`
	InterviewApplications int64 `json:"interview_applications"`
	HiredApplications     int64 `json:"hired_applications"`
	RejectedApplications  int64 `json:"rejected_applications"`
}

// ApplicationTrend represents application trend data
type ApplicationTrend struct {
	Date  string `json:"date"`
	Count int64  `json:"count"`
}

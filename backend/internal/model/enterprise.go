package model

import (
	"time"

	"gorm.io/datatypes"
)

// Enterprise represents an enterprise in job module.
type Enterprise struct {
	ID            uint           `gorm:"primaryKey;type:bigserial" json:"id"`
	UserID        uint           `gorm:"not null;comment:创建用户ID" json:"user_id"`
	Name          string         `gorm:"size:255;not null;comment:企业名称" json:"name"`
	Description   string         `gorm:"type:text;comment:企业介绍" json:"description"`
	LogoURL       string         `gorm:"type:text;comment:企业Logo" json:"logo_url"`
	Type          string         `gorm:"type:varchar(20);not null;default:'enterprise';comment:企业类型" json:"type"`
	Industry      string         `gorm:"size:100;comment:所属行业" json:"industry"`
	CompanySize   int16          `gorm:"type:smallint;default:0;comment:公司规模" json:"company_size"`
	ContactPerson string         `gorm:"size:100;comment:联系人" json:"contact_person"`
	ContactPhone  string         `gorm:"size:50;comment:联系电话" json:"contact_phone"`
	Address       string         `gorm:"type:text;comment:企业地址" json:"address"`
	Latitude      float64        `gorm:"type:decimal(10,7);default:0;comment:纬度" json:"latitude"`
	Longitude     float64        `gorm:"type:decimal(10,7);default:0;comment:经度" json:"longitude"`
	WelfareTags   datatypes.JSON `gorm:"type:text[];default:'{}';comment:福利标签" json:"welfare_tags"`
	CreatedAt     time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt     time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"updated_at"`
	IsDel         int            `gorm:"not null;default:0;comment:软删除标志" json:"-"`

	// 关联字段
	User          *User                   `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Verification  *EnterpriseVerification `gorm:"foreignKey:EnterpriseID" json:"verification,omitempty"`
	Jobs          []Job                   `gorm:"foreignKey:EnterpriseID" json:"jobs,omitempty"`
	Subscriptions []Subscription          `gorm:"foreignKey:EnterpriseID" json:"subscriptions,omitempty"`

	// 虚拟字段
	IsVerified         bool   `gorm:"->:false;<-:false;comment:是否已认证" json:"is_verified"`
	VerificationStatus string `gorm:"->:false;<-:false;comment:认证状态" json:"verification_status"`
	ActiveJobCount     int    `gorm:"->:false;<-:false;comment:活跃职位数" json:"active_job_count"`
	TotalViewCount     int    `gorm:"->:false;<-:false;comment:总浏览数" json:"total_view_count"`
}

// TableName returns the table name for the Enterprise model.
func (Enterprise) TableName() string {
	return "enterprises"
}

// EnterpriseVerification represents enterprise verification information.
type EnterpriseVerification struct {
	EnterpriseID                 uint      `gorm:"primaryKey;comment:企业ID" json:"enterprise_id"`
	VerificationType             string    `gorm:"type:varchar(20);not null;default:'enterprise';comment:认证类型" json:"verification_type"`
	EncryptedCreditCode          string    `gorm:"type:text;comment:加密后的信用代码" json:"encrypted_credit_code"`
	EncryptedLicenseURL          string    `gorm:"type:text;comment:加密后的营业执照URL" json:"encrypted_license_url"`
	EncryptedLegalPersonName     string    `gorm:"type:text;comment:加密后的法人姓名" json:"encrypted_legal_person_name"`
	EncryptedLegalPersonIDNumber string    `gorm:"type:text;comment:加密后的法人身份证号" json:"encrypted_legal_person_id_number"`
	EncryptedAdditionalDocs      string    `gorm:"type:text;comment:加密后的其他文档" json:"encrypted_additional_docs"`
	Status                       string    `gorm:"type:varchar(20);not null;default:'pending';comment:认证状态" json:"status"`
	RejectReason                 string    `gorm:"type:text;comment:拒绝原因" json:"reject_reason"`
	CreatedAt                    time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt                    time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 关联字段
	Enterprise *Enterprise `gorm:"foreignKey:EnterpriseID" json:"enterprise,omitempty"`
}

// TableName returns the table name for the EnterpriseVerification model.
func (EnterpriseVerification) TableName() string {
	return "enterprise_verifications"
}

// UserVerification represents user verification information for job module.
type UserVerification struct {
	UserID            uint      `gorm:"primaryKey;comment:用户ID" json:"user_id"`
	EncryptedRealName string    `gorm:"type:text;comment:加密后的真实姓名" json:"encrypted_real_name"`
	EncryptedIDNumber string    `gorm:"type:text;comment:加密后的身份证号" json:"encrypted_id_number"`
	EncryptedIDPhotos string    `gorm:"type:text;comment:加密后的身份证照片" json:"encrypted_id_photos"`
	MaskedRealName    string    `gorm:"size:255;comment:脱敏后的真实姓名" json:"masked_real_name"`
	MaskedIDNumber    string    `gorm:"size:255;comment:脱敏后的身份证号" json:"masked_id_number"`
	Status            string    `gorm:"type:varchar(20);not null;default:'pending';comment:认证状态" json:"status"`
	RejectReason      string    `gorm:"type:text;comment:拒绝原因" json:"reject_reason"`
	CreatedAt         time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt         time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 关联字段
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the UserVerification model.
func (UserVerification) TableName() string {
	return "user_verifications"
}

// MembershipPlan represents a membership plan template.
type MembershipPlan struct {
	ID           uint           `gorm:"primaryKey;type:bigserial" json:"id"`
	Key          string         `gorm:"size:100;not null;unique;comment:套餐标识" json:"key"`
	Name         string         `gorm:"size:255;not null;comment:套餐名称" json:"name"`
	Description  string         `gorm:"type:text;comment:套餐描述" json:"description"`
	Price        float64        `gorm:"type:decimal(10,2);default:0.00;comment:套餐价格" json:"price"`
	DurationDays int            `gorm:"default:30;comment:有效期天数" json:"duration_days"`
	Benefits     datatypes.JSON `gorm:"comment:套餐权益" json:"benefits"`
	IsActive     bool           `gorm:"not null;default:true;comment:是否可用" json:"is_active"`
	SortOrder    int            `gorm:"default:0;comment:排序权重" json:"sort_order"`
	CreatedAt    time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt    time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 关联字段
	Subscriptions []Subscription `gorm:"foreignKey:PlanID" json:"subscriptions,omitempty"`
}

// TableName returns the table name for the MembershipPlan model.
func (MembershipPlan) TableName() string {
	return "membership_plans"
}

// Subscription represents an enterprise subscription.
type Subscription struct {
	ID                  uint           `gorm:"primaryKey;type:bigserial" json:"id"`
	EnterpriseID        uint           `gorm:"not null;comment:企业ID" json:"enterprise_id"`
	PlanID              *uint          `gorm:"comment:套餐ID" json:"plan_id"`
	PlanDetailsSnapshot datatypes.JSON `gorm:"comment:套餐详情快照" json:"plan_details_snapshot"`
	StartDate           time.Time      `gorm:"type:timestamp(0);not null;default:CURRENT_TIMESTAMP;comment:开始时间" json:"start_date"`
	EndDate             time.Time      `gorm:"type:timestamp(0);not null;default:CURRENT_TIMESTAMP;comment:结束时间" json:"end_date"`
	Status              string         `gorm:"type:varchar(20);not null;default:'active';comment:订阅状态" json:"status"`
	CreatedAt           time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt           time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 关联字段
	Enterprise *Enterprise     `gorm:"foreignKey:EnterpriseID" json:"enterprise,omitempty"`
	Plan       *MembershipPlan `gorm:"foreignKey:PlanID" json:"plan,omitempty"`

	// 虚拟字段
	IsActive bool `gorm:"->:false;<-:false;comment:是否有效" json:"is_active"`
	DaysLeft int  `gorm:"->:false;<-:false;comment:剩余天数" json:"days_left"`
}

// TableName returns the table name for the Subscription model.
func (Subscription) TableName() string {
	return "subscriptions"
}

// IsExpired checks if the subscription is expired.
func (s *Subscription) IsExpired() bool {
	return s.EndDate.Before(time.Now())
}

// GetRemainingDays returns the remaining days of the subscription.
func (s *Subscription) GetRemainingDays() int {
	if s.IsExpired() {
		return 0
	}
	return int(time.Until(s.EndDate).Hours() / 24)
}

// EnterpriseStatistics represents enterprise statistics data
type EnterpriseStatistics struct {
	EnterpriseID        uint  `json:"enterprise_id"`
	TotalJobs           int64 `json:"total_jobs"`
	ActiveJobs          int64 `json:"active_jobs"`
	TotalApplications   int64 `json:"total_applications"`
	PendingApplications int64 `json:"pending_applications"`
	TotalViews          int64 `json:"total_views"`
}

// JobStatistics represents job statistics data
type JobStatistics struct {
	JobID             uint  `json:"job_id"`
	ViewCount         int64 `json:"view_count"`
	ApplicationCount  int64 `json:"application_count"`
	FavoriteCount     int64 `json:"favorite_count"`
	TotalJobs         int64 `json:"total_jobs"`
	ActiveJobs        int64 `json:"active_jobs"`
	TodayViews        int64 `json:"today_views"`
	TodayApplications int64 `json:"today_applications"`
	WeekViews         int64 `json:"week_views"`
	WeekApplications  int64 `json:"week_applications"`
}

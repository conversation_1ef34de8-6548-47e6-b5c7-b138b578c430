package model

import "time"

// Favorite represents a user's favorite job.
type Favorite struct {
	ID        uint      `gorm:"primaryKey;type:bigserial" json:"id"`
	UserID    uint      `gorm:"not null;comment:用户ID" json:"user_id"`
	JobID     uint      `gorm:"not null;comment:职位ID" json:"job_id"`
	CreatedAt time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`

	// 关联字段
	Job  *Job  `gorm:"foreignKey:JobID" json:"job,omitempty"`
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the Favorite model.
func (Favorite) TableName() string {
	return "favorites"
}

// Type aliases for backward compatibility
type JobFavorite = Favorite

package model

import "time"

// JobNotification represents a job-related notification.
type JobNotification struct {
	ID        uint      `gorm:"primaryKey;type:bigserial" json:"id"`
	UserID    uint      `gorm:"not null;comment:用户ID" json:"user_id"`
	Title     string    `gorm:"size:255;not null;comment:通知标题" json:"title"`
	Content   string    `gorm:"type:text;comment:通知内容" json:"content"`
	Type      string    `gorm:"type:varchar(20);not null;default:'system';comment:通知类型" json:"type"`
	RelatedID *uint     `gorm:"comment:相关ID" json:"related_id"`
	IsRead    bool      `gorm:"default:false;comment:是否已读" json:"is_read"`
	CreatedAt time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`

	// 关联字段
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the JobNotification model.
func (JobNotification) TableName() string {
	return "job_notifications"
}

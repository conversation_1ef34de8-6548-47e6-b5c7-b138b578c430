package model

import (
	"time"
)

// ConversationType 会话类型
type ConversationType string

const (
	ConversationTypeSingle ConversationType = "single" // 单聊
	ConversationTypeSystem ConversationType = "system" // 系统通知
)

// Conversation 会话基础信息 - 使用经典的user_id1/user_id2设计
type Conversation struct {
	ID        uint             `json:"id" gorm:"primarykey"`
	Type      ConversationType `json:"type" gorm:"type:varchar(20);not null;default:'single';comment:会话类型：single-单聊，system-系统通知"`
	UserID1   uint             `json:"user_id1" gorm:"not null;index;comment:用户ID1，单聊时较小的用户ID，系统通知时为0"`
	UserID2   uint             `json:"user_id2" gorm:"not null;index;comment:用户ID2，单聊时较大的用户ID，系统通知时为接收者ID"`
	CreatedBy uint             `json:"created_by" gorm:"not null;comment:会话创建者用户ID"`
	CreatedAt time.Time        `json:"created_at" gorm:"type:timestamp(0);not null;default:CURRENT_TIMESTAMP"`
}

// TableName 设置表名
func (Conversation) TableName() string {
	return "conversations"
}

// GetOtherUserID 获取单聊中的另一个用户ID
func (c *Conversation) GetOtherUserID(currentUserID uint) uint {
	if c.Type == ConversationTypeSystem {
		return 0 // 系统通知没有"对方"
	}
	if c.UserID1 == currentUserID {
		return c.UserID2
	}
	if c.UserID2 == currentUserID {
		return c.UserID1
	}
	return 0
}

// IsParticipant 判断用户是否是会话参与者
func (c *Conversation) IsParticipant(userID uint) bool {
	if c.Type == ConversationTypeSystem {
		return c.UserID2 == userID // 系统通知时，UserID2是接收者
	}
	return c.UserID1 == userID || c.UserID2 == userID
}

// IsSystemConversation 判断是否为系统通知会话
func (c *Conversation) IsSystemConversation() bool {
	return c.Type == ConversationTypeSystem
}

// IsSingleConversation 判断是否为单聊会话
func (c *Conversation) IsSingleConversation() bool {
	return c.Type == ConversationTypeSingle
}

// GetTargetUserID 获取目标用户ID（系统通知时返回接收者ID）
func (c *Conversation) GetTargetUserID() uint {
	if c.Type == ConversationTypeSystem {
		return c.UserID2 // 系统通知时，UserID2是接收者
	}
	return 0
}

// GetParticipants 获取单聊的两个参与者ID
func (c *Conversation) GetParticipants() (uint, uint) {
	if c.Type == ConversationTypeSingle {
		return c.UserID1, c.UserID2
	}
	return 0, 0
}

// CreateSingleConversation 创建单聊会话的便捷方法
func CreateSingleConversation(userID1, userID2, createdBy uint) *Conversation {
	// 确保 user_id1 < user_id2
	if userID1 > userID2 {
		userID1, userID2 = userID2, userID1
	}

	return &Conversation{
		Type:      ConversationTypeSingle,
		UserID1:   userID1,
		UserID2:   userID2,
		CreatedBy: createdBy,
	}
}

// CreateSystemConversation 创建系统通知会话的便捷方法
func CreateSystemConversation(userID uint) *Conversation {
	return &Conversation{
		Type:      ConversationTypeSystem,
		UserID1:   0,      // 系统通知时UserID1为0
		UserID2:   userID, // UserID2为接收者
		CreatedBy: 0,      // 系统创建
	}
}

package model

import "time"

// Report represents a job or user report.
type Report struct {
	ID             uint      `gorm:"primaryKey;type:bigserial" json:"id"`
	ReporterUserID uint      `gorm:"not null;comment:举报人ID" json:"reporter_user_id"`
	ReportedJobID  *uint     `gorm:"comment:被举报职位ID" json:"reported_job_id"`
	ReportedUserID *uint     `gorm:"comment:被举报用户ID" json:"reported_user_id"`
	Reason         string    `gorm:"size:100;not null;comment:举报原因" json:"reason"`
	Description    string    `gorm:"type:text;comment:详细描述" json:"description"`
	Status         string    `gorm:"type:varchar(20);default:'pending';comment:处理状态" json:"status"`
	CreatedAt      time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt      time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 关联字段
	ReporterUser *User `gorm:"foreignKey:ReporterUserID" json:"reporter_user,omitempty"`
	ReportedJob  *Job  `gorm:"foreignKey:ReportedJobID" json:"reported_job,omitempty"`
	ReportedUser *User `gorm:"foreignKey:ReportedUserID" json:"reported_user,omitempty"`
}

// TableName returns the table name for the Report model.
func (Report) TableName() string {
	return "reports"
}

// Type aliases for backward compatibility
type JobReport = Report

package model

import (
	"time"

	"gorm.io/datatypes"
)

// Resume represents a user's resume.
type Resume struct {
	ID                 uint           `gorm:"primaryKey;type:bigserial" json:"id"`
	UserID             uint           `gorm:"not null;unique;comment:用户ID" json:"user_id"`
	Name               string         `gorm:"size:100;comment:姓名" json:"name"`
	Age                int            `gorm:"default:0;comment:年龄" json:"age"`
	Gender             int16          `gorm:"type:smallint;default:0;comment:性别" json:"gender"`
	Phone              string         `gorm:"size:20;comment:手机号" json:"phone"`
	Email              string         `gorm:"size:100;comment:邮箱" json:"email"`
	AvatarURL          string         `gorm:"type:text;comment:头像URL" json:"avatar_url"`
	WorkExperience     datatypes.JSON `gorm:"comment:工作经历" json:"work_experience"`
	EducationHistory   datatypes.JSON `gorm:"comment:教育背景" json:"education_history"`
	Skills             datatypes.JSON `gorm:"comment:技能标签" json:"skills"`
	JobIntentions      datatypes.JSON `gorm:"comment:求职意向" json:"job_intentions"`
	ExpectedSalaryMin  int            `gorm:"default:0;comment:期望薪资最低值" json:"expected_salary_min"`
	ExpectedSalaryMax  int            `gorm:"default:0;comment:期望薪资最高值" json:"expected_salary_max"`
	PreferredLocations datatypes.JSON `gorm:"type:text[];comment:意向工作地点" json:"preferred_locations"`
	WorkStatus         string         `gorm:"type:varchar(20);default:'currently_employed';comment:工作状态" json:"work_status"`
	AvailabilityDate   *time.Time     `gorm:"type:date;comment:可到岗时间" json:"availability_date"`
	SelfIntroduction   string         `gorm:"type:text;comment:自我介绍" json:"self_introduction"`
	CreatedAt          time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt          time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 关联字段
	User         *User            `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Applications []JobApplication `gorm:"foreignKey:UserID" json:"applications,omitempty"`

	// 虚拟字段
	ViewCount      int       `gorm:"->:false;<-:false;comment:被查看次数" json:"view_count"`
	DownloadCount  int       `gorm:"->:false;<-:false;comment:被下载次数" json:"download_count"`
	LastActiveAt   time.Time `gorm:"->:false;<-:false;comment:最后活跃时间" json:"last_active_at"`
	MatchScore     float64   `gorm:"->:false;<-:false;comment:匹配度分数" json:"match_score,omitempty"`
	CompletionRate float64   `gorm:"->:false;<-:false;comment:简历完整度" json:"completion_rate"`
}

// TableName returns the table name for the Resume model.
func (Resume) TableName() string {
	return "resumes"
}

// WorkExperienceItem represents a work experience entry.
type WorkExperienceItem struct {
	CompanyName string `json:"company_name"`
	Position    string `json:"position"`
	StartDate   string `json:"start_date"` // YYYY-MM format
	EndDate     string `json:"end_date"`   // YYYY-MM format or "present"
	Description string `json:"description"`
	Industry    string `json:"industry"`
	IsCurrent   bool   `json:"is_current"`
}

// EducationHistoryItem represents an education history entry.
type EducationHistoryItem struct {
	School      string `json:"school"`
	Major       string `json:"major"`
	Degree      string `json:"degree"`
	StartDate   string `json:"start_date"` // YYYY-MM format
	EndDate     string `json:"end_date"`   // YYYY-MM format or "present"
	Description string `json:"description"`
	IsGraduated bool   `json:"is_graduated"`
}

// SkillItem represents a skill entry.
type SkillItem struct {
	Name        string `json:"name"`
	Level       string `json:"level"`    // beginner, intermediate, advanced, expert
	Category    string `json:"category"` // technical, language, soft_skill, etc.
	Description string `json:"description"`
}

// JobIntentionItem represents a job intention entry.
type JobIntentionItem struct {
	Position  string   `json:"position"`
	Industry  string   `json:"industry"`
	SalaryMin int      `json:"salary_min"`
	SalaryMax int      `json:"salary_max"`
	WorkType  string   `json:"work_type"` // full_time, part_time, internship, contract
	Locations []string `json:"locations"`
	Priority  int      `json:"priority"` // 1-5, 1 is highest priority
}

// ResumeStatistics represents resume statistics data
type ResumeStatistics struct {
	UserID              uint      `json:"user_id"`
	ResumeID            uint      `json:"resume_id"`
	TotalViews          int64     `json:"total_views"`
	ViewCount           int64     `json:"view_count"`
	DownloadCount       int64     `json:"download_count"`
	ApplicationCount    int64     `json:"application_count"`
	FavoriteCount       int64     `json:"favorite_count"`
	RecentApplications  int64     `json:"recent_applications"`
	TodayViews          int64     `json:"today_views"`
	WeekViews           int64     `json:"week_views"`
	MonthViews          int64     `json:"month_views"`
	ProfileCompleteness int       `json:"profile_completeness"`
	LastActiveAt        time.Time `json:"last_active_at"`
}

// ResumeViewHistory represents resume view history data
type ResumeViewHistory struct {
	ID           uint      `json:"id"`
	ResumeID     uint      `json:"resume_id"`
	ViewerID     uint      `json:"viewer_id"`
	ViewerUserID uint      `json:"viewer_user_id"`
	ViewerType   string    `json:"viewer_type"`
	ViewTime     time.Time `json:"view_time"`
	ViewedAt     time.Time `json:"viewed_at"`
}

// GetCompletionRate calculates the completion rate of the resume.
func (r *Resume) GetCompletionRate() float64 {
	totalFields := 12.0 // Total number of important fields
	filledFields := 0.0

	if r.Name != "" {
		filledFields++
	}
	if r.Age > 0 {
		filledFields++
	}
	if r.Phone != "" {
		filledFields++
	}
	if r.Email != "" {
		filledFields++
	}
	if r.SelfIntroduction != "" {
		filledFields++
	}
	if r.WorkExperience.String() != "[]" && r.WorkExperience.String() != "" {
		filledFields++
	}
	if r.EducationHistory.String() != "[]" && r.EducationHistory.String() != "" {
		filledFields++
	}
	if r.Skills.String() != "[]" && r.Skills.String() != "" {
		filledFields++
	}
	if r.JobIntentions.String() != "[]" && r.JobIntentions.String() != "" {
		filledFields++
	}
	if r.ExpectedSalaryMin > 0 {
		filledFields++
	}
	if r.PreferredLocations.String() != "[]" && r.PreferredLocations.String() != "" {
		filledFields++
	}
	if r.AvailabilityDate != nil {
		filledFields++
	}

	return (filledFields / totalFields) * 100
}

// IsComplete checks if the resume has all essential information.
func (r *Resume) IsComplete() bool {
	return r.GetCompletionRate() >= 70.0 // Consider complete if >= 70%
}

// CanApplyJobs checks if the resume is ready for job applications.
func (r *Resume) CanApplyJobs() bool {
	essentialFields := r.Name != "" &&
		r.Phone != "" &&
		r.JobIntentions.String() != "[]" &&
		r.JobIntentions.String() != ""

	return essentialFields
}

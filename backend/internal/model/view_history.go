package model

import "time"

// ViewHistory represents a user's job viewing history.
type ViewHistory struct {
	ID       uint      `gorm:"primaryKey;type:bigserial" json:"id"`
	UserID   uint      `gorm:"not null;comment:用户ID" json:"user_id"`
	JobID    uint      `gorm:"not null;comment:职位ID" json:"job_id"`
	ViewTime time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP;comment:浏览时间" json:"view_time"`

	// 关联字段
	Job  *Job  `gorm:"foreignKey:JobID" json:"job,omitempty"`
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the ViewHistory model.
func (ViewHistory) TableName() string {
	return "view_history"
}

// Type aliases for backward compatibility
type JobViewHistory = ViewHistory

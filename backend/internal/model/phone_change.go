package model

// PhoneChangeRecord 手机号更换记录
type PhoneChangeRecord struct {
	BaseModel
	UserID           uint   `gorm:"not null;index:idx_user_phone_change" json:"user_id"`
	OldPhone         string `gorm:"type:varchar(20);not null;index:idx_old_phone" json:"old_phone"`
	NewPhone         string `gorm:"type:varchar(20);not null;index:idx_new_phone" json:"new_phone"`
	ChangeType       string `gorm:"type:varchar(20);not null" json:"change_type"` // sms, wechat
	VerificationCode string `gorm:"type:varchar(10)" json:"verification_code"`
	WechatAuthCode   string `gorm:"type:varchar(100)" json:"wechat_auth_code"`
	Status           int    `gorm:"default:1;index:idx_status" json:"status"` // 1-成功 2-失败
	FailReason       string `gorm:"type:varchar(500)" json:"fail_reason"`
	OperatorIP       string `gorm:"type:varchar(45)" json:"operator_ip"`
	UserAgent        string `gorm:"type:text" json:"user_agent"`
}

func (PhoneChangeRecord) TableName() string {
	return "phone_change_records"
}

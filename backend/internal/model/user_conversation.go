package model

import (
	"time"
)

// UserConversation 用户的会话个人状态
type UserConversation struct {
	UserID         uint `json:"user_id" gorm:"primaryKey;comment:用户ID"`
	ConversationID uint `json:"conversation_id" gorm:"primaryKey;index;comment:会话ID"`

	// 可见性控制
	IsVisible bool       `json:"is_visible" gorm:"default:true;index;comment:是否可见"`
	IsDeleted bool       `json:"is_deleted" gorm:"default:false;comment:是否删除"`
	DeletedAt *time.Time `json:"deleted_at" gorm:"comment:删除时间"`

	// 个人设置
	IsPinned   bool   `json:"is_pinned" gorm:"default:false;index;comment:是否置顶"`
	IsMuted    bool   `json:"is_muted" gorm:"default:false;comment:是否静音"`
	CustomName string `json:"custom_name" gorm:"type:varchar(100);comment:自定义会话名称"`

	// 已读状态
	LastReadMessageID uint       `json:"last_read_message_id" gorm:"default:0;comment:最后已读消息ID"`
	LastReadTime      *time.Time `json:"last_read_time" gorm:"comment:最后已读时间"`

	// 最后活跃时间 - 用于排序，只在发送消息时更新
	LastActiveTime time.Time `json:"last_active_time" gorm:"index;comment:最后活跃时间"`

	CreatedAt time.Time `json:"created_at" gorm:"type:timestamp(0);not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time `json:"updated_at" gorm:"type:timestamp(0);not null;default:CURRENT_TIMESTAMP"`
}

// TableName 设置表名
func (UserConversation) TableName() string {
	return "user_conversations"
}

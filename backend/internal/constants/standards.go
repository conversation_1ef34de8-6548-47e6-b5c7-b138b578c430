package constants

// =====================================================
// 数据标准化常量定义
// =====================================================

// 性别标准
const (
	GenderUnlimited int16 = 0 // 不限
	GenderMale      int16 = 1 // 男
	GenderFemale    int16 = 2 // 女
)

// 年龄范围标准 (简化版)
const (
	AgeUnlimited int16 = 0 // 不限
	Age18To30    int16 = 1 // 18-30岁
	Age31To40    int16 = 2 // 31-40岁
	Age41To50    int16 = 3 // 41-50岁
	Age50Plus    int16 = 4 // 50岁以上
)

// 学历标准 (移除博士)
const (
	EducationUnlimited  int16 = 0 // 不限
	EducationJuniorHigh int16 = 1 // 初中及以下
	EducationHighSchool int16 = 2 // 高中/中专
	EducationAssociate  int16 = 3 // 大专
	EducationBachelor   int16 = 4 // 本科
	EducationMaster     int16 = 5 // 硕士
)

// 工作经验标准
const (
	ExperienceUnlimited     int16 = 0 // 不限
	ExperienceFreshGraduate int16 = 1 // 应届毕业生
	ExperienceUnder1Year    int16 = 2 // 1年以下
	Experience1To3Years     int16 = 3 // 1-3年
	Experience3To5Years     int16 = 4 // 3-5年
	Experience5To10Years    int16 = 5 // 5-10年
	ExperienceOver10Years   int16 = 6 // 10年以上
)

// 零工薪资单位标准
const (
	SalaryUnitUnlimited int16 = 0 // 不限
	SalaryUnitHour      int16 = 1 // 按小时
	SalaryUnitDay       int16 = 2 // 按天
	SalaryUnitPiece     int16 = 3 // 按件
	SalaryUnitTotal     int16 = 4 // 一口价
)

// 结算方式标准
const (
	SettlementTypeUnlimited int16 = 0 // 面议
	SettlementTypeDaily     int16 = 1 // 日结
	SettlementTypeWeekly    int16 = 2 // 周结
	SettlementTypeMonthly   int16 = 3 // 月结
	SettlementTypeFinished  int16 = 4 // 完工结
)

// 零工薪资标准 (简化版 - 按小时，单位：分)
const (
	GigSalaryUnlimited int16 = 0 // 不限
	GigSalary20To40    int16 = 1 // 20-40元/小时 (2000-4000分)
	GigSalary40To70    int16 = 2 // 40-70元/小时 (4000-7000分)
	GigSalary70To100   int16 = 3 // 70-100元/小时 (7000-10000分)
	GigSalaryOver100   int16 = 4 // 100元以上/小时 (10000分以上)
)

// 公司规模标准 (使用数值编码)
const (
	CompanySizeUnlimited  int16 = 0 // 不限
	CompanySizeStartup    int16 = 1 // 1-20人
	CompanySizeSmall      int16 = 2 // 21-100人
	CompanySizeMedium     int16 = 3 // 101-500人
	CompanySizeLarge      int16 = 4 // 501-1000人
	CompanySizeEnterprise int16 = 5 // 1000人以上
)

// 福利待遇标准 (JSON数组，适合中低端岗位)
const (
	// 基础保障类
	WelfareSocialInsurance int16 = 1 // 五险一金
	WelfareInsurance       int16 = 2 // 五险
	WelfareAccommodation   int16 = 3 // 包食宿
	WelfareFood            int16 = 4 // 包吃
	WelfareHousing         int16 = 5 // 包住
	WelfareMealAllowance   int16 = 6 // 餐补

	// 薪酬奖励类
	WelfareAnnualBonus      int16 = 7  // 年终奖
	WelfarePerformanceBonus int16 = 8  // 绩效奖金
	WelfareFullAttendance   int16 = 9  // 全勤奖
	WelfareOvertimePay      int16 = 10 // 加班费

	// 时间福利类
	WelfarePaidLeave     int16 = 11 // 带薪年假
	WelfareFlexibleWork  int16 = 12 // 弹性工作
	WelfareWeekendOff    int16 = 13 // 双休
	WelfareLegalHolidays int16 = 14 // 法定节假日

	// 交通住宿类
	WelfareTransportAllowance int16 = 15 // 交通补贴
	WelfareDormitory          int16 = 16 // 宿舍
	WelfareShuttleBus         int16 = 17 // 班车接送

	// 发展培训类
	WelfareTraining      int16 = 18 // 培训机会
	WelfarePromotionPath int16 = 19 // 晋升通道
	WelfareSkillTraining int16 = 20 // 技能培训

	// 生活关怀类
	WelfareHolidayBenefits  int16 = 21 // 节日福利
	WelfareBirthdayBenefit  int16 = 22 // 生日福利
	WelfareTeamBuilding     int16 = 23 // 团建活动
	WelfareEmployeeDiscount int16 = 24 // 员工优惠

	// 健康医疗类
	WelfareMedicalExam         int16 = 25 // 体检
	WelfareCommercialInsurance int16 = 26 // 商业保险

)

// =====================================================
// 标准选项数据结构
// =====================================================

// StandardOption 标准选项结构
type StandardOption struct {
	Code  int16  `json:"code"`
	Label string `json:"label"`
	Value int16  `json:"value"`
	Min   *int   `json:"min,omitempty"` // 最小值（用于范围类型）
	Max   *int   `json:"max,omitempty"` // 最大值（用于范围类型）
}

// SalaryRange 薪资范围结构
type SalaryRange struct {
	Code int `json:"code"`
	Min  int `json:"min"` // 最小值（分）
	Max  int `json:"max"` // 最大值（分）
}

// AgeRange 年龄范围结构
type AgeRange struct {
	Code int `json:"code"`
	Min  int `json:"min"` // 最小年龄
	Max  int `json:"max"` // 最大年龄
}

// CompanySizeRange 公司规模范围结构
type CompanySizeRange struct {
	Code int `json:"code"`
	Min  int `json:"min"` // 最小人数
	Max  int `json:"max"` // 最大人数
}

// =====================================================
// 预定义选项数据
// =====================================================

// GenderOptions 性别选项
var GenderOptions = []StandardOption{
	{Code: GenderUnlimited, Label: "不限", Value: GenderUnlimited},
	{Code: GenderMale, Label: "男", Value: GenderMale},
	{Code: GenderFemale, Label: "女", Value: GenderFemale},
}

// AgeRangeOptions 年龄范围选项
var AgeRangeOptions = []StandardOption{
	{Code: AgeUnlimited, Label: "不限", Value: AgeUnlimited},
	{Code: Age18To30, Label: "18-30岁", Value: Age18To30},
	{Code: Age31To40, Label: "31-40岁", Value: Age31To40},
	{Code: Age41To50, Label: "41-50岁", Value: Age41To50},
	{Code: Age50Plus, Label: "50岁以上", Value: Age50Plus},
}

// EducationOptions 学历选项
var EducationOptions = []StandardOption{
	{Code: EducationUnlimited, Label: "不限", Value: EducationUnlimited},
	{Code: EducationJuniorHigh, Label: "初中及以下", Value: EducationJuniorHigh},
	{Code: EducationHighSchool, Label: "高中/中专", Value: EducationHighSchool},
	{Code: EducationAssociate, Label: "大专", Value: EducationAssociate},
	{Code: EducationBachelor, Label: "本科", Value: EducationBachelor},
	{Code: EducationMaster, Label: "硕士", Value: EducationMaster},
}

// ExperienceOptions 工作经验选项
var ExperienceOptions = []StandardOption{
	{Code: ExperienceUnlimited, Label: "不限", Value: ExperienceUnlimited},
	{Code: ExperienceFreshGraduate, Label: "应届毕业生", Value: ExperienceFreshGraduate},
	{Code: ExperienceUnder1Year, Label: "1年以下", Value: ExperienceUnder1Year},
	{Code: Experience1To3Years, Label: "1-3年", Value: Experience1To3Years},
	{Code: Experience3To5Years, Label: "3-5年", Value: Experience3To5Years},
	{Code: Experience5To10Years, Label: "5-10年", Value: Experience5To10Years},
	{Code: ExperienceOver10Years, Label: "10年以上", Value: ExperienceOver10Years},
}

// SalaryUnitOptions 零工薪资单位选项
var SalaryUnitOptions = []StandardOption{
	{Code: SalaryUnitUnlimited, Label: "不限", Value: SalaryUnitUnlimited},
	{Code: SalaryUnitHour, Label: "元/小时", Value: SalaryUnitHour},
	{Code: SalaryUnitDay, Label: "元/天", Value: SalaryUnitDay},
	{Code: SalaryUnitPiece, Label: "元/件", Value: SalaryUnitPiece},
	{Code: SalaryUnitTotal, Label: "元/总价", Value: SalaryUnitTotal},
}

// SettlementTypeOptions 结算方式选项
var SettlementTypeOptions = []StandardOption{
	{Code: SettlementTypeUnlimited, Label: "不限", Value: SettlementTypeUnlimited},
	{Code: SettlementTypeDaily, Label: "日结", Value: SettlementTypeDaily},
	{Code: SettlementTypeWeekly, Label: "周结", Value: SettlementTypeWeekly},
	{Code: SettlementTypeMonthly, Label: "月结", Value: SettlementTypeMonthly},
	{Code: SettlementTypeFinished, Label: "完工结", Value: SettlementTypeFinished},
}

// CompanySizeOptions 公司规模选项
var CompanySizeOptions = []StandardOption{
	{Code: CompanySizeUnlimited, Label: "不限", Value: CompanySizeUnlimited},
	{Code: CompanySizeStartup, Label: "1-20人", Value: CompanySizeStartup, Min: intPtr(1), Max: intPtr(20)},
	{Code: CompanySizeSmall, Label: "21-100人", Value: CompanySizeSmall, Min: intPtr(21), Max: intPtr(100)},
	{Code: CompanySizeMedium, Label: "101-500人", Value: CompanySizeMedium, Min: intPtr(101), Max: intPtr(500)},
	{Code: CompanySizeLarge, Label: "501-1000人", Value: CompanySizeLarge, Min: intPtr(501), Max: intPtr(1000)},
	{Code: CompanySizeEnterprise, Label: "1000人以上", Value: CompanySizeEnterprise, Min: intPtr(1001), Max: intPtr(999999)},
}

// WelfareOptions 福利待遇选项（多选，JSON数组）
var WelfareOptions = []StandardOption{
	// 基础保障类
	{Code: WelfareSocialInsurance, Label: "五险一金", Value: WelfareSocialInsurance},
	{Code: WelfareInsurance, Label: "五险", Value: WelfareInsurance},
	{Code: WelfareAccommodation, Label: "包食宿", Value: WelfareAccommodation},
	{Code: WelfareFood, Label: "包吃", Value: WelfareFood},
	{Code: WelfareHousing, Label: "包住", Value: WelfareHousing},
	{Code: WelfareMealAllowance, Label: "餐补", Value: WelfareMealAllowance},

	// 薪酬奖励类
	{Code: WelfareAnnualBonus, Label: "年终奖", Value: WelfareAnnualBonus},
	{Code: WelfarePerformanceBonus, Label: "绩效奖金", Value: WelfarePerformanceBonus},
	{Code: WelfareFullAttendance, Label: "全勤奖", Value: WelfareFullAttendance},
	{Code: WelfareOvertimePay, Label: "加班费", Value: WelfareOvertimePay},

	// 时间福利类
	{Code: WelfarePaidLeave, Label: "带薪年假", Value: WelfarePaidLeave},
	{Code: WelfareFlexibleWork, Label: "弹性工作", Value: WelfareFlexibleWork},
	{Code: WelfareWeekendOff, Label: "双休", Value: WelfareWeekendOff},
	{Code: WelfareLegalHolidays, Label: "法定节假日", Value: WelfareLegalHolidays},

	// 交通住宿类
	{Code: WelfareTransportAllowance, Label: "交通补贴", Value: WelfareTransportAllowance},
	{Code: WelfareDormitory, Label: "宿舍", Value: WelfareDormitory},
	{Code: WelfareShuttleBus, Label: "班车接送", Value: WelfareShuttleBus},

	// 发展培训类
	{Code: WelfareTraining, Label: "培训机会", Value: WelfareTraining},
	{Code: WelfarePromotionPath, Label: "晋升通道", Value: WelfarePromotionPath},
	{Code: WelfareSkillTraining, Label: "技能培训", Value: WelfareSkillTraining},

	// 生活关怀类
	{Code: WelfareHolidayBenefits, Label: "节日福利", Value: WelfareHolidayBenefits},
	{Code: WelfareBirthdayBenefit, Label: "生日福利", Value: WelfareBirthdayBenefit},
	{Code: WelfareTeamBuilding, Label: "团建活动", Value: WelfareTeamBuilding},
	{Code: WelfareEmployeeDiscount, Label: "员工优惠", Value: WelfareEmployeeDiscount},

	// 健康医疗类
	{Code: WelfareMedicalExam, Label: "体检", Value: WelfareMedicalExam},
	{Code: WelfareCommercialInsurance, Label: "商业保险", Value: WelfareCommercialInsurance},
}

// =====================================================
// 薪资和范围映射
// =====================================================

// GigSalaryRanges 零工薪资范围映射 (单位：分/小时)
var GigSalaryRanges = map[int16]SalaryRange{
	GigSalaryUnlimited: {Code: int(GigSalaryUnlimited), Min: 0, Max: 0},
	GigSalary20To40:    {Code: int(GigSalary20To40), Min: 2000, Max: 4000},
	GigSalary40To70:    {Code: int(GigSalary40To70), Min: 4000, Max: 7000},
	GigSalary70To100:   {Code: int(GigSalary70To100), Min: 7000, Max: 10000},
	GigSalaryOver100:   {Code: int(GigSalaryOver100), Min: 10000, Max: 999999},
}

// AgeRanges 年龄范围映射
var AgeRanges = map[int16]AgeRange{
	AgeUnlimited: {Code: int(AgeUnlimited), Min: 16, Max: 65},
	Age18To30:    {Code: int(Age18To30), Min: 18, Max: 30},
	Age31To40:    {Code: int(Age31To40), Min: 31, Max: 40},
	Age41To50:    {Code: int(Age41To50), Min: 41, Max: 50},
	Age50Plus:    {Code: int(Age50Plus), Min: 51, Max: 65},
}

// CompanySizeRanges 公司规模范围映射
var CompanySizeRanges = map[int16]CompanySizeRange{
	CompanySizeUnlimited:  {Code: int(CompanySizeUnlimited), Min: 0, Max: 0},
	CompanySizeStartup:    {Code: int(CompanySizeStartup), Min: 1, Max: 20},
	CompanySizeSmall:      {Code: int(CompanySizeSmall), Min: 21, Max: 100},
	CompanySizeMedium:     {Code: int(CompanySizeMedium), Min: 101, Max: 500},
	CompanySizeLarge:      {Code: int(CompanySizeLarge), Min: 501, Max: 1000},
	CompanySizeEnterprise: {Code: int(CompanySizeEnterprise), Min: 1001, Max: 999999},
}

// =====================================================
// 工具函数
// =====================================================

// intPtr 创建int指针的辅助函数
func intPtr(i int) *int {
	return &i
}

// GetGigSalaryRange 获取零工薪资范围
func GetGigSalaryRange(code int16) (SalaryRange, bool) {
	r, ok := GigSalaryRanges[code]
	return r, ok
}

// GetAgeRange 获取年龄范围
func GetAgeRange(code int16) (AgeRange, bool) {
	r, ok := AgeRanges[code]
	return r, ok
}

// GetCompanySizeRange 获取公司规模范围
func GetCompanySizeRange(code int16) (CompanySizeRange, bool) {
	r, ok := CompanySizeRanges[code]
	return r, ok
}

// GetLabelByCode 根据选项列表和代码获取标签
func GetLabelByCode(options []StandardOption, code int16) string {
	for _, option := range options {
		if option.Code == code {
			return option.Label
		}
	}
	return ""
}

// GetWelfareLabels 根据编码数组获取福利标签
func GetWelfareLabels(codes []int16) []string {
	labels := make([]string, 0, len(codes))
	for _, code := range codes {
		for _, option := range WelfareOptions {
			if option.Code == code {
				labels = append(labels, option.Label)
				break
			}
		}
	}
	return labels
}

// GetWelfareByCode 根据编码获取福利选项
func GetWelfareByCode(code int16) (StandardOption, bool) {
	for _, option := range WelfareOptions {
		if option.Code == code {
			return option, true
		}
	}
	return StandardOption{}, false
}

// ContainsWelfare 检查福利编码数组是否包含指定福利
func ContainsWelfare(codes []int16, targetCode int16) bool {
	for _, code := range codes {
		if code == targetCode {
			return true
		}
	}
	return false
}

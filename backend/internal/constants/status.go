package constants

// =====================================================
// 通用状态常量 - 适用于所有业务模块
// =====================================================

// 通用激活状态
const (
	StatusInactive = 0 // 未激活/禁用
	StatusActive   = 1 // 激活/正常
	StatusDeleted  = 2 // 已删除
	StatusFrozen   = 3 // 冻结
)

// 通用审核状态
const (
	AuditStatusPending  = 0 // 待审核
	AuditStatusApproved = 1 // 已通过
	AuditStatusRejected = 2 // 已拒绝
)

// 通用布尔状态的数值表示
const (
	BoolNo  = 0 // 否
	BoolYes = 1 // 是
)

// =====================================================
// 软删除常量
// =====================================================

const (
	NotDeleted = 0 // 未删除
	IsDeleted  = 1 // 已删除
)

// =====================================================
// 状态验证和转换接口
// =====================================================

// StatusValidator 状态验证器接口
type StatusValidator interface {
	IsValid() bool
	CanTransitionTo(newStatus int) bool
	GetAllowedTransitions() []int
}

// StatusTransition 状态转换规则
type StatusTransition struct {
	From         int      `json:"from"`
	To           int      `json:"to"`
	AllowedRoles []string `json:"allowed_roles,omitempty"` // 允许执行转换的角色
}

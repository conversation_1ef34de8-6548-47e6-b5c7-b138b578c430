package constants

// =====================================================
// Job模块常量定义
// =====================================================

// =====================================================
// 职位状态
// =====================================================
const (
	JobStatusPendingReview = "pending_review" // 待审核
	JobStatusActive        = "active"         // 招聘中
	JobStatusPaused        = "paused"         // 暂停招聘
	JobStatusClosed        = "closed"         // 已关闭
	JobStatusExpired       = "expired"        // 已过期
	JobStatusRejected      = "rejected"       // 审核拒绝
)

// =====================================================
// 投递申请状态
// =====================================================
const (
	ApplicationStatusSubmitted        = "submitted"         // 已投递
	ApplicationStatusViewed           = "viewed"            // 已查看
	ApplicationStatusInterviewInvited = "interview_invited" // 邀请面试
	ApplicationStatusInterviewing     = "interviewing"      // 面试中
	ApplicationStatusInterviewPassed  = "interview_passed"  // 面试通过
	ApplicationStatusInterviewFailed  = "interview_failed"  // 面试未通过
	ApplicationStatusHired            = "hired"             // 已录用
	ApplicationStatusRejected         = "rejected"          // 已拒绝
)

// =====================================================
// 认证状态
// =====================================================
const (
	VerificationStatusPending  = "pending"  // 待认证
	VerificationStatusApproved = "approved" // 认证通过
	VerificationStatusRejected = "rejected" // 认证拒绝
)

// =====================================================
// 企业认证类型
// =====================================================
const (
	EnterpriseTypeEnterprise    = "enterprise"     // 企业
	EnterpriseTypeSmallBusiness = "small_business" // 个体户
	EnterpriseTypeIndividual    = "individual"     // 个人
)

// =====================================================
// 工作类型 - 使用int16类型
// =====================================================
const (
	WorkTypeFullTime   int16 = 1 // 全职
	WorkTypePartTime   int16 = 2 // 兼职
	WorkTypeInternship int16 = 3 // 实习
	WorkTypeContract   int16 = 4 // 合同工
)

// =====================================================
// 简历工作状态
// =====================================================
const (
	WorkStatusCurrentlyEmployed = "currently_employed" // 在职
	WorkStatusUnemployed        = "unemployed"         // 离职
	WorkStatusStudent           = "student"            // 学生
)

// =====================================================
// 联系方式
// =====================================================
const (
	ContactMethodPhone  = "phone"  // 电话
	ContactMethodWeChat = "wechat" // 微信
	ContactMethodEmail  = "email"  // 邮箱
)

// =====================================================
// 订阅状态
// =====================================================
const (
	SubscriptionStatusActive    = "active"    // 有效
	SubscriptionStatusExpired   = "expired"   // 已过期
	SubscriptionStatusCancelled = "cancelled" // 已取消
)

// =====================================================
// 通知类型
// =====================================================
const (
	NotificationTypeSystem      = "system"      // 系统通知
	NotificationTypeApplication = "application" // 申请通知
	NotificationTypeInterview   = "interview"   // 面试通知
	NotificationTypeOffer       = "offer"       // 录用通知
)

// =====================================================
// 举报状态
// =====================================================
const (
	ReportStatusPending    = "pending"    // 待处理
	ReportStatusProcessing = "processing" // 处理中
	ReportStatusResolved   = "resolved"   // 已解决
	ReportStatusRejected   = "rejected"   // 已拒绝
)

// =====================================================
// 会员套餐键值
// =====================================================
const (
	MembershipPlanFreeTrial = "free_trial"    // 免费试用
	MembershipPlanBasic     = "basic_plan"    // 基础套餐
	MembershipPlanStandard  = "standard_plan" // 标准套餐
	MembershipPlanPremium   = "premium_plan"  // 高级套餐
)

// =====================================================
// 职位薪资标准 (按月，单位：元) - 使用int16类型
// =====================================================
const (
	JobSalaryUnlimited int16 = 0 // 不限
	JobSalary2KTo4K    int16 = 1 // 2K-4K
	JobSalary4KTo6K    int16 = 2 // 4K-6K
	JobSalary6KTo8K    int16 = 3 // 6K-8K
	JobSalary8KTo12K   int16 = 4 // 8K-12K
	JobSalary12KTo18K  int16 = 5 // 12K-18K
	JobSalary18KTo25K  int16 = 6 // 18K-25K
	JobSalaryOver25K   int16 = 7 // 25K以上
)

// =====================================================
// 选项数据结构
// =====================================================

// JobStatusOption 职位状态选项
type JobStatusOption struct {
	Code  string `json:"code"`
	Label string `json:"label"`
	Color string `json:"color"`
}

// ApplicationStatusOption 申请状态选项
type ApplicationStatusOption struct {
	Code  string `json:"code"`
	Label string `json:"label"`
	Color string `json:"color"`
}

// WorkTypeOption 工作类型选项
type WorkTypeOption struct {
	Code  int16  `json:"code"`
	Label string `json:"label"`
	Value string `json:"value"`
}

// WorkStatusOption 工作状态选项
type WorkStatusOption struct {
	Code  string `json:"code"`
	Label string `json:"label"`
	Value string `json:"value"`
}

// ContactMethodOption 联系方式选项
type ContactMethodOption struct {
	Code  string `json:"code"`
	Label string `json:"label"`
	Icon  string `json:"icon"`
}

// =====================================================
// 预定义选项数据
// =====================================================

// JobStatusOptions 职位状态选项
var JobStatusOptions = []JobStatusOption{
	{Code: JobStatusPendingReview, Label: "待审核", Color: "#f59e0b"},
	{Code: JobStatusActive, Label: "招聘中", Color: "#10b981"},
	{Code: JobStatusPaused, Label: "暂停招聘", Color: "#6b7280"},
	{Code: JobStatusClosed, Label: "已关闭", Color: "#ef4444"},
	{Code: JobStatusExpired, Label: "已过期", Color: "#9ca3af"},
	{Code: JobStatusRejected, Label: "审核拒绝", Color: "#dc2626"},
}

// ApplicationStatusOptions 申请状态选项
var ApplicationStatusOptions = []ApplicationStatusOption{
	{Code: ApplicationStatusSubmitted, Label: "已投递", Color: "#3b82f6"},
	{Code: ApplicationStatusViewed, Label: "已查看", Color: "#8b5cf6"},
	{Code: ApplicationStatusInterviewInvited, Label: "邀请面试", Color: "#f59e0b"},
	{Code: ApplicationStatusInterviewing, Label: "面试中", Color: "#06b6d4"},
	{Code: ApplicationStatusInterviewPassed, Label: "面试通过", Color: "#10b981"},
	{Code: ApplicationStatusInterviewFailed, Label: "面试未通过", Color: "#f97316"},
	{Code: ApplicationStatusHired, Label: "已录用", Color: "#059669"},
	{Code: ApplicationStatusRejected, Label: "已拒绝", Color: "#ef4444"},
}

// WorkTypeOptions 工作类型选项
var WorkTypeOptions = []WorkTypeOption{
	{Code: WorkTypeFullTime, Label: "全职", Value: "full_time"},
	{Code: WorkTypePartTime, Label: "兼职", Value: "part_time"},
	{Code: WorkTypeInternship, Label: "实习", Value: "internship"},
	{Code: WorkTypeContract, Label: "合同工", Value: "contract"},
}

// WorkStatusOptions 工作状态选项
var WorkStatusOptions = []WorkStatusOption{
	{Code: WorkStatusCurrentlyEmployed, Label: "在职", Value: "在职"},
	{Code: WorkStatusUnemployed, Label: "离职", Value: "离职"},
	{Code: WorkStatusStudent, Label: "学生", Value: "学生"},
}

// ContactMethodOptions 联系方式选项
var ContactMethodOptions = []ContactMethodOption{
	{Code: ContactMethodPhone, Label: "电话", Icon: "phone"},
	{Code: ContactMethodWeChat, Label: "微信", Icon: "wechat"},
	{Code: ContactMethodEmail, Label: "邮箱", Icon: "email"},
}

// VerificationStatusOptions 认证状态选项
var VerificationStatusOptions = []StandardOption{
	{Code: 0, Label: "待认证", Value: 0},
	{Code: 1, Label: "认证通过", Value: 1},
	{Code: 2, Label: "认证拒绝", Value: 2},
}

// EnterpriseTypeOptions 企业类型选项
var EnterpriseTypeOptions = []StandardOption{
	{Code: 1, Label: "企业", Value: 1},
	{Code: 2, Label: "个体户", Value: 2},
	{Code: 3, Label: "个人", Value: 3},
}

// JobSalaryOptions 职位薪资选项 (单位：元/月)
var JobSalaryOptions = []StandardOption{
	{Code: JobSalaryUnlimited, Label: "不限", Value: JobSalaryUnlimited},
	{Code: JobSalary2KTo4K, Label: "2K-4K", Value: JobSalary2KTo4K},
	{Code: JobSalary4KTo6K, Label: "4K-6K", Value: JobSalary4KTo6K},
	{Code: JobSalary6KTo8K, Label: "6K-8K", Value: JobSalary6KTo8K},
	{Code: JobSalary8KTo12K, Label: "8K-12K", Value: JobSalary8KTo12K},
	{Code: JobSalary12KTo18K, Label: "12K-18K", Value: JobSalary12KTo18K},
	{Code: JobSalary18KTo25K, Label: "18K-25K", Value: JobSalary18KTo25K},
	{Code: JobSalaryOver25K, Label: "25K以上", Value: JobSalaryOver25K},
}

// =====================================================
// 薪资范围映射
// =====================================================

// JobSalaryRanges 职位薪资范围映射 (单位：元/月)
var JobSalaryRanges = map[int16]SalaryRange{
	JobSalaryUnlimited: {Code: int(JobSalaryUnlimited), Min: 0, Max: 0},
	JobSalary2KTo4K:    {Code: int(JobSalary2KTo4K), Min: 2000, Max: 4000},
	JobSalary4KTo6K:    {Code: int(JobSalary4KTo6K), Min: 4000, Max: 6000},
	JobSalary6KTo8K:    {Code: int(JobSalary6KTo8K), Min: 6000, Max: 8000},
	JobSalary8KTo12K:   {Code: int(JobSalary8KTo12K), Min: 8000, Max: 12000},
	JobSalary12KTo18K:  {Code: int(JobSalary12KTo18K), Min: 12000, Max: 18000},
	JobSalary18KTo25K:  {Code: int(JobSalary18KTo25K), Min: 18000, Max: 25000},
	JobSalaryOver25K:   {Code: int(JobSalaryOver25K), Min: 25000, Max: -1},
}

// =====================================================
// 业务限制常量
// =====================================================

// 会员权益限制
const (
	// 免费试用套餐
	FreeTrialJobPostLimit      = 2  // 发布职位数量限制
	FreeTrialDailyRefreshLimit = 1  // 每日刷新次数限制
	FreeTrialResumeViewLimit   = 10 // 简历查看数量限制

	// 基础套餐
	BasicPlanJobPostLimit      = 5  // 发布职位数量限制
	BasicPlanDailyRefreshLimit = 3  // 每日刷新次数限制
	BasicPlanResumeViewLimit   = 50 // 简历查看数量限制

	// 标准套餐
	StandardPlanJobPostLimit      = 15  // 发布职位数量限制
	StandardPlanDailyRefreshLimit = 5   // 每日刷新次数限制
	StandardPlanResumeViewLimit   = 200 // 简历查看数量限制
	StandardPlanUrgentJobLimit    = 3   // 紧急招聘数量限制

	// 高级套餐
	PremiumPlanJobPostLimit      = 50  // 发布职位数量限制
	PremiumPlanDailyRefreshLimit = 10  // 每日刷新次数限制
	PremiumPlanResumeViewLimit   = 500 // 简历查看数量限制
	PremiumPlanUrgentJobLimit    = 10  // 紧急招聘数量限制
)

// 业务规则限制
const (
	MaxJobTitleLength       = 50   // 职位标题最大长度
	MaxJobDescriptionLength = 5000 // 职位描述最大长度
	MaxCompanyNameLength    = 100  // 公司名称最大长度
	MaxContactPersonLength  = 50   // 联系人姓名最大长度
	MaxJobHighlightsCount   = 10   // 职位亮点最大数量
	MaxRequirementsCount    = 20   // 任职要求最大数量
	MaxBenefitsCount        = 15   // 福利待遇最大数量

	MaxResumeNameLength        = 20   // 简历姓名最大长度
	MaxSelfIntroductionLength  = 1000 // 自我介绍最大长度
	MaxWorkExperienceCount     = 10   // 工作经历最大数量
	MaxEducationHistoryCount   = 5    // 教育背景最大数量
	MaxSkillsCount             = 30   // 技能标签最大数量
	MaxJobIntentionsCount      = 5    // 求职意向最大数量
	MaxPreferredLocationsCount = 10   // 意向地点最大数量

	MaxApplicationsPerDay = 20  // 每日申请数量限制
	MaxFavoritesCount     = 100 // 收藏数量限制
	MaxReportsPerDay      = 5   // 每日举报数量限制

	UrgentJobExpirationDays = 7  // 紧急招聘有效期天数
	JobRefreshCooldownHours = 2  // 职位刷新冷却时间
	ApplicationRetryDays    = 30 // 申请重试间隔天数
)

// =====================================================
// 工具函数
// =====================================================

// GetJobSalaryRange 获取职位薪资范围
func GetJobSalaryRange(code int16) (SalaryRange, bool) {
	r, ok := JobSalaryRanges[code]
	return r, ok
}

// GetJobStatusLabel 根据状态代码获取状态标签
func GetJobStatusLabel(status string) string {
	for _, option := range JobStatusOptions {
		if option.Code == status {
			return option.Label
		}
	}
	return status
}

// GetJobStatusColor 根据状态代码获取状态颜色
func GetJobStatusColor(status string) string {
	for _, option := range JobStatusOptions {
		if option.Code == status {
			return option.Color
		}
	}
	return "#6b7280" // 默认灰色
}

// GetApplicationStatusLabel 根据申请状态代码获取状态标签
func GetApplicationStatusLabel(status string) string {
	for _, option := range ApplicationStatusOptions {
		if option.Code == status {
			return option.Label
		}
	}
	return status
}

// GetApplicationStatusColor 根据申请状态代码获取状态颜色
func GetApplicationStatusColor(status string) string {
	for _, option := range ApplicationStatusOptions {
		if option.Code == status {
			return option.Color
		}
	}
	return "#6b7280" // 默认灰色
}

// GetWorkTypeLabel 根据工作类型代码获取类型标签
func GetWorkTypeLabel(workType int16) string {
	for _, option := range WorkTypeOptions {
		if option.Code == workType {
			return option.Label
		}
	}
	return "未知"
}

// GetContactMethodLabel 根据联系方式代码获取标签
func GetContactMethodLabel(method string) string {
	for _, option := range ContactMethodOptions {
		if option.Code == method {
			return option.Label
		}
	}
	return method
}

// IsValidJobStatus 检查职位状态是否有效
func IsValidJobStatus(status string) bool {
	validStatuses := []string{
		JobStatusPendingReview,
		JobStatusActive,
		JobStatusPaused,
		JobStatusClosed,
		JobStatusExpired,
		JobStatusRejected,
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// IsValidApplicationStatus 检查申请状态是否有效
func IsValidApplicationStatus(status string) bool {
	validStatuses := []string{
		ApplicationStatusSubmitted,
		ApplicationStatusViewed,
		ApplicationStatusInterviewInvited,
		ApplicationStatusInterviewing,
		ApplicationStatusInterviewPassed,
		ApplicationStatusInterviewFailed,
		ApplicationStatusHired,
		ApplicationStatusRejected,
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// IsValidWorkType 检查工作类型是否有效
func IsValidWorkType(workType int16) bool {
	validTypes := []int16{
		WorkTypeFullTime,
		WorkTypePartTime,
		WorkTypeInternship,
		WorkTypeContract,
	}

	for _, validType := range validTypes {
		if workType == validType {
			return true
		}
	}
	return false
}

// IsValidContactMethod 检查联系方式是否有效
func IsValidContactMethod(method string) bool {
	validMethods := []string{
		ContactMethodPhone,
		ContactMethodWeChat,
		ContactMethodEmail,
	}

	for _, validMethod := range validMethods {
		if method == validMethod {
			return true
		}
	}
	return false
}

// IsJobActive 检查职位是否处于活跃状态
func IsJobActive(status string) bool {
	return status == JobStatusActive
}

// CanJobBeRefreshed 检查职位是否可以刷新
func CanJobBeRefreshed(status string) bool {
	return status == JobStatusActive || status == JobStatusPaused
}

// CanApplyToJob 检查是否可以申请职位
func CanApplyToJob(status string) bool {
	return status == JobStatusActive
}

// IsApplicationPending 检查申请是否为待处理状态
func IsApplicationPending(status string) bool {
	pendingStatuses := []string{
		ApplicationStatusSubmitted,
		ApplicationStatusViewed,
		ApplicationStatusInterviewInvited,
		ApplicationStatusInterviewing,
		ApplicationStatusInterviewPassed,
	}

	for _, pendingStatus := range pendingStatuses {
		if status == pendingStatus {
			return true
		}
	}
	return false
}

// IsApplicationFinal 检查申请是否为最终状态
func IsApplicationFinal(status string) bool {
	finalStatuses := []string{
		ApplicationStatusHired,
		ApplicationStatusRejected,
		ApplicationStatusInterviewFailed,
	}

	for _, finalStatus := range finalStatuses {
		if status == finalStatus {
			return true
		}
	}
	return false
}

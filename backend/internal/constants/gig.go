package constants

// GigStatus 定义了零工（Gig）的各种状态
type GigStatus string

const (
	// GigStatusDraft 草稿: 已创建但未发布
	GigStatusDraft GigStatus = "draft"
	// GigStatusRecruiting 招募中: 已发布，正在接受申请
	GigStatusRecruiting GigStatus = "recruiting"
	// GigStatusPaused 暂停中: 发布者手动暂停招募，可随时恢复
	GigStatusPaused GigStatus = "paused"
	// GigStatusLocked 招募截止: 已停止接受新申请，等待工作开始
	GigStatusLocked GigStatus = "locked"
	// GigStatusInProgress 进行中: 工作已开始
	GigStatusInProgress GigStatus = "in_progress"
	// GigStatusCompleted 已完成: 支付和所有流程已结束
	GigStatusCompleted GigStatus = "completed"
	// GigStatusClosed 已关闭: 在完成前被终止（手动或系统过期）
	GigStatusClosed GigStatus = "closed"
)

// GigApplicationStatus 定义了零工申请的各种状态
type GigApplicationStatus string

const (
	GigApplicationStatusPending   GigApplicationStatus = "pending"
	GigApplicationStatusWithdrawn GigApplicationStatus = "withdrawn"
	GigApplicationStatusConfirmed GigApplicationStatus = "confirmed"
	GigApplicationStatusRejected  GigApplicationStatus = "rejected"
	GigApplicationStatusCancelled GigApplicationStatus = "cancelled"
	GigApplicationStatusCompleted GigApplicationStatus = "completed"
	GigApplicationStatusNoShow    GigApplicationStatus = "no_show"
)

// GigApprovalMode 定义了零工的审批模式
type GigApprovalMode string

const (
	GigApprovalModeManual GigApprovalMode = "manual" // 手动审核
	GigApprovalModeAuto   GigApprovalMode = "auto"   // 自动录用
)

// GigCheckInMethod 定义了零工的打卡方式
type GigCheckInMethod string

const (
	GigCheckInMethodNone   GigCheckInMethod = "none"   // 无需打卡
	GigCheckInMethodGPS    GigCheckInMethod = "gps"    // GPS打卡
	GigCheckInMethodQRCode GigCheckInMethod = "qrcode" // 二维码打卡
)

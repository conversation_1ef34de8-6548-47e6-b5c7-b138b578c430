---
alwaysApply: true
---
# Go 编程规范 (Go 1.24)

## 📋 目录

- [工具链](#工具链)
- [核心原则](#核心原则)
- [错误处理](#错误处理)
- [并发编程](#并发编程)
- [性能优化](#性能优化)
- [代码风格](#代码风格)
- [结构体和接口](#结构体和接口)
- [测试规范](#测试规范)

## 工具链

### 推荐的代码检查工具

```bash
# 格式化代码
goimports -w .

# 静态分析 
staticcheck ./...

# 官方检查
go vet ./...

# 安全检查
gosec ./...
```

### 编辑器配置

建议配置编辑器在保存时自动运行：
- `goimports` - 自动导入和格式化
- `go vet` - 官方检查工具

## 核心原则

### 1. 指向接口的指针

❌ **错误做法**
```go
func process(user *UserInterface) error {
    // 几乎不需要指向接口的指针
}
```

✅ **正确做法**
```go
func process(user UserInterface) error {
    // 接口作为值传递
}
```

### 2. 接收器与接口

值接收器的方法可以被值和指针调用，指针接收器只能被指针调用。

```go
type User struct {
    name string
}

// 值接收器 - 值和指针都可以调用
func (u User) GetName() string {
    return u.name
}

// 指针接收器 - 只有指针可以调用
func (u *User) SetName(name string) {
    u.name = name
}
```

### 3. 零值 Mutex 有效

❌ **错误做法**
```go
mu := new(sync.Mutex)
mu.Lock()
```

✅ **正确做法**
```go
var mu sync.Mutex
mu.Lock()
```

### 4. 在边界处复制切片和映射

❌ **错误做法**
```go
func (d *Driver) SetTrips(trips []Trip) {
    d.trips = trips // 直接引用，用户可能会修改
}
```

✅ **正确做法**
```go
func (d *Driver) SetTrips(trips []Trip) {
    d.trips = make([]Trip, len(trips))
    copy(d.trips, trips)
}
```

### 5. 使用 defer 清理资源

❌ **错误做法**
```go
p.Lock()
if p.count < 10 {
    p.Unlock()
    return p.count
}
p.count++
p.Unlock()
return p.count
```

✅ **正确做法**
```go
p.Lock()
defer p.Unlock()

if p.count < 10 {
    return p.count
}
p.count++
return p.count
```

### 6. 枚举从 1 开始

❌ **错误做法**
```go
type Operation int

const (
    Add Operation = iota  // Add=0
    Subtract
    Multiply
)
```

✅ **正确做法**
```go
type Operation int

const (
    Add Operation = iota + 1  // Add=1
    Subtract
    Multiply
)
```

## 错误处理

### 1. 错误类型选择

| 需要匹配错误？ | 错误消息 | 推荐方案                |
| -------------- | -------- | ----------------------- |
| 否             | 静态     | `errors.New`            |
| 否             | 动态     | `fmt.Errorf`            |
| 是             | 静态     | 全局变量 + `errors.New` |
| 是             | 动态     | 自定义错误类型          |

### 2. 错误封装

✅ **使用 %w 保留原始错误**
```go
if err != nil {
    return fmt.Errorf("创建存储失败: %w", err)
}
```

### 3. 错误命名

```go
var (
    // 导出错误使用 Err 前缀
    ErrUserNotFound = errors.New("用户未找到")
    
    // 非导出错误使用 err 前缀
    errInvalidInput = errors.New("输入无效")
)

// 自定义错误类型使用 Error 后缀
type ValidationError struct {
    Field string
    Value interface{}
}
```

### 4. 只处理一次错误

❌ **错误做法**
```go
if err := doSomething(); err != nil {
    log.Printf("操作失败: %v", err)  // 记录错误
    return err                      // 又返回错误
}
```

✅ **正确做法**
```go
// 要么记录并处理
if err := doSomething(); err != nil {
    log.Printf("操作失败: %v", err)
    return nil  // 已处理，返回 nil
}

// 要么封装并返回
if err := doSomething(); err != nil {
    return fmt.Errorf("执行操作: %w", err)
}
```

### 5. 处理类型断言失败

❌ **错误做法**
```go
t := i.(string)  // 会 panic
```

✅ **正确做法**
```go
t, ok := i.(string)
if !ok {
    return fmt.Errorf("类型断言失败")
}
```

### 6. 不要使用 panic

❌ **错误做法**
```go
func run(args []string) {
    if len(args) == 0 {
        panic("需要参数")
    }
}
```

✅ **正确做法**
```go
func run(args []string) error {
    if len(args) == 0 {
        return errors.New("需要参数")
    }
    return nil
}

func main() {
    if err := run(os.Args[1:]); err != nil {
        log.Fatal(err)
    }
}
```

## 并发编程

### 1. Channel 大小

Channel 应该是无缓冲的（大小为 0）或大小为 1。

❌ **错误做法**
```go
c := make(chan int, 64)  // 任意大小需要仔细考虑
```

✅ **正确做法**
```go
// 无缓冲
c := make(chan int)

// 或者大小为 1
c := make(chan int, 1)
```

### 2. 避免可变全局变量

❌ **错误做法**
```go
var timeNow = time.Now

func sign(msg string) string {
    now := timeNow()
    return signWithTime(msg, now)
}
```

✅ **正确做法**
```go
type Signer struct {
    now func() time.Time
}

func NewSigner() *Signer {
    return &Signer{
        now: time.Now,
    }
}

func (s *Signer) Sign(msg string) string {
    now := s.now()
    return signWithTime(msg, now)
}
```

### 3. 使用标准库原子操作

在 Go 1.19+ 中，使用标准库的类型安全原子操作：

```go
type Counter struct {
    value atomic.Int64
}

func (c *Counter) Add(delta int64) {
    c.value.Add(delta)
}

func (c *Counter) Value() int64 {
    return c.value.Load()
}
```

## 性能优化

### 1. 优先使用 strconv 而不是 fmt

❌ **较慢**
```go
s := fmt.Sprint(123)
```

✅ **更快**
```go
s := strconv.Itoa(123)
```

### 2. 避免重复的字符串到字节转换

❌ **错误做法**
```go
for i := 0; i < n; i++ {
    w.Write([]byte("Hello World"))
}
```

✅ **正确做法**
```go
data := []byte("Hello World")
for i := 0; i < n; i++ {
    w.Write(data)
}
```

### 3. 指定容器容量

```go
// 指定切片容量
data := make([]int, 0, expectedSize)

// 指定映射容量
m := make(map[string]int, expectedSize)
```

## 代码风格

### 1. 相似声明分组

```go
import (
    "fmt"
    "os"
    
    "github.com/pkg/errors"
    "go.uber.org/zap"
)

const (
    MaxRetries = 3
    Timeout    = 30 * time.Second
)

var (
    ErrNotFound = errors.New("未找到")
    ErrTimeout  = errors.New("超时")
)
```

### 2. 包命名

- 全部小写，无下划线
- 简短且有意义
- 不使用复数
- 避免使用 `common`、`util`、`shared`

### 3. 函数分组与顺序

```go
type User struct {
    id   int
    name string
}

// 构造函数紧跟类型定义
func NewUser(id int, name string) *User {
    return &User{id: id, name: name}
}

// 方法按接收器分组
func (u *User) ID() int {
    return u.id
}

func (u *User) Name() string {
    return u.name
}

// 工具函数放在文件末尾
func validateUserInput(input string) error {
    // ...
}
```

### 4. 减少嵌套

❌ **错误做法**
```go
for _, v := range data {
    if v.IsValid() {
        if v.Process() {
            v.Save()
        } else {
            return err
        }
    } else {
        log.Printf("无效数据: %v", v)
    }
}
```

✅ **正确做法**
```go
for _, v := range data {
    if !v.IsValid() {
        log.Printf("无效数据: %v", v)
        continue
    }
    
    if !v.Process() {
        return err
    }
    
    v.Save()
}
```

### 5. 变量声明

```go
// 简短声明
s := "hello"

// 零值清晰时使用 var
var users []User

// 指定类型（当类型不匹配时）
var timeout time.Duration = 30 * time.Second
```

### 6. nil 是有效的 slice

```go
// 返回 nil 而不是空切片
if len(users) == 0 {
    return nil
}

// 检查长度而不是 nil
if len(users) == 0 {
    // 处理空切片
}
```

## 结构体和接口

### 1. 避免在公共结构中嵌入类型

❌ **错误做法**
```go
type User struct {
    sync.Mutex  // 暴露了内部实现
    name string
}
```

✅ **正确做法**
```go
type User struct {
    mu   sync.Mutex
    name string
}
```

### 2. 使用字段名初始化结构体

❌ **错误做法**
```go
user := User{"Alice", 25, true}
```

✅ **正确做法**
```go
user := User{
    Name: "Alice",
    Age:  25,
    Active: true,
}
```

### 3. 初始化结构体引用

✅ **正确做法**
```go
// 保持一致性
user := User{Name: "Alice"}
userPtr := &User{Name: "Bob"}
```

## 测试规范

### 1. 表驱动测试

```go
func TestSplitHostPort(t *testing.T) {
    tests := []struct {
        name     string
        give     string
        wantHost string
        wantPort string
        wantErr  bool
    }{
        {
            name:     "标准地址",
            give:     "***********:8080",
            wantHost: "***********",
            wantPort: "8080",
        },
        {
            name:     "IPv6地址",
            give:     "[::1]:8080",
            wantHost: "::1",
            wantPort: "8080",
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            host, port, err := net.SplitHostPort(tt.give)
            
            if tt.wantErr {
                assert.Error(t, err)
                return
            }
            
            assert.NoError(t, err)
            assert.Equal(t, tt.wantHost, host)
            assert.Equal(t, tt.wantPort, port)
        })
    }
}
```

### 2. 测试文件命名

- 测试文件以 `_test.go` 结尾
- 测试函数以 `Test` 开头
- 基准测试以 `Benchmark` 开头
- 示例函数以 `Example` 开头

## 特定于项目的约定

### 1. API 响应格式

```go
type Response struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

type PaginatedResponse struct {
    Response
    Total int `json:"total,omitempty"`
    Page  int `json:"page,omitempty"`
    Size  int `json:"size,omitempty"`
}
```

### 2. 日志记录

```go
// 使用结构化日志
logger.Info("用户创建成功",
    zap.String("user_id", userID),
    zap.String("email", user.Email),
    zap.Duration("duration", time.Since(start)),
)

// 错误日志包含上下文
logger.Error("数据库查询失败",
    zap.Error(err),
    zap.String("query", query),
    zap.Any("params", params),
)
```

### 3. 配置管理

```go
type Config struct {
    Server ServerConfig `yaml:"server"`
    DB     DBConfig     `yaml:"database"`
    Redis  RedisConfig  `yaml:"redis"`
}

type ServerConfig struct {
    Host     string        `yaml:"host"`
    Port     int           `yaml:"port"`
    Timeout  time.Duration `yaml:"timeout"`
}
```

---

## 📝 总结

这份规范基于 Uber Go Style Guide 并结合实际项目需求制定。重点关注：

1. **代码可读性** - 代码是给人读的
2. **错误处理** - 明确的错误处理策略
3. **并发安全** - 正确使用 Go 的并发特性
4. **性能考虑** - 避免常见的性能陷阱
5. **一致性** - 团队统一的编码风格

记住：好的代码不仅要能运行，还要易于理解、维护和扩展。# Go 编程规范 (Go 1.24)

## 📋 目录

- [工具链](#工具链)
- [核心原则](#核心原则)
- [错误处理](#错误处理)
- [并发编程](#并发编程)
- [性能优化](#性能优化)
- [代码风格](#代码风格)
- [结构体和接口](#结构体和接口)
- [测试规范](#测试规范)

## 工具链

### 推荐的代码检查工具

```bash
# 格式化代码
goimports -w .

# 静态分析 
staticcheck ./...

# 官方检查
go vet ./...

# 安全检查
gosec ./...
```

### 编辑器配置

建议配置编辑器在保存时自动运行：
- `goimports` - 自动导入和格式化
- `go vet` - 官方检查工具

## 核心原则

### 1. 指向接口的指针

❌ **错误做法**
```go
func process(user *UserInterface) error {
    // 几乎不需要指向接口的指针
}
```

✅ **正确做法**
```go
func process(user UserInterface) error {
    // 接口作为值传递
}
```

### 2. 接收器与接口

值接收器的方法可以被值和指针调用，指针接收器只能被指针调用。

```go
type User struct {
    name string
}

// 值接收器 - 值和指针都可以调用
func (u User) GetName() string {
    return u.name
}

// 指针接收器 - 只有指针可以调用
func (u *User) SetName(name string) {
    u.name = name
}
```

### 3. 零值 Mutex 有效

❌ **错误做法**
```go
mu := new(sync.Mutex)
mu.Lock()
```

✅ **正确做法**
```go
var mu sync.Mutex
mu.Lock()
```

### 4. 在边界处复制切片和映射

❌ **错误做法**
```go
func (d *Driver) SetTrips(trips []Trip) {
    d.trips = trips // 直接引用，用户可能会修改
}
```

✅ **正确做法**
```go
func (d *Driver) SetTrips(trips []Trip) {
    d.trips = make([]Trip, len(trips))
    copy(d.trips, trips)
}
```

### 5. 使用 defer 清理资源

❌ **错误做法**
```go
p.Lock()
if p.count < 10 {
    p.Unlock()
    return p.count
}
p.count++
p.Unlock()
return p.count
```

✅ **正确做法**
```go
p.Lock()
defer p.Unlock()

if p.count < 10 {
    return p.count
}
p.count++
return p.count
```

### 6. 枚举从 1 开始

❌ **错误做法**
```go
type Operation int

const (
    Add Operation = iota  // Add=0
    Subtract
    Multiply
)
```

✅ **正确做法**
```go
type Operation int

const (
    Add Operation = iota + 1  // Add=1
    Subtract
    Multiply
)
```

## 错误处理

### 1. 错误类型选择

| 需要匹配错误？ | 错误消息 | 推荐方案                |
| -------------- | -------- | ----------------------- |
| 否             | 静态     | `errors.New`            |
| 否             | 动态     | `fmt.Errorf`            |
| 是             | 静态     | 全局变量 + `errors.New` |
| 是             | 动态     | 自定义错误类型          |

### 2. 错误封装

✅ **使用 %w 保留原始错误**
```go
if err != nil {
    return fmt.Errorf("创建存储失败: %w", err)
}
```

### 3. 错误命名

```go
var (
    // 导出错误使用 Err 前缀
    ErrUserNotFound = errors.New("用户未找到")
    
    // 非导出错误使用 err 前缀
    errInvalidInput = errors.New("输入无效")
)

// 自定义错误类型使用 Error 后缀
type ValidationError struct {
    Field string
    Value interface{}
}
```

### 4. 只处理一次错误

❌ **错误做法**
```go
if err := doSomething(); err != nil {
    log.Printf("操作失败: %v", err)  // 记录错误
    return err                      // 又返回错误
}
```

✅ **正确做法**
```go
// 要么记录并处理
if err := doSomething(); err != nil {
    log.Printf("操作失败: %v", err)
    return nil  // 已处理，返回 nil
}

// 要么封装并返回
if err := doSomething(); err != nil {
    return fmt.Errorf("执行操作: %w", err)
}
```

<!-- -Repository层：只返回错误，不记录日志。Service层：处理业务逻辑错误，可以记录包含关键业务信息的 Info 或 Warn 级别日志，然后将错误包装后返回。Controller层：接收Service层返回的错误，调用统一的响应函数返回给前端，并在此处记录 Error 级别的日志，包含 request_id 等上下文信息。 -->

### 5. 处理类型断言失败

❌ **错误做法**
```go
t := i.(string)  // 会 panic
```

✅ **正确做法**
```go
t, ok := i.(string)
if !ok {
    return fmt.Errorf("类型断言失败")
}
```

### 6. 不要使用 panic

❌ **错误做法**
```go
func run(args []string) {
    if len(args) == 0 {
        panic("需要参数")
    }
}
```

✅ **正确做法**
```go
func run(args []string) error {
    if len(args) == 0 {
        return errors.New("需要参数")
    }
    return nil
}

func main() {
    if err := run(os.Args[1:]); err != nil {
        log.Fatal(err)
    }
}
```

## 并发编程

### 1. Channel 大小

Channel 应该是无缓冲的（大小为 0）或大小为 1。

❌ **错误做法**
```go
c := make(chan int, 64)  // 任意大小需要仔细考虑
```

✅ **正确做法**
```go
// 无缓冲
c := make(chan int)

// 或者大小为 1
c := make(chan int, 1)
```

### 2. 避免可变全局变量

❌ **错误做法**
```go
var timeNow = time.Now

func sign(msg string) string {
    now := timeNow()
    return signWithTime(msg, now)
}
```

✅ **正确做法**
```go
type Signer struct {
    now func() time.Time
}

func NewSigner() *Signer {
    return &Signer{
        now: time.Now,
    }
}

func (s *Signer) Sign(msg string) string {
    now := s.now()
    return signWithTime(msg, now)
}
```

### 3. 使用标准库原子操作

在 Go 1.19+ 中，使用标准库的类型安全原子操作：

```go
type Counter struct {
    value atomic.Int64
}

func (c *Counter) Add(delta int64) {
    c.value.Add(delta)
}

func (c *Counter) Value() int64 {
    return c.value.Load()
}
```

## 性能优化

### 1. 优先使用 strconv 而不是 fmt

❌ **较慢**
```go
s := fmt.Sprint(123)
```

✅ **更快**
```go
s := strconv.Itoa(123)
```

### 2. 避免重复的字符串到字节转换

❌ **错误做法**
```go
for i := 0; i < n; i++ {
    w.Write([]byte("Hello World"))
}
```

✅ **正确做法**
```go
data := []byte("Hello World")
for i := 0; i < n; i++ {
    w.Write(data)
}
```

### 3. 指定容器容量

```go
// 指定切片容量
data := make([]int, 0, expectedSize)

// 指定映射容量
m := make(map[string]int, expectedSize)
```

## 代码风格

### 1. 相似声明分组

```go
import (
    "fmt"
    "os"
    
    "github.com/pkg/errors"
    "go.uber.org/zap"
)

const (
    MaxRetries = 3
    Timeout    = 30 * time.Second
)

var (
    ErrNotFound = errors.New("未找到")
    ErrTimeout  = errors.New("超时")
)
```

### 2. 包命名

- 全部小写，无下划线
- 简短且有意义
- 不使用复数
- 避免使用 `common`、`util`、`shared`

### 3. 函数分组与顺序

```go
type User struct {
    id   int
    name string
}

// 构造函数紧跟类型定义
func NewUser(id int, name string) *User {
    return &User{id: id, name: name}
}

// 方法按接收器分组
func (u *User) ID() int {
    return u.id
}

func (u *User) Name() string {
    return u.name
}

// 工具函数放在文件末尾
func validateUserInput(input string) error {
    // ...
}
```

### 4. 减少嵌套

❌ **错误做法**
```go
for _, v := range data {
    if v.IsValid() {
        if v.Process() {
            v.Save()
        } else {
            return err
        }
    } else {
        log.Printf("无效数据: %v", v)
    }
}
```

✅ **正确做法**
```go
for _, v := range data {
    if !v.IsValid() {
        log.Printf("无效数据: %v", v)
        continue
    }
    
    if !v.Process() {
        return err
    }
    
    v.Save()
}
```

### 5. 变量声明

```go
// 简短声明
s := "hello"

// 零值清晰时使用 var
var users []User

// 指定类型（当类型不匹配时）
var timeout time.Duration = 30 * time.Second
```

### 6. nil 是有效的 slice

```go
// 返回 nil 而不是空切片
if len(users) == 0 {
    return nil
}

// 检查长度而不是 nil
if len(users) == 0 {
    // 处理空切片
}
```

## 结构体和接口

### 1. 避免在公共结构中嵌入类型

❌ **错误做法**
```go
type User struct {
    sync.Mutex  // 暴露了内部实现
    name string
}
```

✅ **正确做法**
```go
type User struct {
    mu   sync.Mutex
    name string
}
```

### 2. 使用字段名初始化结构体

❌ **错误做法**
```go
user := User{"Alice", 25, true}
```

✅ **正确做法**
```go
user := User{
    Name: "Alice",
    Age:  25,
    Active: true,
}
```

### 3. 初始化结构体引用

✅ **正确做法**
```go
// 保持一致性
user := User{Name: "Alice"}
userPtr := &User{Name: "Bob"}
```

## 测试规范

### 1. 表驱动测试

```go
func TestSplitHostPort(t *testing.T) {
    tests := []struct {
        name     string
        give     string
        wantHost string
        wantPort string
        wantErr  bool
    }{
        {
            name:     "标准地址",
            give:     "***********:8080",
            wantHost: "***********",
            wantPort: "8080",
        },
        {
            name:     "IPv6地址",
            give:     "[::1]:8080",
            wantHost: "::1",
            wantPort: "8080",
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            host, port, err := net.SplitHostPort(tt.give)
            
            if tt.wantErr {
                assert.Error(t, err)
                return
            }
            
            assert.NoError(t, err)
            assert.Equal(t, tt.wantHost, host)
            assert.Equal(t, tt.wantPort, port)
        })
    }
}
```

### 2. 测试文件命名

- 测试文件以 `_test.go` 结尾
- 测试函数以 `Test` 开头
- 基准测试以 `Benchmark` 开头
- 示例函数以 `Example` 开头

## 特定于项目的约定

### 1. API 响应格式

```go
type Response struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

type PaginatedResponse struct {
    Response
    Total int `json:"total,omitempty"`
    Page  int `json:"page,omitempty"`
    Size  int `json:"size,omitempty"`
}
```

### 2. 日志记录

```go
// 使用结构化日志
logger.Info("用户创建成功",
    zap.String("user_id", userID),
    zap.String("email", user.Email),
    zap.Duration("duration", time.Since(start)),
)

// 错误日志包含上下文
logger.Error("数据库查询失败",
    zap.Error(err),
    zap.String("query", query),
    zap.Any("params", params),
)
```

### 3. 配置管理

```go
type Config struct {
    Server ServerConfig `yaml:"server"`
    DB     DBConfig     `yaml:"database"`
    Redis  RedisConfig  `yaml:"redis"`
}

type ServerConfig struct {
    Host     string        `yaml:"host"`
    Port     int           `yaml:"port"`
    Timeout  time.Duration `yaml:"timeout"`
}
```

---

## 📝 总结

这份规范基于 Uber Go Style Guide 并结合实际项目需求制定。重点关注：

1. **代码可读性** - 代码是给人读的
2. **错误处理** - 明确的错误处理策略
3. **并发安全** - 正确使用 Go 的并发特性
4. **性能考虑** - 避免常见的性能陷阱
5. **一致性** - 团队统一的编码风格

记住：好的代码不仅要能运行，还要易于理解、维护和扩展。
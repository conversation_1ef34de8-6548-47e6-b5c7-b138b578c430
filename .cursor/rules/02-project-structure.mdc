---
description: 项目目录结构说明
globs: 
alwaysApply: true
---

# 📁 项目目录结构

## 项目整体结构

本项目采用前后端分离架构，根目录下包含以下主要目录：

```
fnbdb-mini/
├── front/          # 前端工程目录
├── backend/        # 后端工程目录
├── .cursor/        # Cursor 编辑器配置
├── .git/           # Git 版本控制
└── 项目文档文件...
```

## 📱 前端工程目录 (front/)

**前端技术栈**：基于 uni-app CLI 框架的跨平台应用，使用 Vue 3 + TypeScript 开发

**主要结构**：
```
front/
├── src/
│   ├── pages/           # 页面组件
│   ├── components/      # 通用组件
│   ├── stores/          # Pinia 状态管理
│   ├── utils/           # 工具函数
│   ├── styles/          # 样式文件
│   ├── api/             # API 接口
│   ├── constants/       # 常量定义
│   └── types/           # TypeScript 类型定义
├── static/              # 静态资源
├── package.json         # 依赖管理
├── pages.json           # 页面配置
├── uni.scss             # 全局样式
└── vite.config.ts       # 构建配置
```

**关键文件**：
- 入口文件：`front/src/main.ts`
- 应用组件：`front/src/App.vue`
- 页面配置：`front/src/pages.json`

## 🚀 后端工程目录 (backend/)

**后端技术栈**：待定（根据具体需求选择合适的技术栈）

**预期结构**：
```
backend/
├── src/                 # 源代码目录
├── config/              # 配置文件
├── docs/                # 后端文档
├── package.json         # 依赖管理
└── 其他配置文件...
```

## 🔧 开发工作流

### 前端开发任务
执行前端相关任务时，请：
1. 切换到 `front/` 目录
2. 使用前端项目的配置和依赖
3. 遵循前端编码规范和组件设计原则

**示例命令**：
```bash
cd front/
pnpm install
pnpm run dev:mp-weixin
```

### 后端开发任务
执行后端相关任务时，请：
1. 切换到 `backend/` 目录
2. 使用后端项目的配置和依赖
3. 遵循后端 API 设计和数据库规范

**示例命令**：
```bash
cd backend/
# 根据具体技术栈执行相应命令
```

## 📋 开发规范

### 任务执行原则
1. **明确目标目录**：每次执行任务前，明确是前端还是后端任务
2. **切换工作目录**：使用正确的工作目录进行开发
3. **保持结构清晰**：前后端代码严格分离，避免混合
4. **统一代码规范**：前后端分别遵循各自的编码规范

### 文件路径引用
- 前端文件路径：`front/src/...`
- 后端文件路径：`backend/src/...`
- 共享文档：项目根目录

### 依赖管理
- 前端依赖：`front/package.json`
- 后端依赖：`backend/package.json`
- 分别独立管理，避免依赖冲突

## 🎯 注意事项

1. **工作目录切换**：执行任务时请确保在正确的子目录中
2. **文件路径**：引用文件时请使用完整的相对路径
3. **配置文件**：前后端各自维护独立的配置文件
4. **版本控制**：Git 仓库在根目录，统一管理版本控制

**记住**：前端专注于用户界面和交互体验，后端专注于数据处理和业务逻辑，保持职责分离！

##  指导原则 (Guiding Principles)

1.  **资源导向 (Resource-Oriented)**: API 的核心是名词（资源），而不是动词（操作）。
2.  **方法明确 (Explicit Methods)**: 采用RESTful 风格API，严格使用 HTTP 方法(`GET`, `POST`, `PUT`, `PATCH`, `DELETE`).
3.  **格式统一 (Unified Case)**: 查询参数及 JSON 数据体 **全局统一使用 `snake_case` (下划线命名法)**。
4.  **版本化 (Versioning)**: 所有 API 都应通过路径进行版本控制，例如 `/api/v1/...`，为未来演进做准备。

##  URL 结构规范

### 资源命名

- **必须** 使用复数名词，以表示资源集合。
# ✅ 正确示例 (RESTful + kebab-case)
- GET    /api/users          # 获取用户列表
- GET    /api/users/{id}     # 获取单个用户
- POST   /api/users          # 创建用户
- PUT    /api/users/{id}     # 更新用户
- GET    /api/user-orders    # 获取用户订单（复合资源）

# ❌ 避免（反模式）
- GET /getUsers             # 冗余动词
- GET /get_user_list        # 蛇形命名
- GET /getUserOrders        # 驼峰命名

### 路径参数 (Path Parameters)

- **必须** 使用花括号 `{}` 包裹，并采用 `snake_case`。
- ✅ 正确: `/gigs/{gig_id}/comments/{comment_id}`
- ❌ 错误: `/gigs/:gigId`

### 查询参数 (Query Parameters)

- **必须** 使用 `snake_case`。
- **排序**: 使用 `sort` 参数，多个字段用逗号分隔，降序在字段前加 `-`。
  - `GET /gigs?sort=-created_at,title` (按创建时间降序，然后按标题升序)
- **筛选**: 直接使用参数名进行筛选。
  - `GET /gigs?status=active&category_id=3`
- **分页**:
  - `GET /gigs?page=1&page_size=20`
- **字段选择 (Sparse Fieldsets)**: 允许客户端指定返回哪些字段，以减少带宽。
  - `GET /gigs?fields=id,title,salary_min,salary_max`

## 3. JSON Body 数据规范

- **键名 (Keys)**: **必须** 使用 `snake_case`。

### 标准响应结构

- 在pkg/response/response.go中定义

## 4. 前端 TypeScript 类型命名规范

1.  **接口/类型 (Interfaces/Types)**: **必须** 使用 `PascalCase`，并可附带描述性后缀。
    - `Gig`: 代表一个零工的核心对象。
    - `GigCreateRequest`: 创建零工的请求体类型。
    - `GigUpdateRequest`: 更新零工的请求体类型。
    - `PaginatedGigsResponse`: 分页返回零工列表的响应类型。

2.  **变量/属性 (Variables/Properties)**: **必须** 使用 `camelCase`。
    - **说明**: 前后端数据格式不一致是行业惯例。前端 JS/TS 生态普遍使用 `camelCase`。我们应在网络请求层（例如 `alova` 或 `axios` 的拦截器）中，自动完成 `snake_case` (API) ⇔ `camelCase` (前端应用) 的转换，业务代码中只使用 `camelCase`。
    - ✅ 正确: `const gigTitle = gig.jobTitle;`
    - ❌ 错误: `const gig_title = gig.job_title;` (在业务代码中)

3.  **枚举 (Enums)**: **必须** 使用 `PascalCase` 定义枚举名，成员使用 `PascalCase`。
    ```typescript
    export enum GigStatus {
      Active = 'active',
      Paused = 'paused',
      Closed = 'closed',
    }
    ```
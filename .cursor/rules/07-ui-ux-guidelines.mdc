---
description: 
globs: 
alwaysApply: true
---
# 🎨 UI/UX设计与开发规范

本项目遵循现代化设计原则，追求简洁、优雅、用户友好的界面体验。每一个组件和页面都应当体现专业水准的设计品质。

## 📱 设计核心理念 (Dribbble Style)

### ✨ 极简主义 (Minimalism)
- **减少视觉噪音**：每个元素都有明确目的，移除多余装饰
- **专注内容核心**：突出主要信息，弱化次要信息
- **留白艺术**：合理使用空白空间，让界面呼吸感更强
- **信息层次**：通过字体大小、颜色、位置建立清晰的视觉层次

### 🎯 卡片设计 (Card-Based Design)
- **现代扁平化**：避免过度装饰，使用简洁的卡片容器
- **统一圆角**：标准圆角值 16-24rpx，营造柔和现代感
- **微妙阴影**：使用轻量级阴影增加层次，避免厚重感
- **内容分组**：相关信息组织在同一卡片内，提升可读性

### 🌊 微交互 (Micro-interactions)
- **流畅动画**：使用 `cubic-bezier(0.25, 0.46, 0.45, 0.94)` 等自然缓动
- **即时反馈**：按压、悬停状态要有明显但不突兀的反馈
- **状态转换**：平滑的状态变化，避免突兀的跳转
- **加载体验**：优雅的加载状态和骨架屏设计

### 📐 信息架构 (Information Architecture)
- **层次清晰**：主要信息 → 次要信息 → 辅助信息
- **扫视模式**：支持用户快速扫视获取关键信息
- **认知负荷**：减少用户思考成本，直观易懂
- **一致性**：相同类型信息使用统一的呈现方式

### 🎨 色彩克制 (Restrained Color Palette)
- **有限色彩**：主色调 + 1-2个辅助色 + 中性色系
- **品牌一致**：严格遵循品牌色彩规范
- **语义化色彩**：成功(绿)、警告(橙)、错误(红)、信息(蓝)
- **渐变运用**：适度使用渐变营造现代感，避免过度炫技

## 🚀 布局规范

### Flex布局优先
```html
<!-- 推荐的布局模式 -->
<view class="flex-x-between items-center p-4">
  <view class="flex-x items-center gap-3">
    <image class="avatar" />
    <text class="name">用户名</text>
  </view>
  <button class="action-btn">操作</button>
</view>
```

**预定义工具类：**
- `flex-x`: 水平排列
- `flex-y`: 垂直排列  
- `flex-x-center`: 水平排列并垂直居中
- `flex-x-between`: 水平排列、两端对齐、垂直居中
- `flex-y-center`: 垂直排列并水平居中

- 更多配置的css属性在[app.css](mdc:front/src/styles/app.css)中

💡 **记住**: 每一个像素都有意义，每一个交互都应当自然流畅。我们追求的不仅是功能实现，更是用户体验的艺术。

# 本地宝 - 项目设计文档

## 📋 项目摘要

### 项目概述
本地宝是一个专为地级市（3、4级）城市、县城用户以及普工群体设计的本地生活服务平台。项目采用前后端分离架构，前端基于 uni-app 开发跨平台小程序，后端基于 Go 语言构建高性能 API 服务。

### 目标用户
- **主要用户**: 地级市、县城居民
- **核心群体**: 普工、蓝领工作者
- **扩展用户**: 小微企业、个体经营者

### 核心价值
- **本地化服务** - 深度服务本地用户需求
- **便捷高效** - 简化求职、租房、社交流程
- **安全可靠** - 实名认证、企业认证保障
- **成本优化** - 降低用户生活成本

## 🏗️ 系统架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用层     │    │   后端服务层     │    │   数据存储层     │
│                 │    │                 │    │                 │
│ • 微信小程序     │◄──►│ • API 服务      │◄──►│ • PostgreSQL    │
│ • H5 应用       │    │ • 实时通信       │    │ • Redis         │
│ • 其他小程序     │    │ • 文件存储       │    │ • 云存储        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   第三方服务     │
                       │                 │
                       │ • 微信生态       │
                       │ • 短信服务       │
                       │ • 支付系统       │
                       │ • 云存储         │
                       └─────────────────┘
```

### 技术架构特点
- **微服务设计** - 模块化、可扩展的架构
- **前后端分离** - 独立开发、部署和维护
- **多端支持** - 一套代码，多端运行
- **实时通信** - WebSocket 支持即时消息
- **缓存策略** - Redis 提升系统性能
- **安全机制** - JWT 认证、数据加密

## 🎯 核心模块设计

### 1. 用户认证模块

#### 功能概述
提供完整的用户身份认证和授权体系，支持多种登录方式和安全机制。

#### 核心功能点
- **微信登录** - 基于微信生态的快速登录
- **手机号登录** - 短信验证码登录
- **实名认证** - 身份证实名验证
- **企业认证** - 企业资质认证
- **设备管理** - 多设备登录控制
- **隐私保护** - 用户隐私设置

#### 技术实现
```typescript
// 前端认证流程
interface AuthFlow {
  // 微信登录
  wechatLogin(): Promise<LoginResult>
  
  // 手机号登录
  phoneLogin(phone: string, code: string): Promise<LoginResult>
  
  // 实名认证
  realNameAuth(idCard: string, realName: string): Promise<AuthResult>
  
  // 企业认证
  enterpriseAuth(enterpriseInfo: EnterpriseInfo): Promise<AuthResult>
}
```

#### 安全机制
- JWT Token 认证
- 设备 ID 绑定
- 登录状态管理
- 敏感数据加密

### 2. 求职招聘模块

#### 功能概述
连接求职者与企业，提供完整的招聘求职服务，包括职位发布、简历管理、人才匹配等。

#### 核心功能点
- **职位发布** - 企业发布招聘信息
- **简历管理** - 求职者简历创建和维护
- **职位搜索** - 多维度职位筛选
- **人才匹配** - 智能推荐匹配
- **企业认证** - 企业资质验证
- **数据分析** - 招聘效果分析

#### 数据结构

- 数据表结构sql在backend/migrations/schema.sql，这是最终的sql

#### 前端组件
- `JobItem.vue` - 职位卡片组件
- `JobFilter.vue` - 职位筛选组件
- `ResumeEditor.vue` - 简历编辑组件
- `JobDetail.vue` - 职位详情组件

### 3. 零工广场模块

#### 功能概述
为临时工作需求提供发布和接单平台，支持灵活的工作时间和结算方式。

#### 核心功能点
- **零工发布** - 发布临时工作需求
- **零工接单** - 工作者接单申请
- **任务管理** - 任务进度跟踪
- **结算系统** - 灵活结算方式
- **评价系统** - 双向评价机制
- **安全保障** - 工作安全保障

#### 业务逻辑
```go
// 零工服务接口
type GigService interface {
    // 发布零工
    CreateGig(ctx context.Context, req *CreateGigRequest) (*Gig, error)
    
    // 获取零工列表
    ListGigs(ctx context.Context, req *ListGigsRequest) (*PaginatedGigsResponse, error)
    
    // 申请接单
    ApplyGig(ctx context.Context, req *ApplyGigRequest) error
    
    // 确认接单
    ConfirmApplication(ctx context.Context, req *ConfirmApplicationRequest) error
    
    // 完成任务
    CompleteGig(ctx context.Context, req *CompleteGigRequest) error
}
```

#### 状态流转
```
发布零工 → 等待接单 → 接单申请 → 确认接单 → 进行中 → 完成任务 → 结算
    ↓         ↓         ↓         ↓         ↓         ↓         ↓
  草稿状态   活跃状态   待确认    已确认    进行中    已完成    已结算
```

### 4. 房产服务模块

#### 功能概述
提供租房、二手房、新房等房产信息服务，支持房源发布、搜索、管理等功能。

#### 核心功能点
- **房源发布** - 房东发布房源信息
- **房源搜索** - 多维度房源筛选
- **房源管理** - 房东房源管理
- **预约看房** - 在线预约看房
- **合同管理** - 电子合同签署
- **交易服务** - 房产交易服务

#### 房源类型
- **租房** - 短期、长期租赁
- **二手房** - 住宅、商铺、写字楼
- **新房** - 期房、现房
- **商铺** - 商业地产

#### 前端组件
- `HouseCard.vue` - 房源卡片组件
- `HouseFilter.vue` - 房源筛选组件
- `HouseDetail.vue` - 房源详情组件
- `PricePublisher.vue` - 价格发布组件

### 5. 同城交友模块

#### 功能概述
基于地理位置的社交功能，帮助用户建立本地社交圈，支持动态发布、用户匹配等。

#### 核心功能点
- **用户匹配** - 基于兴趣和位置的匹配
- **动态发布** - 用户发布生活动态
- **互动功能** - 点赞、评论、分享
- **私信聊天** - 用户间私信交流
- **话题讨论** - 热门话题参与
- **隐私保护** - 社交隐私设置

#### 匹配算法
```typescript
interface MatchAlgorithm {
  // 基于地理位置的匹配
  locationBasedMatch(userId: string, radius: number): Promise<User[]>
  
  // 基于兴趣的匹配
  interestBasedMatch(userId: string, interests: string[]): Promise<User[]>
  
  // 综合匹配评分
  calculateMatchScore(user1: User, user2: User): number
}
```

### 6. 即时通讯模块

#### 功能概述
提供实时聊天功能，支持一对一聊天、群聊、消息推送等。

#### 核心功能点
- **一对一聊天** - 用户间私聊
- **群组聊天** - 多人聊天群
- **消息推送** - 实时消息通知
- **消息管理** - 消息历史、删除
- **文件传输** - 图片、文件发送
- **在线状态** - 用户在线状态

#### 技术实现
- **WebSocket** - 实时通信
- **Centrifugo** - 消息推送服务
- **消息队列** - 消息持久化
- **离线消息** - 消息缓存机制

## 🛠️ 前端架构详解

### 目录结构说明

#### `src/constants/` - 常量定义目录
这是项目中非常重要的目录，包含所有业务常量和标准化数据定义。

**核心文件说明：**
- `standards.ts` - **标准化常量定义**
  - 性别选项、年龄范围、教育程度、工作经验等
  - 薪资范围、公司规模、福利待遇等
  - 所有选择器数据的标准化定义

- `common.ts` - **通用业务常量**
  - 工作类型、紧急程度、零工分类
  - 表单验证规则、图片上传配置
  - API 状态码、缓存键名等

- `area.ts` - **地区数据**
  - 省市区三级联动数据
  - 地区编码和名称映射
  - 热门城市列表

- `gig.ts` - **零工相关常量**
  - 零工分类、薪资范围、结算方式
  - 零工状态、技能标签等

- `house.ts` - **房产相关常量**
  - 房源类型、房屋朝向、装修程度
  - 租金范围、房屋面积等

**使用建议：**
```typescript
// ✅ 正确使用方式
import { GENDER_OPTIONS, EDUCATION_OPTIONS } from '@/constants/standards'
import { jobCategories, workTypes } from '@/constants/common'

// ❌ 避免重复定义
const genderOptions = [
  { label: '男', value: 'male' },
  { label: '女', value: 'female' }
] // 已在 constants 中定义，不要重复创建
```

#### `src/utils/` - 工具函数目录
提供丰富的工具函数库，避免重复造轮子。

**核心工具文件：**

- `date.ts` - **日期处理工具**
  ```typescript
  // 日期格式化
  formatDate('Y-m-d', '2024-01-01')
  
  // 获取时间段
  getDateTimeSlot(1) // 今天
  getDateTimeSlot(3) // 本周
  
  // 判断日期
  isToday(date)
  isThisWeek(date)
  ```

- `format.ts` - **格式化工具**
  ```typescript
  // 金额格式化
  formatMoney(1234.56) // "¥1,234.56"
  
  // 手机号格式化
  formatPhone('13812345678') // "138****5678"
  
  // 身份证格式化
  formatIdCard('123456789012345678') // "123456****5678"
  ```

- `form-validator.ts` - **表单验证工具**
  ```typescript
  // 手机号验证
  validatePhone('13812345678') // true
  
  // 邮箱验证
  validateEmail('<EMAIL>') // true
  
  // 身份证验证
  validateIdCard('123456789012345678') // true
  ```

- `string.ts` - **字符串工具**
  ```typescript
  // 生成唯一ID
  generateUUID() // "uuid-string"
  
  // 驼峰转换
  camelCase('user_name') // "userName"
  
  // 字符串截断
  truncate('长文本内容', 10) // "长文本内容..."
  ```

- `ui.ts` - **UI 交互工具**
  ```typescript
  // 显示提示
  showToast('操作成功')
  
  // 显示加载
  showLoading('加载中...')
  
  // 显示确认弹窗
  showModal('确认删除？')
  ```

**使用建议：**
```typescript
// ✅ 使用现有工具函数
import { formatMoney, validatePhone, showToast } from '@/utils'

// ❌ 避免重复实现
const formatMoney = (amount: number) => {
  return `¥${amount.toFixed(2)}`
} // 已有现成工具，不要重复实现
```

#### `src/components/` - 组件目录
高度模块化的组件设计，按业务模块组织。

**通用组件 (`common/`)：**
- `Card.vue` - 卡片容器组件
- `EmptyResult.vue` - 空状态组件
- `FormInput.vue` - 表单输入组件
- `Tag.vue` - 标签组件

**业务组件：**
- `job/` - 招聘相关组件
- `gig/` - 零工相关组件
- `house/` - 房产相关组件
- `dating/` - 交友相关组件

**使用建议：**
```vue
<!-- ✅ 使用现有组件 -->
<Card>
  <Tag type="primary">热门</Tag>
  <EmptyResult v-if="!data.length" />
</Card>

<!-- ❌ 避免重复创建相似组件 -->
<view class="custom-card">
  <!-- 已有 Card 组件，不要重复创建 -->
</view>
```

#### `src/stores/` - 状态管理
基于 Pinia 的响应式状态管理。

**核心 Store：**
- `global.ts` - 全局状态（应用配置、隐私设置等）
- `user.ts` - 用户状态（用户信息、登录状态等）
- `job.ts` - 招聘状态（职位数据、筛选条件等）

**使用建议：**
```typescript
// ✅ 使用 Store 管理状态
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
userStore.setUserInfo(userInfo)

// ❌ 避免使用全局变量
window.userInfo = userInfo // 不要这样做
```

### 样式规范

#### 设计原则
- **极简主义** - 减少视觉噪音，突出核心内容
- **卡片设计** - 现代扁平化卡片容器
- **微交互** - 流畅的动画和即时反馈
- **信息架构** - 清晰的层次结构

#### CSS 类名规范
```css
/* 布局类 */
.flex-x          /* 水平排列 */
.flex-y          /* 垂直排列 */
.flex-x-center   /* 水平居中 */
.flex-x-between  /* 两端对齐 */

/* 间距类 */
.p-4             /* 内边距 16rpx */
.m-4             /* 外边距 16rpx */
.gap-3           /* 元素间距 12rpx */

/* 圆角类 */
.rounded-2       /* 圆角 8rpx */
.rounded-3       /* 圆角 12rpx */
.rounded-full    /* 圆形 */
```

## 🔧 后端架构详解

### 分层架构设计

#### Controller 层
负责 HTTP 请求处理和响应，进行参数验证和权限检查。

```go
// 控制器基类
type BaseController struct {
    logger *zerolog.Logger
}

// 零工控制器示例
type GigController struct {
    BaseController
    gigService service.GigService
}

func (c *GigController) CreateGig(ctx *gin.Context) {
    // 1. 参数验证
    var req types.CreateGigRequest
    if err := validator.CheckBody(ctx, &req); err != nil {
        response.Fail(ctx, http.StatusBadRequest, err.Error())
        return
    }
    
    // 2. 权限检查
    userID := middleware.GetUserID(ctx)
    
    // 3. 业务处理
    gig, err := c.gigService.CreateGig(ctx, &req)
    if err != nil {
        response.Fail(ctx, http.StatusInternalServerError, err.Error())
        return
    }
    
    // 4. 响应结果
    response.Success(ctx, gig)
}
```

#### Service 层
负责业务逻辑处理，协调多个 Repository 完成复杂业务。

```go
type GigService interface {
    CreateGig(ctx context.Context, req *types.CreateGigRequest) (*model.Gig, error)
    ListGigs(ctx context.Context, req *types.ListGigsRequest) (*types.PaginatedGigsResponse, error)
    GetGigByID(ctx context.Context, id uint) (*model.Gig, error)
    UpdateGig(ctx context.Context, id uint, req *types.UpdateGigRequest) error
    DeleteGig(ctx context.Context, id uint) error
}

type gigService struct {
    gigRepo repository.GigRepository
    userRepo repository.UserRepository
    logger   *zerolog.Logger
}

func (s *gigService) CreateGig(ctx context.Context, req *types.CreateGigRequest) (*model.Gig, error) {
    // 1. 业务验证
    if err := s.validateGigRequest(req); err != nil {
        return nil, err
    }
    
    // 2. 数据转换
    gig := &model.Gig{
        UserID:          req.UserID,
        Title:           req.Title,
        Description:     req.Description,
        Category:        req.Category,
        SalaryMin:       req.SalaryMin,
        SalaryMax:       req.SalaryMax,
        Location:        req.Location,
        WorkDate:        req.WorkDate,
        Requirements:    req.Requirements,
        Status:          constants.GigStatusActive,
    }
    
    // 3. 数据持久化
    if err := s.gigRepo.Create(gig); err != nil {
        s.logger.Error().Err(err).Msg("Failed to create gig")
        return nil, err
    }
    
    // 4. 业务事件处理
    s.handleGigCreated(gig)
    
    return gig, nil
}
```

#### Repository 层
负责数据访问，封装数据库操作。

```go
type GigRepository interface {
    Create(gig *model.Gig) error
    FindByID(id uint) (*model.Gig, error)
    FindByUserID(userID uint, page, pageSize int) ([]*model.Gig, int64, error)
    Update(gig *model.Gig) error
    Delete(id uint) error
    FindByFilters(filters *types.GigFilters, page, pageSize int) ([]*model.Gig, int64, error)
}

type gigRepository struct {
    db *gorm.DB
}

func (r *gigRepository) FindByFilters(filters *types.GigFilters, page, pageSize int) ([]*model.Gig, int64, error) {
    var gigs []*model.Gig
    var total int64
    
    query := r.db.Model(&model.Gig{}).Where("deleted_at IS NULL")
    
    // 应用筛选条件
    if filters.Category != "" {
        query = query.Where("category = ?", filters.Category)
    }
    if filters.Location != "" {
        query = query.Where("location LIKE ?", "%"+filters.Location+"%")
    }
    if filters.SalaryMin > 0 {
        query = query.Where("salary_max >= ?", filters.SalaryMin)
    }
    if filters.SalaryMax > 0 {
        query = query.Where("salary_min <= ?", filters.SalaryMax)
    }
    
    // 获取总数
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // 分页查询
    if err := query.Offset((page - 1) * pageSize).Limit(pageSize).
        Order("created_at DESC").Find(&gigs).Error; err != nil {
        return nil, 0, err
    }
    
    return gigs, total, nil
}
```

#### Model 层
定义数据模型和数据库表结构。

```go
type Gig struct {
    BaseModel
    UserID          uint      `json:"user_id" gorm:"not null"`
    Title           string    `json:"title" gorm:"not null;size:100"`
    Description     string    `json:"description" gorm:"type:text"`
    Category        string    `json:"category" gorm:"not null;size:50"`
    SalaryMin       int       `json:"salary_min" gorm:"not null"`
    SalaryMax       int       `json:"salary_max" gorm:"not null"`
    Location        string    `json:"location" gorm:"not null;size:200"`
    WorkDate        time.Time `json:"work_date" gorm:"not null"`
    Requirements    string    `json:"requirements" gorm:"type:text"`
    Status          string    `json:"status" gorm:"not null;size:20;default:'active'"`
    ViewCount       int       `json:"view_count" gorm:"default:0"`
    ApplyCount      int       `json:"apply_count" gorm:"default:0"`
    
    // 关联关系
    User            User      `json:"user" gorm:"foreignKey:UserID"`
    Applications    []GigApplication `json:"applications" gorm:"foreignKey:GigID"`
}

type BaseModel struct {
    ID        uint           `json:"id" gorm:"primarykey"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}
```

### 核心包说明

#### `pkg/response/` - 响应处理包
统一的 API 响应格式处理。

```go
// 响应结构
type Response struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

// 分页响应
type PaginatedResponse struct {
    Response
    Total int `json:"total,omitempty"`
    Page  int `json:"page,omitempty"`
    Size  int `json:"size,omitempty"`
}

// 使用示例
func (c *GigController) ListGigs(ctx *gin.Context) {
    gigs, total, err := c.gigService.ListGigs(ctx, req)
    if err != nil {
        response.Fail(ctx, http.StatusInternalServerError, err.Error())
        return
    }
    
    response.Paginated(ctx, gigs, total, req.Page, req.PageSize)
}
```

#### `pkg/logger/` - 日志系统
基于 Zerolog 的结构化日志系统。

```go
// 日志配置
type LoggerConfig struct {
    Level  string `yaml:"level"`
    Format string `yaml:"format"`
    Output string `yaml:"output"`
}

// 使用示例
logger.Info("User login successful", "user_id", userID, "ip", clientIP)
logger.ErrorCtx(ctx, "Database operation failed", err, "table", "users")
```

#### `pkg/validator/` - 参数验证
统一的参数验证处理。

```go
// 验证请求体
func CheckBody(ctx *gin.Context, obj interface{}) error {
    if err := ctx.ShouldBindJSON(obj); err != nil {
        return err
    }
    
    if err := validator.New().Struct(obj); err != nil {
        return err
    }
    
    return nil
}

// 验证查询参数
func CheckQuery(ctx *gin.Context, obj interface{}) error {
    if err := ctx.ShouldBindQuery(obj); err != nil {
        return err
    }
    
    if err := validator.New().Struct(obj); err != nil {
        return err
    }
    
    return nil
}
```

## 🔐 安全设计

### 认证与授权
- **JWT Token** - 无状态认证机制
- **设备绑定** - 设备 ID 安全验证
- **权限控制** - 基于角色的访问控制
- **会话管理** - 登录状态管理

### 数据安全
- **数据加密** - 敏感数据加密存储
- **API 签名** - 请求签名验证
- **SQL 注入防护** - 参数化查询
- **XSS 防护** - 输入输出过滤

### 隐私保护
- **数据脱敏** - 用户信息脱敏显示
- **隐私设置** - 用户隐私控制
- **数据审计** - 数据访问记录
- **合规检查** - 法律法规遵循

## 📊 性能优化

### 数据库优化
- **索引设计** - 合理的数据库索引
- **查询优化** - 高效的 SQL 查询
- **连接池** - 数据库连接池管理
- **读写分离** - 主从数据库分离

### 缓存策略
- **Redis 缓存** - 热点数据缓存
- **本地缓存** - 应用级缓存
- **CDN 加速** - 静态资源加速
- **缓存更新** - 缓存一致性保证

### 前端优化
- **代码分割** - 按需加载
- **图片优化** - 图片压缩和懒加载
- **缓存策略** - 浏览器缓存
- **预加载** - 关键资源预加载

## 🚀 部署架构

### 开发环境
- **本地开发** - Docker Compose 本地环境
- **热重载** - Air 工具支持热重载
- **调试工具** - 完整的调试支持

### 生产环境
- **容器化部署** - Docker 容器部署
- **负载均衡** - Nginx 负载均衡
- **监控告警** - 系统监控和告警
- **日志管理** - 集中化日志管理

### CI/CD 流程
- **代码检查** - 自动化代码质量检查
- **测试验证** - 自动化测试
- **构建部署** - 自动化构建部署
- **回滚机制** - 快速回滚能力

## 📈 扩展性设计

### 水平扩展
- **微服务拆分** - 按业务模块拆分服务
- **负载均衡** - 多实例负载均衡
- **数据库分片** - 数据库水平分片
- **缓存集群** - Redis 集群部署

### 垂直扩展
- **资源优化** - CPU、内存资源优化
- **代码优化** - 算法和代码优化
- **架构优化** - 系统架构优化
- **监控优化** - 性能监控优化

## 🔮 未来规划

### 技术升级
- **微服务架构** - 逐步迁移到微服务
- **云原生** - 云原生技术栈
- **AI 集成** - 人工智能功能集成
- **区块链** - 区块链技术应用

### 功能扩展
- **支付系统** - 完整的支付体系
- **物流配送** - 物流配送服务
- **金融服务** - 金融产品服务
- **数据分析** - 大数据分析平台

---

**本地宝项目设计文档** - 为本地生活服务提供完整的技术解决方案 🏠✨ 
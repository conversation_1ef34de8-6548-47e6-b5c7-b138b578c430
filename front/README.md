# 本地宝 - 前端项目

## 📱 项目概述

本地宝是一个基于 uni-app CLI 框架的跨平台本地生活服务小程序，专为地级市（3、4级）城市、县城用户以及普工群体设计。项目采用 Vue 3 + TypeScript 开发，支持微信小程序、H5 等多端部署。

## 🎯 核心功能

### 主要模块
- **首页** - 综合信息展示，包含热门模块入口
- **社区** - 用户动态发布与互动
- **消息** - 即时通讯与通知系统
- **个人中心** - 用户资料管理与设置

### 业务模块
- **求职招聘** - 全职、兼职、实习岗位发布与求职
- **零工广场** - 临时工作发布与接单
- **房产服务** - 租房、二手房、新房信息
- **同城交友** - 社交功能与用户匹配
- **企业服务** - 企业认证与合作

## 🏗️ 技术架构

### 核心技术栈
- **框架**: Vue 3.4.21 + TypeScript 4.9.4
- **跨平台**: uni-app CLI 3.0.0
- **构建工具**: Vite 5.2.8
- **包管理**: pnpm 10.9.0
- **状态管理**: Pinia 2.1.7
- **网络请求**: alova 3.3.4
- **UI 组件库**: 
  - uni-ui 1.5.7 (官方组件库)
  - uv-ui 1.1.20 (现代化组件库)
  - ThorUI (自定义组件库)
- **样式方案**: UnoCSS 0.58.0 + SCSS
- **分页组件**: z-paging 2.8.6
- **工具库**: dayjs 1.11.10 (日期处理)

### 架构特点
- **组件化开发** - 高度模块化的组件设计
- **类型安全** - 完整的 TypeScript 类型定义
- **状态管理** - 基于 Pinia 的响应式状态管理
- **网络层** - 统一的 API 请求封装
- **工具函数** - 丰富的工具函数库
- **常量管理** - 标准化的常量定义

## 📁 项目结构

```
front/
├── src/                          # 源代码目录
│   ├── api/                      # API 接口定义
│   │   ├── auth.ts              # 认证相关接口
│   │   ├── gig.ts               # 零工相关接口
│   │   ├── user.ts              # 用户相关接口
│   │   └── index.ts             # 接口导出
│   ├── components/              # 组件目录
│   │   ├── common/              # 通用组件
│   │   │   ├── Card.vue         # 卡片组件
│   │   │   ├── EmptyResult.vue  # 空状态组件
│   │   │   ├── FormInput.vue    # 表单输入组件
│   │   │   └── Tag.vue          # 标签组件
│   │   ├── home/                # 首页组件
│   │   ├── job/                 # 招聘相关组件
│   │   ├── gig/                 # 零工相关组件
│   │   ├── house/               # 房产相关组件
│   │   ├── dating/              # 交友相关组件
│   │   ├── post/                # 社区相关组件
│   │   ├── thorui/              # ThorUI 组件库
│   │   ├── AddressSelector.vue  # 地址选择器
│   │   ├── CustomNavBar.vue     # 自定义导航栏
│   │   ├── ImageLoader.vue      # 图片加载组件
│   │   ├── NetworkImage.vue     # 网络图片组件
│   │   ├── PageLayout.vue       # 页面布局组件
│   │   ├── PostItem.vue         # 帖子项组件
│   │   └── PropertyTag.vue      # 属性标签组件
│   ├── constants/               # 常量定义
│   │   ├── static/              # 静态数据
│   │   ├── area.ts              # 地区数据
│   │   ├── common.ts            # 通用常量
│   │   ├── gig.ts               # 零工相关常量
│   │   ├── house.ts             # 房产相关常量
│   │   ├── index.ts             # 常量导出
│   │   ├── response.ts          # 响应状态常量
│   │   └── standards.ts         # 标准化常量
│   ├── pages/                   # 页面目录
│   │   ├── auth/                # 认证页面
│   │   ├── dating/              # 交友页面
│   │   ├── gig/                 # 零工页面
│   │   ├── home/                # 首页
│   │   ├── house/               # 房产页面
│   │   ├── job/                 # 招聘页面
│   │   ├── message/             # 消息页面
│   │   ├── mine/                # 个人中心
│   │   ├── post/                # 社区页面
│   │   ├── publish/             # 发布页面
│   │   └── verification/        # 认证页面
│   ├── services/                # 服务层
│   │   ├── auth.ts              # 认证服务
│   │   ├── gig.ts               # 零工服务
│   │   └── index.ts             # 服务导出
│   ├── stores/                  # 状态管理
│   │   ├── global.ts            # 全局状态
│   │   ├── user.ts              # 用户状态
│   │   ├── job.ts               # 招聘状态
│   │   └── index.ts             # 状态导出
│   ├── styles/                  # 样式文件
│   │   └── app.css              # 全局样式
│   ├── types/                   # 类型定义
│   │   ├── chatMsg.d.ts         # 聊天消息类型
│   │   ├── common.ts            # 通用类型
│   │   ├── gig.ts               # 零工类型
│   │   └── index.ts             # 类型导出
│   ├── utils/                   # 工具函数
│   │   ├── alova.ts             # 网络请求配置
│   │   ├── api-helper.ts        # API 辅助函数
│   │   ├── assets.ts            # 资源工具
│   │   ├── assetsUtil.ts        # 资源工具扩展
│   │   ├── date.ts              # 日期处理工具
│   │   ├── format.ts            # 格式化工具
│   │   ├── form-validator.ts    # 表单验证工具
│   │   ├── gig.ts               # 零工相关工具
│   │   ├── index.ts             # 通用工具函数
│   │   ├── navigation.ts        # 导航工具
│   │   ├── request.ts           # 请求工具
│   │   ├── string.ts            # 字符串工具
│   │   └── ui.ts                # UI 工具
│   ├── App.vue                  # 应用根组件
│   ├── main.ts                  # 应用入口
│   ├── pages.json               # 页面配置
│   ├── manifest.json            # 应用配置
│   ├── uni.scss                 # 全局样式
│   └── env.d.ts                 # 环境类型
├── static/                      # 静态资源
│   ├── images/                  # 图片资源
│   ├── logo.png                 # 应用图标
│   ├── svg/                     # SVG 图标
│   └── tabbar/                  # 底部导航图标
├── dist/                        # 构建输出目录
├── env/                         # 环境配置
├── docs/                        # 项目文档
├── index.html                   # HTML 模板
├── package.json                 # 依赖配置
├── pnpm-lock.yaml              # 依赖锁定文件
├── tsconfig.json               # TypeScript 配置
├── uno.config.ts               # UnoCSS 配置
├── vite.config.ts              # Vite 配置
└── shims-uni.d.ts              # uni-app 类型声明
```

## 🚀 快速开始

### 环境要求
- Node.js 18.0+
- pnpm 8.0+
- 微信开发者工具（小程序开发）

### 安装依赖
```bash
# 安装 pnpm（如果未安装）
npm install -g pnpm

# 安装项目依赖
pnpm install
```

### 开发模式
```bash
# 微信小程序开发
pnpm dev:mp-weixin

# H5 开发
pnpm dev:h5

# 支付宝小程序开发
pnpm dev:mp-alipay

# 其他平台...
```

### 构建生产版本
```bash
# 构建微信小程序
pnpm build:mp-weixin

# 构建 H5
pnpm build:h5

# 构建支付宝小程序
pnpm build:mp-alipay
```

### 类型检查
```bash
# 运行 TypeScript 类型检查
pnpm type-check
```

## 🛠️ 开发指南

### 代码规范
- 使用 TypeScript 进行类型安全的开发
- 遵循 Vue 3 Composition API 最佳实践
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case
- 使用 ESLint 进行代码检查

### 组件开发
```vue
<template>
  <view class="component-name">
    <!-- 组件内容 -->
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 定义 props
interface Props {
  title: string
  count?: number
}

const props = withDefaults(defineProps<Props>(), {
  count: 0
})

// 定义 emits
const emit = defineEmits<{
  click: [value: string]
}>()

// 组件逻辑
const handleClick = () => {
  emit('click', props.title)
}
</script>

<style lang="scss" scoped>
.component-name {
  // 组件样式
}
</style>
```

### 状态管理
```typescript
// stores/user.ts
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    isLogin: false
  }),
  
  getters: {
    userId: (state) => state.userInfo?.id
  },
  
  actions: {
    setUserInfo(userInfo: any) {
      this.userInfo = userInfo
      this.isLogin = true
    },
    
    logout() {
      this.userInfo = null
      this.isLogin = false
    }
  },
  
  persist: true
})
```

### API 请求
```typescript
// api/user.ts
import { http } from '@/utils/request'

export const getUserInfo = (userId: string) => {
  return http.Get(`/api/users/${userId}`)
}

export const updateUserInfo = (userId: string, data: any) => {
  return http.Put(`/api/users/${userId}`, data)
}
```

### 工具函数使用
```typescript
import { formatDate, debounce } from '@/utils'

// 日期格式化
const formattedDate = formatDate('Y-m-d', '2024-01-01')

// 防抖函数
const debouncedSearch = debounce((keyword: string) => {
  // 搜索逻辑
}, 500)
```

## 📦 核心工具函数

### 日期处理 (`utils/date.ts`)
- `formatDate()` - 日期格式化
- `getDateTimeSlot()` - 获取时间段
- `isToday()` - 判断是否为今天
- `getWeekRange()` - 获取周范围

### 格式化工具 (`utils/format.ts`)
- `formatMoney()` - 金额格式化
- `formatPhone()` - 手机号格式化
- `formatIdCard()` - 身份证格式化
- `formatFileSize()` - 文件大小格式化

### 表单验证 (`utils/form-validator.ts`)
- `validatePhone()` - 手机号验证
- `validateEmail()` - 邮箱验证
- `validateIdCard()` - 身份证验证
- `validatePassword()` - 密码验证

### 字符串工具 (`utils/string.ts`)
- `generateUUID()` - 生成唯一标识
- `camelCase()` - 驼峰命名转换
- `snakeCase()` - 下划线命名转换
- `truncate()` - 字符串截断

### UI 工具 (`utils/ui.ts`)
- `showToast()` - 显示提示
- `showLoading()` - 显示加载
- `showModal()` - 显示弹窗
- `navigateTo()` - 页面跳转

## 🎨 样式规范

### 设计原则
- **极简主义** - 减少视觉噪音，突出核心内容
- **卡片设计** - 使用现代扁平化卡片容器
- **微交互** - 流畅的动画和即时反馈
- **信息架构** - 清晰的层次结构
- **色彩克制** - 有限的主色调和辅助色

### CSS 类名规范
```css
/* 布局类 */
.flex-x          /* 水平排列 */
.flex-y          /* 垂直排列 */
.flex-x-center   /* 水平居中 */
.flex-x-between  /* 两端对齐 */
.flex-y-center   /* 垂直居中 */

/* 间距类 */
.p-4             /* 内边距 16rpx */
.m-4             /* 外边距 16rpx */
.gap-3           /* 元素间距 12rpx */

/* 圆角类 */
.rounded-2       /* 圆角 8rpx */
.rounded-3       /* 圆角 12rpx */
.rounded-full    /* 圆形 */
```

## 🔧 配置说明

### 页面配置 (`pages.json`)
- 支持主包和分包加载
- 自定义导航栏配置
- 页面权限控制
- 底部导航配置

### 构建配置 (`vite.config.ts`)
- 多平台构建支持
- 自动导入配置
- 路径别名配置
- 插件配置

### UnoCSS 配置 (`uno.config.ts`)
- 原子化 CSS 配置
- 自定义工具类
- 响应式设计
- 主题配置

## 📱 平台支持

### 已支持平台
- ✅ 微信小程序
- ✅ H5
- ✅ 支付宝小程序
- ✅ 百度小程序
- ✅ 字节跳动小程序
- ✅ QQ 小程序
- ✅ 快手小程序
- ✅ 小红书小程序
- ✅ 京东小程序
- ✅ 飞书小程序

### 平台特性
- **微信小程序**: 支持 Skyline 渲染引擎
- **H5**: 支持 SSR 服务端渲染
- **多端**: 一套代码，多端运行

## 🚀 部署指南

### 微信小程序部署
1. 构建项目：`pnpm build:mp-weixin`
2. 打开微信开发者工具
3. 导入 `dist/build/mp-weixin` 目录
4. 上传代码到微信后台
5. 提交审核并发布

### H5 部署
1. 构建项目：`pnpm build:h5`
2. 将 `dist/build/h5` 目录部署到 Web 服务器
3. 配置域名和 HTTPS

## 📋 开发规范

### TypeScript 类型命名规范

#### 接口/类型命名
- **必须** 使用 `PascalCase`，并可附带描述性后缀
- **示例**：
  ```typescript
  // ✅ 正确
  interface User { }
  interface UserCreateRequest { }
  interface UserUpdateRequest { }
  interface PaginatedUsersResponse { }
  
  // ❌ 错误
  interface user { }
  interface user_create_request { }
  ```

#### 变量/属性命名
- **必须** 使用 `camelCase`
- **说明**：前后端数据格式不一致是行业惯例，前端 JS/TS 生态普遍使用 `camelCase`
- **示例**：
  ```typescript
  // ✅ 正确
  const userName = user.nickName
  const jobTitle = job.jobTitle
  
  // ❌ 错误
  const user_name = user.nick_name
  ```

#### 枚举命名
- **必须** 使用 `PascalCase` 定义枚举名，成员使用 `PascalCase`
- **示例**：
  ```typescript
  export enum GigStatus {
    Active = 'active',
    Paused = 'paused',
    Closed = 'closed',
  }
  ```

### API 请求规范

#### 分页请求参数
- **统一使用** `page` 和 `pageSize` 参数
- **示例**：
  ```typescript
  // ✅ 正确
  const params = {
    page: 1,
    pageSize: 20,
    category: '搬运装卸'
  }
  
  // ❌ 错误
  const params = {
    current: 1,
    size: 20
  }
  ```

#### 分页响应格式
- **后端返回格式**：
  ```typescript
  interface PaginatedResponse<T> {
    code: number
    message: string
    data: {
      list: T[]
      total: number
      page: number
      page_size: number
    }
  }
  ```

#### URL 参数规范
- **查询参数**：使用 `snake_case`
- **路径参数**：使用 `snake_case`
- **示例**：
  ```typescript
  // ✅ 正确
  GET /api/v1/gigs?category_id=1&salary_min=100&page=1&page_size=20
  GET /api/v1/gigs/{gig_id}/applications/{application_id}
  
  // ❌ 错误
  GET /api/v1/gigs?categoryId=1&salaryMin=100
  GET /api/v1/gigs/{gigId}/applications/{applicationId}
  ```

### 全局工具使用规范

#### Toast 提示
```typescript
import { showToast } from '@/utils/ui'

// 成功提示
showToast('操作成功', 'success')

// 错误提示
showToast('操作失败', 'error')

// 警告提示
showToast('请注意', 'warning')

// 信息提示
showToast('提示信息', 'info')
```

#### Loading 加载
```typescript
import { showLoading, hideLoading } from '@/utils/ui'

// 显示加载
showLoading('加载中...')

// 隐藏加载
hideLoading()

// 自动隐藏（推荐）
showLoading('提交中...')
try {
  await submitData()
  showToast('提交成功')
} finally {
  hideLoading()
}
```

#### Modal 弹窗
```typescript
import { showModal } from '@/utils/ui'

// 确认弹窗
showModal('确认删除？', '提示').then(() => {
  // 用户确认
  deleteItem()
}).catch(() => {
  // 用户取消
  console.log('用户取消')
})

// 自定义弹窗
showModal('自定义内容', '标题', {
  confirmText: '确定',
  cancelText: '取消'
})
```

### 函数优化规范

#### 防抖函数 (Debounce)
```typescript
import { debounce } from '@/utils'

// 搜索防抖
const debouncedSearch = debounce((keyword: string) => {
  searchGigs(keyword)
}, 500)

// 输入框搜索
const handleInput = (e: any) => {
  debouncedSearch(e.detail.value)
}
```

#### 节流函数 (Throttle)
```typescript
import { throttle } from '@/utils'

// 滚动节流
const throttledScroll = throttle(() => {
  checkScrollPosition()
}, 200)

// 页面滚动
onPageScroll(() => {
  throttledScroll()
})
```

#### 使用场景
- **防抖**：搜索输入、窗口调整、表单提交
- **节流**：滚动事件、按钮点击、鼠标移动

### 组件开发规范

#### Props 定义
```typescript
// ✅ 正确
interface Props {
  title: string
  count?: number
  onConfirm?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  count: 0
})
```

#### Emits 定义
```typescript
// ✅ 正确
const emit = defineEmits<{
  click: [value: string]
  confirm: [data: any]
  cancel: []
}>()
```

#### 组件命名
```typescript
// ✅ 正确 - 使用 PascalCase
export default defineComponent({
  name: 'GigCard'
})

// ❌ 错误
export default defineComponent({
  name: 'gig-card'
})
```

### 状态管理规范

#### Store 命名
```typescript
// ✅ 正确
export const useUserStore = defineStore('user', { })
export const useGigStore = defineStore('gig', { })

// ❌ 错误
export const useUser = defineStore('user', { })
```

#### 持久化配置
```typescript
// ✅ 正确
export const useUserStore = defineStore('user', {
  state: () => ({ }),
  persist: {
    key: 'user-store',
    storage: uni.getStorageSync
  }
})
```

### 错误处理规范

#### API 错误处理
```typescript
// ✅ 正确
try {
  const result = await getUserInfo(userId)
  // 处理成功结果
} catch (error) {
  showToast('获取用户信息失败', 'error')
  console.error('API Error:', error)
}
```

#### 表单验证
```typescript
import { validatePhone, validateEmail } from '@/utils/form-validator'

// ✅ 正确
if (!validatePhone(phone)) {
  showToast('手机号格式不正确', 'error')
  return
}
```

### 性能优化规范

#### 图片懒加载
```vue
<template>
  <!-- ✅ 正确 -->
  <NetworkImage 
    :src="imageUrl" 
    :lazy="true"
    placeholder="/static/placeholder.png"
  />
</template>
```

#### 列表优化
```vue
<template>
  <!-- ✅ 正确 - 使用 z-paging -->
  <z-paging 
    ref="paging" 
    v-model="dataList" 
    @query="queryList"
    :auto="true"
  >
    <view v-for="item in dataList" :key="item.id">
      <GigItem :data="item" />
    </view>
  </z-paging>
</template>
```

#### 条件渲染
```vue
<template>
  <!-- ✅ 正确 -->
  <view v-if="dataList.length > 0">
    <GigList :data="dataList" />
  </view>
  <EmptyResult v-else />
</template>
```

### 代码质量规范

#### 导入规范
```typescript
// ✅ 正确 - 按类型分组
// 第三方库
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

// 项目内部
import { showToast } from '@/utils/ui'
import { useUserStore } from '@/stores/user'
import type { Gig } from '@/types/gig'
```

#### 注释规范
```typescript
// ✅ 正确
/**
 * 获取零工列表
 * @param params 查询参数
 * @returns 分页数据
 */
export const getGigList = async (params: GigListParams) => {
  // 实现逻辑
}

// 单行注释
const status = 'active' // 默认状态
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -m 'feat: add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建 Pull Request

## 📄 许可证

MIT License

## 📞 联系方式

- 项目地址：[GitHub Repository]
- 问题反馈：[Issues]
- 技术交流：[Discussions]

---

**本地宝** - 连接本地生活，服务你我他 🏠✨ 
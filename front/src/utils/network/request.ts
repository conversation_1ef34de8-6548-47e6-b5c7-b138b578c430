/**
 * 类型安全的请求封装
 */
import { request } from './client'

// 创建类型安全的请求方法
export function createRequest() {
    return {
        /**
         * GET 请求
         */
        get<T = any>(url: string, config?: any): Promise<ResponseData<T>> {
            return request.Get(url, config)
        },

        /**
         * POST 请求
         */
        post<T = any>(url: string, data?: any, config?: any): Promise<ResponseData<T>> {
            return request.Post(url, data, config)
        },

        /**
         * PUT 请求
         */
        put<T = any>(url: string, data?: any, config?: any): Promise<ResponseData<T>> {
            return request.Put(url, data, config)
        },

        /**
         * DELETE 请求
         */
        delete<T = any>(url: string, config?: any): Promise<ResponseData<T>> {
            return request.Delete(url, config)
        }
    }
}

// 导出类型安全的请求实例
export const http = createRequest()

// 向后兼容
export { request }
export default http
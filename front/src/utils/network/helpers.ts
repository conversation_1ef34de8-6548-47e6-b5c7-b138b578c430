/**
 * API 调用辅助工具
 * 简化版本，提供核心的 API 调用处理功能
 */

import { showLoading, hideLoading, showToast, showSuccessToast } from '@/utils/ui/feedback';
import { HTTP_STATUS, RESPONSE_MESSAGE } from '@/constants/response';

// ====================================================================
// 类型定义
// ====================================================================

export interface ApiOptions {
  showLoading?: boolean;
  loadingText?: string;
  showSuccessToast?: boolean;
  successText?: string;
  showToast?: boolean;
  retryCount?: number;
  timeout?: number;
}

export interface ApiResult<T = any> {
  success: boolean;
  data?: T;
  error?: any;
  message?: string;
}

// ====================================================================
// 核心 API 调用函数
// ====================================================================

export interface CallApiOptions extends ApiOptions {
  onSuccess?: (data: any) => void | Promise<void>  // 成功回调
  onError?: (error: any) => void | Promise<void>   // 错误回调
}

/**
 * 通用 API 调用包装器 - 唯一的API调用入口
 * @param apiFunction API 调用函数
 * @param options 配置选项
 */
export async function callApi<T = any>(
  apiFunction: () => Promise<T>,
  options: CallApiOptions & { onSuccess?: (data: T) => void | Promise<void> } = {}
): Promise<ApiResult<T>> {
  const {
    showLoading: shouldShowLoading = false,
    loadingText = '加载中...',
    showSuccessToast: shouldShowSuccessToast = false,
    successText = '操作成功',
    showToast: shouldshowToast = true,
    retryCount = 0,
    timeout = 30000,
  } = options;

  let attempt = 0;
  let lastError: any;

  // 显示加载状态
  if (shouldShowLoading) {
    showLoading(loadingText);
  }

  try {
    while (attempt <= retryCount) {
      try {
        // 设置超时
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), timeout);
        });

        const result = await Promise.race([
          apiFunction(),
          timeoutPromise,
        ]);

        // 成功时显示提示
        if (shouldShowSuccessToast) {
          showSuccessToast(successText);
        }

        // 执行成功回调
        if (options.onSuccess) {
          await options.onSuccess(result);
        }

        return {
          success: true,
          data: result,
        };
      } catch (error) {
        lastError = error;
        attempt++;

        // 如果还有重试机会，等待一段时间后重试
        if (attempt <= retryCount) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    // 所有重试都失败了
    throw lastError;
  } catch (error) {
    const errorMessage = getErrorMessage(error);

    // 显示错误提示
    if (shouldshowToast) {
      showToast(errorMessage);
    }

    // 执行错误回调
    if (options.onError) {
      await options.onError(error);
    }

    return {
      success: false,
      error,
      message: errorMessage,
    };
  } finally {
    // 隐藏加载状态
    if (shouldShowLoading) {
      hideLoading();
    }
  }
}

// ====================================================================
// 快捷调用函数 - 基于callApi的预设配置
// ====================================================================

/**
 * 静默API调用 - 只返回结果，不显示任何提示
 */
export async function silentCall<T = any>(
  apiFunction: () => Promise<T>
): Promise<ApiResult<T>> {
  return callApi(apiFunction, {
    showLoading: false,
    showToast: false,
    showSuccessToast: false
  });
}

/**
 * 标准API调用 - 错误提示，无加载状态
 */
export async function standardCall<T = any>(
  apiFunction: () => Promise<T>
): Promise<ApiResult<T>> {
  return callApi(apiFunction, { showToast: true });
}

/**
 * 加载API调用 - 显示加载状态和错误提示
 */
export async function loadingCall<T = any>(
  apiFunction: () => Promise<T>,
  loadingText?: string
): Promise<ApiResult<T>> {
  return callApi(apiFunction, {
    showLoading: true,
    loadingText,
    showToast: true
  });
}

// ====================================================================
// 错误处理工具
// ====================================================================

/**
 * 获取错误信息 - 使用标准化常量
 * @param error 错误对象
 * @param defaultMessage 默认错误信息
 */
export function getErrorMessage(error: any, defaultMessage: string = RESPONSE_MESSAGE.ERROR): string {
  if (!error) return defaultMessage;

  // 网络错误
  if (error.name === 'NetworkError' || error.message === 'Network Error') {
    return RESPONSE_MESSAGE.NETWORK_ERROR;
  }

  // 超时错误
  if (error.name === 'TimeoutError' || error.message === '请求超时') {
    return RESPONSE_MESSAGE.REQUEST_TIMEOUT;
  }

  // HTTP 状态码错误 - 使用标准化常量
  if (error.status) {
    switch (error.status) {
      case HTTP_STATUS.UNAUTHORIZED:
        return RESPONSE_MESSAGE.UNAUTHORIZED;
      case HTTP_STATUS.FORBIDDEN:
        return RESPONSE_MESSAGE.FORBIDDEN;
      case HTTP_STATUS.NOT_FOUND:
        return RESPONSE_MESSAGE.NOT_FOUND;
      case HTTP_STATUS.METHOD_NOT_ALLOWED:
        return RESPONSE_MESSAGE.METHOD_NOT_ALLOWED;
      case HTTP_STATUS.TOO_MANY_REQUESTS:
        return RESPONSE_MESSAGE.TOO_MANY_REQUESTS;
      case HTTP_STATUS.INTERNAL_SERVER_ERROR:
        return RESPONSE_MESSAGE.SERVER_ERROR;
      case HTTP_STATUS.BAD_GATEWAY:
      case HTTP_STATUS.SERVICE_UNAVAILABLE:
        return RESPONSE_MESSAGE.SERVICE_UNAVAILABLE;
      case HTTP_STATUS.BAD_REQUEST:
        return RESPONSE_MESSAGE.BAD_REQUEST;
      default:
        return `${RESPONSE_MESSAGE.ERROR} (${error.status})`;
    }
  }

  // API 返回的错误信息
  if (error.data && error.data.message) {
    return error.data.message;
  }

  // 标准错误对象
  if (error.message) {
    return error.message;
  }

  // 字符串错误
  if (typeof error === 'string') {
    return error;
  }

  return defaultMessage;
}

// ====================================================================
// 缓存相关（简化版）
// ====================================================================

const cache = new Map<string, { data: any; expiry: number }>();

/**
 * 带缓存的 API 调用
 * @param key 缓存键
 * @param apiFunction API 调用函数
 * @param cacheDuration 缓存时长（毫秒）
 */
export async function cachedApiCall<T = any>(
  key: string,
  apiFunction: () => Promise<T>,
  cacheDuration: number = 5 * 60 * 1000 // 默认5分钟
): Promise<ApiResult<T>> {
  // 检查缓存
  const cached = cache.get(key);
  if (cached && Date.now() < cached.expiry) {
    return {
      success: true,
      data: cached.data,
    };
  }

  // 调用 API
  const result = await standardCall(apiFunction);

  // 缓存成功的结果
  if (result.success && result.data) {
    cache.set(key, {
      data: result.data,
      expiry: Date.now() + cacheDuration,
    });
  }

  return result;
}

/**
 * 清除指定缓存
 */
export function clearCache(key: string) {
  cache.delete(key);
}

/**
 * 清除所有缓存
 */
export function clearAllCache() {
  cache.clear();
}

// ====================================================================
// 业务场景快捷函数 - 支持回调的高级封装
// ====================================================================

export interface SubmitOptions<T> {
  successText?: string
  loadingText?: string
  onSuccess?: (data: T) => void | Promise<void>
  onError?: (error: any) => void | Promise<void>
}

/**
 * 表单提交 - 支持业务回调
 * @param apiFunction API调用函数
 * @param options 提交选项
 */
export async function submitForm<T = any>(
  apiFunction: () => Promise<T>,
  options: SubmitOptions<T> = {}
): Promise<ApiResult<T>> {
  const {
    successText = '提交成功',
    loadingText = '提交中...',
    onSuccess,
    onError
  } = options;

  return callApi(apiFunction, {
    showLoading: true,
    loadingText,
    showSuccessToast: true,
    successText,
    showToast: true,
    onSuccess,
    onError
  });
}

/**
 * 删除操作 - 支持业务回调
 * @param apiFunction API调用函数
 * @param onSuccess 删除成功回调
 */
export async function deleteItem<T = any>(
  apiFunction: () => Promise<T>,
  onSuccess?: (data: T) => void | Promise<void>
): Promise<ApiResult<T>> {
  return callApi(apiFunction, {
    showLoading: true,
    loadingText: '删除中...',
    showSuccessToast: true,
    successText: '删除成功',
    showToast: true,
    onSuccess
  });
}
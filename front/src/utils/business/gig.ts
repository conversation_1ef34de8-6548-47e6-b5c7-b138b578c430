/**
 * 零工业务工具函数
 * 不包含业务逻辑判断（业务逻辑在 services/gig.ts 中）
 */

import { GIG_SALARY_UNIT_MAP, GigStatus, GigApplicationStatus, GIG_STATUS_DETAILS, GIG_APPLICATION_STATUS_DETAILS_SEEKER, GIG_APPLICATION_STATUS_DETAILS_RECRUITER } from '@/constants/gig';
import type { Gig } from '@/types/gig';
import dayjs from 'dayjs';

// ====================================================================
// 薪资格式化函数
// ====================================================================

/**
 * 格式化薪资显示（统一版本）
 * @param salary 薪资金额
 * @param salaryUnit 薪资单位
 * @param isFromCents 是否是分为单位（默认false，即元为单位）
 */
export function formatGigSalary(salary: number, salaryUnit: number, isFromCents: boolean = false): string {
    if (!salary || salary <= 0) return '面议';

    // 统一转换为元
    const yuan = isFromCents ? salary / 100 : salary;
    const unitText = GIG_SALARY_UNIT_MAP[salaryUnit] || '次';

    // 格式化显示，整数时不显示小数点，有小数时最多显示1位
    if (yuan >= 10000) {
        const wanYuan = yuan / 10000;
        const formattedWanYuan = wanYuan % 1 === 0 ? wanYuan.toString() : wanYuan.toFixed(1);
        return `${formattedWanYuan}万元/${unitText}`;
    }

    const formattedYuan = yuan % 1 === 0 ? yuan.toString() : yuan.toFixed(1);
    return `${formattedYuan}元/${unitText}`;
}


/**
 * 格式化薪资金额（不包含单位）
 * @param salary 薪资（分）
 */
export function formatSalaryAmount(salary: number): string {
    return (salary / 100).toFixed(2);
}

/**
 * 获取薪资单位文本
 * @param salaryUnit 薪资单位
 */
export function getSalaryUnitText(salaryUnit: number): string {
    return GIG_SALARY_UNIT_MAP[salaryUnit] || '次';
}

// ====================================================================
// 状态相关函数
// ====================================================================

/**
 * 获取零工状态详情
 * @param status 状态值
 */
export function getGigStatusDetails(status: GigStatus) {
    return GIG_STATUS_DETAILS[status] || GIG_STATUS_DETAILS[GigStatus.Draft];
}

/**
 * 获取申请状态详情
 * @param status 状态值
 * @param role 角色: 'seeker' 或 'recruiter'
 */
export function getGigApplicationStatusDetails(status: GigApplicationStatus, role: 'seeker' | 'recruiter' = 'seeker') {
    const detailsMap = role === 'seeker' ? GIG_APPLICATION_STATUS_DETAILS_SEEKER : GIG_APPLICATION_STATUS_DETAILS_RECRUITER;
    return detailsMap[status] || GIG_APPLICATION_STATUS_DETAILS_SEEKER[GigApplicationStatus.Pending];
}

/**
 * 获取零工状态文本
 * @param status 状态值
 */
export function getGigStatusText(status: GigStatus): string {
    return getGigStatusDetails(status).text;
}

/**
 * 获取零工状态样式类
 * @param status 状态值
 */
export function getGigStatusClass(status: GigStatus): string {
    return getGigStatusDetails(status).class;
}

/**
 * 获取申请状态文本
 * @param status 申请状态值
 */
export function getApplicationStatusText(status: GigApplicationStatus): string {
    return getGigApplicationStatusDetails(status).text;
}

/**
 * 获取申请状态样式类
 * @param status 申请状态值
 */
export function getApplicationStatusClass(status: GigApplicationStatus): string {
    return getGigApplicationStatusDetails(status).class;
}

// ====================================================================
// 时间相关工具函数
// ====================================================================


// ====================================================================
// 数据处理工具函数
// ====================================================================

/**
 * 计算零工进度百分比
 */
export function calculateGigProgress(currentCount: number, totalCount: number): number {
    if (!totalCount || totalCount <= 0) return 0;
    return Math.round((currentCount / totalCount) * 100);
}

/**
 * 格式化零工参与人数显示
 */
export function formatGigParticipantCount(currentCount: number, totalCount: number): string {
    return `${currentCount}/${totalCount}人`;
}

/**
 * 检查零工是否即将开始
 * @param startTime 开始时间
 * @param hoursThreshold 阈值（小时）
 */
export function isGigStartingSoon(startTime: string, hoursThreshold: number = 24): boolean {
    const start = dayjs(startTime);
    const now = dayjs();
    const diffHours = start.diff(now, 'hour');

    return diffHours > 0 && diffHours <= hoursThreshold;
}

/**
 * 检查零工是否已过期
 */
export function isGigExpired(endTime: string): boolean {
    return dayjs().isAfter(dayjs(endTime));
}

// ====================================================================
// 排序和筛选工具函数
// ====================================================================

/**
 * 零工排序比较函数
 */
export function compareGigs(a: Gig, b: Gig, sortBy: 'salary' | 'startTime' | 'createTime' = 'createTime'): number {
    switch (sortBy) {
        case 'salary':
            return (b.salary || 0) - (a.salary || 0);
        case 'startTime':
            return dayjs(a.start_time).valueOf() - dayjs(b.start_time).valueOf();
        case 'createTime':
        default:
            return dayjs(b.created_at).valueOf() - dayjs(a.created_at).valueOf();
    }
}

/**
 * 筛选可申请的零工
 */
export function filterApplicableGigs(gigs: Gig[]): Gig[] {
    return gigs.filter(gig =>
        gig.status === GigStatus.Recruiting &&
        gig.current_people_count < gig.people_count &&
        !isGigExpired(gig.end_time)
    );
}
export function getGigActions(gig: Gig, isOwner: boolean = false) {
    if (isOwner) {
        // 发布者的操作
        switch (gig.status) {
            case GigStatus.Draft:
                return [
                    { text: '发布', action: 'publish', type: 'primary' },
                    { text: '编辑', action: 'edit', type: 'secondary' },
                    { text: '删除', action: 'delete', type: 'danger' }
                ]
            case GigStatus.Recruiting:
                return [
                    { text: '查看申请', action: 'viewApplicants', type: 'primary' },
                    { text: '暂停', action: 'pause', type: 'warning' },
                    { text: '编辑', action: 'edit', type: 'secondary' }
                ]
            case GigStatus.Paused:
                return [
                    { text: '继续招聘', action: 'resume', type: 'success' },
                    { text: '关闭', action: 'close', type: 'danger' }
                ]
            case GigStatus.Locked:
            case GigStatus.InProgress:
                return [
                    { text: '查看申请', action: 'viewApplicants', type: 'primary' },
                    { text: '联系工人', action: 'contact', type: 'success' }
                ]
            case GigStatus.Completed:
            case GigStatus.Closed:
                return [
                    { text: '删除', action: 'delete', type: 'danger' },
                    { text: '再次发布', action: 'republish', type: 'secondary' }
                ]
            default:
                return []
        }
    } else {
        // 求职者的操作
        if (this.canApplyForGig(gig)) {
            return [{ text: '立即申请', action: 'apply', type: 'primary' }]
        } else {
            return [{ text: '查看详情', action: 'viewDetail', type: 'secondary' }]
        }
    }
}
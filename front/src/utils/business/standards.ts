/**
 * 标准化数据工具函数
 * 从 constants/standards.ts 迁移而来
 */

import {
  genderOptions,
  educationOptions,
  experienceOptions,
  welfareOptions,
  jobSalaryRanges,
  ageRanges,
  companySizeRanges,
  type SalaryRange,
  type AgeRange,
  type CompanySizeRange,
  type StandardOption
} from '@/constants/standards';

// =====================================================
// 范围查询工具函数
// =====================================================

/**
 * 获取正职薪资范围
 */
export function getJobSalaryRange(code: number): SalaryRange | undefined {
  return jobSalaryRanges[code];
}

/**
 * 获取年龄范围
 */
export function getAgeRange(code: number): AgeRange | undefined {
  return ageRanges[code];
}

/**
 * 获取公司规模范围
 */
export function getCompanySizeRange(code: number): CompanySizeRange | undefined {
  return companySizeRanges[code];
}

// =====================================================
// 福利工具函数
// =====================================================

/**
 * 根据编码数组获取福利标签
 */
export function getWelfareLabels(codes: number[]): string[] {
  return codes.map(code => {
    const option = welfareOptions.find(opt => opt.code === code);
    return option ? option.label : '';
  }).filter(Boolean);
}

/**
 * 根据编码获取福利选项
 */
export function getWelfareByCode(code: number): StandardOption | undefined {
  return welfareOptions.find(option => option.code === code);
}

/**
 * 检查福利编码数组是否包含指定福利
 */
export function containsWelfare(codes: number[], targetCode: number): boolean {
  return codes.includes(targetCode);
}

// =====================================================
// 标准化文本获取函数
// =====================================================

/**
 * 获取性别文本
 * @param gender 性别值
 */
export function getGenderText(gender: number): string {
  const option = genderOptions.find(opt => opt.code === gender);
  return option ? option.label : '不限';
}

/**
 * 获取经验文本
 * @param experience 经验值
 */
export function getExperienceText(experience: number): string {
  const option = experienceOptions.find(opt => opt.code === experience);
  return option ? option.label : '不限';
}

/**
 * 获取学历文本
 * @param education 学历值
 */
export function getEducationText(education: number): string {
  const option = educationOptions.find(opt => opt.code === education);
  return option ? option.label : '不限';
}

// =====================================================
// 反向映射工具函数 
// =====================================================

/**
 * 创建反向映射的工具函数
 */
function createReverseMap(map: Record<number, string>): Record<string, number> {
  const reverseMap: Record<string, number> = {};
  for (const key in map) {
    if (Object.prototype.hasOwnProperty.call(map, key)) {
      reverseMap[map[key]] = Number(key);
    }
  }
  return reverseMap;
}

/**
 * 根据性别文本获取对应的编码
 */
export function getGenderCodeByText(text: string): number {
  const option = genderOptions.find(opt => opt.label === text);
  return option ? option.code : 0;
}

/**
 * 根据经验文本获取对应的编码
 */
export function getExperienceCodeByText(text: string): number {
  const option = experienceOptions.find(opt => opt.label === text);
  return option ? option.code : 0;
}

/**
 * 根据学历文本获取对应的编码
 */
export function getEducationCodeByText(text: string): number {
  const option = educationOptions.find(opt => opt.label === text);
  return option ? option.code : 0;
}

// =====================================================
// 批量处理工具函数
// =====================================================

/**
 * 批量获取福利文本
 * @param codeArrays 多个福利编码数组
 * @returns 对应的标签数组
 */
export function batchGetWelfareLabels(codeArrays: number[][]): string[][] {
  return codeArrays.map(codes => getWelfareLabels(codes));
}

/**
 * 批量验证福利编码
 * @param codes 福利编码数组
 * @returns 有效的编码数组
 */
export function validateWelfareCodes(codes: number[]): number[] {
  return codes.filter(code => getWelfareByCode(code) !== undefined);
}

/**
 * 格式化薪资范围显示文本
 * @param code 薪资范围编码
 * @returns 格式化的薪资文本
 */
export function formatSalaryRangeText(code: number): string {
  const range = getJobSalaryRange(code);
  if (!range || range.code === 0) {
    return '面议';
  }
  
  if (range.max >= 99999999) {
    return `${(range.min / 1000).toFixed(0)}K以上`;
  }
  
  return `${(range.min / 1000).toFixed(0)}K-${(range.max / 1000).toFixed(0)}K`;
}

/**
 * 格式化年龄范围显示文本
 * @param code 年龄范围编码
 * @returns 格式化的年龄文本
 */
export function formatAgeRangeText(code: number): string {
  const range = getAgeRange(code);
  if (!range || range.code === 0) {
    return '不限';
  }
  
  if (range.max >= 100) {
    return `${range.min}岁以上`;
  }
  
  return `${range.min}-${range.max}岁`;
}
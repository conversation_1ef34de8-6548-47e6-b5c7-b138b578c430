/**
 * 颜色处理工具
 * 提供颜色格式转换、计算等功能
 */

export interface RGBColor {
  r: number
  g: number
  b: number
}

export interface HSLColor {
  h: number
  s: number
  l: number
}

// ====================================================================
// RGB 与 HEX 转换
// ====================================================================

/**
 * RGB颜色转十六进制颜色
 * @param r 红色值 (0-255)
 * @param g 绿色值 (0-255)
 * @param b 蓝色值 (0-255)
 * @returns 十六进制颜色字符串 (如: #FF0000)
 */
export function rgbToHex(r: number, g: number, b: number): string {
  return "#" + componentToHex(r) + componentToHex(g) + componentToHex(b)
}

/**
 * RGB对象转十六进制颜色
 * @param rgb RGB颜色对象
 * @returns 十六进制颜色字符串
 */
export function rgbObjectToHex(rgb: RGBColor): string {
  return rgbToHex(rgb.r, rgb.g, rgb.b)
}

/**
 * 十六进制颜色转RGB颜色
 * @param hex 颜色值 #333 或 #333333
 * @returns RGB颜色对象，解析失败返回null
 */
export function hexToRGB(hex: string): RGBColor | null {
  // 处理简写形式 #333 -> #333333
  if (hex.length === 4) {
    const text = hex.substring(1, 4)
    hex = '#' + text + text
  }
  
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

/**
 * 单个颜色分量转十六进制
 * @param c 颜色分量值 (0-255)
 * @returns 两位十六进制字符串
 */
function componentToHex(c: number): string {
  const hex = Math.max(0, Math.min(255, Math.round(c))).toString(16)
  return hex.length === 1 ? "0" + hex : hex
}

// ====================================================================
// RGB 与 HSL 转换
// ====================================================================

/**
 * RGB转HSL
 * @param r 红色值 (0-255)
 * @param g 绿色值 (0-255)
 * @param b 蓝色值 (0-255)
 * @returns HSL颜色对象
 */
export function rgbToHsl(r: number, g: number, b: number): HSLColor {
  r /= 255
  g /= 255
  b /= 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h = 0
  let s = 0
  const l = (max + min) / 2

  if (max === min) {
    h = s = 0 // 无色彩
  } else {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break
      case g: h = (b - r) / d + 2; break
      case b: h = (r - g) / d + 4; break
    }
    h /= 6
  }

  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100)
  }
}

/**
 * HSL转RGB
 * @param h 色调 (0-360)
 * @param s 饱和度 (0-100)
 * @param l 明度 (0-100)
 * @returns RGB颜色对象
 */
export function hslToRgb(h: number, s: number, l: number): RGBColor {
  h /= 360
  s /= 100
  l /= 100

  let r: number, g: number, b: number

  if (s === 0) {
    r = g = b = l // 无色彩
  } else {
    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1
      if (t > 1) t -= 1
      if (t < 1/6) return p + (q - p) * 6 * t
      if (t < 1/2) return q
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
      return p
    }

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s
    const p = 2 * l - q
    r = hue2rgb(p, q, h + 1/3)
    g = hue2rgb(p, q, h)
    b = hue2rgb(p, q, h - 1/3)
  }

  return {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(b * 255)
  }
}

// ====================================================================
// 颜色调整与生成
// ====================================================================

/**
 * 调整颜色亮度
 * @param hex 十六进制颜色
 * @param percent 调整百分比 (-100 到 100)
 * @returns 调整后的十六进制颜色
 */
export function adjustBrightness(hex: string, percent: number): string {
  const rgb = hexToRGB(hex)
  if (!rgb) return hex

  const factor = Math.max(-1, Math.min(1, percent / 100))
  
  const adjust = (value: number) => {
    if (factor < 0) {
      return Math.round(value * (1 + factor))
    } else {
      return Math.round(value + (255 - value) * factor)
    }
  }

  return rgbToHex(adjust(rgb.r), adjust(rgb.g), adjust(rgb.b))
}

/**
 * 调整颜色饱和度
 * @param hex 十六进制颜色
 * @param percent 调整百分比 (-100 到 100)
 * @returns 调整后的十六进制颜色
 */
export function adjustSaturation(hex: string, percent: number): string {
  const rgb = hexToRGB(hex)
  if (!rgb) return hex

  const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b)
  hsl.s = Math.max(0, Math.min(100, hsl.s + percent))
  
  const newRgb = hslToRgb(hsl.h, hsl.s, hsl.l)
  return rgbToHex(newRgb.r, newRgb.g, newRgb.b)
}

/**
 * 获取颜色的对比色
 * @param hex 十六进制颜色
 * @returns 对比色的十六进制颜色
 */
export function getContrastColor(hex: string): string {
  const rgb = hexToRGB(hex)
  if (!rgb) return '#000000'

  // 计算亮度
  const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000
  
  // 根据亮度返回黑色或白色
  return brightness > 128 ? '#000000' : '#FFFFFF'
}

/**
 * 计算两个颜色之间的对比度
 * @param color1 第一个颜色
 * @param color2 第二个颜色
 * @returns 对比度值 (1-21)
 */
export function getColorContrast(color1: string, color2: string): number {
  const getLuminance = (hex: string): number => {
    const rgb = hexToRGB(hex)
    if (!rgb) return 0

    const toLinear = (value: number) => {
      const normalized = value / 255
      return normalized <= 0.03928 
        ? normalized / 12.92 
        : Math.pow((normalized + 0.055) / 1.055, 2.4)
    }

    const r = toLinear(rgb.r)
    const g = toLinear(rgb.g)
    const b = toLinear(rgb.b)

    return 0.2126 * r + 0.7152 * g + 0.0722 * b
  }

  const lum1 = getLuminance(color1)
  const lum2 = getLuminance(color2)
  
  const brightest = Math.max(lum1, lum2)
  const darkest = Math.min(lum1, lum2)
  
  return (brightest + 0.05) / (darkest + 0.05)
}

/**
 * 生成渐变色数组
 * @param startColor 起始颜色
 * @param endColor 结束颜色
 * @param steps 步数
 * @returns 渐变色数组
 */
export function generateGradient(startColor: string, endColor: string, steps: number): string[] {
  const startRgb = hexToRGB(startColor)
  const endRgb = hexToRGB(endColor)
  
  if (!startRgb || !endRgb) return [startColor, endColor]

  const gradient: string[] = []
  
  for (let i = 0; i < steps; i++) {
    const ratio = i / (steps - 1)
    
    const r = Math.round(startRgb.r + (endRgb.r - startRgb.r) * ratio)
    const g = Math.round(startRgb.g + (endRgb.g - startRgb.g) * ratio)
    const b = Math.round(startRgb.b + (endRgb.b - startRgb.b) * ratio)
    
    gradient.push(rgbToHex(r, g, b))
  }
  
  return gradient
}

/**
 * 生成互补色
 * @param hex 十六进制颜色
 * @returns 互补色的十六进制颜色
 */
export function getComplementaryColor(hex: string): string {
  const rgb = hexToRGB(hex)
  if (!rgb) return hex

  return rgbToHex(255 - rgb.r, 255 - rgb.g, 255 - rgb.b)
}

/**
 * 生成类似色调色板
 * @param hex 基准颜色
 * @param count 生成颜色数量
 * @returns 类似色数组
 */
export function generateAnalogousPalette(hex: string, count: number = 5): string[] {
  const rgb = hexToRGB(hex)
  if (!rgb) return [hex]

  const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b)
  const palette: string[] = []
  
  const step = 30 // 色调间隔
  const startHue = hsl.h - (step * Math.floor(count / 2))
  
  for (let i = 0; i < count; i++) {
    const newHue = (startHue + i * step + 360) % 360
    const newRgb = hslToRgb(newHue, hsl.s, hsl.l)
    palette.push(rgbToHex(newRgb.r, newRgb.g, newRgb.b))
  }
  
  return palette
}

/**
 * 验证颜色格式是否有效
 * @param color 颜色字符串
 * @returns 是否为有效的颜色格式
 */
export function isValidColor(color: string): boolean {
  // 验证十六进制格式
  if (/^#([0-9A-F]{3}){1,2}$/i.test(color)) {
    return true
  }
  
  // 验证RGB格式
  if (/^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/i.test(color)) {
    return true
  }
  
  // 验证RGBA格式
  if (/^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[01]?\.?\d*\s*\)$/i.test(color)) {
    return true
  }
  
  return false
}

/**
 * 随机生成颜色
 * @param format 返回格式 ('hex' | 'rgb')
 * @returns 随机颜色
 */
export function generateRandomColor(format: 'hex' | 'rgb' = 'hex'): string {
  const r = Math.floor(Math.random() * 256)
  const g = Math.floor(Math.random() * 256)
  const b = Math.floor(Math.random() * 256)
  
  if (format === 'rgb') {
    return `rgb(${r}, ${g}, ${b})`
  }
  
  return rgbToHex(r, g, b)
}

// ====================================================================
// 常用颜色常量
// ====================================================================

export const CommonColors = {
  // 基础色
  BLACK: '#000000',
  WHITE: '#FFFFFF',
  TRANSPARENT: 'transparent',
  
  // 灰色系
  GRAY_50: '#F9FAFB',
  GRAY_100: '#F3F4F6',
  GRAY_200: '#E5E7EB',
  GRAY_300: '#D1D5DB',
  GRAY_400: '#9CA3AF',
  GRAY_500: '#6B7280',
  GRAY_600: '#4B5563',
  GRAY_700: '#374151',
  GRAY_800: '#1F2937',
  GRAY_900: '#111827',
  
  // 主色调
  PRIMARY: '#3B82F6',
  SECONDARY: '#6366F1',
  SUCCESS: '#10B981',
  WARNING: '#F59E0B',
  ERROR: '#EF4444',
  INFO: '#06B6D4',
  
  // 浅色变体
  PRIMARY_LIGHT: '#DBEAFE',
  SECONDARY_LIGHT: '#E0E7FF',
  SUCCESS_LIGHT: '#D1FAE5',
  WARNING_LIGHT: '#FEF3C7',
  ERROR_LIGHT: '#FEE2E2',
  INFO_LIGHT: '#CFFAFE'
} as const
/**
 * 性能优化工具
 * 提供防抖、节流、缓存等性能优化功能
 */

// ====================================================================
// 类型定义
// ====================================================================

export type DebouncedFunction<T extends (...args: any[]) => any> = {
  (...args: Parameters<T>): void
  cancel: () => void
  flush: () => void
}

export type ThrottledFunction<T extends (...args: any[]) => any> = {
  (...args: Parameters<T>): void
  cancel: () => void
}

export interface CacheOptions {
  maxSize?: number
  ttl?: number // 生存时间（毫秒）
}

export interface PerformanceMetrics {
  executionTime: number
  memoryUsage?: number
  callCount: number
  averageTime: number
}

// ====================================================================
// 函数防抖
// ====================================================================

/**
 * 函数防抖
 * @desc 短时间内多次触发同一事件，只执行最后一次，或者只执行最开始的一次，中间的不执行。
 * @param func 目标函数
 * @param wait 延迟执行毫秒数
 * @param immediate true - 立即执行， false - 延迟执行
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number = 1000,
  immediate: boolean = true
): DebouncedFunction<T> {
  let timer: number | null = null
  let result: any

  const debounced = function (this: any, ...args: Parameters<T>) {
    const context = this

    const later = () => {
      timer = null
      if (!immediate) {
        result = func.apply(context, args)
      }
    }

    const callNow = immediate && !timer

    if (timer) {
      clearTimeout(timer)
    }

    timer = setTimeout(later, wait)

    if (callNow) {
      result = func.apply(context, args)
    }

    return result
  } as DebouncedFunction<T>

  debounced.cancel = () => {
    if (timer) {
      clearTimeout(timer)
      timer = null
    }
  }

  debounced.flush = () => {
    if (timer) {
      clearTimeout(timer)
      timer = null
      result = func.apply(this, arguments as any)
    }
    return result
  }

  return debounced
}

/**
 * 高级防抖 - 支持最大等待时间
 * @param func 目标函数
 * @param wait 延迟执行毫秒数
 * @param options 选项配置
 */
export function advancedDebounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  options: {
    immediate?: boolean
    maxWait?: number
  } = {}
): DebouncedFunction<T> {
  const { immediate = false, maxWait } = options

  let timer: number | null = null
  let maxTimer: number | null = null
  let result: any

  const debounced = function (this: any, ...args: Parameters<T>) {
    const context = this

    const later = () => {
      timer = null
      if (maxTimer) {
        clearTimeout(maxTimer)
        maxTimer = null
      }
      if (!immediate) {
        result = func.apply(context, args)
      }
    }

    const callNow = immediate && !timer

    if (timer) {
      clearTimeout(timer)
    }

    timer = setTimeout(later, wait)

    // 设置最大等待时间
    if (maxWait && !maxTimer) {
      maxTimer = setTimeout(() => {
        if (timer) {
          clearTimeout(timer)
          timer = null
        }
        maxTimer = null
        result = func.apply(context, args)
      }, maxWait)
    }

    if (callNow) {
      result = func.apply(context, args)
    }

    return result
  } as DebouncedFunction<T>

  debounced.cancel = () => {
    if (timer) {
      clearTimeout(timer)
      timer = null
    }
    if (maxTimer) {
      clearTimeout(maxTimer)
      maxTimer = null
    }
  }

  debounced.flush = () => {
    if (timer) {
      clearTimeout(timer)
      timer = null
    }
    if (maxTimer) {
      clearTimeout(maxTimer)
      maxTimer = null
    }
    result = func.apply(this, arguments as any)
    return result
  }

  return debounced
}

// ====================================================================
// 函数节流
// ====================================================================

/**
 * 函数节流
 * @desc 指连续触发事件，但是在 n 秒内只执行一次函数。即 2n 秒内执行 2 次... 。会稀释函数的执行频率。
 * @param func 函数
 * @param wait 延迟执行毫秒数
 * @param type 1 在时间段开始的时候触发 2 在时间段结束的时候触发
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number = 1000,
  type: 1 | 2 = 1
): ThrottledFunction<T> {
  let previous = 0
  let timeout: number | null = null
  let result: any

  const throttled = function (this: any, ...args: Parameters<T>) {
    const context = this

    if (type === 1) {
      const now = Date.now()
      if (now - previous > wait) {
        result = func.apply(context, args)
        previous = now
      }
    } else if (type === 2) {
      if (!timeout) {
        timeout = setTimeout(() => {
          timeout = null
          result = func.apply(context, args)
        }, wait)
      }
    }

    return result
  } as ThrottledFunction<T>

  throttled.cancel = () => {
    if (type === 1) {
      previous = 0
    } else if (type === 2 && timeout) {
      clearTimeout(timeout)
      timeout = null
    }
  }

  return throttled
}

/**
 * 高级节流 - 支持首次和末次执行
 * @param func 目标函数
 * @param wait 等待时间
 * @param options 配置选项
 */
export function advancedThrottle<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  options: {
    leading?: boolean // 首次是否执行
    trailing?: boolean // 末次是否执行
  } = {}
): ThrottledFunction<T> {
  const { leading = true, trailing = true } = options

  let timeout: number | null = null
  let previous = 0
  let result: any

  const throttled = function (this: any, ...args: Parameters<T>) {
    const context = this
    const now = Date.now()

    if (!previous && !leading) {
      previous = now
    }

    const remaining = wait - (now - previous)

    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      previous = now
      result = func.apply(context, args)
    } else if (!timeout && trailing) {
      timeout = setTimeout(() => {
        previous = !leading ? 0 : Date.now()
        timeout = null
        result = func.apply(context, args)
      }, remaining)
    }

    return result
  } as ThrottledFunction<T>

  throttled.cancel = () => {
    if (timeout) {
      clearTimeout(timeout)
      timeout = null
    }
    previous = 0
  }

  return throttled
}

// ====================================================================
// 记忆化缓存
// ====================================================================

/**
 * 简单的记忆化函数
 * @param func 要缓存的函数
 * @param resolver 自定义key生成器
 */
export function memoize<T extends (...args: any[]) => any>(
  func: T,
  resolver?: (...args: Parameters<T>) => string
): T {
  const cache = new Map<string, any>()

  return ((...args: Parameters<T>) => {
    const key = resolver ? resolver(...args) : JSON.stringify(args)

    if (cache.has(key)) {
      return cache.get(key)
    }

    const result = func(...args)
    cache.set(key, result)

    return result
  }) as T
}

/**
 * 高级记忆化 - 支持TTL和大小限制
 * @param func 要缓存的函数
 * @param options 缓存选项
 */
export function advancedMemoize<T extends (...args: any[]) => any>(
  func: T,
  options: CacheOptions & {
    resolver?: (...args: Parameters<T>) => string
  } = {}
): T & { cache: Map<string, any>; clear: () => void } {
  const { maxSize = 100, ttl, resolver } = options
  const cache = new Map<string, { value: any; expiry?: number }>()

  const memoized = ((...args: Parameters<T>) => {
    const key = resolver ? resolver(...args) : JSON.stringify(args)
    const now = Date.now()

    // 检查缓存是否存在且未过期
    const cached = cache.get(key)
    if (cached && (!cached.expiry || now < cached.expiry)) {
      return cached.value
    }

    // 执行函数并缓存结果
    const result = func(...args)

    // 如果缓存满了，删除最旧的项
    if (cache.size >= maxSize) {
      const firstKey = cache.keys().next().value
      cache.delete(firstKey)
    }

    const cacheValue = {
      value: result,
      expiry: ttl ? now + ttl : undefined
    }

    cache.set(key, cacheValue)

    return result
  }) as T & { cache: Map<string, any>; clear: () => void }

  memoized.cache = cache as any
  memoized.clear = () => cache.clear()

  return memoized
}

// ====================================================================
// 异步控制
// ====================================================================

/**
 * 请求去重 - 相同的请求只执行一次
 * @param func 异步函数
 * @param resolver 自定义key生成器
 */
export function dedupe<T extends (...args: any[]) => Promise<any>>(
  func: T,
  resolver?: (...args: Parameters<T>) => string
): T {
  const pendingPromises = new Map<string, Promise<any>>()

  return (async (...args: Parameters<T>) => {
    const key = resolver ? resolver(...args) : JSON.stringify(args)

    // 如果有正在进行的相同请求，直接返回
    if (pendingPromises.has(key)) {
      return pendingPromises.get(key)
    }

    // 执行新请求
    const promise = func(...args)
    pendingPromises.set(key, promise)

    try {
      const result = await promise
      return result
    } finally {
      // 请求完成后清理
      pendingPromises.delete(key)
    }
  }) as T
}

/**
 * 并发控制 - 限制同时执行的任务数量
 * @param limit 最大并发数
 */
export function concurrencyLimit(limit: number) {
  let running = 0
  const queue: Array<() => void> = []

  return function <T>(task: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      const execute = async () => {
        running++
        try {
          const result = await task()
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          running--
          if (queue.length > 0 && running < limit) {
            const next = queue.shift()
            if (next) next()
          }
        }
      }

      if (running < limit) {
        execute()
      } else {
        queue.push(execute)
      }
    })
  }
}

// ====================================================================
// 性能监控
// ====================================================================

/**
 * 性能监控装饰器
 * @param func 要监控的函数
 * @param name 函数名称
 */
export function performanceMonitor<T extends (...args: any[]) => any>(
  func: T,
  name?: string
): T & { getMetrics: () => PerformanceMetrics } {
  let callCount = 0
  let totalTime = 0
  const functionName = name || func.name || 'anonymous'

  const monitored = ((...args: Parameters<T>) => {
    const startTime = performance.now()
    const startMemory = (performance as any).memory?.usedJSHeapSize

    try {
      const result = func(...args)

      // 如果是异步函数
      if (result instanceof Promise) {
        return result.finally(() => {
          const endTime = performance.now()
          const executionTime = endTime - startTime
          callCount++
          totalTime += executionTime

          console.log(`[Performance] ${functionName}: ${executionTime.toFixed(2)}ms`)
        })
      }

      // 同步函数
      const endTime = performance.now()
      const executionTime = endTime - startTime
      callCount++
      totalTime += executionTime

      console.log(`[Performance] ${functionName}: ${executionTime.toFixed(2)}ms`)

      return result
    } catch (error) {
      const endTime = performance.now()
      const executionTime = endTime - startTime
      callCount++
      totalTime += executionTime

      console.error(`[Performance] ${functionName} failed: ${executionTime.toFixed(2)}ms`, error)
      throw error
    }
  }) as T & { getMetrics: () => PerformanceMetrics }

  monitored.getMetrics = () => ({
    executionTime: totalTime,
    callCount,
    averageTime: callCount > 0 ? totalTime / callCount : 0,
    memoryUsage: (performance as any).memory?.usedJSHeapSize
  })

  return monitored
}

/**
 * 批处理 - 将多个调用合并为一次执行
 * @param func 要批处理的函数
 * @param wait 等待时间
 * @param maxSize 最大批次大小
 */
export function batch<T, R>(
  func: (items: T[]) => Promise<R[]>,
  wait: number = 10,
  maxSize: number = 10
) {
  let items: T[] = []
  let resolvers: Array<(value: R) => void> = []
  let rejecters: Array<(reason: any) => void> = []
  let timer: number | null = null

  const flush = async () => {
    if (items.length === 0) return

    const currentItems = [...items]
    const currentResolvers = [...resolvers]
    const currentRejecters = [...rejecters]

    items = []
    resolvers = []
    rejecters = []

    if (timer) {
      clearTimeout(timer)
      timer = null
    }

    try {
      const results = await func(currentItems)
      results.forEach((result, index) => {
        currentResolvers[index]?.(result)
      })
    } catch (error) {
      currentRejecters.forEach(reject => reject(error))
    }
  }

  return (item: T): Promise<R> => {
    return new Promise<R>((resolve, reject) => {
      items.push(item)
      resolvers.push(resolve)
      rejecters.push(reject)

      if (items.length >= maxSize) {
        flush()
      } else if (!timer) {
        timer = setTimeout(flush, wait)
      }
    })
  }
}

// ====================================================================
// 工具函数
// ====================================================================

/**
 * 延迟执行
 * @param ms 延迟毫秒数
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 重试机制
 * @param func 要重试的函数
 * @param maxRetries 最大重试次数
 * @param delay 重试延迟
 */
export async function retry<T>(
  func: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: any

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await func()
    } catch (error) {
      lastError = error

      if (attempt === maxRetries) {
        throw error
      }

      console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms...`)
      await sleep(delay)
    }
  }

  throw lastError
}

/**
 * 超时控制
 * @param promise 要控制的Promise
 * @param timeoutMs 超时时间
 * @param timeoutMessage 超时错误消息
 */
export function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  timeoutMessage: string = 'Operation timed out'
): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error(timeoutMessage)), timeoutMs)
  })

  return Promise.race([promise, timeoutPromise])
}
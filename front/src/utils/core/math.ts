/**
 * 数学计算和数据处理工具
 * 提供数学运算、数组处理、随机数生成等功能
 */

// ====================================================================
// 数学计算
// ====================================================================

/**
 * 生成指定范围内的随机整数
 * @param min 最小值（包含）
 * @param max 最大值（包含）
 */
export function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * 生成指定范围内的随机浮点数
 * @param min 最小值
 * @param max 最大值
 * @param decimals 小数位数
 */
export function randomFloat(min: number, max: number, decimals: number = 2): number {
  const random = Math.random() * (max - min) + min
  return Number(random.toFixed(decimals))
}

/**
 * 唯一标识，随机数
 * @param n 随机数位数
 */
export function generateUniqueId(n: number = 6): string {
  let rnd = ''
  for (let i = 0; i < n; i++) {
    rnd += Math.floor(Math.random() * 10)
  }
  return 'id_' + new Date().getTime() + rnd
}

/**
 * 生成UUID (简化版本)
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 计算两点之间的距离
 * @param x1 第一个点的x坐标
 * @param y1 第一个点的y坐标
 * @param x2 第二个点的x坐标
 * @param y2 第二个点的y坐标
 */
export function calculateDistance(x1: number, y1: number, x2: number, y2: number): number {
  return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2))
}

/**
 * 计算地球上两点间的距离（使用Haversine公式）
 * @param lat1 第一个点的纬度
 * @param lon1 第一个点的经度
 * @param lat2 第二个点的纬度
 * @param lon2 第二个点的经度
 * @returns 距离（单位：公里）
 */
export function calculateGeoDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371 // 地球半径（公里）
  const dLat = toRadians(lat2 - lat1)
  const dLon = toRadians(lon2 - lon1)
  
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2)
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  
  return R * c
}

/**
 * 角度转弧度
 * @param degrees 角度
 */
export function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180)
}

/**
 * 弧度转角度
 * @param radians 弧度
 */
export function toDegrees(radians: number): number {
  return radians * (180 / Math.PI)
}

/**
 * 限制数值在指定范围内
 * @param value 要限制的值
 * @param min 最小值
 * @param max 最大值
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max)
}

/**
 * 线性插值
 * @param start 起始值
 * @param end 结束值
 * @param factor 插值因子 (0-1)
 */
export function lerp(start: number, end: number, factor: number): number {
  return start + (end - start) * clamp(factor, 0, 1)
}

/**
 * 计算百分比
 * @param value 当前值
 * @param total 总值
 * @param decimals 小数位数
 */
export function calculatePercentage(value: number, total: number, decimals: number = 2): number {
  if (total === 0) return 0
  return Number(((value / total) * 100).toFixed(decimals))
}

// ====================================================================
// 数组处理
// ====================================================================

/**
 * 简单数组合并去重
 * @param arr1 数组1
 * @param arr2 数组2 可不传
 */
export function distinctArray<T>(arr1: T[] = [], arr2: T[] = []): T[] {
  const combinedArray = arr1.concat(arr2)
  return [...new Set(combinedArray)]
}

/**
 * 数组分块
 * @param array 要分块的数组
 * @param size 块大小
 */
export function chunkArray<T>(array: T[], size: number): T[][] {
  if (size <= 0) throw new Error('Chunk size must be greater than 0')
  
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size))
  }
  return chunks
}

/**
 * 数组乱序（Fisher-Yates算法）
 * @param array 要乱序的数组
 */
export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

/**
 * 数组求和
 * @param array 数值数组
 */
export function sum(array: number[]): number {
  return array.reduce((acc, val) => acc + val, 0)
}

/**
 * 数组平均值
 * @param array 数值数组
 */
export function average(array: number[]): number {
  if (array.length === 0) return 0
  return sum(array) / array.length
}

/**
 * 数组最大值
 * @param array 数值数组
 */
export function max(array: number[]): number {
  return Math.max(...array)
}

/**
 * 数组最小值
 * @param array 数值数组
 */
export function min(array: number[]): number {
  return Math.min(...array)
}

/**
 * 数组中位数
 * @param array 数值数组
 */
export function median(array: number[]): number {
  if (array.length === 0) return 0
  
  const sorted = [...array].sort((a, b) => a - b)
  const mid = Math.floor(sorted.length / 2)
  
  return sorted.length % 2 !== 0 
    ? sorted[mid] 
    : (sorted[mid - 1] + sorted[mid]) / 2
}

/**
 * 数组分组
 * @param array 要分组的数组
 * @param keyFn 获取分组key的函数
 */
export function groupBy<T, K extends string | number>(
  array: T[], 
  keyFn: (item: T) => K
): Record<K, T[]> {
  return array.reduce((groups, item) => {
    const key = keyFn(item)
    if (!groups[key]) {
      groups[key] = []
    }
    groups[key].push(item)
    return groups
  }, {} as Record<K, T[]>)
}

/**
 * 从数组中随机选择元素
 * @param array 数组
 * @param count 选择数量
 */
export function sample<T>(array: T[], count: number = 1): T[] {
  if (count >= array.length) return [...array]
  
  const shuffled = shuffleArray(array)
  return shuffled.slice(0, count)
}

// ====================================================================
// 数值格式化
// ====================================================================

/**
 * 格式化数字，添加千位分隔符
 * @param num 数字
 * @param separator 分隔符
 */
export function formatNumber(num: number, separator: string = ','): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, separator)
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数
 */
export function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 数字转中文
 * @param num 数字
 */
export function numberToChinese(num: number): string {
  const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const units = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿']
  
  if (num === 0) return digits[0]
  
  let result = ''
  let unitIndex = 0
  
  while (num > 0) {
    const digit = num % 10
    if (digit !== 0) {
      result = digits[digit] + units[unitIndex] + result
    } else if (result && !result.startsWith('零')) {
      result = digits[0] + result
    }
    num = Math.floor(num / 10)
    unitIndex++
  }
  
  return result.replace(/零+/g, '零').replace(/零$/, '')
}

// ====================================================================
// 时间相关计算
// ====================================================================

/**
 * 获取日期时间段
 * @param type 1-今天 2-昨天 3-本周 4-本月 5-本年
 */
export function getDateTimeSlot(type: number): { start: Date; end: Date } {
  const now = new Date()
  let start = now.toDateString()
  let end = now.toDateString()
  
  switch (type) {
    case 1: // 今天
      start = `${start} 00:00:00`
      end = `${end} 23:59:59`
      break
    case 2: // 昨天
      now.setTime(now.getTime() - 3600 * 1000 * 24 * 1)
      start = `${now.toDateString()} 00:00:00`
      end = `${now.toDateString()} 23:59:59`
      break
    case 3: // 本周
      const weekday = now.getDay() || 7
      now.setDate(now.getDate() - weekday + 1)
      start = `${now.toDateString()} 00:00:00`
      end = `${end} 23:59:59`
      break
    case 4: // 本月
      start = `${now.getFullYear()}-${now.getMonth() + 1}-01 00:00:00`
      end = `${end} 23:59:59`
      break
    case 5: // 本年
      start = `${now.getFullYear()}-01-01 00:00:00`
      end = `${end} 23:59:59`
      break
  }

  return {
    start: new Date(start.replace(/\-/g, '/')),
    end: new Date(end.replace(/\-/g, '/'))
  }
}

// ====================================================================
// 验证和检查
// ====================================================================

/**
 * 检查是否为数字
 * @param value 要检查的值
 */
export function isNumber(value: any): value is number {
  return typeof value === 'number' && !isNaN(value) && isFinite(value)
}

/**
 * 检查是否为整数
 * @param value 要检查的值
 */
export function isInteger(value: any): value is number {
  return isNumber(value) && Number.isInteger(value)
}

/**
 * 检查是否在范围内
 * @param value 要检查的值
 * @param min 最小值
 * @param max 最大值
 * @param inclusive 是否包含边界值
 */
export function isInRange(value: number, min: number, max: number, inclusive: boolean = true): boolean {
  if (inclusive) {
    return value >= min && value <= max
  } else {
    return value > min && value < max
  }
}

/**
 * 检查是否为偶数
 * @param value 要检查的值
 */
export function isEven(value: number): boolean {
  return isInteger(value) && value % 2 === 0
}

/**
 * 检查是否为奇数
 * @param value 要检查的值
 */
export function isOdd(value: number): boolean {
  return isInteger(value) && value % 2 !== 0
}

/**
 * 检查是否为质数
 * @param value 要检查的值
 */
export function isPrime(value: number): boolean {
  if (!isInteger(value) || value < 2) return false
  if (value === 2) return true
  if (value % 2 === 0) return false
  
  for (let i = 3; i <= Math.sqrt(value); i += 2) {
    if (value % i === 0) return false
  }
  
  return true
}
import recruiting from '@/static/svg/recruit.svg';
import renting from '@/static/svg/renting.svg';
import dating from '@/static/svg/dating.svg';
import newHouse from '@/static/svg/newHouse.svg';
import secondHandHouse from '@/static/svg/secondHandHouse.svg';
import gig from '@/static/svg/gig.svg';

// 商家入驻
import consociation from '@/static/svg/consociation.svg';
// 写字楼
import officeBuilding from '@/static/svg/officeBuilding.svg';

// Home 目录中的服务图标
import homeDecoration from '@/static/svg/home/<USER>';
import gigJobs from '@/static/svg/home/<USER>';
import nannyMaternity from '@/static/svg/home/<USER>';
import applianceRepair from '@/static/svg/home/<USER>';
import locksmith from '@/static/svg/home/<USER>';
import weddingPhotography from '@/static/svg/home/<USER>';
import cleaning from '@/static/svg/home/<USER>';
import recycle from '@/static/svg/home/<USER>';
import plumbing from '@/static/svg/home/<USER>';
import moving from '@/static/svg/home/<USER>';

// 缺省状态图标 - Default/Empty State Icons
// 系统类图标
import icon404 from '@/static/svg/default/404.svg';                      // 页面未找到
import iconLoading from '@/static/svg/default/loading.svg';              // 加载中状态
import iconLoadFailed from '@/static/svg/default/load-failed.svg';       // 加载失败状态

// 网络类图标
import iconNoNetwork from '@/static/svg/default/no-network.svg';         // 无网络连接
import iconNetworkError from '@/static/svg/default/network-error.svg';   // 网络连接错误
import iconNoSignal from '@/static/svg/default/no-signal.svg';           // 无信号

// 数据类图标
import iconNoData from '@/static/svg/default/no-data.svg';               // 暂无数据
import iconEmptyData from '@/static/svg/default/empty-data.svg';         // 空数据状态
import iconNoRecord from '@/static/svg/default/no-record.svg';           // 暂无记录
import iconNoSearchResult from '@/static/svg/default/no-search-result.svg'; // 暂无搜索结果

// 权限类图标
import iconNoPermission from '@/static/svg/default/no-permission.svg';   // 无权限访问

// 功能模块类图标
import iconNoMessage from '@/static/svg/default/no-message.svg';         // 暂无消息
import iconNoSchedule from '@/static/svg/default/no-schedule.svg';       // 暂无日程
import iconNoFavorite from '@/static/svg/default/no-favorite.svg';       // 没有收藏
import iconNoCoupon from '@/static/svg/default/no-coupon.svg';           // 暂无优惠券
import iconNoBankcard from '@/static/svg/default/no-bankcard.svg';       // 暂无银行卡

// 容器类图标
import iconFolderEmpty from '@/static/svg/default/folder-empty.svg';     // 文件夹为空
import iconCartEmpty from '@/static/svg/default/cart-empty.svg';         // 购物车为空

export const assetsSvg = {
    recruiting,
    renting,
    dating,
    newHouse,
    secondHandHouse,
    consociation,
    officeBuilding,
    gig,
    // Home 服务图标
    homeDecoration,
    gigJobs,
    nannyMaternity,
    applianceRepair,
    locksmith,
    weddingPhotography,
    cleaning,
    recycle,
    plumbing,
    moving,
};

/**
 * 缺省状态图标集合
 * 用于PageLayout组件的各种空状态展示
 * 按使用场景分类，便于维护和使用
 */
export const defaultIcons = {
    // 系统状态类 - System Status
    system: {
        404: icon404,                          // 页面未找到 - 适用于路由错误、页面不存在
        loading: iconLoading,                  // 加载中 - 适用于数据请求、页面初始化
        loadFailed: iconLoadFailed,            // 加载失败 - 适用于请求失败、服务异常
    },
    
    // 网络状态类 - Network Status  
    network: {
        noNetwork: iconNoNetwork,              // 无网络 - 适用于离线状态、网络断开
        networkError: iconNetworkError,        // 网络错误 - 适用于网络超时、连接异常
        noSignal: iconNoSignal,                // 无信号 - 适用于信号弱、通讯故障
    },
    
    // 数据状态类 - Data Status
    data: {
        noData: iconNoData,                    // 暂无数据 - 通用空数据状态
        emptyData: iconEmptyData,              // 空数据 - 数据表、列表为空
        noRecord: iconNoRecord,                // 暂无记录 - 历史记录、操作记录为空
        noSearchResult: iconNoSearchResult,    // 暂无搜索结果 - 搜索无匹配项
    },
    
    // 权限状态类 - Permission Status
    permission: {
        noPermission: iconNoPermission,        // 无权限 - 访问受限、需要登录
    },
    
    // 功能模块类 - Feature Modules
    feature: {
        noMessage: iconNoMessage,              // 暂无消息 - 消息中心、通知列表
        noSchedule: iconNoSchedule,            // 暂无日程 - 日历、待办事项
        noFavorite: iconNoFavorite,            // 没有收藏 - 收藏夹、心愿单
        noCoupon: iconNoCoupon,                // 暂无优惠券 - 优惠券列表
        noBankcard: iconNoBankcard,            // 暂无银行卡 - 支付方式、卡包
    },
    
    // 容器状态类 - Container Status
    container: {
        folderEmpty: iconFolderEmpty,          // 文件夹空 - 文件管理、目录浏览
        cartEmpty: iconCartEmpty,              // 购物车空 - 购物车、订单列表
    }
};

/**
 * 根据场景获取合适的缺省图标
 * @param category 图标分类 
 * @param type 图标类型
 * @returns 图标路径
 */
export function getDefaultIcon(category: keyof typeof defaultIcons, type: string): string {
    const categoryIcons = defaultIcons[category];
    if (!categoryIcons) {
        console.warn(`Unknown icon category: ${category}`);
        return defaultIcons.data.noData; // 返回默认图标
    }
    
    const icon = (categoryIcons as any)[type];
    if (!icon) {
        console.warn(`Unknown icon type: ${type} in category: ${category}`);
        return defaultIcons.data.noData; // 返回默认图标
    }
    
    return icon;
}

/**
 * 常用缺省状态图标快捷方式
 * 提供最常用的图标，便于快速使用
 */
export const quickIcons = {
    noData: defaultIcons.data.noData,           // 最通用的无数据状态
    loading: defaultIcons.system.loading,       // 加载中状态
    networkError: defaultIcons.network.networkError, // 网络错误
    loadFailed: defaultIcons.system.loadFailed, // 加载失败
    noPermission: defaultIcons.permission.noPermission, // 无权限
};
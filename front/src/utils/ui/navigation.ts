/**
 * 页面跳转工具
 * 统一管理页面跳转逻辑，包括登录拦截、权限检查等
 */

import { useUserStore } from '@/stores/user'
import { useGlobalStore } from '@/stores/global'

export interface NavigationOptions {
  url: string
  requireLogin?: boolean    // 是否需要登录
  requireVip?: boolean      // 是否需要VIP
  openType?: 'navigate' | 'redirect' | 'reLaunch' | 'switchTab' | 'navigateBack'
  delta?: number           // navigateBack 时的层数
  animationType?: 'pop-in' | 'auto' | 'none' | 'slide-in-right' | 'slide-in-left' | 'slide-in-top' | 'slide-in-bottom' | 'fade-in' | 'zoom-out' | 'zoom-fade-out'   // 动画类型
  animationDuration?: number
  success?: () => void
  fail?: (error: any) => void
  complete?: () => void
}

/**
 * 统一跳转方法
 */
export const navigateTo = (options: NavigationOptions | string) => {
  const userStore = useUserStore()
  const globalStore = useGlobalStore()

  // 如果传入的是字符串，转换为配置对象
  const config: NavigationOptions = typeof options === 'string'
    ? { url: options }
    : options

  const {
    url,
    requireLogin = false,
    requireVip = false,
    openType = 'navigate',
    delta = 1,
    animationType = 'pop-in',
    animationDuration = 300,
    success,
    fail,
    complete
  } = config

  // 登录检查
  // if (requireLogin && !userStore.isLogin) {
  //   showLoginModal()
  //   return
  // }

  // VIP检查
  if (requireVip && !userStore.user?.isVip) {
    uni.showModal({
      title: '提示',
      content: '此功能仅限VIP用户使用，是否前往开通VIP？',
      success: (res) => {
        if (res.confirm) {
          navigateTo('/pages/vip/index')
        }
      }
    })
    return
  }

  // 执行跳转
  const navigationParams = {
    url,
    animationType,
    animationDuration,
    success,
    fail,
    complete
  }

  switch (openType) {
    case 'navigate':
      uni.navigateTo(navigationParams)
      break
    case 'redirect':
      uni.redirectTo(navigationParams)
      break
    case 'reLaunch':
      uni.reLaunch(navigationParams)
      break
    case 'switchTab':
      uni.switchTab({ url, success, fail, complete })
      break
    case 'navigateBack':
      uni.navigateBack({ delta, success, fail, complete })
      break
    default:
      uni.navigateTo(navigationParams)
  }
}

/**
 * 需要登录的跳转
 */
export const secureNavigateTo = (url: string, options?: Omit<NavigationOptions, 'url' | 'requireLogin'>) => {
  navigateTo({
    url,
    requireLogin: true,
    ...options
  })
}

/**
 * 需要登录的跳转（直接启动登录流程，无确认对话框）
 * 适用于用户主动点击登录相关元素的场景
 */
export const secureNavigateToWithDirectLogin = (url: string, options?: Omit<NavigationOptions, 'url' | 'requireLogin'>) => {
  const userStore = useUserStore()

  // if (!userStore.isLogin) {
  //   startLoginProcess()
  //   return
  // }

  navigateTo({
    url,
    ...options
  })
}

/**
 * 需要VIP的跳转
 */
export const vipNavigateTo = (url: string, options?: Omit<NavigationOptions, 'url' | 'requireVip'>) => {
  navigateTo({
    url,
    requireLogin: true,
    requireVip: true,
    ...options
  })
}

/**
 * Tab页跳转
 */
export const switchTab = (url: string, options?: Pick<NavigationOptions, 'success' | 'fail' | 'complete'>) => {
  navigateTo({
    url,
    openType: 'switchTab',
    ...options
  })
}

/**
 * 返回上一页
 */
export const navigateBack = (delta: number = 1, options?: Pick<NavigationOptions, 'success' | 'fail' | 'complete'>) => {
  navigateTo({
    url: '', // navigateBack 不需要url
    openType: 'navigateBack',
    delta,
    ...options
  })
}

/**
 * 重启应用到指定页面
 */
export const reLaunch = (url: string, options?: Pick<NavigationOptions, 'success' | 'fail' | 'complete'>) => {
  navigateTo({
    url,
    openType: 'reLaunch',
    ...options
  })
}

// =====================================================
// 注意：登录相关业务逻辑已迁移到 @/services/auth
// showLoginModal 和 startLoginProcess 已从 auth 服务导入
// =====================================================

/**
 * 检查页面是否需要登录 - 纯导航相关逻辑
 */
export const checkPageAccess = (pagePath: string): boolean => {
  const userStore = useUserStore()

  // 定义需要登录的页面
  const loginRequiredPages = [
    '/pages/mine/profile',
    '/pages/mine/collections',
    '/pages/post/mine',
    '/pages/gig/publish',
    '/pages/gig/manage',
    '/pages/gig/my-applications',
    '/pages/chat/index',
    '/pages/verification/index'
  ]

  // 定义需要VIP的页面
  const vipRequiredPages = [
    '/pages/vip/index'
  ]

  if (loginRequiredPages.some(page => pagePath.startsWith(page))) {
    return userStore.isLogin
  }

  if (vipRequiredPages.some(page => pagePath.startsWith(page))) {
    return userStore.isLogin && userStore.user?.isVip
  }

  return true
}

/**
 * 页面跳转拦截器
 * 可以在app.vue中使用，拦截所有页面跳转
 */
export const setupNavigationGuard = () => {
  // 这里可以添加全局的页面跳转拦截逻辑
  // 例如：统计页面访问、检查页面权限等
}

/**
 * 构建带参数的URL
 */
export const buildUrl = (basePath: string, params: Record<string, any> = {}) => {
  const query = Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null)
    .map(key => `${key}=${encodeURIComponent(params[key])}`)
    .join('&')

  return query ? `${basePath}?${query}` : basePath
}

/**
 * 解析URL参数
 */
export const parseUrlParams = (url: string): Record<string, string> => {
  const params: Record<string, string> = {}
  const queryString = url.split('?')[1]

  if (queryString) {
    queryString.split('&').forEach(param => {
      const [key, value] = param.split('=')
      if (key && value) {
        params[key] = decodeURIComponent(value)
      }
    })
  }

  return params
}
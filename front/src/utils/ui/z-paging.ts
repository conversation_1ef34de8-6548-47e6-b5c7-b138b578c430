/**
 * z-paging通用集成工具
 * 提供标准化的z-paging与API结合使用的最佳实践
 */
import { ref, type Ref } from 'vue';
import type { ApiResponse } from '@/constants/response';

/**
 * z-paging查询函数的标准类型
 */
export type ZPagingQueryFunction<T, P = any> = (
  params: P & { page: number; page_size: number }
) => Promise<ApiResponse<{ list: T[]; total?: number }>>

/**
 * z-paging集成配置
 */
export interface ZPagingConfig<T, P = any> {
  /** API查询函数 */
  queryFn: ZPagingQueryFunction<T, P>;
  /** 基础查询参数 */
  baseParams?: P;
  /** 默认页面大小 */
  defaultPageSize?: number;
  /** 初始数据 */
  initialData?: T[];
}

/**
 * 创建标准化的z-paging集成Hook
 * @param config z-paging配置
 * @returns z-paging集成对象
 */
export function useZPaging<T = any, P = any>(config: ZPagingConfig<T, P>) {
  const { queryFn, baseParams = {} as P, defaultPageSize = 10, initialData = [] } = config;
  
  // z-paging相关的响应式变量
  const pagingRef = ref<any>(null);
  const dataList = ref<T[]>(initialData);
  const searchParams = ref<P>(baseParams);
  const isLoading = ref(false);
  
  /**
   * 标准的z-paging查询方法
   * 符合z-paging最佳实践
   */
  const queryList = async (pageNo: number, pageSize: number) => {
    try {
      isLoading.value = true;
      
      // 构建完整的查询参数
      const params = {
        ...searchParams.value,
        page: pageNo,
        page_size: pageSize,
      } as P & { page: number; page_size: number };
      
      // 调用API查询函数
      const response = await queryFn(params);
      const resultList = response.data?.list || [];
      
      // 将结果传递给z-paging
      if (pagingRef.value) {
        pagingRef.value.complete(resultList);
      }
    } catch (error) {
      console.error('z-paging查询失败:', error);
      if (pagingRef.value) {
        pagingRef.value.complete(false);
      }
    } finally {
      isLoading.value = false;
    }
  };
  
  /**
   * 更新搜索参数并重新加载
   */
  const updateParams = (newParams: Partial<P>) => {
    searchParams.value = { ...searchParams.value, ...newParams };
    reload();
  };
  
  /**
   * 重新加载列表
   */
  const reload = () => {
    if (pagingRef.value) {
      pagingRef.value.reload();
    }
  };
  
  /**
   * 刷新列表（下拉刷新）
   */
  const refresh = () => {
    if (pagingRef.value) {
      pagingRef.value.refresh();
    }
  };
  
  return {
    // 响应式数据
    pagingRef,
    dataList,
    searchParams,
    isLoading,
    
    // 方法
    queryList,
    updateParams,
    reload,
    refresh,
    
    // z-paging标准配置
    zpagingConfig: {
      defaultPageSize,
      safeAreaInsetBottom: true,
      refresherEnabled: true,
      auto: true,
    }
  };
}

/**
 * z-paging组件的标准Props
 */
export const standardZPagingProps = {
  safeAreaInsetBottom: true,
  refresherEnabled: true,
  auto: true,
  defaultPageSize: 10,
};

/**
 * z-paging错误处理工具
 */
export const handleZPagingError = (error: any, pagingRef: Ref<any>) => {
  console.error('z-paging操作失败:', error);
  if (pagingRef.value) {
    pagingRef.value.complete(false);
  }
};
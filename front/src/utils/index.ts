/**
 * Utils 工具函数统一导出
 * 新的分层架构：core -> business -> ui -> network
 */

// ====================================================================
// 核心工具函数 (core/)
// ====================================================================

// 日期时间工具
export * from './core/date';

// 字符串处理工具
export * from './core/string';

// 数据格式化工具
export * from './core/format';

// 表单验证工具
export * from './core/validation';

// ====================================================================
// 业务相关工具 (business/)
// ====================================================================

// 零工业务工具
export * from './business/gig';

// ====================================================================
// UI 交互工具 (ui/)
// ====================================================================

// 用户反馈工具 (toast, loading, dialog)
export * from './ui/feedback';

// 页面导航工具
export * from './ui/navigation';

// z-paging集成工具
export * from './ui/z-paging';

// 页面状态管理工具
export * from './ui/page-state';

// ====================================================================
// 网络请求工具 (network/)
// ====================================================================

// HTTP 客户端配置
export * from './network/client';

// 类型安全的请求封装
export * from './network/request';

// API 调用辅助工具
export * from './network/helpers';

// ====================================================================
// 其他工具函数
// ====================================================================

// 资源工具
export * from './assetsUtil';

// ====================================================================
// 兼容性导出 - 逐步迁移到对应的 core 模块
// ====================================================================

// 颜色处理函数 - 已迁移到 core/color.ts
export { rgbToHex, hexToRGB } from './core/color'

// 性能优化函数 - 已迁移到 core/performance.ts  
export { debounce, throttle } from './core/performance'

// 数学和数据处理函数 - 已迁移到 core/math.ts
export { 
  generateUniqueId as unique, 
  distinctArray, 
  getDateTimeSlot 
} from './core/math'

// 防抖和节流函数已迁移到 core/performance.ts
// 这里保留兼容性导出，实际实现请使用 core/performance.ts 中的版本
import { defineStore } from 'pinia';

export const useGlobalStore = defineStore('global', {
    state: () => ({
        isPrivacyPopupVisible: false,
        isDevLoginVisible: false, // 开发登录弹窗可见性
        loginCode: '', // 微信登录code
        isUserRegistered: false, // 用户是否已注册
        hideLoginTip: false, // 是否隐藏登录提示条
        gigRole: 'seeker' as 'seeker' | 'recruiter', // 零工角色：找零工或招人
    }),
    actions: {
        showPrivacyPopup() {
            uni.hideTabBar()
            this.isPrivacyPopupVisible = true;
        },
        hidePrivacyPopup() {
            uni.showTabBar()
            this.isPrivacyPopupVisible = false;
        },
        showDevLogin() {
            uni.hideTabBar()
            this.isDevLoginVisible = true;
        },
        hideDevLogin() {
            uni.showTabBar()
            this.isDevLoginVisible = false;
        },
        setLoginCode(code: string) {
            this.loginCode = code;
        },
        setUserRegistered(isRegistered: boolean) {
            this.isUserRegistered = isRegistered;
        },
        setHideLoginTip(hide: boolean) {
            this.hideLoginTip = hide;
        },
        setGigRole(role: 'seeker' | 'recruiter') {
            this.gigRole = role;
            // 持久化存储到本地
            try {
                uni.setStorageSync('gigRole', role);
            } catch (error) {
                console.error('保存零工角色失败:', error);
            }
        },
        loadGigRole() {
            // 从本地存储加载零工角色
            try {
                const savedRole = uni.getStorageSync('gigRole');
                if (savedRole && (savedRole === 'seeker' || savedRole === 'recruiter')) {
                    this.gigRole = savedRole;
                }
            } catch (error) {
                console.error('加载零工角色失败:', error);
            }
        },
    },
}); 
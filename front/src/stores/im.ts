import { defineStore } from 'pinia';
import type { Conversation, Message, User, TextMessagePayload, ImageMessagePayload, VideoMessagePayload, JobMessagePayload } from '@/types/im';
import { imService } from '@/services/imService';
import { centrifugoService } from '@/services/centrifugoService';

interface ImState {
  currentUser: User;
  conversations: Conversation[];
  messages: Record<string, Message[]>;
  currentConversationId: string | null;
}

export const useImStore = defineStore('im', {
  state: (): ImState => ({
    currentUser: {
      id: 'user-1',
      name: 'Me',
      avatar: 'https://picsum.photos/seed/user-1/100/100'
    },
    conversations: [],
    messages: {},
    currentConversationId: null,
  }),

  getters: {
    getConversationById: (state) => (id: string) => {
      return state.conversations.find(c => c.id === id);
    },
    getCurrentMessages: (state): Message[] => {
      if (!state.currentConversationId) return [];
      return state.messages[state.currentConversationId] || [];
    },
  },

  actions: {
    setCurrentConversation(conversationId: string) {
      this.currentConversationId = conversationId;
      if (!this.messages[conversationId]) {
        this.messages[conversationId] = [];
      }
    },

    clearCurrentConversation() {
      this.currentConversationId = null;
    },

    async fetchConversations() {
      this.conversations = await imService.fetchConversations();
    },

    async fetchHistoryMessages(conversationId: string, pageNo: number, pageSize: number): Promise<Message[]> {
      const newMessages = await imService.fetchMessages(conversationId, pageNo, pageSize);
      // Add some mock image and video messages for testing
      if (pageNo === 1) {
        newMessages.unshift({
          id: `mock-img-${Date.now()}`,
          type: 'image',
          sender: { id: 'user-2', name: 'Alice', avatar: '' },
          payload: { url: 'https://picsum.photos/id/237/400/300', imageWidth: 400, imageHeight: 300 },
          timestamp: Date.now() - 50000,
          status: 'sent',
        });
        newMessages.unshift({
          id: `mock-video-${Date.now()}`,
          type: 'video',
          sender: { id: 'user-1', name: 'Me', avatar: '' },
          payload: { videoUrl: 'http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4', thumbnailUrl: 'https://picsum.photos/id/238/400/300', videoWidth: 400, videoHeight: 300, duration: 60 },
          timestamp: Date.now() - 100000,
          status: 'sent',
        });
        newMessages.unshift({
          id: `mock-job-${Date.now()}`,
          type: 'job',
          sender: { id: 'user-2', name: 'Alice', avatar: '' },
          payload: { jobId: 'job-123', title: '前端开发工程师', salary: '15-30K', companyName: '某某科技', companyLogo: 'https://picsum.photos/seed/company/100/100', location: '杭州' },
          timestamp: Date.now() - 150000,
          status: 'sent',
        });
      }

      this.messages[conversationId] = [...newMessages, ...this.messages[conversationId]];
      return newMessages;
    },

    async sendMessage(conversationId: string, payload: TextMessagePayload | ImageMessagePayload | VideoMessagePayload | JobMessagePayload) {
      const newMessage: Message = {
        id: `msg-${Date.now()}`,
        type: payload.type,
        sender: this.currentUser,
        payload: payload,
        timestamp: Date.now(),
        status: 'sending',
      };

      // Add message to UI immediately
      if (!this.messages[conversationId]) {
        this.messages[conversationId] = [];
      }
      this.messages[conversationId].push(newMessage);

      try {
        // Publish message via Centrifugo
        await centrifugoService.publish(`chat:${conversationId}`, newMessage);
        // Update status to sent (Centrifugo usually sends an ack or the message back)
        const index = this.messages[conversationId].findIndex(m => m.id === newMessage.id);
        if (index !== -1) {
          this.messages[conversationId][index].status = 'sent';
        }
      } catch (error) {
        console.error('Failed to send message:', error);
        const index = this.messages[conversationId].findIndex(m => m.id === newMessage.id);
        if (index !== -1) {
          this.messages[conversationId][index].status = 'failed';
        }
      }
    },

    addIncomingMessage(message: Message) {
      if (!this.messages[message.id]) {
        this.messages[message.id] = [];
      }
      this.messages[message.id].push(message);
      // Update last message and unread count for conversation list
      const conversation = this.conversations.find(c => c.id === message.id);
      if (conversation) {
        conversation.lastMessage = message;
        if (message.sender.id !== this.currentUser.id) {
          conversation.unreadCount++;
        }
      }
    },
  },
});

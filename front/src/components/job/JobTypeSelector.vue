<template>
  <view class="job-type-selector">
    <view class="job-type-options flex flex-wrap">
      <view
        v-for="type in jobTypes"
        :key="type.id"
        class="tag mx-20rpx"
        :class="{ 'job-type-active': modelValue === type.value }"
        :style="
          modelValue === type.value ? { borderColor: `var(--primary)` } : {}
        "
        @tap="handleSelect(type)"
      >
        <text class="job-type-label">{{ type.label }}</text>
      </view>
    </view>

    <!-- <view class="job-type-description" v-if="selectedType">
      <text class="description-text">{{ selectedType.description }}</text>
    </view> -->
  </view>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from "vue";
import { jobTypes } from "@/constants/static/job";

const props = defineProps({
  modelValue: {
    type: Number,
    default: 1,
  },
});

const emit = defineEmits(["update:modelValue", "change"]);

// 获取当前选中的类型
const selectedType = computed(() => {
  return jobTypes.find((type) => type.value === props.modelValue);
});

const handleSelect = (type) => {
  emit("update:modelValue", type.value);
  emit("change", type);
};
</script>

<style lang="scss" scoped>
.job-type-selector {
  width: 100%;
}

.job-type-options {
  display: flex;
  flex-wrap: wrap;
}

.job-type-option {
  padding: 15rpx 40rpx;
  background-color: var(--bg-tag);
  border-radius: 8rpx;
  margin-right: 20rpx;
  margin-bottom: 15rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
  border: 1px solid transparent;
  transition: all 0.3s;
}

.job-type-active {
  background-color: rgba(255, 109, 0, 0.1);
  color: var(--primary);
  font-weight: 500;
}
</style>

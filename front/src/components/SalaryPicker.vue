<template>
  <view class="salary-picker">
    <!-- 薪资范围选择器 -->
    <picker
      mode="multiSelector"
      :value="[minSalaryIndex, maxSalaryIndex]"
      :range="[salaryOptions, salaryOptions]"
      range-key="label"
      @change="onSalaryChange"
      @columnchange="onColumnChange"
    >
      <slot>
        <view class="picker-display">
          <text :class="{ placeholder: !displayText }">{{
            displayText || placeholder
          }}</text>
          <text class="i-carbon-chevron-right action-icon"></text>
        </view>
      </slot>
    </picker>
  </view>
</template>

<script setup lang="ts">
interface SalaryOption {
  label: string;
  value: number;
}

interface Props {
  modelValue?: string;
  placeholder?: string;
}

interface Emits {
  (e: "update:modelValue", value: string): void;
  (e: "change", value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: "请选择期望薪资",
});

const emit = defineEmits<Emits>();

// 薪资选项（以千为单位，便于选择）
const salaryOptions: SalaryOption[] = [
  { label: "2K", value: 2000 },
  { label: "3K", value: 3000 },
  { label: "4K", value: 4000 },
  { label: "5K", value: 5000 },
  { label: "6K", value: 6000 },
  { label: "7K", value: 7000 },
  { label: "8K", value: 8000 },
  { label: "9K", value: 9000 },
  { label: "10K", value: 10000 },
  { label: "12K", value: 12000 },
  { label: "15K", value: 15000 },
  { label: "18K", value: 18000 },
  { label: "20K", value: 20000 },
  { label: "25K", value: 25000 },
  { label: "30K", value: 30000 },
  { label: "35K", value: 35000 },
  { label: "40K", value: 40000 },
  { label: "50K", value: 50000 },
];

const minSalaryIndex = ref(0);
const maxSalaryIndex = ref(4); // 默认选择6K

// 显示文本
const displayText = computed(() => {
  if (!props.modelValue) return "";
  return props.modelValue;
});

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      parseModelValue(newValue);
    }
  },
  { immediate: true }
);

// 解析modelValue
function parseModelValue(value: string) {
  if (!value) return;

  // 解析格式如 "3K-6K" 或 "5K以上"
  if (value.includes("-")) {
    const [minStr, maxStr] = value.split("-");
    const minValue = parseFloat(minStr.replace("K", "")) * 1000;
    const maxValue = parseFloat(maxStr.replace("K", "")) * 1000;

    minSalaryIndex.value = salaryOptions.findIndex(
      (opt) => opt.value === minValue
    );
    maxSalaryIndex.value = salaryOptions.findIndex(
      (opt) => opt.value === maxValue
    );
  } else if (value.includes("以上")) {
    const minStr = value.replace("以上", "");
    const minValue = parseFloat(minStr.replace("K", "")) * 1000;
    minSalaryIndex.value = salaryOptions.findIndex(
      (opt) => opt.value === minValue
    );
    maxSalaryIndex.value = salaryOptions.length - 1;
  }

  // 确保索引有效
  if (minSalaryIndex.value < 0) minSalaryIndex.value = 0;
  if (maxSalaryIndex.value < 0) maxSalaryIndex.value = 4;
}

// 列变化事件
function onColumnChange(e: any) {
  const { column, value } = e.detail;
  if (column === 0) {
    minSalaryIndex.value = value;
    // 确保最小值不大于最大值
    if (minSalaryIndex.value > maxSalaryIndex.value) {
      maxSalaryIndex.value = minSalaryIndex.value;
    }
  } else if (column === 1) {
    maxSalaryIndex.value = value;
    // 确保最大值不小于最小值
    if (maxSalaryIndex.value < minSalaryIndex.value) {
      minSalaryIndex.value = maxSalaryIndex.value;
    }
  }
}

// 薪资变化事件
function onSalaryChange(e: any) {
  const [minIndex, maxIndex] = e.detail.value;
  minSalaryIndex.value = minIndex;
  maxSalaryIndex.value = maxIndex;

  const minSalary = salaryOptions[minIndex];
  const maxSalary = salaryOptions[maxIndex];

  let result = "";

  if (minIndex === maxIndex) {
    // 相同薪资
    result = minSalary.label;
  } else if (maxIndex === salaryOptions.length - 1) {
    // 最高薪资选择了最后一个，显示"以上"
    result = `${minSalary.label}以上`;
  } else {
    // 薪资范围
    result = `${minSalary.label}-${maxSalary.label}`;
  }

  emit("update:modelValue", result);
  emit("change", result);
}
</script>

<style lang="scss" scoped>
.salary-picker {
  .picker-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .placeholder {
      color: var(--text-info);
    }

    .action-icon {
      color: var(--text-grey);
      font-size: 32rpx;
    }
  }
}
</style>

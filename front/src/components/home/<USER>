<template>
  <view class="house-container bg-card mb-20rpx">
    <view
      class="section-header flex justify-between items-center px-20rpx py-20rpx"
    >
      <text class="text-32rpx font-500">热门房源</text>
      <view class="flex items-center" @tap="navigateTo('/pages/house/index')">
        <text class="text-26rpx text-grey">全部</text>
        <text class="i-carbon-chevron-right text-grey"></text>
      </view>
    </view>
    <!-- 轮播图 - 调整为黄金比例 -->
    <!-- <swiper
        class="banner"
        circular
        :indicator-dots="true"
        :autoplay="true"
        :interval="3000"
        :duration="500"
      >
        <swiper-item
          v-for="(item, index) in banners"
          :key="index"
          class="cursor-pointer"
        >
          <image :src="item.image" mode="aspectFill"></image>
        </swiper-item>
      </swiper> -->

    <!-- 推荐房源列表 -->
    <view class="house-list px-20rpx pb-20rpx">
      <HouseCard
        v-for="(house, index) in houseList"
        :key="index"
        :house="house"
        :type="getHouseType(house.type)"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import HouseCard from "@/components/house/HouseCard.vue";

// 导航方法
const navigateTo = (path: string) => {
  uni.navigateTo({
    url: path,
  });
};
// 获取房源类型
const getHouseType = (type: string) => {
  return type;
};

// 房源列表数据 - 增加房源类型标识和详细信息
const houseList = reactive([
  {
    id: "1",
    title: "金茂府 南北通透三居室 精装修 拎包入住",
    community: "金茂府",
    layout: "3室2厅2卫",
    area: "120",
    floor: "中楼层",
    direction: "南北",
    location: "朝阳区 · 金茂府",
    tags: ["南北通透", "精装修", "近地铁"],
    price: "890",
    unitPrice: "74167元/㎡",
    type: "二手",
    isHot: true,
    image:
      "https://readdy.ai/api/search-image?query=modern%20apartment%20interior%20with%20large%20windows%2C%20bright%20living%20room%20with%20contemporary%20furniture%2C%20hardwood%20floors%2C%20minimalist%20design%2C%20natural%20lighting%2C%20spacious%20and%20clean%2C%20real%20estate%20photography%20style%2C%20professional%20property%20image&width=240&height=180&seq=8&orientation=landscape",
  },
  {
    id: "2",
    title: "整租2居 · 香山南营66号",
    community: "香山南营66号",
    layout: "2室1厅1卫",
    area: "70",
    floor: "高楼层",
    direction: "南北",
    rentType: "整租",
    location: "西山",
    tags: ["南北", "高楼层", "步梯"],
    price: "4600",
    paymentMethod: "押一付一",
    utilities: "民水民电",
    extraInfo: "今日新上",
    type: "整租",
    hasVR: true,
    image:
      "https://readdy.ai/api/search-image?query=luxury%20condominium%20with%20city%20view%2C%20modern%20high-rise%20apartment%2C%20large%20balcony%2C%20floor%20to%20ceiling%20windows%2C%20contemporary%20design%2C%20evening%20lighting%2C%20urban%20living%2C%20real%20estate%20photography&width=240&height=180&seq=9&orientation=landscape",
  },
  {
    id: "3",
    title: "保利熙悦 三室两厅 低楼层 采光好 交通便利",
    community: "保利熙悦",
    layout: "3室2厅",
    area: "95",
    floor: "低楼层",
    direction: "南",
    location: "丰台区 · 保利熙悦",
    tags: ["低楼层", "采光好", "交通便利"],
    price: "720",
    unitPrice: "75789元/㎡",
    type: "新房",
    image:
      "https://readdy.ai/api/search-image?query=bright%20apartment%20interior%20with%20large%20windows%2C%20modern%20living%20room%2C%20natural%20light%2C%20white%20walls%2C%20wooden%20floors%2C%20contemporary%20furniture%2C%20clean%20and%20spacious%2C%20real%20estate%20photography%20style&width=240&height=180&seq=10&orientation=landscape",
  },
  {
    id: "4",
    title: "临街商铺 适合餐饮零售",
    community: "商业街",
    layout: "一层",
    area: "50",
    floor: "1层",
    location: "朝阳区 · 商业街",
    tags: ["临街", "人流量大", "适合餐饮"],
    price: "8000",
    priceType: "rent",
    type: "商铺",
    image:
      "https://readdy.ai/api/search-image?query=small%20studio%20apartment%20with%20modern%20design%2C%20compact%20living%20space%2C%20efficient%20layout%2C%20contemporary%20furniture%2C%20bright%20colors%2C%20clean%20lines%2C%20real%20estate%20photography%20style&width=240&height=180&seq=11&orientation=landscape",
  },
]);

// 加载状态
const loading = ref(false);
const isRefreshing = ref(false);

// 加载更多
const loadMore = () => {
  if (loading.value) return;
  loading.value = true;
  // 模拟加载更多数据
  const newHouses = [
    {
      id: "5",
      title: "首开华润城 精装三居 南向 采光好 近地铁",
      community: "首开华润城",
      layout: "3室2厅1卫",
      area: "110",
      floor: "中楼层",
      direction: "南向",
      location: "大兴区 · 首开华润城",
      tags: ["精装修", "南向", "近地铁"],
      price: "680",
      unitPrice: "61818元/㎡",
      type: "二手",
      isHot: false,
      discount: "降价14万",
      image:
        "https://readdy.ai/api/search-image?query=modern%20three%20bedroom%20apartment%20interior%2C%20bright%20living%20space%2C%20contemporary%20design%2C%20large%20windows%2C%20natural%20light%2C%20clean%20aesthetic%2C%20real%20estate%20photography%20style&width=240&height=180&seq=14&orientation=landscape",
    },
    {
      id: "6",
      title: "万科翡翠公园 全新毛坯 大三居 视野开阔",
      community: "万科翡翠公园",
      layout: "3室2厅2卫",
      area: "140",
      floor: "高楼层",
      direction: "东南",
      location: "顺义区 · 万科翡翠公园",
      tags: ["毛坯房", "大三居", "视野开阔"],
      price: "950",
      unitPrice: "67857元/㎡",
      type: "新房",
      isHot: true,
      image:
        "https://readdy.ai/api/search-image?query=empty%20apartment%20interior%20with%20large%20windows%2C%20unfurnished%20space%2C%20white%20walls%2C%20concrete%20floors%2C%20spacious%20rooms%2C%20bright%20natural%20lighting%2C%20real%20estate%20photography%20style&width=240&height=180&seq=15&orientation=landscape",
    },
  ];
  houseList.push(...newHouses);
  loading.value = false;
};

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true;
  setTimeout(() => {
    // 模拟刷新数据
    isRefreshing.value = false;
  }, 1000);
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  flex: 1;
  height: calc(100vh - var(--window-top) - var(--window-bottom));
}

/* 轮播图 - 调整为黄金比例 */
.banner {
  width: 98%;
  height: 280rpx; /* 调整为黄金比例的高度 750/1.618 ≈ 464，但为了更节省空间，调整为280rpx */
  margin: 20rpx;
}

.banner image {
  width: 100%;
  height: 100%;
  border-radius: 0 0 16rpx 16rpx;
}

/* 房源类型导航 - 优化布局和图标 */
.house-types {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 30rpx 10rpx 20rpx;
  background-color: var(--bg-card);
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-radius: 16rpx;
  margin: 20rpx;
}

.type-item {
  width: 20%;
  margin-bottom: 20rpx;
}

.type-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon-wrapper {
  width: 90rpx;
  height: 90rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.bg-orange {
  background: linear-gradient(135deg, #ff9f43, #ff6b6b);
}

.bg-blue {
  background: linear-gradient(135deg, #54a0ff, #2e86de);
}

.bg-green {
  background: linear-gradient(135deg, #1dd1a1, #10ac84);
}

.bg-purple {
  background: linear-gradient(135deg, #5f27cd, #8854d0);
}

.bg-cyan {
  background: linear-gradient(135deg, #00d2d3, #01a3a4);
}

.type-icon {
  width: 50rpx;
  height: 50rpx;
}

.type-name {
  font-size: 24rpx;

  text-align: center;
  font-weight: 500;
}

/* 标签页 - 与图片样式一致 */
.tab-bar {
  display: flex;
  gap: 20rpx;
  margin: 28rpx 0;
  position: sticky;
  top: 0;
  z-index: 99;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid var(--border-color);
  background-color: var(--bg-page);
}

.tab-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  padding: 0 20rpx;
}

.tab-item text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.tab-item.active text {
  font-weight: 500;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: -12rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: var(--primary);
  border-radius: 2rpx;
}

/* 房源列表 - 优化样式 */
.house-list {
  padding: 16rpx 20rpx;
}

.house-card {
  display: flex;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: var(--bg-card);
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.house-image {
  width: 220rpx;
  height: 170rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.house-info {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 170rpx;
}

.house-title-row {
  display: flex;
  align-items: center;
}

.house-type-tag {
  font-size: 22rpx;
  color: var(--text-inverse);
  background-color: var(--primary);
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.house-title {
  font-size: 30rpx;

  font-weight: 600;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.house-location {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-top: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 6rpx;
}

.tag {
  font-size: 22rpx;
  color: #3b7fff;
  background-color: rgba(59, 127, 255, 0.1);
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  margin-bottom: 6rpx;
}

.house-price-info {
  display: flex;
  align-items: baseline;
  margin-top: 6rpx;
}

.house-price {
  font-size: 32rpx;
  color: var(--primary);
  font-weight: 600;
}

.house-unit {
  font-size: 24rpx;
  color: var(--text-grey);
  margin-left: 6rpx;
}

.house-discount {
  font-size: 22rpx;
  color: var(--text-red);
  margin-left: 16rpx;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  color: var(--text-grey);
  font-size: 28rpx;
}

.loading text {
  margin-left: 10rpx;
}
</style>

<template>
  <view class="gig-container bg-card mb-20rpx">
    <view
      class="section-header flex justify-between items-center px-20rpx py-20rpx"
    >
      <text class="text-32rpx font-500">热门零工</text>
      <view class="flex items-center" @tap="navigateTo('/pages/gig/index')">
        <text class="text-26rpx text-grey">全部</text>
        <text class="i-carbon-chevron-right text-grey"></text>
      </view>
    </view>
    <view class="gig-list px-20rpx pb-20rpx">
      <view
        v-for="(gig, index) in gigList"
        :key="index"
        class="gig-item p-20rpx mb-20rpx bg-white rounded-lg shadow-sm"
        @tap="navigateTo(gig.link)"
      >
        <view class="flex justify-between items-start">
          <view class="flex-1">
            <text class="text-32rpx font-500 text-base line-clamp-2">{{
              gig.title
            }}</text>
            <view class="flex items-center mt-10rpx">
              <text class="text-36rpx font-500 text-primary">{{
                gig.price
              }}</text>
              <text class="text-24rpx text-grey ml-10rpx">{{ gig.unit }}</text>
            </view>
            <view class="gig-tags flex flex-wrap mt-10rpx">
              <view
                v-for="(tag, tagIndex) in gig.tags"
                :key="tagIndex"
                class="gig-tag mr-10rpx mb-10rpx"
              >
                {{ tag }}
              </view>
            </view>
            <view class="flex items-center mt-10rpx">
              <text
                class="i-carbon-location text-grey text-24rpx mr-6rpx"
              ></text>
              <text class="text-grey text-26rpx">{{ gig.location }}</text>
              <text class="text-grey text-26rpx ml-20rpx">{{ gig.time }}</text>
            </view>
          </view>
          <view class="gig-avatar">
            <image
              :src="gig.avatar"
              mode="aspectFill"
              class="w-80rpx h-80rpx rounded-full"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 导航方法
const navigateTo = (path: string) => {
  if (!path) return;
  uni.navigateTo({
    url: path,
  });
};

// 热门零工列表
const gigList = [
  {
    title: "搬家拉货 | 面包车/小货车 | 经验丰富",
    price: "80-120",
    unit: "次",
    location: "海淀区 · 清河",
    time: "可立即服务",
    tags: ["包搬包装", "经验丰富", "价格实惠"],
    avatar: "https://picsum.photos/seed/gig1/100/100",
    link: "/pages/gig/detail/index?id=1",
  },
  {
    title: "家庭保洁 | 深度清洁 | 专业设备",
    price: "50-80",
    unit: "小时",
    location: "朝阳区 · 三里屯",
    time: "今日可约",
    tags: ["专业设备", "深度清洁", "五年经验"],
    avatar: "https://picsum.photos/seed/gig2/100/100",
    link: "/pages/gig/detail/index?id=2",
  },
  {
    title: "水电维修 | 24小时服务 | 持证上岗",
    price: "100-200",
    unit: "次",
    location: "丰台区 · 方庄",
    time: "24小时服务",
    tags: ["持证上岗", "经验丰富", "价格透明"],
    avatar: "https://picsum.photos/seed/gig3/100/100",
    link: "/pages/gig/detail/index?id=3",
  },
];
</script>

<style scoped>
/* 零工推荐 */
.gig-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: var(--bg-tag);
  color: var(--text-info);
  border-radius: 6rpx;
}

.gig-item {
  transition: transform 0.2s;
}

.gig-item:active {
  transform: scale(0.98);
}

.text-primary {
  color: var(--primary);
}
</style>

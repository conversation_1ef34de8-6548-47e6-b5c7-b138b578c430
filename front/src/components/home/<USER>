<template>
  <view class="job-container bg-card mb-20rpx">
    <view
      class="section-header flex justify-between items-center px-20rpx py-20rpx"
    >
      <text class="text-32rpx font-500">热门职位</text>
      <view class="flex items-center" @tap="navigateTo('/pages/job/index')">
        <text class="text-26rpx text-grey">全部</text>
        <text class="i-carbon-chevron-right text-grey"></text>
      </view>
    </view>

    <!-- 推荐职位列表 -->
    <view class="job-list px-20rpx">
      <view
        v-for="(job, index) in recommendJobs"
        :key="index"
        class="job-item p-24rpx mb-16rpx bg-white rounded-lg shadow-sm"
        @tap="goToJobDetail(job)"
      >
        <!-- 职位头部信息 -->
        <view class="flex justify-between items-start mb-16rpx">
          <view class="flex-1 mr-20rpx">
            <view class="job-title-row flex items-center">
              <text
                class="job-title text-32rpx font-500 text-base line-clamp-2"
              >
                {{ job.title }}
              </text>
              <view v-if="job.isUrgent" class="tag tag-danger ml-10rpx">
                急聘
              </view>
            </view>

            <!-- 公司信息 -->
            <view class="company-info flex items-center mt-12rpx">
              <text class="company-name text-28rpx font-500 text-info">
                {{ job.companyName }}
              </text>
              <text class="text-grey text-24rpx mx-12rpx">|</text>
              <text class="text-grey text-24rpx">{{ job.industry }}</text>
            </view>

            <!-- 职位标签 -->
            <view class="tag-container mt-12rpx">
              <view
                v-for="(tag, tagIndex) in job.tags.slice(0, 3)"
                :key="tagIndex"
                class="job-tag mr-10rpx mb-6rpx"
              >
                {{ tag }}
              </view>
            </view>
          </view>

          <view class="salary-container">
            <view class="salary text-32rpx font-500 text-primary">
              {{ job.salary }}
            </view>
          </view>
        </view>

        <!-- 底部位置和时间信息 -->
        <view class="divider mb-16rpx"></view>

        <view class="flex justify-between items-center">
          <view class="flex items-center">
            <text class="i-carbon-location text-grey text-24rpx mr-6rpx"></text>
            <text class="area text-grey text-26rpx">{{ job.area }}</text>
          </view>
          <text class="text-grey text-24rpx">{{ job.publishTime }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 导航到职位详情
const goToJobDetail = (job: any) => {
  uni.navigateTo({
    url: `/pages/job/detail?id=${job.id}`,
  });
};

// 导航方法
const navigateTo = (path: string) => {
  uni.navigateTo({
    url: path,
  });
};

// 推荐职位数据 - 基于用户浏览历史推荐
const recommendJobs = [
  {
    id: "1",
    title: "前端开发工程师",
    companyName: "字节跳动",
    industry: "互联网",
    salary: "15K-30K",
    area: "海淀区 · 上地",
    tags: ["五险一金", "年终奖", "弹性工作"],
    isUrgent: true,
    publishTime: "3小时前",
  },
  {
    id: "2",
    title: "UI设计师",
    companyName: "美团",
    industry: "互联网",
    salary: "12K-25K",
    area: "朝阳区 · 望京",
    tags: ["五险一金", "带薪年假", "下午茶"],
    isUrgent: false,
    publishTime: "5小时前",
  },
  {
    id: "3",
    title: "产品经理",
    companyName: "小米科技",
    industry: "互联网",
    salary: "20K-35K",
    area: "海淀区 · 清河",
    tags: ["股票期权", "年终奖", "团建"],
    isUrgent: true,
    publishTime: "1天前",
  },
];
</script>

<style scoped>
.job-item {
  transition: transform 0.2s;
  border: 1rpx solid var(--border-color);
}

.job-item:active {
  transform: scale(0.98);
}

.job-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: var(--bg-tag);
  color: var(--text-info);
  border-radius: 6rpx;
  display: inline-block;
}

.tag-danger {
  background-color: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.divider {
  height: 1rpx;
  background-color: var(--border-color);
}

.text-primary {
  color: var(--primary);
}
</style>

<template>
  <view class="filter-panel">
    <!-- 筛选菜单栏 -->
    <view class="filter-menu">
      <view
        v-for="(menu, index) in filterMenus"
        :key="index"
        class="filter-menu-item"
        :class="{ active: activeMenuIndex === index }"
        @tap="toggleMenu(index)"
      >
        <text class="menu-text">{{ menu.name }}</text>
        <text
          :class="[
            activeMenuIndex === index
              ? 'i-carbon-chevron-up'
              : 'i-carbon-chevron-down',
            'menu-icon',
          ]"
        ></text>
      </view>
    </view>

    <!-- 使用uni-popup组件，类型改为bottom -->
    <uni-popup
      ref="popup"
      type="bottom"
      background-color="transparent"
      :safe-area="false"
      @change="handlePopupChange"
    >
      <view class="popup-container">
        <view class="popup-header">
          <view class="popup-title">{{ getPopupTitle }}</view>
          <view class="popup-close" @tap="closeMenu">
            <text class="i-carbon-close"></text>
          </view>
        </view>

        <view class="popup-content">
          <scroll-view scroll-y class="content-scroll">
            <!-- 区域筛选 -->
            <view v-if="activeMenuIndex === 0" class="content-wrapper">
              <view class="section-title">区域选择</view>
              <view class="option-list">
                <view
                  v-for="(item, index) in areaOptions"
                  :key="index"
                  class="option-item"
                  :class="{ active: selectedFilters.area === item.value }"
                  @tap.stop="selectOption('area', item.value, item.text)"
                >
                  <text class="option-text">{{ item.text }}</text>
                  <view
                    class="option-indicator"
                    v-if="selectedFilters.area === item.value"
                  >
                    <text class="i-carbon-checkmark"></text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 租金筛选 -->
            <view v-else-if="activeMenuIndex === 1" class="content-wrapper">
              <view class="section-title">价格区间</view>
              <!-- 价格选项 -->
              <view class="price-options">
                <view
                  v-for="(item, index) in priceOptions"
                  :key="index"
                  class="tag tag-item"
                  :class="{ active: selectedFilters.price === item.value }"
                  @tap.stop="selectOption('price', item.value, item.text)"
                >
                  {{ item.text }}
                </view>
              </view>
            </view>

            <!-- 户型筛选 -->
            <view v-else-if="activeMenuIndex === 2" class="content-wrapper">
              <view class="option-group">
                <view class="section-title">整租/合租</view>
                <view class="tag-list">
                  <view
                    class="tag tag-item"
                    :class="{ active: selectedFilters.rentType === '整租' }"
                    @tap.stop="selectOption('rentType', '整租', '整租')"
                  >
                    整租
                  </view>
                  <view
                    class="tag tag-item"
                    :class="{ active: selectedFilters.rentType === '合租' }"
                    @tap.stop="selectOption('rentType', '合租', '合租')"
                  >
                    合租
                  </view>
                </view>
              </view>

              <view class="option-group">
                <view class="section-title">户型</view>
                <view class="tag-list">
                  <view
                    v-for="(item, index) in layoutOptions"
                    :key="index"
                    class="tag tag-item"
                    :class="{ active: selectedFilters.layout === item.value }"
                    @tap.stop="selectOption('layout', item.value, item.text)"
                  >
                    {{ item.text }}
                  </view>
                </view>
              </view>
            </view>

            <!-- 更多筛选 -->
            <view v-else-if="activeMenuIndex === 3" class="content-wrapper">
              <!-- 房型亮点 -->
              <view class="option-group">
                <view class="section-title">房型亮点</view>
                <view class="tag-list">
                  <view
                    v-for="(item, index) in featureOptions"
                    :key="index"
                    class="tag tag-item"
                    :class="{ active: moreSelectedValues.includes(item.value) }"
                    @tap.stop="toggleMoreOption(item)"
                  >
                    {{ item.text }}
                  </view>
                </view>
              </view>

              <!-- 朝向 -->
              <view class="option-group">
                <view class="section-title">朝向</view>
                <view class="tag-list">
                  <view
                    v-for="(item, index) in directionOptions"
                    :key="index"
                    class="tag tag-item"
                    :class="{ active: moreSelectedValues.includes(item.value) }"
                    @tap.stop="toggleMoreOption(item)"
                  >
                    {{ item.text }}
                  </view>
                </view>
              </view>

              <!-- 面积 -->
              <view class="option-group">
                <view class="section-title">面积</view>
                <view class="tag-list">
                  <view
                    v-for="(item, index) in areaRangeOptions"
                    :key="index"
                    class="tag tag-item"
                    :class="{ active: moreSelectedValues.includes(item.value) }"
                    @tap.stop="toggleMoreOption(item)"
                  >
                    {{ item.text }}
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>

          <!-- 底部按钮 -->
          <view class="filter-actions">
            <view
              class="reset-btn"
              @tap.stop="resetFilter(filterMenus[activeMenuIndex].value)"
            >
              <text class="i-carbon-reset mr-8rpx"></text>
              <text>重置</text>
            </view>
            <view class="confirm-btn" @tap.stop="applyFilters">确定</view>
          </view>
        </view>

        <!-- 底部安全区域 -->
        <view class="safe-area"></view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";

// 定义组件属性
const props = defineProps({
  // 初始筛选条件
  initialFilters: {
    type: Object,
    default: () => ({}),
  },
});

// 定义事件
const emit = defineEmits(["filter-change", "filter-reset", "menuOpened"]);

// 筛选菜单
const filterMenus = reactive([
  { name: "位置", value: "area" },
  { name: "租金", value: "price" },
  { name: "整·不限", value: "layout" },
  { name: "更多", value: "more" },
]);

// 当前激活的菜单索引
const activeMenuIndex = ref(-1);

// popup引用
const popup = ref(null);

// 价格范围
const priceRange = ref([0, 20000]);

// 已选择的筛选条件
const selectedFilters = reactive({
  area: "",
  price: "",
  layout: "",
  rentType: "",
  more: [],
});

// 更多筛选选中的值
const moreSelectedValues = ref([]);
const moreSelectedLabels = ref([]);

// 弹窗标题计算属性
const getPopupTitle = computed(() => {
  if (activeMenuIndex.value === -1) return "";
  return filterMenus[activeMenuIndex.value]?.name || "";
});

// 区域选项
const areaOptions = reactive([
  { text: "不限", value: "" },
  { text: "海淀区", value: "haidian" },
  { text: "朝阳区", value: "chaoyang" },
  { text: "西城区", value: "xicheng" },
  { text: "东城区", value: "dongcheng" },
  { text: "丰台区", value: "fengtai" },
  { text: "石景山区", value: "shijingshan" },
  { text: "通州区", value: "tongzhou" },
  { text: "昌平区", value: "changping" },
  { text: "大兴区", value: "daxing" },
  { text: "顺义区", value: "shunyi" },
]);

// 租金选项
const priceOptions = reactive([
  { text: "不限", value: "" },
  { text: "≤1000元", value: "0-1000" },
  { text: "1000-3000元", value: "1000-3000" },
  { text: "3000-5000元", value: "3000-5000" },
  { text: "5000-8000元", value: "5000-8000" },
  { text: "≥8000元", value: "8000-" },
]);

// 户型选项
const layoutOptions = reactive([
  { text: "不限", value: "" },
  { text: "1室", value: "1" },
  { text: "2室", value: "2" },
  { text: "3室", value: "3" },
  { text: "4室", value: "4" },
  { text: "5室+", value: "5+" },
]);

// 房型亮点选项
const featureOptions = reactive([
  { text: "双卫生间", value: "double-toilet" },
  { text: "loft/复式", value: "loft" },
  { text: "不看开间", value: "no-open" },
  { text: "开间", value: "open" },
]);

// 朝向选项
const directionOptions = reactive([
  { text: "东", value: "east" },
  { text: "西", value: "west" },
  { text: "南", value: "south" },
  { text: "北", value: "north" },
  { text: "南北", value: "south-north" },
]);

// 面积范围选项
const areaRangeOptions = reactive([
  { text: "≤40㎡", value: "0-40" },
  { text: "40-60㎡", value: "40-60" },
  { text: "60-80㎡", value: "60-80" },
  { text: "80-100㎡", value: "80-100" },
  { text: "100-120㎡", value: "100-120" },
  { text: "≥120㎡", value: "120-" },
]);

// 处理popup状态变化
const handlePopupChange = (e) => {
  if (!e.show) {
    closeMenu();
  }
};

// 切换菜单
const toggleMenu = (index: number) => {
  if (activeMenuIndex.value === index) {
    // 如果点击当前激活的菜单，则关闭
    closeMenu();
  } else {
    // 切换到对应的菜单
    activeMenuIndex.value = index;

    // 如果是更多选项，初始化选中的值
    if (index === 3) {
      moreSelectedValues.value = [...selectedFilters.more];
      moreSelectedLabels.value = selectedFilters.more.map((value) => {
        // 查找对应的标签
        const allOptions = [
          ...featureOptions,
          ...directionOptions,
          ...areaRangeOptions,
        ];
        const option = allOptions.find((opt) => opt.value === value);
        return option ? option.text : value;
      });
    }

    // 打开弹出层
    nextTick(() => {
      popup.value.open();
      emit("menuOpened");
    });
  }
};

// 关闭菜单
const closeMenu = () => {
  activeMenuIndex.value = -1;
  popup.value && popup.value.close();
};

// 选择选项
const selectOption = (type: string, value: string, label: string) => {
  selectedFilters[type] = value;

  // 更新菜单名称
  if (type === "rentType") {
    filterMenus[2].name = value || "整·不限";
  } else if (type === "area") {
    filterMenus[0].name = label || "位置";
  } else if (type === "price") {
    filterMenus[1].name = label || "租金";
  } else if (type === "layout") {
    if (selectedFilters.rentType) {
      filterMenus[2].name = `${selectedFilters.rentType}·${value || "不限"}`;
    } else {
      filterMenus[2].name = value ? `${value}室` : "整·不限";
    }
  }
};

// 切换更多选项
const toggleMoreOption = (option) => {
  const index = moreSelectedValues.value.indexOf(option.value);

  if (index > -1) {
    // 如果已选中，则取消选中
    moreSelectedValues.value.splice(index, 1);
    moreSelectedLabels.value.splice(index, 1);
  } else {
    // 如果未选中，则添加选中
    moreSelectedValues.value.push(option.value);
    moreSelectedLabels.value.push(option.text);
  }
};

// 重置特定筛选条件
const resetFilter = (type: string) => {
  if (type === "more") {
    resetMoreFilters();
    return;
  }

  selectedFilters[type] = "";

  // 更新菜单名称
  if (type === "area") {
    filterMenus[0].name = "位置";
  } else if (type === "price") {
    filterMenus[1].name = "租金";
    priceRange.value = [0, 20000];
  } else if (type === "layout") {
    resetLayoutFilters();
  }
};

// 重置户型筛选
const resetLayoutFilters = () => {
  selectedFilters.layout = "";
  selectedFilters.rentType = "";
  filterMenus[2].name = "整·不限";
};

// 重置更多筛选
const resetMoreFilters = () => {
  moreSelectedValues.value = [];
  moreSelectedLabels.value = [];
};

// 确认更多筛选
const confirmMoreFilters = () => {
  selectedFilters.more = [...moreSelectedValues.value];

  // 更新菜单名称
  if (moreSelectedValues.value.length > 0) {
    filterMenus[3].name = `更多(${moreSelectedValues.value.length})`;
  } else {
    filterMenus[3].name = "更多";
  }

  closeMenu();
  emit("filter-change", { ...selectedFilters });
};

// 应用筛选条件
const applyFilters = () => {
  // 如果是更多选项需要特殊处理
  if (activeMenuIndex.value === 3) {
    confirmMoreFilters();
  } else {
    closeMenu();
    emit("filter-change", { ...selectedFilters });
  }
};

// 重置所有筛选条件
const resetFilters = () => {
  // 重置选中的筛选条件
  Object.keys(selectedFilters).forEach((key) => {
    if (key === "more") {
      selectedFilters[key] = [];
    } else {
      selectedFilters[key] = "";
    }
  });

  // 重置更多筛选选中的值
  moreSelectedValues.value = [];
  moreSelectedLabels.value = [];

  // 重置菜单名称
  filterMenus[0].name = "位置";
  filterMenus[1].name = "租金";
  filterMenus[2].name = "整·不限";
  filterMenus[3].name = "更多";

  // 发送重置事件
  emit("filter-reset");
};

// 初始化
onMounted(() => {
  // 设置初始筛选条件
  if (props.initialFilters) {
    Object.keys(props.initialFilters).forEach((key) => {
      if (selectedFilters.hasOwnProperty(key)) {
        selectedFilters[key] = props.initialFilters[key];
      }
    });
  }
});

// 暴露方法
defineExpose({
  resetFilters,
  closeMenu,
  activeMenuIndex,
});
</script>

<style lang="scss" scoped>
.filter-panel {
  width: 100%;
  z-index: 100;
  position: relative;
}

.filter-menu {
  display: flex;
  align-items: center;
  height: 88rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.filter-menu-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  color: var(--text-secondary);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:active {
    background-color: rgba(0, 0, 0, 0.02);
  }

  &.active {
    color: var(--primary);
    font-weight: 500;
  }
}

.menu-text {
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.menu-icon {
  margin-left: 6rpx;
  font-size: 22rpx;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.popup-container {
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  box-shadow: 0 -6rpx 30rpx rgba(0, 0, 0, 0.1);
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid var(--border-color);
  flex-shrink: 0;
}

.popup-title {
  font-size: 30rpx;
  font-weight: 500;
}

.popup-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  font-size: 36rpx;
}

.popup-content {
  position: relative;
  background-color: #fff;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.content-scroll {
  flex: 1;
  overflow-y: auto;
}

.content-wrapper {
  padding: 24rpx;
}

.section-title {
  font-size: 32rpx;

  font-weight: 500;
  margin: 0 24rpx 16rpx;
}

.option-list {
  padding: 0;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;

  transition: all 0.3s ease;
  padding: 0 24rpx;
  border-bottom: 1rpx solid var(--border-color);

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: rgba(0, 0, 0, 0.02);
  }

  &.active {
    color: var(--primary);
    font-weight: 500;
  }
}

.option-indicator {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary);
  color: var(--text-inverse);
  font-size: 20rpx;
}

.price-options {
  margin: 20rpx 0 32rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.price-options .tag {
  padding: 16rpx 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1rpx solid var(--border-color);
  transition: all 0.25s ease;

  &.active {
    background-color: rgba(255, 109, 0, 0.08);
    color: var(--primary);
    font-weight: 500;
    border-color: var(--primary);
    transform: translateY(-2rpx);
    box-shadow: 0 2rpx 8rpx rgba(255, 109, 0, 0.1);
  }

  &:active {
    transform: scale(0.98);
  }
}

.option-group {
  padding: 16rpx 0;
  margin-bottom: 24rpx;
}

.tag-list {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
  padding: 0 16rpx;
  margin: 16rpx 0;
}

.tag-item {
  padding: 12rpx 24rpx;
  border-radius: 6rpx;
  font-size: 26rpx;
  background-color: var(--bg-tag);

  transition: all 0.25s ease;
  text-align: center;
  border: 1rpx solid var(--border-color);

  &.active {
    background-color: rgba(255, 109, 0, 0.08);
    color: var(--primary);
    font-weight: 500;
    border-color: var(--primary);
    transform: translateY(-2rpx);
    box-shadow: 0 2rpx 8rpx rgba(255, 109, 0, 0.1);
  }

  &:active {
    transform: scale(0.98);
  }
}

.filter-actions {
  display: flex;
  padding: 24rpx;
  border-top: 1rpx solid var(--border-color);
  background-color: #fff;
  flex-shrink: 0;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;

  border-radius: 40rpx;
  transition: all 0.3s ease;
}

.reset-btn {
  margin-right: 20rpx;
  color: var(--text-secondary);
  background-color: var(--bg-tag);
  border: 1rpx solid var(--border-color);

  &:active {
    background-color: var(--bg-input);
  }
}

.confirm-btn {
  color: var(--text-inverse);
  background-color: var(--primary);
  font-weight: 500;

  &:active {
    background-color: #e66200;
  }
}

.safe-area {
  height: env(safe-area-inset-bottom);
  background-color: #fff;
  flex-shrink: 0;
}

.mr-8rpx {
  margin-right: 8rpx;
}
</style>

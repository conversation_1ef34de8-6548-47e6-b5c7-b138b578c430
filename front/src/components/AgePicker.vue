<template>
  <picker
    mode="multiSelector"
    :range="[yearRange, monthRange]"
    :range-key="['label', 'label']"
    :value="pickerValue"
    @change="onPickerChange"
  >
    <slot>
      <view class="age-picker-content">
        <text :class="{ placeholder: !displayText }">{{
          displayText || placeholder
        }}</text>
        <text class="i-carbon-chevron-right action-icon"></text>
      </view>
    </slot>
  </picker>
</template>

<script setup lang="ts">
interface Props {
  modelValue?: string;
  placeholder?: string;
}

interface Emits {
  (e: "update:modelValue", value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: "请选择年龄",
});

const emit = defineEmits<Emits>();

// 生成年份范围（1950-当前年份）
const currentYear = new Date().getFullYear();
const yearRange = computed(() => {
  const years = [];
  for (let year = currentYear; year >= 1950; year--) {
    years.push({ label: `${year}年`, value: year });
  }
  return years;
});

// 生成月份范围
const monthRange = computed(() => {
  const months = [];
  for (let month = 1; month <= 12; month++) {
    months.push({ label: `${month}月`, value: month });
  }
  return months;
});

// 当前选择的索引
const pickerValue = computed(() => {
  if (!props.modelValue) return [0, 0];

  const [year, month] = props.modelValue.split("-");
  const yearIndex = yearRange.value.findIndex(
    (item) => item.value === parseInt(year)
  );
  const monthIndex = monthRange.value.findIndex(
    (item) => item.value === parseInt(month)
  );

  return [yearIndex === -1 ? 0 : yearIndex, monthIndex === -1 ? 0 : monthIndex];
});

// 显示文本
const displayText = computed(() => {
  if (!props.modelValue) return "";

  const [year, month] = props.modelValue.split("-");
  const age = currentYear - parseInt(year);
  return `${year}年${month}月 (${age}岁)`;
});

// 选择器变化事件
const onPickerChange = (e: any) => {
  const [yearIndex, monthIndex] = e.detail.value;
  const selectedYear = yearRange.value[yearIndex].value;
  const selectedMonth = monthRange.value[monthIndex].value;

  const value = `${selectedYear}-${selectedMonth.toString().padStart(2, "0")}`;
  emit("update:modelValue", value);
};
</script>

<style lang="scss" scoped>
.age-picker-content {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-2);

  font-weight: 500;

  .placeholder {
    color: var(--text-info);
  }

  .action-icon {
    color: var(--text-grey);
    font-size: 32rpx;
  }
}
</style>

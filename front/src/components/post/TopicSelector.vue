<template>
  <tui-bottom-popup :show="popupVisible" @close="close" :zIndex="1002">
    <view class="topic-selector-container">
      <!-- 头部 -->
      <view class="popup-header">
        <view class="popup-title">选择话题</view>
        <view class="close-btn" @tap="close">
          <text class="i-carbon-close"></text>
        </view>
      </view>

      <!-- 搜索框 -->
      <view class="search-bar">
        <view class="search-input-wrapper">
          <text class="i-carbon-search search-icon"></text>
          <input
            v-model="searchKeyword"
            class="search-input"
            placeholder="搜索话题"
            confirm-type="search"
          />
        </view>
      </view>
      
      <!-- 筛选 -->
      <uv-tabs
        :list="tabs"
        :current="currentTab"
        @change="onTabChange"
        lineColor="#ff860a"
        :activeStyle="{
          color: '#ff860a',
          fontWeight: 'bold',
        }"
      ></uv-tabs>

      <!-- 已选话题 -->
      <view class="selection-info">
        <text class="selection-text">
          已选择 <text class="selection-count">{{ selectedTopics.length }}/3</text>
        </text>
      </view>

      <!-- 话题列表 -->
      <scroll-view scroll-y class="topic-list-scroll">
        <view class="topic-list">
          <view
            v-for="topic in filteredTopics"
            :key="topic.id"
            class="topic-item"
            :class="{ selected: isSelected(topic) }"
            @tap="toggleSelect(topic)"
          >
            <text># {{ topic.name }}</text>
            <text v-if="isSelected(topic)" class="i-carbon-checkmark-outline"></text>
          </view>
        </view>
      </scroll-view>

      <!-- 确认按钮 -->
      <view class="confirm-btn-container">
        <view class="confirm-btn" @tap="confirmSelection">完成</view>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";

const popupVisible = ref(false);
const searchKeyword = ref("");
const currentTab = ref(0);
const allTopics = ref([
  { id: 1, name: '职场分享', category: 'recommended', hotness: 99 },
  { id: 2, name: '面试经验', category: 'recommended', hotness: 95 },
  { id: 3, name: '租房避坑', category: 'hot', hotness: 98 },
  { id: 4, name: '好工作推荐', category: 'hot', hotness: 100 },
  { id: 5, name: '我的求职日记', category: 'recommended', hotness: 80 },
  { id: 6, name: '本地美食', category: 'hot', hotness: 92 },
  { id: 7, name: '周末去哪儿', category: 'hot', hotness: 88 },
  { id: 8, name: '装修心得', category: 'recommended', hotness: 70 },
  { id: 9, name: '通勤路上', category: 'recommended', hotness: 60 },
  { id: 10, name: '创业想法', category: 'hot', hotness: 85 },
]);

const selectedTopics = ref<{ id: number; name: string }[]>([]);

const tabs = ref([{ name: "热门" }, { name: "推荐" }]);

const emit = defineEmits(["confirm"]);

const filteredTopics = computed(() => {
  let topics = [...allTopics.value];
  if (currentTab.value === 0) { // 热门
    topics.sort((a, b) => b.hotness - a.hotness);
  } else { // 推荐
    topics = topics.filter(t => t.category === 'recommended');
  }

  if (searchKeyword.value) {
    return topics.filter(t => t.name.includes(searchKeyword.value));
  }
  return topics;
});

const isSelected = (topic: { id: number; name: string }) => {
  return selectedTopics.value.some(st => st.id === topic.id);
};

const toggleSelect = (topic: { id: number; name: string }) => {
  const index = selectedTopics.value.findIndex(st => st.id === topic.id);
  if (index > -1) {
    selectedTopics.value.splice(index, 1);
  } else {
    if (selectedTopics.value.length >= 3) {
      uni.showToast({
        title: "最多选择3个话题",
        icon: "none",
      });
      return;
    }
    selectedTopics.value.push(topic);
  }
};

const onTabChange = (tab: any) => {
  currentTab.value = tab.index;
};

const open = (currentSelection: { id: number; name: string }[]) => {
  selectedTopics.value = [...currentSelection];
  popupVisible.value = true;
};

const close = () => {
  popupVisible.value = false;
};

const confirmSelection = () => {
  emit("confirm", selectedTopics.value);
  close();
};

defineExpose({ open });
</script>

<style lang="scss" scoped>
.topic-selector-container {
  background-color: #f7f8fa;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  height: 70vh;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
}

.search-bar {
  margin-bottom: 20rpx;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 36rpx;
  padding: 0 24rpx;
  height: 72rpx;
}

.search-icon {
  font-size: 32rpx;
  color: #999;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
}

.selection-info {
  margin: 20rpx 0;
  font-size: 26rpx;
  color: #666;
}

.selection-count {
  color: var(--primary);
  font-weight: bold;
}

.topic-list-scroll {
  flex: 1;
  min-height: 0;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 10rpx;
}

.topic-list {
  display: flex;
  flex-direction: column;
}

.topic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 20rpx;
  font-size: 28rpx;
  border-bottom: 1rpx solid #f5f5f5;

  &.selected {
    color: var(--primary);
    font-weight: 500;
  }
}

.confirm-btn-container {
  padding-top: 20rpx;
}

.confirm-btn {
  background-color: var(--primary);
  color: white;
  text-align: center;
  padding: 24rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}
</style> 
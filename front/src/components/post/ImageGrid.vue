<template>
  <view v-if="images && images.length > 0" class="image-grid-container" :class="gridClass">
    <view
      v-for="(image, index) in images"
      :key="index"
      class="image-item"
      @tap.stop="previewImage(index)"
    >
      <image :src="image" class="image" mode="aspectFill" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  images: string[];
}>();

const emit = defineEmits(['preview']);

const gridClass = computed(() => {
  const count = props.images.length;
  if (count === 1) return 'single-image';
  if (count === 2 || count === 4) return 'two-columns';
  return 'three-columns';
});

const previewImage = (index: number) => {
  uni.previewImage({
    urls: props.images,
    current: index,
  });
};
</script>

<style lang="scss" scoped>
.image-grid-container {
  display: grid;
  gap: 8rpx;
  margin-top: 16rpx;
}

.image-item {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 12rpx;

  .image {
    width: 100%;
    height: 100%;
    display: block;
  }
}

// 单张图片
.single-image {
  grid-template-columns: minmax(0, 2fr);
  .image-item {
    aspect-ratio: 4 / 3;
  }
}

// 2列布局 (2张或4张图)
.two-columns {
  grid-template-columns: repeat(2, 1fr);
  .image-item {
    aspect-ratio: 1 / 1;
  }
}

// 3列布局 (3, 5, 6, 7, 8, 9+ 图)
.three-columns {
  grid-template-columns: repeat(3, 1fr);
  .image-item {
    aspect-ratio: 1 / 1;
  }
}
</style> 
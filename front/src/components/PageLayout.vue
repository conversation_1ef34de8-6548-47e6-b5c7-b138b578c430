<template>
  <view class="page-layout" :style="{ backgroundColor: bgColor }">
    <!-- Custom Navigation Bar -->
    <CustomNavBar v-if="showNavBar" :title="title">
      <!-- 支持自定义导航栏内容 -->
      <template v-if="$slots.navbar" #default>
        <slot name="navbar"></slot>
      </template>
    </CustomNavBar>

    <!-- Page Content Area -->
    <view class="page-content">
      <!-- Loading 状态 -->
      <template v-if="loading">
        <slot name="loading">
          <LoadingState
            :config="{
              type: 'skeleton',
              text: texts?.loading || '加载中...',
              showText: true,
            }"
          />
        </slot>
      </template>

      <!-- Error 状态 -->
      <template v-else-if="error">
        <slot
          name="error"
          :error="error"
          :retry="handleRetry"
          :isRetrying="isRetrying"
        >
          <ErrorState
            :error="error"
            :config="{
              retryText: texts?.retry || '重试',
              showRetry: true,
            }"
            :isRetrying="isRetrying"
            :onRetry="handleRetry"
            @retry="handleRetry"
          />
        </slot>
      </template>

      <!-- Empty 状态 -->
      <template v-else-if="isEmpty">
        <slot name="empty" :emptyConfig="getEmptyIconAndText">
          <EmptyState
            :config="{
              title: texts?.empty || getEmptyIconAndText.title,
              message: getEmptyIconAndText.message,
              icon: getEmptyIconAndText.icon,
            }"
          />
        </slot>
      </template>

      <!-- 正常内容 -->
      <template v-else>
        <slot></slot>
      </template>
    </view>

    <!-- Global Privacy Popup -->
    <PrivacyPopup
      :show="globalStore.isPrivacyPopupVisible"
      @agree="handleAgreePrivacy"
      @directLogin="handleDirectLogin"
      @reject="handleRejectPrivacy"
      @close="handleClosePrivacy"
    />

    <!-- Development Test Login - 备用登录弹框，主要在首页使用 DevQuickLogin -->
    <DevTestLogin
      v-if="isDevLoginEnabled"
      :show="globalStore.isDevLoginVisible"
      @login="handleDevTestLogin"
      @close="handleCloseDevLogin"
      @normalLogin="handleNormalLogin"
    />

    <!-- Login Tip Bar -->
    <LoginTip
      v-if="showLoginTip"
      :is-fixed="true"
      :closable="true"
      @login="handleLoginTip"
      @close="handleCloseLoginTip"
    />
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { useGlobalStore } from "@/stores/global";
import { useUserStore } from "@/stores/user";
import { debounce } from "@/utils/core/performance";
import { defaultIcons, quickIcons } from "@/utils/assetsUtil";
import CustomNavBar from "@/components/CustomNavBar.vue";
import PrivacyPopup from "@/components/common/PrivacyPopup.vue";
import DevTestLogin from "@/components/common/DevTestLogin.vue";
import LoginTip from "@/components/common/LoginTip.vue";
import LoadingState from "@/components/common/LoadingState.vue";
import ErrorState from "@/components/common/ErrorState.vue";
import EmptyState from "@/components/common/EmptyState.vue";

// 简化Props接口 - 遵循反过度封装原则
interface Props {
  // 基础布局配置
  showNavBar?: boolean;
  title?: string;
  bgColor?: string;
  showLoginTip?: boolean;

  // 页面状态 - 简化设计，移除data参数
  loading?: boolean;
  error?: any;
  isEmpty?: boolean;

  // 缺省状态配置
  emptyType?:
    | "data"
    | "network"
    | "search"
    | "message"
    | "favorite"
    | "permission"
    | "cart"
    | "schedule";

  // 事件回调
  onRetry?: () => void;

  // 文案配置
  texts?: {
    loading?: string;
    empty?: string;
    retry?: string;
  };
}

// Props定义
const props = withDefaults(defineProps<Props>(), {
  showNavBar: false,
  title: "",
  bgColor: "transparent",
  showLoginTip: true,
  isEmpty: false,
  emptyType: "data",
  texts: () => ({
    loading: "加载中...",
    empty: "暂无数据",
    retry: "重试",
  }),
});

// 定义emits
const emit = defineEmits<{
  retry: [];
}>();

// Store实例
const globalStore = useGlobalStore();
const userStore = useUserStore();

// 重试状态管理
const isRetrying = ref(false);

// 智能图标匹配
const getEmptyIconAndText = computed(() => {
  const iconMappings = {
    data: {
      icon: defaultIcons.data.noData,
      title: "暂无数据",
      message: "当前没有相关内容",
    },
    network: {
      icon: defaultIcons.network.networkError,
      title: "网络连接失败",
      message: "请检查网络连接后重试",
    },
    search: {
      icon: defaultIcons.data.noSearchResult,
      title: "暂无搜索结果",
      message: "试试其他关键词吧",
    },
    message: {
      icon: defaultIcons.feature.noMessage,
      title: "暂无消息",
      message: "还没有新消息哦",
    },
    favorite: {
      icon: defaultIcons.feature.noFavorite,
      title: "暂无收藏",
      message: "收藏喜欢的内容吧",
    },
    permission: {
      icon: defaultIcons.permission.noPermission,
      title: "无权限访问",
      message: "需要登录后才能查看",
    },
    cart: {
      icon: defaultIcons.container.cartEmpty,
      title: "购物车空空如也",
      message: "快去挑选心仪的商品吧",
    },
    schedule: {
      icon: defaultIcons.feature.noSchedule,
      title: "暂无日程",
      message: "今天还没有安排哦",
    },
  };

  return iconMappings[props.emptyType] || iconMappings.data;
});

// 检查是否启用开发登录
const isDevLoginEnabled = computed(() => {
  const devLoginEnabled = import.meta.env.VITE_ENABLE_DEV_LOGIN === "true";
  const isDev = import.meta.env.DEV;
  console.log("开发登录环境检查:", {
    devLoginEnabled,
    isDev,
    final: devLoginEnabled && isDev,
  });
  return devLoginEnabled && isDev;
});

// 重试处理 - 带防重复点击保护
const handleRetry = debounce(
  async () => {
    if (isRetrying.value || props.loading) {
      return; // 防止重复点击
    }

    isRetrying.value = true;

    try {
      if (props.onRetry) {
        await props.onRetry();
      }
      emit("retry");
    } catch (error) {
      console.error("重试失败:", error);
      uni.showToast({
        title: "重试失败，请稍后再试",
        icon: "none",
        duration: 2000,
      });
    } finally {
      isRetrying.value = false;
    }
  },
  1000,
  true
); // 1秒防抖，立即执行

onLoad(() => {
  uni.$on("onLogin", () => {
    if (isDevLoginEnabled.value) {
      // 如果启用了开发登录，显示开发登录选项
      globalStore.showDevLogin();
    } else {
      // 否则显示正常的隐私弹窗
      globalStore.showPrivacyPopup();
    }
  });
});

const handleAgreePrivacy = async (data: any) => {
  // 处理新用户注册成功的回调
  userStore.setUserInfo(data.user, data.access_token);
};

const handleDirectLogin = async (data: any) => {
  // 处理已注册用户直接登录的回调
  userStore.setUserInfo(data.user, data.access_token);
};

const handleRejectPrivacy = () => {
  // 根据业务需求处理拒绝逻辑，例如退出小程序
  uni.showModal({
    title: "提示",
    content: "您需要同意隐私协议才能继续使用",
    showCancel: false,
  });
};

const handleClosePrivacy = () => {
  // 点击遮罩层关闭，也视为临时拒绝
  handleRejectPrivacy();
};

const handleLoginTip = () => {
  // 处理登录提示条点击
  if (isDevLoginEnabled.value) {
    // 如果启用了开发登录，显示开发登录选项
    globalStore.showDevLogin();
  } else {
    // 否则显示正常的隐私弹窗
    globalStore.showPrivacyPopup();
  }
};

const handleCloseLoginTip = () => {
  // 处理登录提示条关闭
  console.log("登录提示条已关闭");
};

// 开发测试登录相关处理函数
const handleDevTestLogin = (loginResult: any) => {
  // 处理开发测试登录成功的回调
  userStore.setUserInfo(loginResult.user, loginResult.accessToken);
  globalStore.hideDevLogin();

  // 登录成功后跳转到首页
  uni.switchTab({
    url: "/pages/home/<USER>",
  });
};

const handleCloseDevLogin = () => {
  // 关闭开发登录弹窗
  globalStore.hideDevLogin();
};

const handleNormalLogin = () => {
  // 切换到正常登录流程
  globalStore.hideDevLogin();
  globalStore.showPrivacyPopup();
};
</script>

<style scoped>
.page-layout {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: auto;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  position: relative;
}
</style>

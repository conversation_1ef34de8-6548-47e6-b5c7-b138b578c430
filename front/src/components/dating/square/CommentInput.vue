<template>
  <view class="comment-input-container">
    <!-- Emoji 面板 -->
    <view v-if="showEmojiPanel" class="emoji-panel">
      <scroll-view scroll-x class="emoji-scroll">
        <view class="emoji-grid">
          <text 
            v-for="emoji in emojiList" 
            :key="emoji" 
            class="emoji-item" 
            @click="insertEmoji(emoji)"
          >
            {{ emoji }}
          </text>
        </view>
      </scroll-view>
    </view>

    <!-- 输入栏 -->
    <view class="input-bar">
      <view class="input-wrapper">
        <textarea
          v-model="inputText"
          class="comment-textarea"
          placeholder="说点什么..."
          :auto-height="true"
          :max-height="200"
          :show-confirm-bar="false"
          @focus="onInputFocus"
          @blur="onInputBlur"
        />
        <view class="input-actions">
          <view class="emoji-btn" @click="toggleEmojiPanel">
            <uni-icons type="emoji" size="24" color="#8B5CF6"></uni-icons>
          </view>
          <button 
            class="send-btn" 
            :class="{ active: canSend }" 
            :disabled="!canSend"
            @click="handleSend"
          >
            发送
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

const emit = defineEmits(['onSubmit', 'onEmoji']);

const inputText = ref('');
const showEmojiPanel = ref(false);
const isInputFocused = ref(false);

const canSend = computed(() => inputText.value.trim().length > 0);

// 常用 emoji 表情
const emojiList = [
  '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
  '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
  '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
  '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
  '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬',
  '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗',
  '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯',
  '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
  '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '😈',
  '👿', '👹', '👺', '🤡', '💩', '👻', '💀', '☠️', '👽', '👾'
];

const toggleEmojiPanel = () => {
  showEmojiPanel.value = !showEmojiPanel.value;
  emit('onEmoji', showEmojiPanel.value);
};

const insertEmoji = (emoji: string) => {
  inputText.value += emoji;
};

const onInputFocus = () => {
  isInputFocused.value = true;
  showEmojiPanel.value = false;
};

const onInputBlur = () => {
  isInputFocused.value = false;
};

const handleSend = () => {
  if (!canSend.value) return;
  
  const content = inputText.value.trim();
  emit('onSubmit', content);
  inputText.value = '';
  showEmojiPanel.value = false;
};
</script>

<style scoped lang="scss">
.comment-input-container {
  background-color: #fff;
  border-top: 1rpx solid #F3F4F6;
}

.emoji-panel {
  background-color: #F9FAFB;
  border-bottom: 1rpx solid #F3F4F6;
  padding: 20rpx 0;
}

.emoji-scroll {
  height: 200rpx;
}

.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0 20rpx;
  gap: 16rpx;
}

.emoji-item {
  font-size: 48rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  transition: background-color 0.2s ease;
  
  &:active {
    background-color: #E5E7EB;
  }
}

.input-bar {
  padding: 20rpx;
}

.input-wrapper {
  background-color: #F9FAFB;
  border-radius: 48rpx;
  padding: 16rpx 20rpx;
  display: flex;
  align-items: flex-end;
  gap: 16rpx;
  border: 2rpx solid transparent;
  transition: border-color 0.3s ease;
  
  &:focus-within {
    border-color: #8B5CF6;
    background-color: #fff;
  }
}

.comment-textarea {
  flex: 1;
  min-height: 40rpx;
  max-height: 200rpx;
  font-size: 28rpx;
  color: #374151;
  line-height: 1.5;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  
  &::placeholder {
    color: #9CA3AF;
  }
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-shrink: 0;
}

.emoji-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  
  &:active {
    background-color: #E5E7EB;
  }
}

.send-btn {
  background-color: #E5E7EB;
  color: #9CA3AF;
  font-size: 26rpx;
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  line-height: 1;
  margin: 0;
  border: none;
  transition: all 0.3s ease;
  
  &.active {
    background-color: #8B5CF6;
    color: white;
    transform: scale(1.02);
  }
  
  &:active.active {
    transform: scale(0.98);
  }
}
</style> 
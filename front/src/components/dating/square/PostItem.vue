<template>
  <view class="post-card" @click="emitAction('view-post', { id: post.id })">
    <!-- Card Header -->
    <view class="card-header">
      <view class="user-info">
        <image class="avatar" :src="post.user.avatar" mode="aspectFill" @click.stop="emitAction('view-profile', { id: post.user.id })" />
        <view class="user-details">
          <text class="user-name">{{ post.user.name }}</text>
          <view class="meta-info">
            <text>{{ post.timeAgo }}</text>
            <text class="dot">·</text>
            <text>{{ post.distance }}</text>
          </view>
        </view>
      </view>
      <view class="actions">
        <button class="follow-btn" @click.stop="emitAction('follow', { id: post.user.id })">关注</button>
       
      </view>
    </view>

    <!-- Card Body -->
    <view class="card-body">
      <text class="content-text">{{ post.content }}</text>
      <view v-if="post.images && post.images.length" class="image-grid" :class="`grid-${post.images.length}`">
         <image v-for="(image, index) in post.images" :key="index" :src="image" class="grid-image" mode="aspectFill" @click.stop="previewImage(index)" />
      </view>
      
      <!-- 话题标签 -->
      <view v-if="post.topics && post.topics.length" class="topic-tags">
        <view v-for="(topic, index) in post.topics" :key="index" class="topic-tag" @click.stop="emitAction('view-topic', { topic })">
          {{ topic }}
        </view>
      </view>
      
      <!-- 点赞评论 -->
      <view class="social-stats">
        <view class="stat-item" @click.stop="emitAction('like', { id: post.id })">
          <uni-icons :type="post.isLiked ? 'heart-filled' : 'heart'" :color="post.isLiked ? '#EF4444' : '#6B7280'" size="22"></uni-icons>
          <text class="stat-count">{{ post.likes }}</text>
        </view>
        <view class="stat-item" @click.stop="emitAction('comment', { id: post.id })">
          <uni-icons type="chatbubble" color="#6B7280" size="22"></uni-icons>
          <text class="stat-count">{{ post.comments }}</text>
        </view>
      </view>
    </view>

    <!-- Card Footer -->
    <view class="card-footer">
      <uni-icons type="more-filled" size="24" color="#9CA3AF" @click.stop="emitAction('more', { id: post.id })"></uni-icons>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{
  post: {
    id: number;
    user: { id: number; name: string; avatar: string };
    content: string;
    images?: string[];
    topics: string[];
    likes: number;
    comments: number;
    isLiked: boolean;
    distance: string;
    timeAgo: string;
  };
}>();

const emit = defineEmits(['onAction']);

const emitAction = (type: string, payload: any) => {
  emit('onAction', { type, payload });
};

const previewImage = (index: number) => {
  uni.previewImage({
    current: index,
    urls: props.post.images || [],
  });
};
</script>

<style scoped lang="scss">
.post-card {
  background-color: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  &:active {
    transform: scale(0.99);
    box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.08);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
}

.meta-info {
  font-size: 24rpx;
  color: var(--text-info);
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.follow-btn {
  font-size: 26rpx;
  background-color: #8B5CF6;
  color: white;
  padding: 10rpx 24rpx;
  border-radius: 30rpx;
  line-height: 1.5;
  margin: 0;
  &:active {
    background-color: #7c4ee4;
  }
}

.card-body {
  margin-bottom: 16rpx;
}

.content-text {
  font-size: 30rpx;
  color: #374151;
  line-height: 1.6;
  display: block;
  margin-bottom: 16rpx;
}

.image-grid {
  display: grid;
  gap: 8rpx;
  margin: 24rpx 0;
  
  // 1张图片：较大尺寸
  &.grid-1 { 
    grid-template-columns: 1fr;
    max-width: 400rpx;
    .grid-image {
      height: 400rpx;
    }
  }
  
  // 2张图片：1:1比例，稍大
  &.grid-2 { 
    grid-template-columns: 1fr 1fr;
    max-width: 500rpx;
    .grid-image {
      height: 240rpx;
    }
  }
  
  // 3张图片：第一张占两列，下面两张各占一列
  &.grid-3 { 
    grid-template-columns: 1fr 1fr;
    max-width: 500rpx;
    .grid-image:first-child {
      grid-column: 1 / -1;
      height: 240rpx;
    }
    .grid-image:not(:first-child) {
      height: 120rpx;
    }
  }
  
  // 4张图片：2x2网格，稍大
  &.grid-4 { 
    grid-template-columns: 1fr 1fr;
    max-width: 500rpx;
    .grid-image {
      height: 240rpx;
    }
  }
  
  // 5-6张图片：3列网格
  &.grid-5, &.grid-6 { 
    grid-template-columns: repeat(3, 1fr);
    .grid-image {
      height: 160rpx;
    }
  }
  
  // 7-9张图片：3x3网格
  &.grid-7, &.grid-8, &.grid-9 { 
    grid-template-columns: repeat(3, 1fr);
    .grid-image {
      height: 160rpx;
    }
  }
  
  .grid-image {
    width: 100%;
    border-radius: 12rpx;
    background-color: #f5f5f5;
    transition: transform 0.2s ease;
    
    &:active {
      transform: scale(0.98);
    }
  }
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #F3F4F6;
}

.topic-tags {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
  margin: 20rpx 0 16rpx 0;
}

.topic-tag {
  background-color: #EEF2FF;
  color: #6366F1;
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-weight: 500;
  
  &:active {
    background-color: #E0E7FF;
  }
}

.social-stats {
  display: flex;
  align-items: center;
  gap: 40rpx;
  margin-top: 16rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #6B7280;
  font-size: 28rpx;
  transition: color 0.2s ease;
  
  &:active {
    color: #8B5CF6;
  }
}
</style> 
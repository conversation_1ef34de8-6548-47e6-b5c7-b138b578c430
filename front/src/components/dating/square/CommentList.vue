<template>
  <view class="comment-list">
    <view v-for="comment in comments" :key="comment.id" class="comment-item">
      <!-- 主评论 -->
      <view class="comment-main">
        <image class="comment-avatar" :src="comment.user.avatar" mode="aspectFill" @click="goToUserProfile(comment.user.id)" />
        <view class="comment-content">
          <view class="comment-header">
            <text class="comment-user">{{ comment.user.name }}</text>
            <text class="comment-time">{{ comment.timeAgo }}</text>
          </view>
          <text class="comment-text">{{ comment.content }}</text>
          <view class="comment-actions">
            <view class="action-item" @click="emitAction('like', { id: comment.id })">
              <uni-icons :type="comment.isLiked ? 'heart-filled' : 'heart'" :color="comment.isLiked ? '#EF4444' : '#9CA3AF'" size="16"></uni-icons>
              <text class="action-text" :class="{ liked: comment.isLiked }">{{ comment.likes || '点赞' }}</text>
            </view>
            <view class="action-item" @click="emitAction('reply', { id: comment.id, user: comment.user })">
              <uni-icons type="chatbubble" color="#9CA3AF" size="16"></uni-icons>
              <text class="action-text">回复</text>
            </view>
            <view class="action-item" @click="showCommentActions(comment)">
              <uni-icons type="more-filled" color="#9CA3AF" size="16"></uni-icons>
            </view>
          </view>
        </view>
      </view>

      <!-- 回复列表 -->
      <view v-if="comment.replies && comment.replies.length" class="replies-container">
        <view v-for="reply in comment.replies" :key="reply.id" class="reply-item">
          <image class="reply-avatar" :src="reply.user.avatar" mode="aspectFill" @click="goToUserProfile(reply.user.id)" />
          <view class="reply-content">
            <view class="reply-header">
              <text class="reply-user">{{ reply.user.name }}</text>
              <text v-if="reply.replyTo" class="reply-to">回复 {{ reply.replyTo }}</text>
              <text class="reply-time">{{ reply.timeAgo }}</text>
            </view>
            <text class="reply-text">{{ reply.content }}</text>
            <view class="reply-actions">
              <view class="action-item" @click="emitAction('like', { id: reply.id })">
                <uni-icons :type="reply.isLiked ? 'heart-filled' : 'heart'" :color="reply.isLiked ? '#EF4444' : '#9CA3AF'" size="14"></uni-icons>
                <text class="action-text" :class="{ liked: reply.isLiked }">{{ reply.likes || '点赞' }}</text>
              </view>
              <view class="action-item" @click="emitAction('reply', { id: reply.id, user: reply.user, parentId: comment.id })">
                <uni-icons type="chatbubble" color="#9CA3AF" size="14"></uni-icons>
                <text class="action-text">回复</text>
              </view>
              <view class="action-item" @click="showCommentActions(reply)">
                <uni-icons type="more-filled" color="#9CA3AF" size="14"></uni-icons>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="!comments.length" class="empty-comments">
      <text class="empty-text">暂无评论，快来抢沙发吧～</text>
    </view>
  </view>
</template>

<script setup lang="ts">
interface User {
  id: number;
  name: string;
  avatar: string;
}

interface Comment {
  id: number;
  user: User;
  content: string;
  timeAgo: string;
  likes: number;
  isLiked: boolean;
  replies?: Comment[];
  replyTo?: string;
}

const props = defineProps<{
  comments: Comment[];
}>();

const emit = defineEmits(['onAction']);

const emitAction = (type: string, payload: any) => {
  emit('onAction', { type, payload });
};

const goToUserProfile = (userId: number) => {
  uni.navigateTo({
    url: `/pages/dating/user-detail?userId=${userId}`
  });
};

const showCommentActions = (comment: Comment) => {
  uni.showActionSheet({
    itemList: ['举报', '复制'],
    success: (res) => {
      if (res.tapIndex === 0) {
        emitAction('report', { id: comment.id });
      } else if (res.tapIndex === 1) {
        uni.setClipboardData({
          data: comment.content,
          success: () => {
            uni.showToast({
              title: '已复制到剪贴板',
              icon: 'success'
            });
          }
        });
      }
    }
  });
};
</script>

<style scoped lang="scss">
.comment-list {
  display: flex;
  flex-direction: column;
}

.comment-item {
  margin-bottom: 32rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.comment-main {
  display: flex;
  align-items: flex-start;
}

.comment-avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
  min-width: 0;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  gap: 16rpx;
}

.comment-user {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
}

.comment-time {
  font-size: 24rpx;
  color: #9CA3AF;
}

.comment-text {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.5;
  display: block;
  margin-bottom: 16rpx;
  word-break: break-all;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 0;
}

.action-text {
  font-size: 24rpx;
  color: #9CA3AF;
  
  &.liked {
    color: #EF4444;
  }
}

.replies-container {
  margin-top: 20rpx;
  margin-left: 92rpx;
}

.reply-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.reply-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.reply-content {
  flex: 1;
  min-width: 0;
}

.reply-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  gap: 12rpx;
  flex-wrap: wrap;
}

.reply-user {
  font-size: 26rpx;
  font-weight: 600;
  color: #374151;
}

.reply-to {
  font-size: 24rpx;
  color: #8B5CF6;
}

.reply-time {
  font-size: 22rpx;
  color: #9CA3AF;
}

.reply-text {
  font-size: 26rpx;
  color: #374151;
  line-height: 1.5;
  display: block;
  margin-bottom: 12rpx;
  word-break: break-all;
}

.reply-actions {
  display: flex;
  align-items: center;
  gap: 24rpx;
  
  .action-text {
    font-size: 22rpx;
  }
}

.empty-comments {
  padding: 80rpx 0;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #9CA3AF;
}
</style> 
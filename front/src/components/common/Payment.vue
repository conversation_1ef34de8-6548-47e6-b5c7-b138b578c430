<template>
  <view class="payment-container">
    <tui-bottom-popup
      :show="show"
      :height="800"
      :isSafeArea="true"
      :zIndex="1002"
      :maskZIndex="1001"
      backgroundColor="#ffffff"
      @close="close"
    >
      <view class="payment-popup">
        <view class="popup-header">
          <text class="header-title">选择支付方式</text>
          <text class="close-btn i-carbon-close" @tap="close"></text>
        </view>

        <view class="popup-content">
          <view class="payment-amount">
            <text class="amount-label">支付金额</text>
            <text class="amount-value">¥{{ amount }}</text>
          </view>

          <view class="payment-methods">
            <view
              v-for="(method, index) in paymentMethods"
              :key="index"
              class="method-item"
              @tap="selectMethod(index)"
            >
              <view class="method-icon">
                <text :class="method.icon"></text>
              </view>
              <view class="method-info">
                <text class="method-name">{{ method.name }}</text>
                <text class="method-desc">{{ method.description }}</text>
              </view>
              <view class="method-radio">
                <view
                  class="radio-inner"
                  :class="{ selected: selectedMethod === index }"
                ></view>
              </view>
            </view>
          </view>
        </view>

        <view class="popup-footer">
          <view class="footer-btn submit-btn gradient-btn" @tap="submitPayment">
            <text>确认支付</text>
          </view>
        </view>
      </view>
    </tui-bottom-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from "vue";

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  amount: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(["close", "success"]);

const selectedMethod = ref(0);

const paymentMethods = ref([
  {
    name: "微信支付",
    description: "使用微信安全支付",
    icon: "i-carbon-logo-weixin",
  },
  {
    name: "支付宝",
    description: "使用支付宝安全支付",
    icon: "i-carbon-logo-alipay",
  },
]);

const selectMethod = (index: number) => {
  selectedMethod.value = index;
};

const close = () => {
  emit("close");
};

const submitPayment = () => {
  // 模拟支付成功
  uni.showLoading({
    title: "支付中...",
  });

  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: "支付成功",
      icon: "success",
    });
    emit("success");
    close();
  }, 1500);
};
</script>

<style lang="scss" scoped>
.payment-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f2f5;
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  font-size: 32rpx;
  color: #999;
  padding: 8rpx;
}

.popup-content {
  flex: 1;
  padding: 32rpx;
}

.payment-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;

  .amount-label {
    font-size: 28rpx;
    color: #666;
  }

  .amount-value {
    font-size: 40rpx;
    font-weight: 700;
    color: #ff5050;
  }
}

.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.method-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &.selected {
    border-color: #2673ff;
    background-color: #f0f8ff;
  }
}

.method-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  color: #333;
}

.method-info {
  flex: 1;

  .method-name {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
  }

  .method-desc {
    font-size: 24rpx;
    color: #999;
  }
}

.method-radio {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: transparent;
  transition: all 0.3s ease;

  &.selected {
    background-color: #2673ff;
  }
}

.popup-footer {
  padding: 32rpx;
  border-top: 1rpx solid #f0f2f5;
}

.footer-btn {
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  color: white;
}

.gradient-btn {
  background: linear-gradient(135deg, #2673ff, #667eea);
  box-shadow: 0 8rpx 24rpx rgba(38, 115, 255, 0.3);
}
</style>

<template>
  <view class="loading-state" :class="loadingClass">
    <!-- Spinner 模式 -->
    <view v-if="config.type === 'spinner'" class="loading-content">
      <view class="loading-spinner">
        <view class="spinner-icon">
          <text class="i-solar:refresh-circle-broken animate-spin"></text>
        </view>
      </view>
      <text v-if="config.showText && config.text" class="loading-text">
        {{ config.text }}
      </text>
    </view>

    <!-- 骨架屏模式 -->
    <view v-else-if="config.type === 'skeleton'" class="loading-skeleton">
      <view class="skeleton-item skeleton-title"></view>
      <view class="skeleton-item skeleton-text"></view>
      <view class="skeleton-item skeleton-text short"></view>
      <view class="skeleton-card">
        <view class="skeleton-item skeleton-avatar"></view>
        <view class="skeleton-item skeleton-text"></view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { LoadingConfig } from "@/utils/ui/page-state";
import { DEFAULT_LOADING_CONFIG } from "@/utils/ui/page-state";

interface Props {
  config?: Partial<LoadingConfig>;
  fullHeight?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({}),
  fullHeight: true,
});

// 合并配置
const config = computed(() => ({
  ...DEFAULT_LOADING_CONFIG,
  ...props.config,
}));

// 动态类名
const loadingClass = computed(() => ({
  "loading-state--full-height": props.fullHeight,
  [`loading-state--${config.value.type}`]: true,
}));
</script>

<style lang="scss" scoped>
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48rpx;

  &--full-height {
    min-height: 400rpx;
  }
}

// ==================== Spinner 模式 ====================
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-3);
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-icon {
  font-size: 48rpx;
  color: var(--primary);

  text {
    display: inline-block;
  }
}

.loading-text {
  font-size: 26rpx;
  color: var(--text-secondary);
  text-align: center;
}

// ==================== 骨架屏模式 ====================
.loading-skeleton {
  width: 100%;
  max-width: 600rpx;
  padding: var(--spacing);
}

.skeleton-item {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius);
  margin-bottom: var(--spacing-3);

  &.skeleton-title {
    height: 48rpx;
    width: 80%;
  }

  &.skeleton-text {
    height: 32rpx;
    width: 100%;

    &.short {
      width: 60%;
    }
  }

  &.skeleton-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: var(--spacing-3);
  }
}

.skeleton-card {
  display: flex;
  align-items: center;
  margin-top: 40rpx;

  .skeleton-text {
    flex: 1;
    margin-bottom: 0;
  }
}

// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// 旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .skeleton-item {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }
}
</style>

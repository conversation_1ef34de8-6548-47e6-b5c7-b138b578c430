<!-- 金额显示组件 -->
<template>
  <view
    class="amount-display"
    :class="[`amount-${size}`, customClass]"
    :style="customStyle"
    @click="handleClick"
  >
    <!-- 货币符号 -->
    <text class="amount-symbol" :style="symbolStyle">
      {{ currency }}
    </text>

    <!-- 金额数字 -->
    <text class="amount-value" :style="valueStyle">
      {{ formattedAmount }}
    </text>

    <!-- 单位 -->
    <text v-if="unit" class="amount-unit" :style="unitStyle">
      {{ unit }}
    </text>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface Props {
  // 金额数值
  amount: number | string;
  // 货币符号
  currency?: string;
  // 单位（如：元/月、元/天、元/小时等）
  unit?: string;
  // 整体尺寸：small, medium, large
  size?: "small" | "medium" | "large";
  // 货币符号颜色
  symbolColor?: string;
  // 金额数字颜色
  valueColor?: string;
  // 单位颜色
  unitColor?: string;
  // 是否显示千分位分隔符
  separator?: boolean;
  // 小数位数
  decimal?: number;
  // 自定义样式类
  customClass?: string;
  // 自定义样式
  customStyle?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  currency: "￥",
  size: "medium",
  valueColor: "#FF4D4F", // 默认红色
  separator: true,
  decimal: 0,
});

const emit = defineEmits<{
  click: [amount: number | string];
}>();

// 尺寸配置
const sizeConfig = {
  small: {
    symbol: "28rpx",
    value: "36rpx",
    unit: "28rpx",
    gap: "2rpx",
  },
  medium: {
    symbol: "32rpx",
    value: "40rpx",
    unit: "28rpx",
    gap: "4rpx",
  },
  large: {
    symbol: "36rpx",
    value: "48rpx",
    unit: "32rpx",
    gap: "6rpx",
  },
};

// 格式化金额
const formattedAmount = computed(() => {
  let num =
    typeof props.amount === "string" ? parseFloat(props.amount) : props.amount;

  if (isNaN(num)) return "0";

  // 处理小数位
  if (props.decimal > 0) {
    num = parseFloat(num.toFixed(props.decimal));
  }

  let result = num.toString();

  // 添加千分位分隔符
  if (props.separator && num >= 1000) {
    result = num.toLocaleString("zh-CN", {
      minimumFractionDigits: props.decimal,
      maximumFractionDigits: props.decimal,
    });
  }

  return result;
});

// 符号样式
const symbolStyle = computed(() => {
  const config = sizeConfig[props.size];
  return {
    fontSize: config.symbol,
    color: props.symbolColor || "var(--text-base)",
    fontWeight: "normal",
  };
});

// 数值样式
const valueStyle = computed(() => {
  const config = sizeConfig[props.size];
  return {
    fontSize: config.value,
    color: props.valueColor,
    fontWeight: "bold",
    fontVariantNumeric: "tabular-nums",
  };
});

// 单位样式
const unitStyle = computed(() => {
  const config = sizeConfig[props.size];
  return {
    fontSize: config.unit,
    color: props.unitColor || "var(--text-base)",
    fontWeight: "normal",
    marginLeft: config.gap,
  };
});

// 点击事件
const handleClick = () => {
  emit("click", props.amount);
};
</script>

<style scoped>
.amount-display {
  display: flex;
  align-items: baseline;
  line-height: var(--line-height-normal);
}

.amount-value {
  /* 数字等宽字体，保持对齐 */
  font-variant-numeric: tabular-nums;
}

/* 尺寸变体 */
.amount-small {
  gap: 2rpx;
}

.amount-medium {
  gap: 4rpx;
}

.amount-large {
  gap: 6rpx;
}
</style>

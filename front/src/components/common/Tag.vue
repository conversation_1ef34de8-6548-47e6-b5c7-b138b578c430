<!-- 标签组件 -->
<template>
  <view
    @tap="handleClick"
    :style="{
      backgroundColor: backgroundColor,
      color: color,
      padding: padding,
      margin: margin,
      borderRadius: circle ? '160rpx' : radius,
      fontSize: fontSize,
      fontWeight: isActive ? '500' : '400',
      ...style,
      ...(isActive ? activeStyle : {}),
    }"
  >
    {{ text }}
  </view>
</template>

<script setup lang="ts">
const emit = defineEmits(["click"]);

const handleClick = () => {
  emit("click", props.text);
};

const props = defineProps({
  text: {
    type: String,
    default: "",
  },
  // 大小：normal, small, large
  size: {
    type: String,
    default: "normal",
  },
  fontSize: {
    type: String,
    default: "28rpx",
  },
  // 是否圆角
  circle: {
    type: Boolean,
    default: false,
  },
  // 圆角大小
  radius: {
    type: String,
    default: "8rpx",
  },
  // 内边距
  padding: {
    type: String,
    default: "8rpx 24rpx",
  },
  margin: {
    type: String,
    default: "0 8rpx 8rpx 0",
  },
  // 背景颜色
  backgroundColor: {
    type: String,
    default: "var(--bg-tag)",
  },
  // 文字颜色
  color: {
    type: String,
    default: "var(--text-secondary)",
  },
  style: {
    type: Object,
    default: () => ({}),
  },
  isActive: {
    type: Boolean,
    default: false,
  },
  // 激活时加粗
  activeStyle: {
    type: Object,
    default: () => ({
      color: "var(--text-base)",
    }),
  },
});
</script>

<style scoped></style>

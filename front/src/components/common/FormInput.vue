<template>
  <view class="form-input-container">
    <tui-form-item
      :prop="prop"
      :label="label"
      :labelSize="labelSize"
      :labelColor="labelColor"
      :asterisk="asterisk"
      :arrow="arrow"
      :asteriskColor="asteriskColor"
      :bottomBorder="bottomBorder"
      @click="handleClick"
    >
      <slot>
        <tui-input
          v-model="inputValue"
          :placeholder="placeholder"
          :borderBottom="false"
          padding="0"
          :color="inputColor"
          :placeholderStyle="placeholderStyle"
          :disabled="disabled"
          @input="handleInput"
          @blur="handleBlur"
        />
      </slot>
      <template #right v-if="unit">
        <text>{{ unit }}</text>
      </template>
    </tui-form-item>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from "vue";

const props = defineProps({
  prop: String,
  label: String,
  labelSize: {
    type: String,
    default: "30rpx",
  },
  labelColor: String,
  asterisk: Boolean,
  arrow: Boolean,
  asteriskColor: {
    type: String,
    default: "#ff4757",
  },
  bottomBorder: {
    type: Boolean,
    default: true,
  },
  placeholder: String,
  inputColor: {
    type: String,
    default: "var(--text-base)",
  },
  placeholderStyle: {
    type: String,
    default: "color: var(--text-grey)",
  },
  disabled: Boolean,
  unit: String,
  modelValue: [String, Number],
});

const emit = defineEmits(["update:modelValue", "click", "input", "blur"]);

const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const handleClick = () => {
  emit("click");
};

const handleInput = (value: any) => {
  emit("input", value);
};

const handleBlur = (value: any) => {
  emit("blur", value);
};
</script>

<style lang="scss" scoped>
.form-input-container {
  /* Add any specific styling for the container if needed */
}
</style>

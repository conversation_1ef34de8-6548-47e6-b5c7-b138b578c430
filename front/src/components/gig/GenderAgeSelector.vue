<template>
  <tui-bottom-popup
    :show="show"
    @close="handleClose"
    :mask="true"
    :z-index="1001"
    :mask-z-index="1000"
  >
    <view class="gender-age-selector">
      <!-- 弹框头部 -->
      <view class="popup-header">
        <text class="popup-title">性别年龄</text>
        <text class="close-btn" @click="handleClose">×</text>
      </view>

      <!-- 性别选择 -->
      <view class="gender-section">
        <view class="gender-buttons">
          <view
            v-for="option in genderOptions"
            :key="option.code"
            class="gender-btn"
            :class="{ active: selectedGender === option.code }"
            @click="selectGender(option.code)"
          >
            <text class="gender-btn-text">{{ option.label }}</text>
          </view>
        </view>
      </view>

      <!-- 年龄显示 -->
      <view class="age-display">
        <text class="age-text">{{ ageDisplayText }}</text>
      </view>

      <!-- 年龄范围滑块 -->
      <view class="age-slider-container">
        <tui-slider
          :min="MIN_AGE"
          :max="MAX_AGE"
          :step="1"
          :value="currentMinAge"
          :endValue="currentMaxAge"
          :section="true"
          :width="300"
          :height="6"
          showValue
          activeColor="#e9e9e9"
          backgroundColor="var(--primary)"
          :blockWidth="20"
          :blockHeight="20"
          @change="onAgeRangeChange"
        />

        <!-- 年龄刻度标识 -->
        <view class="age-marks">
          <text class="age-mark">16</text>
          <text class="age-mark">20</text>
          <text class="age-mark">30</text>
          <text class="age-mark">40</text>
          <text class="age-mark">50</text>
          <text class="age-mark">不限</text>
        </view>
      </view>

      <!-- 确认按钮 -->
      <view class="confirm-section">
        <tui-button
          @click="handleConfirm"
          preventClick
          width="100%"
          height="88rpx"
          type="primary"
          shape="circle"
          class="background-color:var(--primary)"
        >
          确定
        </tui-button>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { genderOptions } from "@/constants/standards";

// 类型定义
interface SliderChangeEvent {
  detail: {
    value: number;
    endValue: number;
  };
}

// 常量定义
const MIN_AGE = 16;
const MAX_AGE = 65;

// 组件属性
interface Props {
  show: boolean;
  gender?: number;
  minAge?: number;
  maxAge?: number;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  gender: 0, // 默认不限
  minAge: 16,
  maxAge: 65,
});

// 事件定义
const emit = defineEmits<{
  close: [];
  confirm: [gender: number, minAge: number, maxAge: number];
}>();

// 响应式数据
const selectedGender = ref(0);
const currentMinAge = ref(MIN_AGE);
const currentMaxAge = ref(MAX_AGE);

// 初始化标志，避免初始化时触发slider事件
const isInitialized = ref(false);

// 计算属性
const ageDisplayText = computed(() => {
  const minText =
    currentMinAge.value === MIN_AGE ? "16" : currentMinAge.value.toString();
  const maxText =
    currentMaxAge.value === MAX_AGE ? "不限" : currentMaxAge.value.toString();

  if (currentMinAge.value === MIN_AGE && currentMaxAge.value === MAX_AGE) {
    return "16岁~不限";
  }

  return `${minText}岁~${maxText === "不限" ? "不限" : maxText + "岁"}`;
});

// 方法
const selectGender = (genderCode: number) => {
  selectedGender.value = genderCode;
};

const onAgeRangeChange = (e: SliderChangeEvent) => {
  // 避免初始化时的事件触发
  if (!isInitialized.value) {
    return;
  }

  // 验证事件数据的有效性
  if (
    e?.detail &&
    typeof e.detail.value === "number" &&
    typeof e.detail.endValue === "number"
  ) {
    currentMinAge.value = Math.max(MIN_AGE, Math.min(MAX_AGE, e.detail.value));
    currentMaxAge.value = Math.max(
      MIN_AGE,
      Math.min(MAX_AGE, e.detail.endValue)
    );
  } else {
    console.warn("Invalid slider event data:", e);
  }
};

const handleClose = () => {
  emit("close");
};

const handleConfirm = () => {
  emit(
    "confirm",
    selectedGender.value,
    currentMinAge.value,
    currentMaxAge.value
  );
  handleClose();
};

// 初始化组件数据
const initializeData = () => {
  selectedGender.value = props.gender || 0;
  currentMinAge.value = props.minAge || MIN_AGE;
  currentMaxAge.value = props.maxAge || MAX_AGE;
};

// 组件挂载时初始化
onMounted(() => {
  initializeData();
  // 延迟设置初始化标志，确保slider组件已经完成渲染
  setTimeout(() => {
    isInitialized.value = true;
  }, 100);
});

// 监听props变化（移除immediate选项避免初始化时触发）
watch(
  [() => props.gender, () => props.minAge, () => props.maxAge],
  ([newGender, newMinAge, newMaxAge]) => {
    // 临时禁用初始化标志避免触发slider事件
    const wasInitialized = isInitialized.value;
    isInitialized.value = false;

    selectedGender.value = newGender || 0;
    currentMinAge.value = newMinAge || MIN_AGE;
    currentMaxAge.value = newMaxAge || MAX_AGE;

    // 恢复初始化标志
    setTimeout(() => {
      isInitialized.value = wasInitialized;
    }, 50);
  }
);
</script>

<style lang="scss" scoped>
.gender-age-selector {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;

  .popup-title {
    font-size: 36rpx;
    font-weight: 600;
  }

  .close-btn {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48rpx;
    color: var(--text-secondary);
    line-height: 1;
  }
}

.gender-section {
  padding: 32rpx;

  .gender-buttons {
    display: flex;
    gap: 16rpx;

    .gender-btn {
      flex: 1;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--bg-tag);
      border: 2rpx solid transparent;
      border-radius: 12rpx;
      transition: all 0.3s ease;

      &.active {
        background-color: var(--primary-100);

        .gender-btn-text {
          font-weight: 500;
          color: var(--primary);
        }
      }

      .gender-btn-text {
        font-size: 28rpx;
        color: var(--text-secondary);
      }
    }
  }
}

.age-display {
  margin-top: 40rpx;
  text-align: center;
  padding: 0 32rpx 24rpx;

  .age-text {
    font-size: 32rpx;
    font-weight: 600;
  }
}

.age-slider-container {
  padding: 0 32rpx 32rpx;
  position: relative;

  .age-marks {
    display: flex;
    justify-content: space-between;
    margin-top: 24rpx;
    padding: 0 10rpx;

    .age-mark {
      font-size: 24rpx;
      color: var(--text-info);
      text-align: center;
      flex-shrink: 0;
    }
  }
}

.confirm-section {
  margin-top: 24rpx;
  padding: 0 32rpx 32rpx;
}
</style>

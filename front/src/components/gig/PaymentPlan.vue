<template>
  <view class="payment-plan-container">
    <view class="section-title">付费方案</view>
    <view class="plan-card selected">
      <view class="plan-name">信息发布</view>
      <view class="plan-price">
        <text class="price-value">¥3</text>
        <text class="price-duration">/次</text>
      </view>
      <view class="plan-features">
        <view class="feature-item">
          <text class="i-carbon-checkmark"></text>
          <text>标准展示</text>
        </view>
        <view class="feature-item">
          <text class="i-carbon-checkmark"></text>
          <text>7天有效期</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts"></script>

<style lang="scss" scoped>
.payment-plan-container {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-title);
  margin-bottom: 24rpx;
}

.plan-card {
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  padding: 24rpx;
  transition: all 0.3s;
  position: relative;
  background-color: var(--bg-card);

  &.selected {
    border-color: var(--brand-primary);
    background-color: #f6faff;
  }
}

.plan-name {
  font-size: 28rpx;
  font-weight: bold;
}

.plan-price {
  margin: 16rpx 0;
  display: flex;
  align-items: baseline;
  color: var(--text-title);

  .price-value {
    font-size: 48rpx;
    font-weight: bold;
    color: var(--brand-primary);
  }

  .price-duration {
    font-size: 24rpx;
    color: var(--text-secondary);
    margin-left: 8rpx;
  }
}

.plan-features {
  .feature-item {
    display: flex;
    align-items: center;
    font-size: 24rpx;

    margin-top: 12rpx;

    .i-carbon-checkmark {
      color: var(--brand-primary);
      margin-right: 8rpx;
      font-size: 28rpx;
    }
  }
}
</style>

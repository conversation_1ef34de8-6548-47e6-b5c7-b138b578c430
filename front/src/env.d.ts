/// <reference types="vite/client" />

declare module '*.vue' {
  import { DefineComponent } from 'vue'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare const __APP_ENV__: string
declare const __APP_API_URL__: string
declare const __APP_API_KEY__: string
declare const __APP_API_SECRET__: string
declare const __APP_API_TOKEN__: string

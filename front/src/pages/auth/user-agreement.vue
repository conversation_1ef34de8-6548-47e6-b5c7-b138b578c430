<template>
  <view class="user-agreement-page">
    <view class="content">
      <view class="title">用户注册协议</view>
      <view class="paragraph">
        欢迎您使用本地宝平台！本《用户注册协议》（以下简称"本协议"）是您与本地宝平台（以下简称"平台"或"我们"）之间关于您注册、使用平台服务所订立的协议。在您注册使用平台服务前，请您务必仔细阅读并充分理解本协议条款内容。
      </view>

      <view class="subtitle">第一条 协议的接受与生效</view>
      <view class="paragraph">
        1.1
        您点击"同意"按钮或以其他方式选择接受本协议，即表示您完全接受本协议项下的全部条款。
      </view>
      <view class="paragraph">
        1.2 本协议自您完成注册程序之日起生效，在您使用平台服务期间持续有效。
      </view>

      <view class="subtitle">第二条 平台服务内容</view>
      <view class="paragraph">本地宝平台为用户提供以下服务：</view>
      <view class="list-item"
        >2.1
        求职招聘服务：为求职者和招聘方提供职位信息发布、简历投递、在线沟通等服务</view
      >
      <view class="list-item"
        >2.2
        房产服务：提供租房、二手房、新房、商铺写字楼等房产信息发布与查找服务</view
      >
      <view class="list-item"
        >2.3
        零工服务：提供临时性工作、家政服务、维修服务等零工信息发布与匹配服务</view
      >
      <view class="list-item"
        >2.4 交友相亲服务：提供真实身份认证的交友平台和相亲服务</view
      >
      <view class="list-item"
        >2.5
        本地生活服务：提供家政保洁、装修维修、搬家拉货、保姆月嫂等本地服务信息</view
      >
      <view class="list-item"
        >2.6 消息通讯服务：为用户提供在线沟通、消息推送等功能</view
      >
      <view class="list-item">2.7 其他经平台公布的相关服务</view>

      <view class="subtitle">第三条 用户注册与账户管理</view>
      <view class="paragraph">3.1 用户注册：</view>
      <view class="list-item">您需要使用真实有效的手机号码注册账户</view>
      <view class="list-item">支持微信一键登录，授权获取微信基本信息</view>
      <view class="list-item"
        >您保证所提供的注册信息真实、准确、完整、有效</view
      >

      <view class="paragraph">3.2 实名认证：</view>
      <view class="list-item">为保障交易安全，某些服务需要您完成实名认证</view>
      <view class="list-item">您需提供真实的姓名、身份证号等信息</view>
      <view class="list-item">平台有权要求您提供身份证明材料</view>

      <view class="paragraph">3.3 账户安全：</view>
      <view class="list-item"
        >您应妥善保管账户信息，对账户下的一切行为负责</view
      >
      <view class="list-item">如发现账户被盗用，应立即通知平台</view>
      <view class="list-item"
        >平台有权基于安全考虑暂停或终止存在风险的账户</view
      >

      <view class="subtitle">第四条 用户行为规范</view>
      <view class="paragraph">您在使用平台服务时，应当遵守以下规定：</view>
      <view class="list-item"
        >4.1
        发布真实信息：所发布的招聘、房产、零工、交友等信息必须真实有效</view
      >
      <view class="list-item"
        >4.2
        禁止违法信息：不得发布违反法律法规的内容，包括但不限于黄赌毒、诈骗、传销等</view
      >
      <view class="list-item"
        >4.3 禁止骚扰行为：不得进行恶意骚扰、辱骂、威胁其他用户的行为</view
      >
      <view class="list-item"
        >4.4 禁止虚假交易：不得发布虚假的房源、职位、服务信息</view
      >
      <view class="list-item"
        >4.5 尊重他人权益：不得侵犯他人隐私、肖像权、知识产权等合法权益</view
      >
      <view class="list-item"
        >4.6 遵守交易规则：在平台进行交易时应遵守相关交易规则和流程</view
      >

      <view class="subtitle">第五条 信息发布与管理</view>
      <view class="paragraph">5.1 信息发布：</view>
      <view class="list-item">您发布的信息应当真实、合法、有效</view>
      <view class="list-item"
        >房产信息应包含真实的房源详情、价格、联系方式</view
      >
      <view class="list-item"
        >招聘信息应包含真实的岗位需求、薪资待遇、工作地点</view
      >
      <view class="list-item">零工信息应明确工作内容、时间要求、报酬标准</view>

      <view class="paragraph">5.2 信息审核：</view>
      <view class="list-item">平台有权对用户发布的信息进行审核</view>
      <view class="list-item">对于违规信息，平台有权删除或要求修改</view>
      <view class="list-item">平台保留对违规账户进行处罚的权利</view>

      <view class="subtitle">第六条 费用与支付</view>
      <view class="paragraph">6.1 免费服务：平台基础功能为用户免费提供</view>
      <view class="paragraph"
        >6.2 增值服务：部分高级功能可能收取合理费用，具体以页面展示为准</view
      >
      <view class="paragraph">6.3 支付方式：支持微信支付等主流支付方式</view>

      <view class="subtitle">第七条 隐私保护</view>
      <view class="paragraph">
        我们高度重视用户隐私保护，具体的个人信息收集、使用、存储等规则请参阅《隐私政策》。
      </view>

      <view class="subtitle">第八条 知识产权</view>
      <view class="paragraph"
        >8.1 平台拥有本服务的知识产权，包括但不限于商标、专利、著作权等</view
      >
      <view class="paragraph"
        >8.2
        您在平台上发布的内容，其知识产权归您所有。您特此授予我们一项免费的、全球范围内的、非独家的、可再许可的许可，允许我们存储、使用、复制、修订、发布、展示和分发此内容，仅用于运营、推广和改进我们的服务。</view
      >

      <view class="subtitle">第九条 免责声明</view>
      <view class="paragraph"
        >9.1 平台仅提供信息发布和匹配服务，不参与实际交易过程</view
      >
      <view class="paragraph"
        >9.2 用户间的纠纷应自行协商解决，平台可协助但不承担法律责任</view
      >
      <view class="paragraph"
        >9.3 因不可抗力导致的服务中断，平台不承担责任</view
      >

      <view class="subtitle">第十条 违约责任</view>
      <view class="paragraph"
        >10.1 用户违反本协议的，平台有权采取以下措施：</view
      >
      <view class="list-item">警告、限制功能、暂停服务</view>
      <view class="list-item">删除违规信息</view>
      <view class="list-item">终止服务协议</view>
      <view class="list-item">追究法律责任</view>

      <view class="subtitle">第十一条 协议变更</view>
      <view class="paragraph">
        平台有权根据业务发展需要修改本协议，修订后的协议将在平台公布，继续使用服务即视为接受修订后的协议。
      </view>

      <view class="subtitle">第十二条 争议解决</view>
      <view class="paragraph">
        本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。如双方就本协议内容或其执行发生任何争议，双方应尽量友好协商解决；协商不成时，任何一方均可向平台所在地人民法院起诉。</view
      >

      <view class="subtitle">第十三条 联系我们</view>
      <view class="paragraph">
        如果您对本协议有任何疑问，请通过以下方式与我们联系：
      </view>
      <view class="list-item">客服邮箱：<EMAIL></view>
      <view class="list-item">客服电话：400-888-0000</view>
      <view class="list-item">工作时间：周一至周日 9:00-18:00</view>

      <view class="subtitle">第十四条 法律适用与管辖</view>
      <view class="paragraph"
        >14.1
        本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。</view
      >
      <view class="paragraph"
        >14.2
        如双方就本协议内容或其执行发生任何争议，双方应尽量友好协商解决；协商不成时，任何一方均可向本地宝平台所在地有管辖权的人民法院起诉。</view
      >

      <view class="subtitle">第十五条 协议生效与解释</view>
      <view class="paragraph">15.1 本协议于2024年1月1日起生效。</view>
      <view class="paragraph"
        >15.2
        如本协议的任何条款被有管辖权的法院认定为不可执行，该条款将被删除，但不影响其余条款的效力。</view
      >
      <view class="paragraph"
        >15.3 本协议的最终解释权在法律允许的最大范围内归本地宝平台所有。</view
      >

      <view class="important-notice">
        <view class="notice-title">重要提醒</view>
        <view class="notice-content">
          请您在使用本平台服务前仔细阅读本协议全部条款。您的注册、登录、使用等行为即表示您已阅读并同意受本协议约束。如有疑问，请及时联系我们的客服。
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from "@/components/CustomNavBar.vue";
</script>

<style lang="scss" scoped>
.user-agreement-page {
  min-height: 100vh;
  background-color: var(--bg-page);
  padding-bottom: 40rpx;
}

.content {
  padding: 32rpx;
}

.title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;

  text-align: center;
  margin-bottom: 40rpx;
}

.subtitle {
  display: block;
  font-size: 32rpx;
  font-weight: 600;

  margin-top: 40rpx;
  margin-bottom: 20rpx;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid var(--border-color);
}

.paragraph {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.8;
  margin-bottom: 20rpx;
  text-align: justify;
}

.list-item {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.8;
  margin-bottom: 12rpx;
  margin-left: 24rpx;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: -16rpx;
    top: 18rpx;
    width: 6rpx;
    height: 6rpx;
    background-color: var(--primary);
    border-radius: 50%;
  }
}

.important-notice {
  background: var(--bg-secondary);
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 32rpx 0;
  border: 1rpx solid var(--border-color);
}

.notice-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;

  margin-bottom: 16rpx;
  text-align: center;
}

.notice-content {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.8;
  text-align: justify;
}
</style>

<template>
  <view class="wallet-page">
    <CustomNavBar title="我的钱包" />

    <!-- 钱包总览卡片 -->
    <view class="wallet-overview">
      <view class="balance-card">
        <view class="card-header">
          <view class="title-section">
            <tui-icon name="wallet" :size="48" color="#fff"></tui-icon>
            <text class="main-title">账户余额</text>
          </view>
          <view class="eye-icon" @click="toggleBalanceVisibility">
            <tui-icon
              :name="balanceVisible ? 'eye' : 'eye-close'"
              :size="40"
              color="rgba(255,255,255,0.8)"
            ></tui-icon>
          </view>
        </view>

        <view class="balance-section">
          <text class="balance-amount">{{
            balanceVisible ? `¥${userStore.user?.balance || "0.00"}` : "****"
          }}</text>
          <text class="balance-label">可用余额</text>
        </view>

        <view class="quick-actions">
          <view class="action-btn" @click="handleRecharge">
            <tui-icon name="add" :size="36" color="#fff"></tui-icon>
            <text>充值</text>
          </view>
          <view class="action-btn" @click="handleWithdraw">
            <tui-icon name="minus" :size="36" color="#fff"></tui-icon>
            <text>提现</text>
          </view>
          <view class="action-btn" @click="handleTransfer">
            <tui-icon name="transfer" :size="36" color="#fff"></tui-icon>
            <text>转账</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 资产概览 -->
    <view class="assets-overview">
      <view class="section-title">资产概览</view>
      <view class="assets-grid">
        <view class="asset-item">
          <view class="asset-icon">
            <tui-icon name="star" :size="48" color="#FFD700"></tui-icon>
          </view>
          <view class="asset-info">
            <text class="asset-value">{{ userStore.user?.points || "0" }}</text>
            <text class="asset-label">积分</text>
          </view>
        </view>

        <view class="asset-item">
          <view class="asset-icon">
            <tui-icon name="ticket" :size="48" color="#FF6B6B"></tui-icon>
          </view>
          <view class="asset-info">
            <text class="asset-value">{{
              userStore.user?.coupons || "0"
            }}</text>
            <text class="asset-label">优惠券</text>
          </view>
        </view>

        <view class="asset-item">
          <view class="asset-icon">
            <tui-icon name="gift" :size="48" color="#4ECDC4"></tui-icon>
          </view>
          <view class="asset-info">
            <text class="asset-value">{{ userStore.user?.gifts || "0" }}</text>
            <text class="asset-label">礼品</text>
          </view>
        </view>

        <view class="asset-item">
          <view class="asset-icon">
            <tui-icon name="vip" :size="48" color="#9B59B6"></tui-icon>
          </view>
          <view class="asset-info">
            <text class="asset-value">{{
              userStore.user?.isVip ? "VIP" : "普通"
            }}</text>
            <text class="asset-label">会员</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="function-menu">
      <view class="section-title">钱包功能</view>
      <view class="menu-list">
        <view class="menu-item" @click="goToTransactionHistory">
          <view class="menu-icon">
            <tui-icon
              name="time"
              :size="44"
              :color="'var(--text-blue)'"
            ></tui-icon>
          </view>
          <text class="menu-text">交易记录</text>
          <tui-icon
            name="right"
            :size="32"
            :color="'var(--text-grey)'"
          ></tui-icon>
        </view>

        <view class="menu-item" @click="goToBankCards">
          <view class="menu-icon">
            <tui-icon
              name="card"
              :size="44"
              :color="'var(--text-green)'"
            ></tui-icon>
          </view>
          <text class="menu-text">银行卡管理</text>
          <tui-icon
            name="right"
            :size="32"
            :color="'var(--text-grey)'"
          ></tui-icon>
        </view>

        <view class="menu-item" @click="goToPaymentSettings">
          <view class="menu-icon">
            <tui-icon
              name="setup"
              :size="44"
              :color="'var(--text-yellow)'"
            ></tui-icon>
          </view>
          <text class="menu-text">支付设置</text>
          <tui-icon
            name="right"
            :size="32"
            :color="'var(--text-grey)'"
          ></tui-icon>
        </view>

        <view class="menu-item" @click="goToSecuritySettings">
          <view class="menu-icon">
            <tui-icon
              name="lock"
              :size="44"
              :color="'var(--text-red)'"
            ></tui-icon>
          </view>
          <text class="menu-text">安全设置</text>
          <tui-icon
            name="right"
            :size="32"
            :color="'var(--text-grey)'"
          ></tui-icon>
        </view>
      </view>
    </view>

    <!-- 最近交易 -->
    <view class="recent-transactions">
      <view class="section-title">
        <text>最近交易</text>
        <text class="view-all" @click="goToTransactionHistory">查看全部</text>
      </view>

      <view class="transaction-list">
        <view
          v-for="(transaction, index) in recentTransactions"
          :key="index"
          class="transaction-item"
        >
          <view class="transaction-icon">
            <tui-icon
              :name="transaction.icon"
              :size="40"
              :color="transaction.color"
            ></tui-icon>
          </view>
          <view class="transaction-info">
            <text class="transaction-title">{{ transaction.title }}</text>
            <text class="transaction-time">{{ transaction.time }}</text>
          </view>
          <text
            class="transaction-amount"
            :class="{ positive: transaction.amount > 0 }"
          >
            {{ transaction.amount > 0 ? "+" : "" }}¥{{
              Math.abs(transaction.amount)
            }}
          </text>
        </view>

        <view v-if="recentTransactions.length === 0" class="empty-transactions">
          <tui-icon
            name="no-data"
            :size="80"
            :color="'var(--text-grey)'"
          ></tui-icon>
          <text class="empty-text">暂无交易记录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useUserStore } from "@/stores/user";
import CustomNavBar from "@/components/CustomNavBar.vue";

const userStore = useUserStore();

// 余额可见性控制
const balanceVisible = ref(true);

// 最近交易数据
const recentTransactions = ref([
  {
    title: "充值",
    time: "2024-01-15 14:30",
    amount: 100.0,
    icon: "add",
    color: "#4CAF50",
  },
  {
    title: "支付订单",
    time: "2024-01-14 16:22",
    amount: -29.9,
    icon: "minus",
    color: "#F44336",
  },
  {
    title: "提现",
    time: "2024-01-13 10:15",
    amount: -50.0,
    icon: "transfer",
    color: "#FF9800",
  },
]);

// 切换余额可见性
const toggleBalanceVisibility = () => {
  balanceVisible.value = !balanceVisible.value;
};

// 充值
const handleRecharge = () => {
  uni.showToast({
    title: "充值功能开发中",
    icon: "none",
  });
};

// 提现
const handleWithdraw = () => {
  uni.showToast({
    title: "提现功能开发中",
    icon: "none",
  });
};

// 转账
const handleTransfer = () => {
  uni.showToast({
    title: "转账功能开发中",
    icon: "none",
  });
};

// 交易记录
const goToTransactionHistory = () => {
  uni.showToast({
    title: "交易记录功能开发中",
    icon: "none",
  });
};

// 银行卡管理
const goToBankCards = () => {
  uni.showToast({
    title: "银行卡管理功能开发中",
    icon: "none",
  });
};

// 支付设置
const goToPaymentSettings = () => {
  uni.showToast({
    title: "支付设置功能开发中",
    icon: "none",
  });
};

// 安全设置
const goToSecuritySettings = () => {
  uni.showToast({
    title: "安全设置功能开发中",
    icon: "none",
  });
};
</script>

<style lang="scss" scoped>
.wallet-page {
  background: linear-gradient(
    180deg,
    var(--primary) 0%,
    var(--primary-400) 30%,
    var(--bg-page) 30%
  );
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.wallet-overview {
  padding: 32rpx;

  .balance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24rpx;
    padding: 40rpx 32rpx;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: -30%;
      right: -15%;
      width: 200rpx;
      height: 200rpx;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: -20%;
      left: -10%;
      width: 150rpx;
      height: 150rpx;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 50%;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40rpx;
      position: relative;
      z-index: 1;

      .title-section {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .main-title {
          color: #fff;
          font-size: 32rpx;
          font-weight: 600;
        }
      }

      .eye-icon {
        padding: 8rpx;
      }
    }

    .balance-section {
      margin-bottom: 48rpx;
      position: relative;
      z-index: 1;

      .balance-amount {
        display: block;
        color: #fff;
        font-size: 48rpx;
        font-weight: 700;
        margin-bottom: 8rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
      }

      .balance-label {
        color: rgba(255, 255, 255, 0.8);
        font-size: 26rpx;
      }
    }

    .quick-actions {
      display: flex;
      gap: 32rpx;
      position: relative;
      z-index: 1;

      .action-btn {
        flex: 1;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 16rpx;
        padding: 24rpx 16rpx;
        text-align: center;
        backdrop-filter: blur(10rpx);
        transition: all 0.2s ease;

        &:active {
          transform: scale(0.95);
          background: rgba(255, 255, 255, 0.3);
        }

        text {
          display: block;
          color: #fff;
          font-size: 24rpx;
          margin-top: 8rpx;
          font-weight: 500;
        }
      }
    }
  }
}

.assets-overview,
.function-menu,
.recent-transactions {
  margin: 0 32rpx 32rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .view-all {
      font-size: 26rpx;
      color: var(--primary);
    }
  }
}

.assets-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;

  .asset-item {
    background: var(--bg-card);
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
    text-align: center;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.98);
      background: var(--bg-hover);
    }

    .asset-icon {
      margin-bottom: 16rpx;
    }

    .asset-info {
      .asset-value {
        display: block;
        font-size: 32rpx;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 8rpx;
      }

      .asset-label {
        font-size: 24rpx;
        color: var(--text-secondary);
      }
    }
  }
}

.menu-list {
  background: var(--bg-card);
  border-radius: 16rpx;
  overflow: hidden;

  .menu-item {
    display: flex;
    align-items: center;
    padding: 32rpx 24rpx;
    border-bottom: 1rpx solid var(--border-color);
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background: var(--bg-hover);
    }

    .menu-icon {
      margin-right: 24rpx;
    }

    .menu-text {
      flex: 1;
      font-size: 28rpx;
      color: var(--text-primary);
    }
  }
}

.transaction-list {
  background: var(--bg-card);
  border-radius: 16rpx;
  overflow: hidden;

  .transaction-item {
    display: flex;
    align-items: center;
    padding: 32rpx 24rpx;
    border-bottom: 1rpx solid var(--border-color);

    &:last-child {
      border-bottom: none;
    }

    .transaction-icon {
      margin-right: 24rpx;
    }

    .transaction-info {
      flex: 1;

      .transaction-title {
        display: block;
        font-size: 28rpx;
        color: var(--text-primary);
        margin-bottom: 8rpx;
      }

      .transaction-time {
        font-size: 24rpx;
        color: var(--text-secondary);
      }
    }

    .transaction-amount {
      font-size: 32rpx;
      font-weight: 600;
      color: var(--text-primary);

      &.positive {
        color: #4caf50;
      }
    }
  }

  .empty-transactions {
    padding: 80rpx 32rpx;
    text-align: center;

    .empty-text {
      display: block;
      font-size: 26rpx;
      color: var(--text-secondary);
      margin-top: 16rpx;
    }
  }
}
</style>

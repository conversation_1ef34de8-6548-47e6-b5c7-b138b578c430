<template>
  <view class="collections-page">
    <CustomNavBar title="我的收藏" />

    <!-- 顶部统计信息 -->
    <view class="stats-header">
      <view class="stats-card">
        <view class="stat-item">
          <text class="stat-number">{{ collections.length }}</text>
          <text class="stat-label">收藏数量</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{ todayCount }}</text>
          <text class="stat-label">今日新增</text>
        </view>
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view
          v-for="tab in filterTabs"
          :key="tab.value"
          class="filter-tab"
          :class="{ active: activeTab === tab.value }"
          @tap="setActiveTab(tab.value)"
        >
          <text>{{ tab.label }}</text>
        </view>
      </view>
    </view>

    <!-- 收藏列表 -->
    <view class="list-section">
      <z-paging
        ref="paging"
        v-model="collections"
        @query="queryList"
        :show-loading-more="false"
      >
        <view v-if="filteredCollections.length > 0" class="collections-list">
          <view
            v-for="item in filteredCollections"
            :key="item.id"
            class="collection-item"
            @tap="goToDetail(item)"
          >
            <!-- 收藏头部 -->
            <view class="item-header">
              <view class="type-badge" :class="item.type">
                <text class="type-icon" :class="getTypeIcon(item.type)"></text>
                <text class="type-text">{{ getTypeText(item.type) }}</text>
              </view>
              <view class="collect-time">
                <text>{{ formatTime(item.collectTime) }}</text>
              </view>
            </view>

            <!-- 内容区域 -->
            <view class="item-content">
              <text class="item-title">{{ item.title }}</text>
              <view class="item-info">
                <view class="info-item">
                  <text class="i-carbon-currency-dollar info-icon"></text>
                  <text class="info-text"
                    >{{ item.price }}元/{{ item.priceUnit }}</text
                  >
                </view>
                <view class="info-item">
                  <text class="i-carbon-location info-icon"></text>
                  <text class="info-text">{{ item.location }}</text>
                </view>
                <view class="info-item">
                  <text class="i-carbon-time info-icon"></text>
                  <text class="info-text">{{ item.workTime }}</text>
                </view>
              </view>
            </view>

            <!-- 标签区域 -->
            <view class="item-tags">
              <text v-for="tag in item.tags" :key="tag" class="tag">{{
                tag
              }}</text>
            </view>

            <!-- 操作区域 -->
            <view class="item-actions">
              <view
                class="action-btn primary"
                @tap.stop="contactEmployer(item)"
              >
                <text class="i-carbon-chat"></text>
                <text>联系</text>
              </view>
              <view class="action-btn secondary" @tap.stop="applyJob(item)">
                <text class="i-carbon-send-alt"></text>
                <text>投递</text>
              </view>
              <view class="action-btn danger" @tap.stop="uncollect(item.id)">
                <text class="i-carbon-favorite-filled"></text>
                <text>取消收藏</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-else class="empty-state">
          <view class="empty-icon">
            <text class="i-carbon-favorite"></text>
          </view>
          <text class="empty-title">{{ getEmptyTitle() }}</text>
          <text class="empty-desc">{{ getEmptyDesc() }}</text>
          <view class="empty-action" @tap="goToExplore">
            <text>去发现更多</text>
          </view>
        </view>
      </z-paging>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import dayjs from "dayjs";
import CustomNavBar from "@/components/CustomNavBar.vue";
import ZPaging from "z-paging/components/z-paging/z-paging.vue";

const paging = ref<InstanceType<typeof ZPaging> | null>(null);
const collections = ref<any[]>([]);
const activeTab = ref("all");

// 筛选标签
const filterTabs = ref([
  { label: "全部", value: "all" },
  { label: "零工", value: "gig" },
  { label: "招聘", value: "job" },
  { label: "房源", value: "house" },
]);

// 计算属性
const filteredCollections = computed(() => {
  if (activeTab.value === "all") {
    return collections.value;
  }
  return collections.value.filter((item) => item.type === activeTab.value);
});

const todayCount = computed(() => {
  const today = dayjs().format("YYYY-MM-DD");
  return collections.value.filter(
    (item) => dayjs(item.collectTime).format("YYYY-MM-DD") === today
  ).length;
});

// 方法
const queryList = (pageNo: number, pageSize: number) => {
  // Mock API call
  setTimeout(() => {
    const mockData = [
      {
        id: 1,
        title: "周末招聘传单派发员",
        type: "gig",
        price: "150",
        priceUnit: "天",
        location: "市中心广场",
        workTime: "09:00-18:00",
        tags: ["轻松", "日结", "周末"],
        collectTime: dayjs().subtract(1, "day").toISOString(),
        employerName: "广告传媒公司",
      },
      {
        id: 2,
        title: "急招临时搬家师傅",
        type: "gig",
        price: "300",
        priceUnit: "次",
        location: "幸福小区",
        workTime: "08:00-12:00",
        tags: ["体力活", "现结"],
        collectTime: dayjs().subtract(2, "day").toISOString(),
        employerName: "张先生",
      },
      {
        id: 3,
        title: "前端开发工程师",
        type: "job",
        price: "8000-12000",
        priceUnit: "月",
        location: "科技园",
        workTime: "朝九晚六",
        tags: ["五险一金", "双休", "技术"],
        collectTime: dayjs().toISOString(),
        employerName: "科技有限公司",
      },
      {
        id: 4,
        title: "市中心精装两室",
        type: "house",
        price: "2800",
        priceUnit: "月",
        location: "市中心",
        workTime: "随时看房",
        tags: ["精装", "地铁", "配套齐全"],
        collectTime: dayjs().toISOString(),
        employerName: "房东直租",
      },
      {
        id: 5,
        title: "活动现场协助人员",
        type: "gig",
        price: "200",
        priceUnit: "天",
        location: "会展中心",
        workTime: "全天",
        tags: ["活动", "包餐"],
        collectTime: dayjs().subtract(3, "day").toISOString(),
        employerName: "活动策划公司",
      },
    ];

    paging.value?.complete(mockData);
  }, 500);
};

const setActiveTab = (tab: string) => {
  activeTab.value = tab;
};

const getTypeIcon = (type: string) => {
  const iconMap = {
    gig: "i-carbon-tools",
    job: "i-carbon-user-speaker",
    house: "i-carbon-home",
  };
  return iconMap[type] || "i-carbon-bookmark";
};

const getTypeText = (type: string) => {
  const textMap = {
    gig: "零工",
    job: "招聘",
    house: "房源",
  };
  return textMap[type] || type;
};

const formatTime = (time: string) => {
  const now = dayjs();
  const collectTime = dayjs(time);
  const diffDays = now.diff(collectTime, "day");

  if (diffDays === 0) {
    return "今天";
  } else if (diffDays === 1) {
    return "昨天";
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return collectTime.format("MM-DD");
  }
};

const getEmptyTitle = () => {
  if (activeTab.value === "all") {
    return "暂无收藏";
  }
  const typeMap = {
    gig: "没有收藏的零工",
    job: "没有收藏的招聘",
    house: "没有收藏的房源",
  };
  return typeMap[activeTab.value] || "暂无数据";
};

const getEmptyDesc = () => {
  return "收藏感兴趣的内容，方便随时查看";
};

const goToDetail = (item: any) => {
  const routeMap = {
    gig: `/pages/gig/detail/index?id=${item.id}`,
    job: `/pages/job/detail?id=${item.id}`,
    house: `/pages/house/detail?id=${item.id}`,
  };

  const route = routeMap[item.type];
  if (route) {
    uni.navigateTo({ url: route });
  }
};

const contactEmployer = (item: any) => {
  uni.showModal({
    title: "联系雇主",
    content: `是否要联系 ${item.employerName}？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: "正在跳转到聊天",
          icon: "none",
        });
      }
    },
  });
};

const applyJob = (item: any) => {
  uni.showToast({
    title: "投递成功",
    icon: "success",
  });
};

const uncollect = (id: number) => {
  uni.showModal({
    title: "确认取消收藏",
    content: "确定要取消收藏这个项目吗？",
    success: (res) => {
      if (res.confirm) {
        collections.value = collections.value.filter((item) => item.id !== id);
        uni.showToast({
          title: "已取消收藏",
          icon: "success",
        });
      }
    },
  });
};

const goToExplore = () => {
  uni.switchTab({
    url: "/pages/index/index",
  });
};
</script>

<style lang="scss" scoped>
.collections-page {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.stats-header {
  padding: var(--spacing);
  margin-top: 20rpx;
}

.stats-card {
  background-color: var(--bg-card);
  border-radius: var(--radius-3);
  padding: var(--spacing);
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 var(--spacing) var(--spacing) rgba(0, 0, 0, 0.08);
}

.stat-item {
  text-align: center;
  flex: 1;

  .stat-number {
    display: block;
    font-size: 36rpx;
    font-weight: 600;

    margin-bottom: var(--spacing);
  }

  .stat-label {
    font-size: 26rpx;
    color: var(--text-info);
  }
}

.stat-divider {
  width: 1rpx;
  height: 40rpx;
  background-color: var(--border-color);
  margin: 0 40rpx;
}

.filter-section {
  padding: 0 var(--spacing) var(--spacing-3);
}

.filter-tabs {
  display: flex;
  gap: var(--spacing-2);
  background-color: var(--bg-card);
  border-radius: var(--radius-3);
  padding: var(--spacing);
  box-shadow: 0 var(--spacing) var(--spacing) rgba(0, 0, 0, 0.08);
}

.filter-tab {
  flex: 1;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: 16rpx;

  color: var(--text-secondary);
  text-align: center;
  transition: all 0.3s ease;

  &.active {
    background-color: var(--primary);
    color: var(--text-inverse);
    font-weight: 500;
    box-shadow: 0 var(--spacing) var(--spacing) var(--primary-300);
  }
}

.list-section {
  padding: 0 var(--spacing) 40rpx;
}

.collections-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.collection-item {
  background-color: var(--bg-card);
  border-radius: var(--radius-3);
  padding: var(--spacing);
  box-shadow: 0 var(--spacing) var(--spacing) rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.type-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  padding: var(--spacing) var(--spacing-2);
  border-radius: 16rpx;
  font-size: 26rpx;
  font-weight: 500;

  &.gig {
    background-color: var(--bg-warning-light);
    color: var(--text-yellow);
  }

  &.job {
    background-color: var(--bg-success-light);
    color: var(--text-green);
  }

  &.house {
    background-color: var(--bg-info-light);
    color: var(--text-blue);
  }
}

.type-icon {
  font-size: 26rpx;
}

.collect-time {
  font-size: 26rpx;
  color: var(--text-info);
}

.item-content {
  margin-bottom: var(--spacing-3);
}

.item-title {
  font-size: 32rpx;
  font-weight: 600;

  margin-bottom: var(--spacing-2);
  line-height: 1.4;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.info-icon {
  color: var(--text-info);
  flex-shrink: 0;
}

.info-text {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.item-tags {
  display: flex;
  gap: 12rpx;
  margin-bottom: var(--spacing-3);
  flex-wrap: wrap;
}

.tag {
  padding: var(--spacing) var(--spacing-2);
  background-color: var(--bg-tag);
  color: var(--text-secondary);
  border-radius: var(--radius);
  font-size: 26rpx;
}

.item-actions {
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: 16rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;

  &.primary {
    background-color: var(--primary);
    color: var(--text-inverse);
  }

  &.secondary {
    background-color: var(--bg-tag);
    color: var(--text-secondary);
  }

  &.danger {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
  }

  &:active {
    transform: scale(0.95);
  }
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  background-color: var(--bg-card);
  border-radius: var(--radius-3);
  margin: var(--spacing);
  box-shadow: 0 var(--spacing) var(--spacing) rgba(0, 0, 0, 0.08);
}

.empty-icon {
  font-size: 120rpx;
  color: var(--text-disable);
  margin-bottom: var(--spacing);
}

.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;

  margin-bottom: var(--spacing-2);
}

.empty-desc {
  display: block;

  color: var(--text-info);
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.empty-action {
  display: inline-block;
  padding: var(--spacing-2) var(--spacing);
  background-color: var(--primary);
  color: var(--text-inverse);
  border-radius: 16rpx;

  font-weight: 500;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    background-color: var(--primary-700);
  }
}
</style>

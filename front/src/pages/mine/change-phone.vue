<template>
  <view class="change-phone-page">
    <!-- 当前手机号展示 -->
    <view class="current-phone-card">
      <view class="label">当前手机号</view>
      <view class="phone-number">{{ maskedOldPhone }}</view>
    </view>

    <!-- 微信快速更换 -->
    <view class="section-title">推荐更换方式</view>
    <button
      class="wechat-change-card"
      open-type="getPhoneNumber"
      @getphonenumber="onGetPhoneNumber"
    >
      <view class="card-content">
        <text class="i-carbon-logo-wechat wechat-icon"></text>
        <view class="info">
          <view class="title">微信一键更换</view>
          <view class="desc">使用微信绑定的手机号快速更换</view>
        </view>
        <text class="i-carbon-chevron-right arrow-icon"></text>
      </view>
    </button>

    <!-- 手动输入新手机号更换 -->
    <view class="section-title">其他更换方式</view>
    <view class="manual-change-card">
      <view class="form-group">
        <text class="i-carbon-phone form-icon"></text>
        <input
          v-model="newPhone"
          class="input-field"
          placeholder="请输入新手机号"
          type="number"
          :maxlength="11"
        />
      </view>
      <view class="form-group">
        <text class="i-carbon-chat form-icon"></text>
        <input
          v-model="verificationCode"
          class="input-field"
          placeholder="请输入6位验证码"
          type="number"
          :maxlength="6"
        />
        <button
          class="get-code-btn"
          :disabled="isCodeBtnDisabled"
          @click="getVerificationCode"
        >
          {{ codeBtnText }}
        </button>
      </view>
    </view>

    <view class="confirm-btn-container">
      <button
        class="confirm-btn"
        :disabled="!isFormValid"
        @click="confirmChange"
      >
        确认更换
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useUserStore } from "@/stores/user";

const userStore = useUserStore();
const userInfo = computed(() => userStore.getUser);

const newPhone = ref("");
const verificationCode = ref("");

const codeBtnText = ref("获取验证码");
const isCodeBtnDisabled = ref(true);
let codeTimer: any = null;

const maskedOldPhone = computed(() => {
  const phone = userInfo.value?.phone;
  if (phone && phone.length === 11) {
    return phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3");
  }
  return "未绑定";
});

const isNewPhoneValid = computed(() => /^\d{11}$/.test(newPhone.value));

const isFormValid = computed(() => {
  return isNewPhoneValid.value && verificationCode.value.length === 6;
});

// 监听新手机号输入，以控制按钮状态
watch(newPhone, (val) => {
  isCodeBtnDisabled.value = !/^\d{11}$/.test(val);
});

const getVerificationCode = () => {
  if (isCodeBtnDisabled.value) return;
  // TODO: 调用API发送验证码到 newPhone.value
  uni.showToast({ title: "验证码已发送", icon: "none" });
  startCountdown();
};

const startCountdown = () => {
  isCodeBtnDisabled.value = true;
  let seconds = 60;
  codeBtnText.value = `${seconds}s`;
  codeTimer = setInterval(() => {
    seconds--;
    if (seconds <= 0) {
      clearInterval(codeTimer);
      codeBtnText.value = "获取验证码";
      isCodeBtnDisabled.value = !isNewPhoneValid.value;
    } else {
      codeBtnText.value = `${seconds}s`;
    }
  }, 1000);
};

const confirmChange = () => {
  if (!isFormValid.value) return;
  // TODO: 调用API提交更换手机号请求
  uni.showToast({ title: "更换成功", icon: "success" });
  userStore.updateUserInfo({ phone: newPhone.value });
  setTimeout(() => uni.navigateBack(), 1000);
};

const onGetPhoneNumber = (e: any) => {
  if (e.detail.code) {
    // TODO: 将 res.code 发送给后端解密并更换手机号
    console.log("微信授权成功，code:", e.detail.code);
    uni.showToast({ title: "微信更换功能开发中", icon: "none" });
  } else {
    console.error("微信授权失败:", e.detail.errMsg);
    uni.showToast({ title: "授权失败", icon: "error" });
  }
};
</script>

<style lang="scss" scoped>
.change-phone-page {
  background-color: var(--bg-page);
  min-height: 100vh;
  padding: 32rpx;
}

.current-phone-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  text-align: center;
  margin-bottom: 48rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.05);

  .label {
    font-size: 26rpx;
    color: var(--text-secondary);
    margin-bottom: 8rpx;
  }

  .phone-number {
    font-size: 40rpx;
    font-weight: 600;
    color: var(--text-primary);
  }
}

.section-title {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
  padding-left: 8rpx;
}

.wechat-change-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 0 0 48rpx 0; /* 调整 margin */
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s;
  text-align: left; /* 确保内容左对齐 */
  line-height: initial; /* 重置 line-height */

  &::after {
    border: none; /* 去除 button 默认边框 */
  }

  &:active {
    background-color: var(--bg-hover);
  }

  .card-content {
    display: flex;
    align-items: center;
  }

  .wechat-icon {
    font-size: 48rpx;
    color: #07c160;
    margin-right: 24rpx;
  }

  .info {
    flex: 1;
  }

  .title {
    font-size: 32rpx;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4rpx;
  }

  .desc {
    font-size: 24rpx;
    color: var(--text-secondary);
  }

  .arrow-icon {
    font-size: 32rpx;
    color: var(--text-tertiary);
  }
}

.manual-change-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.05);
}

.form-group {
  display: flex;
  align-items: center;
  padding: 24rpx 0;

  &:first-child {
    border-bottom: 1rpx solid var(--border-color);
  }

  .form-icon {
    font-size: 36rpx;
    color: var(--text-tertiary);
    margin-right: 24rpx;
  }

  .input-field {
    flex: 1;
    font-size: 30rpx;
    color: var(--text-primary);
  }
}

.get-code-btn {
  background: none;
  color: var(--primary);
  font-size: 28rpx;
  margin: 0;
  padding: 0 0 0 16rpx;
  border: none;
  white-space: nowrap;

  &::after {
    display: none;
  }
  &[disabled] {
    color: var(--text-info);
  }
}

.confirm-btn-container {
  margin-top: 48rpx;
}

.confirm-btn {
  background-color: var(--primary);
  color: #fff;
  font-size: 32rpx;
  height: 96rpx;
  line-height: 96rpx;
  border-radius: 48rpx;

  &[disabled] {
    background-color: var(--primary-disabled);
    color: var(--text-on-disabled);
  }
}
</style>

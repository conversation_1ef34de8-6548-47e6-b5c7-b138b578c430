<template>
  <view class="gig-action-buttons">
    <!-- 求职者视角 -->
    <template v-if="!isPublisher">
      <!-- 未申请状态 -->
      <template v-if="!hasApplied">
        <tui-button
          v-if="canApply"
          type="primary"
          :width="320"
          :height="88"
          :size="32"
          @click="handleApply"
        >
          立即申请
        </tui-button>
        <tui-button
          v-else
          type="gray"
          :width="320"
          :height="88"
          :size="32"
          :disabled="true"
        >
          {{ getDisabledButtonText }}
        </tui-button>
      </template>

      <!-- 已申请状态 -->
      <template v-else>
        <view class="applied-actions">
          <tui-button
            type="primary"
            :width="150"
            :height="88"
            :size="28"
            @click="handleContact"
          >
            联系雇主
          </tui-button>
          <tui-button
            type="gray"
            :width="150"
            :height="88"
            :size="28"
            @click="handleWithdraw"
          >
            撤销报名
          </tui-button>
        </view>
      </template>
    </template>

    <!-- 发布者视角 -->
    <template v-else>
      <view class="publisher-actions">
        <tui-button
          type="primary"
          :width="150"
          :height="88"
          :size="28"
          @click="handleManageApplications"
        >
          管理申请
        </tui-button>
        <tui-button
          type="gray"
          :width="150"
          :height="88"
          :size="28"
          @click="handlePauseResume"
        >
          {{ gigStatus === 'recruiting' ? '暂停招聘' : '恢复招聘' }}
        </tui-button>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { GigStatus } from '@/constants/gig'
import type { Gig } from '@/types/gig'

// Props 定义
interface Props {
  gig: Gig
  hasApplied: boolean
  canApply: boolean
  isPublisher: boolean
}

const props = defineProps<Props>()

// Events 定义
const emit = defineEmits<{
  apply: []
  contact: []
  withdraw: []
  manageApplications: []
  pauseResume: []
}>()

// 计算属性
const gigStatus = computed(() => props.gig.status)

/**
 * 获取不可申请按钮的文本
 */
const getDisabledButtonText = computed(() => {
  if (!props.gig) return '暂不可申请'
  
  // 检查名额限制
  if (props.gig.people_count && props.gig.current_people_count >= props.gig.people_count) {
    return '名额已满'
  }
  
  // 检查零工状态
  switch (props.gig.status) {
    case GigStatus.Paused:
      return '暂停招聘'
    case GigStatus.Locked:
      return '招聘截止'
    case GigStatus.InProgress:
      return '工作进行中'
    case GigStatus.Completed:
      return '已完成'
    case GigStatus.Closed:
      return '已关闭'
    case GigStatus.Draft:
      return '未发布'
    default:
      return '暂不可申请'
  }
})

// 事件处理函数
const handleApply = () => {
  emit('apply')
}

const handleContact = () => {
  emit('contact')
}

const handleWithdraw = () => {
  emit('withdraw')
}

const handleManageApplications = () => {
  emit('manageApplications')
}

const handlePauseResume = () => {
  emit('pauseResume')
}
</script>

<style lang="scss" scoped>
.gig-action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24rpx;
  gap: 24rpx;
}

.applied-actions,
.publisher-actions {
  display: flex;
  gap: 24rpx;
  align-items: center;
}

// 按钮样式覆盖
:deep(.tui-button) {
  border-radius: 44rpx;
  font-weight: 500;
}

:deep(.tui-button-primary) {
  background: linear-gradient(135deg, var(--primary) 0%, #4facfe 100%);
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(79, 172, 254, 0.3);
}

:deep(.tui-button-gray) {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

:deep(.tui-button-gray[disabled]) {
  background: #f9f9f9;
  color: #ccc;
  border: 1rpx solid #f0f0f0;
}
</style>

<template>
  <!-- 底部操作按钮区域 -->
  <view class="gig-actions-container">
    <!-- 未登录状态：显示登录按钮 -->
    <template v-if="!userStore.isLogin">
      <view class="primary-btn login-btn" @tap="handleLogin">
        <text class="btn-text">登录后申请</text>
      </view>
    </template>

    <!-- 已登录状态：根据角色和权限显示不同按钮 -->
    <template v-else>
      <!-- 求职者模式 -->
      <template v-if="currentUserRole === 'seeker'">
        <!-- 非发布者：显示申请相关按钮 -->
        <template v-if="!isPublisher">
          <!-- 已申请状态 -->
          <view v-if="hasApplied" class="applied-status-wrapper">
            <view class="status-indicator">
              <text class="status-text">已提交申请</text>
              <text class="status-desc">请等待雇主审核</text>
            </view>
            <view class="secondary-btn contact-btn" @tap="handleContact">
              <text class="btn-text">联系雇主</text>
            </view>
          </view>

          <!-- 未申请：显示申请按钮 -->
          <view
            v-else
            class="primary-btn apply-btn"
            :class="{ disabled: !canApply }"
            @tap="handleApply"
          >
            <text class="btn-text">{{ applyButtonText }}</text>
          </view>
        </template>

        <!-- 发布者在求职者模式：提示切换到招聘模式 -->
        <template v-else>
          <view class="secondary-btn switch-btn" @tap="handleSwitchToRecruiter">
            <text class="btn-text">切换到招聘模式管理</text>
          </view>
        </template>
      </template>

      <!-- 招聘者模式 -->
      <template v-if="currentUserRole === 'recruiter'">
        <!-- 发布者：显示管理按钮 -->
        <template v-if="isPublisher">
          <view class="primary-btn manage-btn" @tap="handleManageApplications">
            <text class="btn-text">管理申请</text>
            <text v-if="applicationCount > 0" class="count-badge">{{ applicationCount }}</text>
          </view>
          <view class="secondary-btn more-btn" @tap="showMoreActions">
            <text class="btn-text">更多操作</text>
          </view>
        </template>

        <!-- 非发布者：提示切换到求职模式 -->
        <template v-else>
          <view class="secondary-btn switch-btn" @tap="handleSwitchToSeeker">
            <text class="btn-text">切换找零工</text>
          </view>
        </template>
      </template>
    </template>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useGlobalStore } from '@/stores/global'
import { GigStatus } from '@/constants/gig'
import type { Gig, CheckApplicationStatusResponse } from '@/types/gig'
import { showActionConfirm } from '@/utils/ui/feedback'
import { useUserInfoValidation } from '@/services/gigApplication'

// 组件属性定义
interface Props {
  gig: Gig
  applicationStatusInfo?: CheckApplicationStatusResponse | null
  applicationCount?: number
  isPublisher: boolean
  canApply: boolean
}

const props = withDefaults(defineProps<Props>(), {
  applicationStatusInfo: null,
  applicationCount: 0,
  isPublisher: false,
  canApply: false
})

// 事件定义
const emit = defineEmits<{
  apply: []
  contact: []
  withdraw: []
  manageApplications: []
  pauseResume: []
  close: []
  refresh: []
}>()

// Store 实例
const userStore = useUserStore()
const globalStore = useGlobalStore()
const { checkUserBasicInfo, promptUserToCompleteInfo } = useUserInfoValidation()

// 计算属性
const currentUserRole = computed(() => globalStore.gigRole)
const gigStatus = computed(() => props.gig.status)
const hasApplied = computed(() => props.applicationStatusInfo?.has_applied || false)

// 申请按钮文本
const applyButtonText = computed(() => {
  // 检查名额限制
  if (props.gig.people_count && props.gig.current_people_count >= props.gig.people_count) {
    return '名额已满'
  }
  
  // 检查零工状态
  if (gigStatus.value === GigStatus.Paused) {
    return '暂停招聘'
  }
  
  if (gigStatus.value !== GigStatus.Recruiting) {
    return '暂不可申请'
  }
  
  return '立即申请'
})

// 事件处理函数
const handleLogin = () => {
  globalStore.showPrivacyPopup()
}

const handleApply = async () => {
  if (!props.canApply) return
  
  // 检查用户基本信息
  const userCheck = checkUserBasicInfo()
  if (!userCheck.valid) {
    promptUserToCompleteInfo(userCheck.missingFields)
    return
  }
  
  emit('apply')
}

const handleContact = () => {
  emit('contact')
}

const handleManageApplications = () => {
  emit('manageApplications')
}

const showMoreActions = () => {
  const actions: string[] = []
  
  // 根据零工状态添加操作选项
  if (gigStatus.value === GigStatus.Recruiting) {
    actions.push('暂停招聘')
  } else if (gigStatus.value === GigStatus.Paused) {
    actions.push('恢复招聘')
  }
  
  if ([GigStatus.Recruiting, GigStatus.Paused, GigStatus.InProgress].includes(gigStatus.value)) {
    actions.push('结束招聘')
  }
  
  if (actions.length === 0) return
  
  uni.showActionSheet({
    itemList: actions,
    success: (res) => {
      const selectedAction = actions[res.tapIndex]
      
      if (selectedAction === '暂停招聘' || selectedAction === '恢复招聘') {
        handlePauseResume()
      } else if (selectedAction === '结束招聘') {
        handleClose()
      }
    }
  })
}

const handlePauseResume = async () => {
  const action = gigStatus.value === GigStatus.Paused ? '恢复' : '暂停'
  await showActionConfirm(`${action}招聘`, `此零工的招聘`, () => {
    emit('pauseResume')
  })
}

const handleClose = async () => {
  await showActionConfirm(
    '结束招聘',
    '此零工的招聘（结束后将无法继续接收申请）',
    () => {
      emit('close')
    }
  )
}

const handleSwitchToSeeker = () => {
  globalStore.setGigRole('seeker')
  emit('refresh')
}

const handleSwitchToRecruiter = () => {
  globalStore.setGigRole('recruiter')
  emit('refresh')
}
</script>

<style scoped lang="scss">
.gig-actions-container {
  display: flex;
  align-items: stretch;
  gap: 24rpx;
  padding: 24rpx;
  background-color: var(--bg-card);
  border-top: 1rpx solid var(--border-color);
}

// 基础按钮样式
.primary-btn,
.secondary-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  border-radius: 12rpx;
  font-weight: 500;
  position: relative;
  transition: all 0.2s ease;
  flex: 1;
  
  &:active {
    transform: scale(0.98);
  }
  
  .btn-text {
    font-size: 32rpx;
    line-height: 1;
  }
  
  .count-badge {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    min-width: 32rpx;
    height: 32rpx;
    line-height: 32rpx;
    text-align: center;
    background-color: var(--text-red);
    color: white;
    font-size: 20rpx;
    border-radius: 16rpx;
    padding: 0 8rpx;
    box-shadow: 0 2rpx 8rpx rgba(255, 0, 0, 0.3);
  }
  
  &.disabled {
    opacity: 0.5;
    background-color: var(--bg-disable) !important;
    color: var(--text-disable) !important;
    
    .btn-text {
      color: var(--text-disable);
    }
    
    &:active {
      transform: none;
    }
  }
}

// 主要按钮样式
.primary-btn {
  background: linear-gradient(135deg, var(--primary), #ff8533);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.3);
  
  &:active {
    box-shadow: 0 2rpx 8rpx rgba(255, 109, 0, 0.4);
  }
}

// 次要按钮样式
.secondary-btn {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1rpx solid var(--border-color);
  
  &:active {
    background-color: var(--primary);
    color: white;
  }
}

// 特定按钮调整
.login-btn {
  background: linear-gradient(135deg, var(--primary), #ff8533);
  color: white;
  min-width: 200rpx;
}

.apply-btn {
  min-width: 160rpx;
}

.manage-btn {
  min-width: 140rpx;
}

.contact-btn {
  min-width: 120rpx;
  flex: 0 0 auto;
}

.switch-btn,
.more-btn {
  min-width: 120rpx;
  flex: 0 0 auto;
}

// 已申请状态样式
.applied-status-wrapper {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.status-indicator {
  flex: 1;
  padding: 16rpx 20rpx;
  background: var(--bg-success-light);
  border: 1rpx solid var(--text-green);
  border-radius: 8rpx;
}

.status-text {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-green);
  line-height: 1.2;
  margin-bottom: 4rpx;
}

.status-desc {
  font-size: 22rpx;
  color: var(--text-secondary);
  line-height: 1.3;
}

// 响应式适配
@media (max-width: 750rpx) {
  .gig-actions-container {
    gap: 16rpx;
    padding: 20rpx;
  }
  
  .primary-btn,
  .secondary-btn {
    height: 76rpx;
    
    .btn-text {
      font-size: 28rpx;
    }
  }
  
  .status-text {
    font-size: 26rpx;
  }
  
  .status-desc {
    font-size: 20rpx;
  }
}
</style>
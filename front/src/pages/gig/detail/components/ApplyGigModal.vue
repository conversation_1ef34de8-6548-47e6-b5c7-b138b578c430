<template>
  <!-- <uni-popup ref="popup" type="bottom" :zIndex="9999" :mask-click="false"> -->
  <tui-bottom-popup :zIndex="1002" :maskZIndex="1001" :show="show">
    <view class="apply-modal">
      <!-- 模态框头部 -->
      <view class="modal-header">
        <view class="header-left">
          <text class="modal-title">申请零工</text>
          <text class="modal-subtitle">完善信息提高通过率</text>
        </view>
        <view class="close-btn" @tap="handleClose">
          <text class="i-carbon-close"></text>
        </view>
      </view>

      <!-- 申请表单 -->
      <scroll-view class="form-scroll" scroll-y>
        <view class="form-section">
          <!-- 联系信息 -->
          <view class="form-group">
            <text class="group-title">
              <text class="required-mark">*</text>
              联系信息
            </text>

            <view class="form-item">
              <text class="item-label">姓名</text>
              <input
                v-model="formData.contact_name"
                class="form-input"
                placeholder="请输入您的真实姓名"
                placeholder-class="input-placeholder"
                maxlength="20"
                @input="clearFieldError('contactName')"
              />
            </view>
            <text v-if="errors.contactName" class="error-text">{{
              errors.contactName
            }}</text>

            <view class="form-item">
              <text class="item-label">手机号</text>
              <input
                v-model="formData.contact_phone"
                class="form-input"
                type="number"
                placeholder="请输入手机号码"
                placeholder-class="input-placeholder"
                maxlength="11"
                @input="clearFieldError('contactPhone')"
              />
            </view>
            <text v-if="errors.contactPhone" class="error-text">{{
              errors.contactPhone
            }}</text>
          </view>

          <!-- 工作经验 -->
          <view class="form-group">
            <text class="group-title">工作经验</text>
            <view class="experience-options">
              <view
                v-for="(exp, index) in experienceOptions"
                :key="index"
                class="experience-item"
                :class="{ selected: formData.has_experience === exp.value }"
                @tap="selectExperience(exp.value)"
              >
                <text class="experience-text">{{ exp.label }}</text>
                <text
                  class="experience-icon"
                  :class="
                    formData.has_experience === exp.value
                      ? 'i-carbon-checkmark-filled'
                      : 'i-carbon-radio-button'
                  "
                ></text>
              </view>
            </view>
          </view>

          <!-- 备注信息 -->
          <view class="form-group">
            <text class="group-title">备注信息</text>
            <textarea
              v-model="formData.message"
              class="form-textarea"
              placeholder="可选填写相关经验、优势等补充信息"
              placeholder-class="input-placeholder"
              maxlength="500"
            />
          </view>
        </view>
      </scroll-view>

      <!-- 底部操作区域 -->
      <view class="modal-footer">
        <view class="agreement-section">
          <view class="agreement-checkbox" @tap="toggleAgreement">
            <text
              class="checkbox-icon"
              :class="
                formData.agreed_to_terms
                  ? 'i-carbon-checkmark-filled'
                  : 'i-carbon-checkbox'
              "
            ></text>
            <text class="agreement-text">
              我已阅读并同意
              <text class="agreement-link" @tap.stop="showAgreement"
                >《申请服务协议》</text
              >
            </text>
          </view>
        </view>

        <view class="action-buttons">
          <view class="cancel-btn box-shadow" @tap="handleClose">
            <text class="btn-text">取消</text>
          </view>
          <view
            class="submit-btn box-shadow"
            :class="{ disabled: !canSubmit }"
            @tap="handleSubmit"
          >
            <text v-if="!isApplying && !isSubmitting" class="btn-text"
              >提交申请</text
            >
            <view v-else class="loading-container">
              <text class="loading-spinner"></text>
              <text class="btn-text">提交中...</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- </uni-popup> -->
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import type { Gig } from "@/types/gig";
import { showToast } from "@/utils/ui/feedback";
import { useUserStore } from "@/stores/user";
import { useGigApplication } from "@/services/gigApplication";

interface Props {
  show: boolean;
  gig?: Gig | null;
}

interface ApplyFormData {
  contact_name: string;
  contact_phone: string;
  has_experience: boolean;
  message: string;
  agreed_to_terms: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  gig: null,
});

const emit = defineEmits<{
  close: [];
  success: []; // 提交成功事件
}>();

const userStore = useUserStore();
const popup = ref();

// 引入服务层
const { isApplying, applyForGigWithValidation } = useGigApplication();

// 表单数据
const formData = ref<ApplyFormData>({
  contact_name: "",
  contact_phone: "",
  has_experience: false,
  message: "",
  agreed_to_terms: false,
});

// 表单验证错误
const errors = ref<Record<string, string>>({});

// 提交状态
const isSubmitting = ref(false);

// 经验选项
const experienceOptions = [
  { label: "有经验", value: true },
  { label: "无经验", value: false },
];

// 监听显示状态，控制popup显示和重置表单
watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      resetForm();
      popup.value?.open();
    } else {
      popup.value?.close();
    }
  }
);

// 计算属性
const canSubmit = computed(() => {
  // 结合服务层的loading和组件的提交状态
  const isLoading = isApplying.value || isSubmitting.value;

  // 基本校验：必填字段和同意协议
  const hasRequiredFields =
    formData.value.contact_name.trim() &&
    formData.value.contact_phone.trim() &&
    formData.value.agreed_to_terms;

  return hasRequiredFields && !isLoading;
});

// 方法
const resetForm = () => {
  formData.value = {
    contact_name: userStore.user?.nickname || "",
    contact_phone: userStore.user?.phone || "",
    has_experience: false,
    message: "",
    agreed_to_terms: false,
  };
  errors.value = {};
};

// 清除特定字段的错误信息
const clearFieldError = (fieldName: string) => {
  if (errors.value[fieldName]) {
    delete errors.value[fieldName];
  }
};

// 表单交互方法
const selectExperience = (value: boolean) => {
  formData.value.has_experience = value;
};

const toggleAgreement = () => {
  formData.value.agreed_to_terms = !formData.value.agreed_to_terms;
};

const showAgreement = () => {
  uni.showModal({
    title: "申请服务协议",
    content:
      "这里是申请服务协议的内容，包含用户权利义务、隐私保护、服务条款等相关规定。",
    showCancel: false,
    confirmText: "知道了",
  });
};

// 事件处理
const handleClose = () => {
  if (isApplying.value || isSubmitting.value) return;
  emit("close");
};

const handleSubmit = async () => {
  if (!canSubmit.value) return;

  if (!props.gig) {
    showToast("零工信息不存在");
    return;
  }

  try {
    isSubmitting.value = true;

    // 清空之前的错误信息
    errors.value = {};

    // 使用服务层的完整验证和提交流程
    const result = await applyForGigWithValidation(props.gig.id, {
      contact_name: formData.value.contact_name.trim(),
      contact_phone: formData.value.contact_phone.trim(),
      has_experience: formData.value.has_experience,
      message: formData.value.message.trim(),
      agreed_to_terms: formData.value.agreed_to_terms,
    });

    if (result.success) {
      // 成功后的处理
      emit("success"); // 触发成功事件，父组件处理数据刷新

      // 延迟关闭弹窗，让用户看到成功状态
      setTimeout(() => {
        emit("close");
      }, 1000);
    } else {
      // 处理验证错误 - 直接使用服务层返回的错误信息
      if (result.errors) {
        errors.value = result.errors;
        showToast("请检查表单信息");
      } else {
        showToast("申请提交失败，请重试");
      }
    }
  } catch (error) {
    console.error("申请提交失败:", error);
    showToast("申请提交失败，请重试");
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped lang="scss">
.apply-modal {
  width: 100%;
  height: 70vh;
  background-color: var(--bg-card);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 0 28rpx;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx var(--spacing) var(--spacing-3);
  border-bottom: 1rpx solid var(--border-color);
}

.header-left {
  flex: 1;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;

  display: block;
  margin-bottom: var(--spacing);
}

.modal-subtitle {
  font-size: 26rpx;
  color: var(--text-info);
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--bg-tag);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.9);
    background-color: var(--text-info);
  }

  text {
    font-size: 28rpx;
    color: var(--text-secondary);
  }
}

.form-scroll {
  flex: 1;
  height: 0; /* 配合flex:1使用，确保可滚动 */
  padding: 0 var(--spacing);
  box-sizing: border-box;
}

.form-section {
  padding-bottom: 40rpx;
}

.form-group {
  margin-bottom: 48rpx;
}

.group-title {
  font-weight: 600;
  margin-bottom: var(--spacing-3);
  display: block;
}

.required-mark {
  color: var(--text-red);
  margin-right: var(--spacing);
}

.form-item {
  display: flex;
  align-items: center;
  background-color: var(--bg-input);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: var(--spacing-2);
}

.item-label {
  width: 120rpx;
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  border-radius: 16rpx;
  background-color: transparent;
  min-width: 0; /* 防止输入框撑开容器 */
}

.input-placeholder {
  color: var(--text-info);
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  background-color: var(--bg-input);
  border-radius: 16rpx;
  padding: 24rpx var(--spacing);

  line-height: 1.5;
  box-sizing: border-box;
}

.error-text {
  font-size: 26rpx;
  color: var(--text-red);
  margin-top: var(--spacing);
}

// 经验选项样式
.experience-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.experience-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-radius: 16rpx;
  background-color: var(--bg-input);
  transition: all 0.3s ease;
  box-sizing: border-box;

  &.selected {
    background-color: var(--bg-primary-light);
    border: 1rpx solid var(--primary);
  }
}

.experience-icon {
  font-size: 32rpx;
  color: var(--primary);
}

// 时间选项样式
.time-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-2);
}

.time-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3);
  background-color: var(--bg-input);
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &.selected {
    background-color: var(--primary);
    color: var(--text-inverse);
  }

  &:active {
    transform: scale(0.95);
  }
}

.time-text {
  font-size: 26rpx;
  color: inherit;
}

.modal-footer {
  padding: var(--spacing);
  border-top: 1rpx solid var(--border-color);
  background-color: var(--bg-card);
}

.agreement-section {
  margin: var(--spacing-2) 0;
}

.agreement-checkbox {
  display: flex;
  align-items: center;
  gap: var(--spacing);
}

.checkbox-icon {
  font-size: 32rpx;
  color: var(--primary);
}

.agreement-text {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.agreement-link {
  color: var(--primary);
  text-decoration: underline;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-3);
}

.cancel-btn,
.submit-btn {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-2);
  transition: all 0.3s ease;
}

.cancel-btn {
  flex: 1;
}

.submit-btn {
  flex: 2;
}

.cancel-btn {
  background-color: var(--bg-search);
  color: var(--text-secondary);

  &:active {
    background-color: var(--text-info);
    color: var(--text-inverse);
  }
}

.submit-btn {
  background: linear-gradient(135deg, var(--primary-400), var(--primary));
  color: var(--text-inverse);

  &.disabled {
    opacity: 0.6;
    background: var(--bg-tag);
    color: var(--text-disable);
    box-shadow: none;

    &:active {
      transform: none;
    }
  }
}

.btn-text {
  font-weight: 500;
  font-size: 32rpx;
  color: inherit;
}

.loading-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid var(--text-inverse);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>

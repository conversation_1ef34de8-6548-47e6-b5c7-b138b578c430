<template>
    <view class="gender-selector-wrap">
        <tui-form-item label="性别要求" :borderBottom="false">
            <view class="gender-options flex flex-wrap">
                <view v-for="item in genderOptions" :key="item.value" class="tag mb-20rpx mr-20rpx"
                    :class="{ 'active-option': modelValue === item.value }" @tap="selectGender(item)">
                    {{ item.name }}
                </view>
            </view>
        </tui-form-item>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { genderOptions as genderOptionsConstant } from '@/constants/standards'

interface GenderOption {
    name: string
    value: number
}

defineProps({
    modelValue: {
        type: Number,
        default: 0, // 0 for '不限'
    },
})

const emit = defineEmits(['update:modelValue'])

// 使用常量定义中的性别选项
const genderOptions = ref<GenderOption[]>(genderOptionsConstant.map(option => ({
    name: option.label,
    value: option.code
})));

const selectGender = (gender: GenderOption) => {
    emit('update:modelValue', gender.value)
}
</script>

<style lang="scss" scoped>
.tag {
    padding: 10rpx 30rpx;
    background-color: #f5f5f5;
    border-radius: 30rpx;
    font-size: 26rpx;
    transition: all 0.2s;
}

.active-option {
    background-color: #ffefe6;
    color: #ff6d00;
    font-weight: bold;
}
</style>
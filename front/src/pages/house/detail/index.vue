<template>
  <view class="container">
    <!-- 沉浸式导航栏 -->
    <uni-nav-bar
      fixed
      :border="false"
      status-bar
      :title="showNavTitle ? houseDetail.title : ''"
      left-icon="left"
      :background-color="navBgColor"
      :color="navTextColor"
      @clickLeft="goBack"
    />

    <!-- 内容区域 -->
    <scroll-view
      scroll-y
      enable-flex
      class="content-scroll"
      @scroll="handleScroll"
      :scroll-top="scrollTop"
    >
      <!-- 轮播图 -->
      <swiper
        class="image-swiper"
        :indicator-dots="true"
        :autoplay="false"
        :interval="3000"
        :duration="500"
        :circular="true"
        indicator-color="rgba(255, 255, 255, 0.6)"
        indicator-active-color="#FFFFFF"
      >
        <swiper-item
          v-for="(image, index) in houseDetail.images"
          :key="index"
          @tap="previewImages(index)"
        >
          <image :src="image" mode="aspectFill" class="swiper-image" />
        </swiper-item>
      </swiper>

      <!-- 图片计数 -->
      <view class="image-counter">
        <text>{{ currentImageIndex + 1 }}/{{ houseDetail.images.length }}</text>
      </view>

      <!-- VR标签 -->
      <!-- <view v-if="houseDetail.hasVR" class="vr-tag">
        <text class="i-carbon-view text-20rpx mr-6rpx"></text>
        <text>VR看房</text>
      </view> -->

      <!-- 主要内容区域 - 使用页面级卡片效果 -->
      <view class="content-wrapper">
        <!-- 价格和标签 -->
        <view class="price-section">
          <view class="price-info">
            <text class="price">{{ houseDetail.price }}</text>
            <text class="price-unit">元/月</text>
          </view>
          <view class="payment-method">{{
            houseDetail.paymentMethod || "押一付三"
          }}</view>
        </view>

        <!-- 标题 -->
        <view class="title-section">
          <text class="house-title">{{ formatTitle() }}</text>
        </view>

        <!-- 基本属性 -->
        <view class="attributes">
          <view class="attribute-item">
            <text class="attribute-value">{{ houseDetail.layout }}</text>
            <text class="attribute-label">户型</text>
          </view>
          <view class="attribute-item">
            <text class="attribute-value">{{ houseDetail.area }}㎡</text>
            <text class="attribute-label">面积</text>
          </view>
          <view class="attribute-item">
            <text class="attribute-value">{{ houseDetail.direction }}</text>
            <text class="attribute-label">朝向</text>
          </view>
          <view class="attribute-item">
            <text class="attribute-value">{{ houseDetail.floor }}</text>
            <text class="attribute-label">楼层</text>
          </view>
        </view>

        <!-- 房源标签 -->
        <view class="tags-section">
          <view
            v-for="(tag, index) in houseDetail.tags"
            :key="index"
            class="tag"
            >{{ tag }}</view
          >
        </view>

        <!-- 位置信息 -->
        <view class="content-section">
          <view class="section-header">
            <text class="section-title">小区位置</text>
          </view>
          <view class="location-content">
            <view class="location-info">
              <view class="community-name">{{ houseDetail.community }}</view>
              <view class="address">
                <text class="i-carbon-location text-info mr-10rpx"></text>
                <text class="address-text">{{ houseDetail.address }}</text>
              </view>
            </view>
            <view class="map-container" @tap="openMap">
              <image
                src="/static/images/map-placeholder.png"
                mode="aspectFill"
                class="map-image"
              />
              <view class="map-overlay">
                <text class="i-carbon-map text-36rpx"></text>
              </view>
            </view>
          </view>
        </view>

        <!-- 房源描述 -->
        <view class="content-section">
          <view class="section-header">
            <text class="section-title">房源描述</text>
          </view>
          <view class="description-content">
            <text class="description-text">{{ houseDetail.description }}</text>
            <view
              class="show-more"
              v-if="isDescriptionFolded && houseDetail.description.length > 100"
              @tap="toggleDescription"
            >
              <text class="show-more-text">展开</text>
              <text class="i-carbon-chevron-down text-24rpx ml-4rpx"></text>
            </view>
            <view
              class="show-more"
              v-if="
                !isDescriptionFolded && houseDetail.description.length > 100
              "
              @tap="toggleDescription"
            >
              <text class="show-more-text">收起</text>
              <text class="i-carbon-chevron-up text-24rpx ml-4rpx"></text>
            </view>
          </view>
        </view>

        <!-- 配套设施 -->
        <view class="content-section">
          <view class="section-header">
            <text class="section-title">配套设施</text>
          </view>
          <view class="facilities-content">
            <view
              v-for="(facility, index) in houseDetail.facilities"
              :key="index"
              :class="[
                'facility-item',
                facility.available === false ? 'facility-unavailable' : '',
              ]"
            >
              <view class="facility-icon-wrapper">
                <text
                  v-if="facility.available !== false"
                  class="facility-check i-carbon-checkmark-filled"
                ></text>
                <text
                  v-else
                  class="facility-cross i-carbon-close-filled"
                ></text>
              </view>
              <text class="facility-name">{{ facility.name }}</text>
            </view>
          </view>
        </view>

        <!-- 房东信息 -->
        <view class="content-section">
          <view class="section-header">
            <text class="section-title">房东信息</text>
          </view>
          <view class="landlord-content">
            <view class="landlord-info">
              <image
                :src="houseDetail.landlord.avatar"
                mode="aspectFill"
                class="landlord-avatar"
              />
              <view class="landlord-details">
                <view class="landlord-name">{{
                  houseDetail.landlord.name
                }}</view>
                <view class="landlord-type">{{
                  houseDetail.landlord.type
                }}</view>
              </view>
            </view>
            <view class="landlord-stats">
              <view class="stat-item">
                <text class="stat-value">{{
                  houseDetail.landlord.stats.listings
                }}</text>
                <text class="stat-label">房源</text>
              </view>
              <view class="stat-item">
                <text class="stat-value">{{
                  houseDetail.landlord.stats.reviews
                }}</text>
                <text class="stat-label">评价</text>
              </view>
              <view class="stat-item">
                <text class="stat-value"
                  >{{ houseDetail.landlord.stats.response }}%</text
                >
                <text class="stat-label">响应率</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 推荐房源 -->
        <view class="content-section">
          <view class="section-header">
            <text class="section-title">相似房源推荐</text>
            <view class="more-link" @tap="navigateToList">
              <text class="more-text">查看更多</text>
              <text class="i-carbon-arrow-right text-24rpx ml-4rpx"></text>
            </view>
          </view>
          <scroll-view
            scroll-x
            class="similar-houses-scroll"
            show-scrollbar="false"
          >
            <view class="similar-houses-content">
              <view
                v-for="(house, index) in similarHouses"
                :key="index"
                class="similar-house-card"
                @tap="navigateToDetail(house.id)"
              >
                <image
                  :src="house.image"
                  mode="aspectFill"
                  class="similar-house-image"
                />
                <view class="similar-house-info">
                  <text class="similar-house-title">{{ house.title }}</text>
                  <text class="similar-house-details"
                    >{{ house.layout }} | {{ house.area }}㎡</text
                  >
                  <view class="similar-house-price">
                    <text class="similar-price-value">{{ house.price }}</text>
                    <text class="similar-price-unit">元/月</text>
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
      <view class="h-160rpx"> </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <view class="action-buttons">
        <view class="action-button" @tap="toggleFavorite">
          <text
            :class="[
              isFavorite ? 'i-carbon-favorite-filled' : 'i-carbon-favorite',
              'action-icon',
              isFavorite ? 'favorite-active' : '',
            ]"
          ></text>
          <text class="action-text">{{ isFavorite ? "已收藏" : "收藏" }}</text>
        </view>
        <view class="action-button" @tap="shareHouse">
          <text class="i-carbon-share action-icon"></text>
          <text class="action-text">分享</text>
        </view>
      </view>
      <view class="contact-buttons">
        <view class="contact-button phone-button" @tap="callLandlord">
          <text class="i-carbon-phone mr-8rpx"></text>
          电话咨询
        </view>
        <view class="contact-button message-button" @tap="contactLandlord">
          在线咨询
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import PropertyTag from "@/components/PropertyTag.vue";

// 获取页面参数
const houseId = ref("");
const currentImageIndex = ref(0);
const isDescriptionFolded = ref(true);
const isFavorite = ref(false);
const scrollTop = ref(0);

// 导航栏状态
const showNavTitle = ref(false);
const navBgColor = ref("transparent");
const navTextColor = ref("#ffffff");

// 处理页面滚动
const handleScroll = (e: any) => {
  const scrollTop = e.detail.scrollTop;
  // 滚动距离超过轮播图高度的一半时，显示导航栏背景和标题
  if (scrollTop > 200) {
    navBgColor.value = "#ffffff";
    navTextColor.value = "#000000";
    showNavTitle.value = true;
  } else {
    // 计算透明度，实现渐变效果
    // const opacity = Math.min(scrollTop / 180, 1).toFixed(2);
    navBgColor.value = "transparent";
    navTextColor.value = "#ffffff";
    showNavTitle.value = false;
  }
};

// 页面加载时获取参数
onLoad((options) => {
  if (options.id) {
    houseId.value = options.id;
    console.log("获取到房源ID:", houseId.value);
    // 实际项目中应该根据ID从服务器获取房源详情
    // 此处使用模拟数据
  }
});

// 添加或修改脚本部分中的房源详情类型定义
interface Facility {
  name: string;
  icon?: string;
  available?: boolean;
}

interface HouseDetail {
  id: string;
  title: string;
  community: string;
  images: string[];
  layout: string;
  area: string;
  floor: string;
  direction: string;
  price: string;
  address: string;
  tags: string[];
  description: string;
  paymentMethod?: string;
  facilities: Facility[];
  landlord: {
    name: string;
    avatar: string;
    type: string;
    stats: {
      listings: number;
      reviews: number;
      response: number;
    };
  };
  similarHouses?: any[];
  hasVR?: boolean;
}

// 修改模拟数据，为设施添加available属性
const houseDetail = ref({
  id: houseId.value,
  title: "精装修2居室 南北通透 紧邻地铁",
  rentType: "整租",
  community: "康城暖山",
  address: "北京市朝阳区建国路89号",
  images: [
    "https://picsum.photos/seed/house1/800/600",
    "https://picsum.photos/seed/house2/800/600",
    "https://picsum.photos/seed/house3/800/600",
    "https://picsum.photos/seed/house4/800/600",
    "https://picsum.photos/seed/house5/800/600",
  ],
  layout: "2室1厅1卫",
  area: "89",
  floor: "中楼层/18层",
  direction: "南北",
  price: "5800",
  tags: ["近地铁", "精装修", "南北通透", "电梯房", "随时看房", "家电齐全"],
  description:
    "房屋亮点：\n1. 精装修两居室，家具家电齐全\n2. 小区环境优美，24小时安保\n3. 距离地铁5号线立水桥南站步行5分钟\n4. 周边配套齐全，购物中心、医院、学校应有尽有\n5. 南北通透，采光充足，通风良好\n6. 房屋干净整洁，拎包入住",
  paymentMethod: "押一付三",
  facilities: [
    { name: "空调", available: true },
    { name: "洗衣机", available: true },
    { name: "冰箱", available: true },
    { name: "热水器", available: true },
    { name: "床", available: true },
    { name: "电视", available: true },
    { name: "衣柜", available: true },
    { name: "沙发", available: true },
    { name: "阳台", available: false },
    { name: "宽带", available: true },
    { name: "燃气", available: true },
    { name: "暖气", available: false },
  ],
  landlord: {
    name: "张先生",
    avatar: "https://picsum.photos/seed/landlord1/100/100",
    type: "个人房东",
    stats: {
      listings: 5,
      reviews: 32,
      response: 98,
    },
  },
  hasVR: true,
});

// 相似房源推荐
const similarHouses = ref([
  {
    id: "2",
    title: "整租2居 · 远洋天地",
    layout: "2室1厅1卫",
    area: "75",
    price: "5200",
    image: "https://picsum.photos/seed/similar1/300/200",
  },
  {
    id: "3",
    title: "整租3居 · 万科星园",
    layout: "3室2厅1卫",
    area: "90",
    price: "6800",
    image: "https://picsum.photos/seed/similar2/300/200",
  },
  {
    id: "4",
    title: "整租1居 · 金茂府",
    layout: "1室1厅1卫",
    area: "45",
    price: "3500",
    image: "https://picsum.photos/seed/similar3/300/200",
  },
]);

// 格式化标题
const formatTitle = () => {
  const { rentType, layout, community } = houseDetail.value;
  const rooms = layout.match(/(\d+)室/) ? layout.match(/(\d+)室/)![1] : "";
  return `${rentType}${rooms}居 · ${community}`;
};

// 预览图片
const previewImages = (index: number) => {
  currentImageIndex.value = index;
  uni.previewImage({
    current: houseDetail.value.images[index],
    urls: houseDetail.value.images,
  });
};

// 展开/收起描述
const toggleDescription = () => {
  isDescriptionFolded.value = !isDescriptionFolded.value;
};

// 打开地图
const openMap = () => {
  uni.navigateTo({
    url: `/pages/house/map?id=${houseId.value}&community=${encodeURIComponent(
      houseDetail.value.community
    )}&address=${encodeURIComponent(houseDetail.value.address)}`,
  });
};

// 收藏房源
const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value;
  uni.showToast({
    title: isFavorite.value ? "已收藏" : "已取消收藏",
    icon: "none",
  });
};

// 分享房源
const shareHouse = () => {
  uni.showActionSheet({
    itemList: ["分享给朋友", "生成分享图片", "复制链接"],
    success: function (res) {
      uni.showToast({
        title: "分享功能开发中",
        icon: "none",
      });
    },
  });
};

// 电话联系
const callLandlord = () => {
  uni.showModal({
    title: "联系房东",
    content: "确定要拨打房东电话吗？",
    success: function (res) {
      if (res.confirm) {
        uni.makePhoneCall({
          phoneNumber: "13888888888", // 实际应从房源数据中获取
          fail: function () {
            uni.showToast({
              title: "拨号失败，请手动拨打",
              icon: "none",
            });
          },
        });
      }
    },
  });
};

// 在线咨询
const contactLandlord = () => {
  uni.navigateTo({
    url: `/pages/message/chat?targetId=${houseDetail.value.landlord.name}&targetType=landlord&houseId=${houseId.value}`,
  });
};

// 前往房源列表
const navigateToList = () => {
  uni.navigateTo({
    url: "/pages/house/rent/list",
  });
};

// 前往其他房源详情
const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/detail/index?id=${id}`,
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  position: relative;
  overflow-y: scroll;
}

.content-scroll {
  height: 100vh;
  position: absolute;
  left: 0;
  top: 0;
}

/* 轮播图区域 */
.image-swiper {
  width: 100%;
  height: 600rpx;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.image-counter {
  position: absolute;
  right: 30rpx;
  top: calc(44px + env(safe-area-inset-top) + 20rpx);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  z-index: 10;
}

.vr-tag {
  position: absolute;
  left: 30rpx;
  top: calc(44px + env(safe-area-inset-top) + 20rpx);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  z-index: 10;
  display: flex;
  align-items: center;
}

/* 主要内容包装器 - 页面级卡片效果 */
.content-wrapper {
  position: relative;
  top: -40rpx;
  background-color: var(--bg-card);
  border-radius: 24rpx 24rpx 0 0;
  padding: var(--spacing);
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  min-height: calc(100vh - 560rpx);
}

.price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: var(--spacing-2);
  border-bottom: 1rpx solid var(--border-color);
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 6rpx;
}

.price {
  font-size: 48rpx;
  font-weight: 600;
  color: var(--primary);
  text-shadow: 0 2rpx 4rpx rgba(255, 109, 0, 0.1);
}

.price-unit {
  color: var(--primary);
  font-weight: 500;
  opacity: 0.8;
}

.payment-method {
  font-size: 26rpx;
  color: var(--text-secondary);
  background-color: var(--bg-tag);
  padding: 8rpx var(--spacing-2);
  border-radius: var(--radius);
  border: 1rpx solid var(--border-color);
}

.title-section {
  margin-bottom: var(--spacing);
}

.house-title {
  font-size: 36rpx;
  font-weight: 500;
  line-height: var(--line-height-small);
}

.attributes {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing);
  padding: var(--spacing-2) 0;
  background-color: rgba(255, 109, 0, 0.02);
  border-radius: var(--radius-2);
  border: 1rpx solid rgba(255, 109, 0, 0.08);
}

.attribute-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.attribute-value {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: var(--spacing);
}

.attribute-label {
  font-size: 24rpx;
  color: var(--text-info);
}

.tags-section {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

/* 内容区域 - 无嵌套卡片 */
.content-section {
  margin: var(--spacing) 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  position: relative;
}

.more-link {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    color: var(--primary);
  }
}

.more-text {
  font-size: 26rpx;
  color: var(--text-info);
}

/* 位置信息 */
.location-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(255, 109, 0, 0.02);
  padding: var(--spacing-2);
  border-radius: var(--radius-2);
  border: 1rpx solid rgba(255, 109, 0, 0.08);
}

.location-info {
  flex: 1;
}

.community-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: var(--spacing);
}

.address {
  display: flex;
  align-items: center;
}

.address-text {
  font-size: 26rpx;
  color: var(--text-secondary);
  flex: 1;
}

.map-container {
  width: 200rpx;
  height: 140rpx;
  border-radius: var(--radius-2);
  overflow: hidden;
  position: relative;
  margin-left: 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }
}

.map-image {
  width: 100%;
  height: 100%;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-inverse);
  backdrop-filter: blur(2rpx);
  -webkit-backdrop-filter: blur(2rpx);
}

/* 房源描述 */
.description-content {
  position: relative;
  padding: var(--spacing-2);
  border-radius: var(--radius-2);
}

.description-text {
  line-height: var(--line-height-normal);
  text-align: justify;

  max-height: v-bind('isDescriptionFolded ? "200rpx" : "auto"');
  overflow: v-bind('isDescriptionFolded ? "hidden" : "visible"');
}

.show-more {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2rpx);
  }
}

.show-more-text {
  font-size: 26rpx;
  color: var(--primary);
  font-weight: 500;
}

/* 配套设施 */
.facilities-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  padding: 16rpx 0;
}

.facility-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.facility-unavailable {
  opacity: 0.5;
}

.facility-icon-wrapper {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.facility-check {
  color: #22c55e;
  font-size: 32rpx;
}

.facility-cross {
  color: #ef4444;
  font-size: 32rpx;
}

.facility-name {
  font-size: 26rpx;
}

/* 房东信息 */
.landlord-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(255, 109, 0, 0.02);
  padding: var(--spacing-2);
  border-radius: var(--radius-2);
  border: 1rpx solid rgba(255, 109, 0, 0.08);
}

.landlord-info {
  display: flex;
  align-items: center;
}

.landlord-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 2rpx solid var(--border-color);
}

.landlord-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: var(--spacing);
}

.landlord-type {
  font-size: 24rpx;
  color: var(--text-info);
}

.landlord-stats {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-weight: 500;
  margin-bottom: var(--spacing);
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-info);
}

/* 相似房源 */
.similar-houses-scroll {
  width: 100%;
  margin-top: 12rpx;
}

.similar-houses-content {
  display: flex;
  padding: 12rpx 0;
  gap: var(--spacing-2);
}

.similar-house-card {
  width: 280rpx;
  border-radius: var(--radius-2);
  overflow: hidden;
  background-color: var(--bg-tag);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  }
}

.similar-house-image {
  width: 100%;
  height: 180rpx;
  transition: transform 0.3s ease;

  .similar-house-card:hover & {
    transform: scale(1.05);
  }
}

.similar-house-info {
  padding: var(--spacing-2);
}

.similar-house-title {
  font-size: 26rpx;
  font-weight: 500;
  margin-bottom: var(--spacing);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.similar-house-details {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: var(--spacing);
}

.similar-house-price {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.similar-price-value {
  font-weight: 500;
  color: var(--primary);
}

.similar-price-unit {
  font-size: 24rpx;
  color: var(--primary);
  opacity: 0.8;
}

/* 底部操作栏 */
.bottom-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 160rpx;
  padding: 20rpx;
  background-color: var(--bg-card);
  display: flex;
  align-items: center;
  box-shadow: 0 -4rpx 24rpx rgba(0, 0, 0, 0.08);
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-top: 1rpx solid var(--border-color);
}

.action-buttons {
  display: flex;
  width: 30%;
  height: 100%;
}

.action-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2rpx);
  }
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  color: var(--text-secondary);
}

.favorite-active {
  color: #ef4444;
}

.action-text {
  font-size: 24rpx;
}

.contact-buttons {
  display: flex;
  width: 70%;
  height: 100%;
  gap: 12rpx;
}

.contact-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  border-radius: 40rpx;

  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.phone-button {
  background-color: rgba(255, 109, 0, 0.08);
  color: var(--primary);
  border: 1rpx solid rgba(255, 109, 0, 0.2);

  &:hover {
    background-color: rgba(255, 109, 0, 0.12);
    transform: translateY(-2rpx);
  }
}

.message-button {
  background: linear-gradient(135deg, var(--primary) 0%, #ff8d42 100%);
  color: var(--text-inverse);
  box-shadow: 0 4rpx 12rpx rgba(255, 109, 0, 0.2);

  &:hover {
    box-shadow: 0 6rpx 16rpx rgba(255, 109, 0, 0.3);
    transform: translateY(-2rpx);
  }
}
</style>

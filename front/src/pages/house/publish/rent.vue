<template>
  <view class="rent-publish-page">
    <uni-nav-bar
      :border="false"
      fixed
      background-color="#ffffff"
      :status-bar="true"
      title="发布租房"
      left-icon="back"
      @clickLeft="goBack"
    />

    <view class="content">
      <view class="guide-section">
        <text class="guide-title">轻轻松松，上架房源</text>
        <text class="guide-desc"
          >发布房源 → 进行真房认证 → 58/安居客同步展示</text
        >
      </view>

      <view class="form-content">
        <tui-form ref="formRef" :model="formData" :rules="formRules">
          <view class="card">
            <view class="section-title">
              <text class="required">*</text>
              <text>出租方式</text>
            </view>
            <view class="rent-type-tabs">
              <view
                v-for="type in rentTypes"
                :key="type.value"
                class="tab-item"
                :class="{ active: formData.rentType === type.value }"
                @tap="selectRentType(type.value)"
              >
                {{ type.label }}
              </view>
            </view>
          </view>

          <view class="card">
            <view class="section-title">房源信息</view>

            <tui-form-item
              prop="city"
              label="当前城市"
              labelSize="30rpx"
              :labelColor="getLabelColor('city')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="selectCity"
            >
              <tui-input
                v-model="formData.city"
                placeholder="请选择城市"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                :disabled="true"
              />
              <template v-slot:right>
                <text class="i-carbon-chevron-right form-arrow"></text>
              </template>
            </tui-form-item>

            <tui-form-item
              prop="community"
              label="小区名称"
              labelSize="30rpx"
              :labelColor="getLabelColor('community')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('community')"
            >
              <tui-input
                v-model="formData.community"
                placeholder="请输入小区名称"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('community')"
                @blur="handleBlur('community')"
              />
            </tui-form-item>

            <view class="form-tip">
              （只用于平台验真，不会展示给租客，请如实填写）
            </view>

            <tui-form-item
              prop="building"
              label="楼栋号"
              labelSize="30rpx"
              :labelColor="getLabelColor('building')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('building')"
            >
              <tui-input
                v-model="formData.building"
                placeholder="请输入楼栋号"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('building')"
                @blur="handleBlur('building')"
              />
            </tui-form-item>

            <tui-form-item
              prop="area"
              label="房屋面积"
              labelSize="30rpx"
              :labelColor="getLabelColor('area')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('area')"
            >
              <view class="input-with-unit">
                <tui-input
                  v-model="formData.area"
                  placeholder="请输入面积"
                  type="number"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  @input="handleInput('area')"
                  @blur="handleBlur('area')"
                />
              </view>
              <template #right>
                <text>m²</text>
              </template>
            </tui-form-item>

            <tui-form-item
              prop="houseType"
              label="房屋户型"
              labelSize="30rpx"
              :labelColor="getLabelColor('houseType')"
              asterisk
              arrow
              asteriskColor="#ff4757"
            >
              <picker
                mode="multiSelector"
                :range="houseTypeRange"
                :value="houseTypePickerValue"
                @change="onHouseTypeChange"
              >
                <tui-input
                  v-model="formData.houseType"
                  placeholder="请选择"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  :disabled="true"
                />
              </picker>
            </tui-form-item>

            <tui-form-item
              prop="orientation"
              label="房屋朝向"
              labelSize="30rpx"
              :labelColor="getLabelColor('orientation')"
              :bottomBorder="false"
              asterisk
              arrow
            >
              <picker
                mode="selector"
                :range="orientationList"
                :value="orientationPickerValue"
                @change="onOrientationChange"
              >
                <tui-input
                  v-model="formData.orientation"
                  placeholder="请选择"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  :disabled="true"
                />
              </picker>
            </tui-form-item>
          </view>

          <view class="card">
            <view class="section-title">价格信息</view>

            <tui-form-item
              prop="monthlyRent"
              label="期望租金"
              labelSize="30rpx"
              :labelColor="getLabelColor('monthlyRent')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('monthlyRent')"
            >
              <view class="input-with-unit">
                <tui-input
                  v-model="formData.monthlyRent"
                  placeholder="请输入月租金"
                  type="number"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  @input="handleInput('monthlyRent')"
                  @blur="handleBlur('monthlyRent')"
                />
              </view>
              <template #right>
                <text>元/月</text>
              </template>
            </tui-form-item>

            <tui-form-item
              prop="paymentMethod"
              label="付款方式"
              labelSize="30rpx"
              :labelColor="getLabelColor('paymentMethod')"
              asterisk
              arrow
              asteriskColor="#ff4757"
              :bottomBorder="false"
            >
              <picker
                mode="selector"
                :range="paymentMethodList"
                :value="paymentMethodPickerValue"
                @change="onPaymentMethodChange"
              >
                <tui-input
                  v-model="formData.paymentMethod"
                  placeholder="请选择"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  :disabled="true"
                />
              </picker>
            </tui-form-item>
          </view>

          <view class="card">
            <tui-form-item
              prop="includedFees"
              label="包含费用"
              labelSize="30rpx"
              arrow
              :labelColor="getLabelColor('includedFees')"
              :bottomBorder="false"
              @click="selectIncludedFees"
            >
              <tui-input
                v-model="selectedFeesText"
                placeholder="请选择"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                :disabled="true"
              />
            </tui-form-item>
          </view>

          <view class="card">
            <view class="section-title">
              <text class="required">*</text>
              <text>房源图片</text>
            </view>
            <text class="section-tip"
              >至少上传1张图片，最多9张，遵守图片上传规范</text
            >

            <view class="image-upload-area">
              <view class="upload-grid">
                <view
                  v-for="(image, index) in formData.images"
                  :key="index"
                  class="image-item"
                >
                  <image :src="image" mode="aspectFill" class="preview-image" />
                  <view class="delete-btn" @tap="deleteImage(index)">
                    <text class="i-carbon-close"></text>
                  </view>
                </view>

                <view
                  v-if="formData.images.length < 9"
                  class="upload-btn"
                  @tap="uploadImage"
                >
                  <text class="i-carbon-camera upload-icon"></text>
                  <text class="upload-text">添加房源图片</text>
                  <text class="upload-text sub">真实房源更吸引人</text>
                </view>
              </view>
            </view>
          </view>

          <view class="card">
            <view class="section-title">房源配套</view>
            <text class="section-tip">选择房源配套设施，提升房源吸引力</text>

            <view class="facilities-section">
              <view
                v-for="category in FACILITIES_OPTIONS"
                :key="category.type"
                class="facility-category"
              >
                <view class="category-title">{{ category.title }}</view>
                <view class="facility-tags">
                  <view
                    v-for="facility in category.items"
                    :key="facility.value"
                    class="facility-tag"
                    :class="{
                      active: formData.facilities.includes(facility.value),
                    }"
                    @tap="toggleFacility(facility.value)"
                  >
                    {{ facility.label }}
                  </view>
                </view>
              </view>
            </view>
          </view>

          <view class="card">
            <view class="section-title">房源亮点</view>
            <text class="section-tip">选择房源特色标签，突出房源优势</text>

            <view class="tags-section">
              <view
                v-for="tag in HOUSE_TAGS_OPTIONS"
                :key="tag.value"
                class="house-tag"
                :class="{ active: formData.houseTags.includes(tag.value) }"
                @tap="toggleHouseTag(tag.value)"
              >
                {{ tag.label }}
              </view>
            </view>
          </view>

          <view class="card">
            <view class="section-title">房源描述</view>
            <text class="section-tip"
              >详细介绍房屋特色、周边配套等，吸引更多租客</text
            >

            <tui-form-item
              prop="description"
              asteriskColor="#ff4757"
              :bottomBorder="false"
              padding="0 0"
              flexStart
            >
              <tui-textarea
                v-model="formData.description"
                placeholder="请详细描述房屋的位置优势、装修状况、周边配套等信息"
                maxlength="500"
                height="200rpx"
                minHeight="200rpx"
                :borderTop="false"
                :borderBottom="false"
                backgroundColor="var(--bg-input)"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                showConfirmBar
                isCounter
                counterSize="24"
                counterColor="var(--text-info)"
                @input="handleInput('description')"
                @blur="handleBlur('description')"
              />
            </tui-form-item>
          </view>
        </tui-form>

        <view class="agreement-section">
          <view class="agreement-item" @tap="toggleAgreement">
            <view class="checkbox" :class="{ checked: agreedToTerms }">
              <text v-if="agreedToTerms" class="i-carbon-checkmark"></text>
            </view>
            <text class="agreement-text">
              我承诺房源真实并同意《个人用户租房房源推广服务协议》
            </text>
          </view>
        </view>
      </view>

      <view class="submit-section">
        <tui-button
          @click="submitForm"
          type="primary"
          width="100%"
          height="96rpx"
          :bold="true"
          shape="circle"
        >
          免费发布
        </tui-button>
      </view>
    </view>

    <tui-bottom-popup :show="showCityPicker" @close="closeCityPicker">
      <view class="picker-container">
        <view class="picker-header">
          <text class="picker-cancel" @tap="closeCityPicker">取消</text>
          <text class="picker-title">选择城市</text>
          <text class="picker-confirm" @tap="confirmCity">确定</text>
        </view>
        <picker-view
          class="picker-view"
          :value="cityPickerValue"
          @change="onCityPickerChange"
        >
          <picker-view-column>
            <view
              v-for="(city, index) in CITY_LIST"
              :key="index"
              class="picker-item"
            >
              {{ city }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </tui-bottom-popup>

    <tui-bottom-popup
      :show="showIncludedFeesPicker"
      @close="closeIncludedFeesPicker"
    >
      <view class="picker-container">
        <view class="picker-header">
          <text class="picker-cancel" @tap="closeIncludedFeesPicker">取消</text>
          <text class="picker-title">选择包含费用</text>
          <text class="picker-confirm" @tap="confirmIncludedFees">确定</text>
        </view>
        <view class="checkbox-list">
          <view
            v-for="(fee, index) in INCLUDED_FEES_OPTIONS"
            :key="index"
            class="checkbox-item"
            @tap="toggleIncludedFee(fee.value)"
          >
            <view class="checkbox-text">{{ fee.label }}</view>
            <view
              class="checkbox"
              :class="{ checked: formData.includedFees.includes(fee.value) }"
            >
              <text
                v-if="formData.includedFees.includes(fee.value)"
                class="i-carbon-checkmark text-32rpx"
              ></text>
            </view>
          </view>
        </view>
      </view>
    </tui-bottom-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import {
  HOUSE_TYPE_RANGE,
  ORIENTATION_OPTIONS,
  PAYMENT_METHOD_OPTIONS,
  INCLUDED_FEES_OPTIONS,
  FACILITIES_OPTIONS,
  HOUSE_TAGS_OPTIONS,
  CITY_LIST,
} from "@/constants";

const formRef = ref<any>(null);

const formData = reactive({
  rentType: "whole",
  city: "北京",
  community: "",
  building: "",
  area: "",
  houseType: "",
  orientation: "",
  monthlyRent: "",
  paymentMethod: "",
  includedFees: [] as string[],
  facilities: [] as string[],
  houseTags: [] as string[],
  images: [] as string[],
  description: "",
});

const agreedToTerms = ref(false);

const fieldFocusState = reactive({
  city: false,
  community: false,
  building: false,
  area: false,
  houseType: false,
  orientation: false,
  monthlyRent: false,
  paymentMethod: false,
  includedFees: false,
  description: false,
});

const rentTypes = [
  { label: "整租", value: "whole" },
  { label: "合租", value: "shared" },
];

const formRules = {
  city: {
    name: "city",
    rule: ["required"],
    msg: ["请选择城市"],
  },
  community: {
    name: "community",
    rule: ["required"],
    msg: ["请输入小区名称"],
  },
  building: {
    name: "building",
    rule: ["required"],
    msg: ["请输入楼栋号"],
  },
  area: {
    name: "area",
    rule: ["required", "number"],
    msg: ["请输入房屋面积", "请输入正确的面积数字"],
  },
  houseType: {
    name: "houseType",
    rule: ["required"],
    msg: ["请选择房屋户型"],
  },
  monthlyRent: {
    name: "monthlyRent",
    rule: ["required", "number"],
    msg: ["请输入月租金", "请输入正确的租金"],
  },
  paymentMethod: {
    name: "paymentMethod",
    rule: ["required"],
    msg: ["请选择付款方式"],
  },
};

const showCityPicker = ref(false);
const showIncludedFeesPicker = ref(false);

const cityPickerValue = ref([0]);
const selectedCityIndex = ref(0);

// 房屋户型 - 三列选择器
const houseTypeRange = HOUSE_TYPE_RANGE;
const houseTypePickerValue = ref([0, 0, 0]);

// 房屋朝向
const orientationList = ORIENTATION_OPTIONS.map((item) => item.label);
const orientationPickerValue = ref(0);

// 付款方式
const paymentMethodList = PAYMENT_METHOD_OPTIONS.map((item) => item.label);
const paymentMethodPickerValue = ref(0);

const selectedFeesText = computed(() => {
  if (formData.includedFees.length === 0) return "";
  const selectedFees = INCLUDED_FEES_OPTIONS.filter((fee) =>
    formData.includedFees.includes(fee.value)
  ).map((fee) => fee.label);
  return selectedFees.join("、");
});

const getLabelColor = (field: string) => {
  const value = formData[field as keyof typeof formData];
  if (typeof value === "string") {
    return value ? "var(--text-info)" : "var(--text-secondary)";
  }
  return "var(--text-secondary)";
};

let inputTimer: any = null;
const handleInput = (field: string) => {
  if (inputTimer) clearTimeout(inputTimer);
  inputTimer = setTimeout(() => {
    if (formRef.value) {
      requestAnimationFrame(() => {
        formRef.value.validateField(field);
      });
    }
  }, 300);
};

const handleBlur = (field: string) => {
  fieldFocusState[field as keyof typeof fieldFocusState] = false;
};

const handleFormItemClick = (field: string) => {
  fieldFocusState[field as keyof typeof fieldFocusState] = true;
};

const selectRentType = (type: string) => {
  formData.rentType = type;
};

const selectCity = () => {
  showCityPicker.value = true;
};

const closeCityPicker = () => {
  showCityPicker.value = false;
};

const onCityPickerChange = (e: any) => {
  selectedCityIndex.value = e.detail.value[0];
};

const confirmCity = () => {
  formData.city = CITY_LIST[selectedCityIndex.value];
  closeCityPicker();
};

// 房屋户型选择事件
const onHouseTypeChange = (e: any) => {
  const values = e.detail.value;
  houseTypePickerValue.value = values;
  const room = houseTypeRange[0][values[0]];
  const hall = houseTypeRange[1][values[1]];
  const bath = houseTypeRange[2][values[2]];
  formData.houseType = `${room}${hall}${bath}`;
};

// 房屋朝向选择事件
const onOrientationChange = (e: any) => {
  const index = e.detail.value;
  orientationPickerValue.value = index;
  formData.orientation = orientationList[index];
};

// 付款方式选择事件
const onPaymentMethodChange = (e: any) => {
  const index = e.detail.value;
  paymentMethodPickerValue.value = index;
  formData.paymentMethod = paymentMethodList[index];
};

const selectIncludedFees = () => {
  showIncludedFeesPicker.value = true;
};

const closeIncludedFeesPicker = () => {
  showIncludedFeesPicker.value = false;
};

const toggleIncludedFee = (value: string) => {
  const index = formData.includedFees.indexOf(value);
  if (index > -1) {
    formData.includedFees.splice(index, 1);
  } else {
    formData.includedFees.push(value);
  }
};

const confirmIncludedFees = () => {
  closeIncludedFeesPicker();
};

// 配套设施选择
const toggleFacility = (value: string) => {
  const index = formData.facilities.indexOf(value);
  if (index > -1) {
    formData.facilities.splice(index, 1);
  } else {
    formData.facilities.push(value);
  }
};

// 房源标签选择
const toggleHouseTag = (value: string) => {
  const index = formData.houseTags.indexOf(value);
  if (index > -1) {
    formData.houseTags.splice(index, 1);
  } else {
    formData.houseTags.push(value);
  }
};

const uploadImage = () => {
  uni.showLoading({
    title: "准备上传...",
  });

  uni.chooseImage({
    count: 9 - formData.images.length,
    sizeType: ["compressed"],
    sourceType: ["camera", "album"],
    success: (res) => {
      if (Array.isArray(res.tempFilePaths)) {
        setTimeout(() => {
          const paths = res.tempFilePaths as string[];
          const newImages = paths.slice(0, 9 - formData.images.length);

          if (newImages.length > 3) {
            for (let i = 0; i < 3; i++) {
              formData.images.push(newImages[i]);
            }
            setTimeout(() => {
              for (let i = 3; i < newImages.length; i++) {
                formData.images.push(newImages[i]);
              }
              uni.hideLoading();
            }, 100);
          } else {
            for (let i = 0; i < newImages.length; i++) {
              formData.images.push(newImages[i]);
            }
            uni.hideLoading();
          }
        }, 100);
      } else {
        uni.hideLoading();
      }
    },
    fail: () => {
      uni.hideLoading();
    },
  });
};

const deleteImage = (index: number) => {
  formData.images.splice(index, 1);
};

const toggleAgreement = () => {
  agreedToTerms.value = !agreedToTerms.value;
};

const submitForm = async () => {
  try {
    if (formData.images.length === 0) {
      uni.showToast({
        title: "请至少上传1张房源图片",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    if (!agreedToTerms.value) {
      uni.showToast({
        title: "请先同意服务协议",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    if (formRef.value) {
      const validateResult = await formRef.value.validate();
      if (!validateResult.isPass) {
        uni.showToast({
          title: validateResult.errorMsg || "请完善必填信息",
          icon: "none",
          duration: 2000,
        });
        return;
      }
    }

    await publishHouse();
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: "发布失败，请重试",
      icon: "none",
      duration: 2000,
    });
  }
};

// 发布房源
const publishHouse = async () => {
  try {
    uni.showLoading({ title: "发布中..." });

    // 构建发布数据
    const publishData = {
      ...formData,
    };

    console.log("发布数据:", publishData);

    await new Promise((resolve) => setTimeout(resolve, 2000));
    uni.hideLoading();

    uni.showToast({
      title: "发布成功",
      icon: "success",
      success: () => {
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      },
    });
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: "发布失败，请重试",
      icon: "none",
      duration: 2000,
    });
  }
};

const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.rent-publish-page {
  min-height: 100vh;
  background: var(--bg-page);
}

.content {
  padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
  background: transparent;
  box-sizing: border-box;
}

.guide-section {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  border-radius: 20rpx;
  padding: 20rpx;
  margin-top: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  text-align: center;
  box-shadow: 0 8rpx 20rpx rgba(79, 172, 254, 0.15);
  will-change: transform;
  transform: translateZ(0);
  contain: layout style;
}

.guide-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-inverse);
  display: block;
  margin-bottom: 12rpx;
}

.guide-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  display: block;
  line-height: var(--line-height-normal);
}

.form-content {
  padding-bottom: 64rpx;
}

.card {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;

  margin: 12rpx 0 12rpx 12rpx;
  display: flex;
  align-items: center;
}

.section-tip {
  font-size: 24rpx;
  color: var(--text-info);
  margin-bottom: var(--spacing);
  display: block;
  line-height: var(--line-height-normal);
}

.required {
  color: var(--text-red);
  margin-right: var(--spacing);
  font-weight: 600;
}

.rent-type-tabs {
  display: flex;
  gap: var(--spacing-2);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx var(--spacing-2);
  border-radius: var(--radius-2);
  background: var(--bg-tag);
  color: var(--text-secondary);

  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 2rpx solid transparent;

  &.active {
    background: #4facfe;
    color: var(--text-inverse);
    border-color: #4facfe;
  }

  &:active {
    transform: scale(0.95);
  }
}

.form-tip {
  font-size: 24rpx;
  color: var(--text-info);
  margin-top: var(--spacing);
  line-height: var(--line-height-normal);
}

.input-with-unit {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
}

.form-arrow {
  font-size: 20rpx;
  color: var(--text-grey);
  margin-left: var(--spacing-2);
}

.image-upload-area {
  margin-top: var(--spacing-2);
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-2);
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: var(--radius-2);
  overflow: hidden;
  background: var(--bg-tag);
}

.preview-image {
  width: 100%;
  height: 100%;
  will-change: transform;
  transform: translateZ(0);
  contain: paint;
}

.delete-btn {
  position: absolute;
  top: var(--spacing);
  right: var(--spacing);
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-size: 20rpx;

  &:active {
    transform: scale(0.9);
  }
}

.upload-btn {
  aspect-ratio: 1;
  border: 2rpx dashed var(--text-grey);
  border-radius: var(--radius-2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-info);
  background: var(--bg-tag);
  transition: all 0.3s ease;

  &:active {
    border-color: #4facfe;
    color: #4facfe;
    background: rgba(79, 172, 254, 0.1);
  }
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: var(--spacing);
}

.upload-text {
  font-size: 24rpx;
  text-align: center;
  line-height: var(--line-height-normal);

  &.sub {
    margin-top: 2rpx;
    opacity: 0.8;
  }
}

.agreement-section {
  margin-bottom: 40rpx;
  padding: var(--spacing);
  background: var(--bg-card);
  border-radius: var(--radius-2);
  border: 1rpx solid var(--border-color);
}

.agreement-item {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-2) 0;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid var(--text-grey);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-3);
  margin-top: 2rpx;
  transition: all 0.2s ease;

  &.checked {
    background: var(--primary);
    border-color: var(--primary);
    color: var(--text-inverse);
  }

  &:active {
    transform: scale(0.9);
  }
}

.agreement-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: var(--line-height-large);
  flex: 1;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing) var(--spacing);
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  background: var(--bg-card);
  border-top: 1rpx solid var(--border-color);
  z-index: 100;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.08);
  will-change: transform;
  transform: translateZ(0);
}

.service-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx var(--spacing);
  margin-bottom: var(--spacing-3);
  background: linear-gradient(135deg, #f0f8ff, #e6f7ff);
  border-radius: var(--radius-2);
  border: 1rpx solid #d1e9ff;
}

.summary-text {
  font-size: 26rpx;
  font-weight: var(--font-weight-semibold);
  color: var(--primary);
}

.summary-benefit {
  font-size: 24rpx;
  color: var(--text-info);
  background: var(--primary);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.picker-container {
  padding: 32rpx;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing) 0;
  border-bottom: 1rpx solid var(--border-color);
}

.picker-cancel,
.picker-confirm {
  color: var(--text-secondary);
  font-weight: 500;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;

  display: block;
  margin-bottom: var(--spacing);
}

.picker-view {
  height: 200rpx;
  overflow: hidden;
}

.picker-view-column {
  height: 100%;
}

.picker-item {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing) 0;
  border-bottom: 1rpx solid var(--border-color);

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: var(--bg-tag);
  }
}

.checkbox-text {
  font-size: 30rpx;
}

// 配套设施样式
.facilities-section {
  margin-top: var(--spacing);
}

.facility-category {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.category-title {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-3);
  font-weight: 500;
}

.facility-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.facility-tag {
  padding: var(--spacing-2) var(--spacing-3);
  background: var(--bg-tag);
  color: var(--text-secondary);
  border-radius: var(--radius);
  font-size: 24rpx;
  font-weight: 500;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;

  &.active {
    background: rgba(79, 172, 254, 0.1);
    color: var(--primary);
    border-color: var(--primary);
  }

  &:active {
    transform: scale(0.95);
  }
}

// 房源标签样式
.tags-section {
  margin-top: var(--spacing);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.house-tag {
  padding: var(--spacing-2) var(--spacing);
  background: var(--bg-tag);
  color: var(--text-secondary);
  border-radius: var(--radius-2);
  font-size: 26rpx;
  font-weight: 500;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;

  &.active {
    background: var(--primary);
    color: var(--text-inverse);
    border-color: var(--primary);
  }

  &:active {
    transform: scale(0.95);
  }
}
</style>

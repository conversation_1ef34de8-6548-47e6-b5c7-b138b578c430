<template>
  <view class="container">
    <!-- 使用z-paging组件代替scroll-view -->
   
    <z-paging
      ref="paging"
      v-model="houseList"
      @query="queryHouseList"
      :refresher-enabled="true"
      :show-scrollbar="false"
      @scroll="onPageScroll"
    >
    <template #top>
      <uni-nav-bar
      :fixed="true"
      :border="false"
      :statusBar="true"
      title=""
      left-icon="left"
      :backgroundColor="scrollTop > 150 ? '#ffffff' : 'transparent'"
      @clickLeft="goBack"
    />

    </template>
      <!-- 顶部搜索栏 -->
      <view class="search-header">
        <view class="search-box" @tap="goToSearch">
          <text class="i-solar:minimalistic-magnifer-linear text-28rpx text-info mr-12rpx"></text>
          <text class="search-placeholder">搜索小区、地标、学校</text>
        </view>
      </view>

      <!-- 功能区导航栏 -->
      <view class="nav-list">
        <view
          v-for="(item, index) in functionNavs"
          :key="index"
          class="nav-item"
          :class="{ active: activeFunctionIndex === index }"
          @tap="switchFunction(index)"
        >
          <view class="nav-icon-wrapper">
            <text
              :class="[item.icon, 'nav-icon']"
              :style="{
                color: activeFunctionIndex === index ? 'var(--primary)' : 'var(--text-secondary)',
              }"
            ></text>
          </view>
          <text
            class="nav-text"
            :style="{
              color: activeFunctionIndex === index ? 'var(--primary)' : 'var(--text-secondary)',
            }"
            >{{ item.name }}</text
          >
          <view
            v-if="activeFunctionIndex === index"
            class="nav-indicator"
          ></view>
        </view>
      </view>

      <!-- 筛选面板组件，吸顶后背景色集成父组件背景色-->
      <view class="filter-panel-wrapper" :class="{'bg-white': scrollTop > 150}">
        <HouseFilterPanel
          ref="filterPanelRef"
          :initial-filters="filterParams"
          @filter-change="onFilterChange"
          @filter-reset="onFilterReset"
          @menuOpened="handleFilterMenuOpened"
        />
      </view>

      <!-- 房源列表 -->
      <view class="house-list">
        <view
          v-for="(house, index) in houseList"
          :key="house.id"
          class="card house-card"
          @tap="goToDetail(house.id)"
        >
          <!-- 房源图片 -->
          <view class="image-container">
            <image
              :src="house.image"
              mode="aspectFill"
              class="house-image"
              :lazy-load="true"
            />
            <!-- VR看房标签 -->
            <view v-if="house.hasVR" class="vr-tag">
              <text class="i-carbon-view mr-4rpx"></text>
              <text class="vr-text">VR看房</text>
            </view>
            <!-- 新上标签 -->
            <view v-if="house.isNew" class="new-tag">新上</view>
          </view>

          <!-- 房源信息 -->
          <view class="house-info">
            <!-- 上半部分：标题和基本信息 -->
            <view class="info-top">
              <!-- 标题行 -->
              <text class="house-title">{{ formatTitle(house) }}</text>

              <!-- 房源详情 -->
              <text class="details">{{ formatDetails(house) }}</text>

              <!-- 标签区域 -->
              <view class="tags-container">
                <view
                  v-for="(tag, tagIndex) in house.tags.slice(0, 4)"
                  :key="tagIndex"
                  class="tag"
                  >{{ tag }}</view
                >
              </view>
            </view>

            <!-- 下半部分：价格信息 -->
            <view class="info-bottom">
              <view class="price-row">
                <text class="price">{{ house.price }}</text>
                <text class="price-unit">元/月</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import HouseFilterPanel from "@/components/house/HouseFilterPanel.vue";

// 房源数据接口定义
interface RentHouse {
  id: string;
  title: string;
  community: string;
  layout: string; // 如"2室1厅1卫"
  area: string;
  floor: string;
  direction: string;
  price: string;
  location: string;
  tags: string[];
  image: string;
  hasVR?: boolean;
  isNew?: boolean;
  paymentMethod?: string; // 如"押一付一"
  utilities?: string; // 如"民水民电"
  rentType?: string; // 整租/合租
}

// 筛选面板引用
const filterPanelRef = ref(null);

// 分页组件引用
const paging = ref(null);

// 筛选面板是否吸顶
const isFilterSticky = ref(false);

// 筛选参数
const filterParams = reactive({
  area: "",
  price: "",
  layout: "",
  rentType: "",
  more: [],
});

// 功能导航当前选中索引
const activeFunctionIndex = ref(0);

// 功能导航数据 - 使用更美观的图标
const functionNavs = reactive([
  {
    name: "公寓",
    icon: "i-carbon-building-insights-3",
    type: "apartment",
    color: "#FF6B35",
  },
  {
    name: "合租",
    icon: "i-carbon-user-multiple",
    type: "shared",
    color: "#4ECDC4",
  },
  { name: "整租", icon: "i-carbon-home", type: "whole", color: "#45B7D1" },
  {
    name: "民宿",
    icon: "i-carbon-location-heart",
    type: "bnb",
    color: "#F1948A",
  },
]);

// 房源列表数据
const houseList = ref<RentHouse[]>([]);

// 页面滚动位置
const scrollTop = ref(0);

// 页面方法
const goBack = () => {
  uni.navigateBack();
};

const goToSearch = () => {
  uni.navigateTo({
    url: "/pages/house/search",
  });
};

const goToMap = () => {
  uni.navigateTo({
    url: "/pages/house/rent/map",
  });
};

const goToDetail = (houseId: string) => {
  uni.navigateTo({
    url: `/pages/house/detail/index?id=${houseId}`,
  });
};

// 功能导航切换
const switchFunction = (index: number) => {
  activeFunctionIndex.value = index;
  // 使用z-paging的reload方法重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 筛选条件变化
const onFilterChange = (filters) => {
  Object.assign(filterParams, filters);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 重置筛选条件
const onFilterReset = () => {
  Object.keys(filterParams).forEach((key) => {
    if (key === "more") {
      filterParams[key] = [];
    } else {
      filterParams[key] = "";
    }
  });

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// z-paging查询房源列表数据
const queryHouseList = (pageNo: number, pageSize: number) => {
  // 模拟网络请求延迟
  setTimeout(() => {
    // 模拟数据
    const mockData: RentHouse[] = [
      {
        id: "1",
        title: "整租2居 · 香山南营66号",
        community: "香山南营66号",
        layout: "2室1厅1卫",
        area: "70",
        floor: "高楼层",
        direction: "南北",
        price: "4600",
        location: "西山",
        tags: ["南北通透", "高楼层", "步梯"],
        image: "https://picsum.photos/seed/rent1/240/200",
        hasVR: true,
        isNew: false,
        paymentMethod: "押一付一",
        utilities: "民水民电",
        rentType: "整租",
      },
      {
        id: "2",
        title: "整租1居 · 金域华府",
        community: "金域华府",
        layout: "1室1厅1卫",
        area: "55",
        floor: "中楼层",
        direction: "南",
        price: "3800",
        location: "望京",
        tags: ["近地铁", "精装修", "电梯房"],
        image: "https://picsum.photos/seed/rent2/240/200",
        hasVR: false,
        isNew: true,
        paymentMethod: "押一付三",
        utilities: "民水民电",
        rentType: "整租",
      },
      {
        id: "3",
        title: "合租3居 · 远洋天地",
        community: "远洋天地",
        layout: "3室2厅2卫",
        area: "20",
        floor: "低楼层",
        direction: "东",
        price: "2600",
        location: "酒仙桥",
        tags: ["独立卫生间", "拎包入住", "押一付一"],
        image: "https://picsum.photos/seed/rent3/240/200",
        hasVR: true,
        isNew: true,
        paymentMethod: "押一付一",
        utilities: "民水民电",
        rentType: "合租",
      },
      {
        id: "4",
        title: "整租2居 · 富力城",
        community: "富力城",
        layout: "2室1厅1卫",
        area: "85",
        floor: "高楼层",
        direction: "南北",
        price: "6800",
        location: "双井",
        tags: ["南北通透", "精装修", "近地铁"],
        image: "https://picsum.photos/seed/rent4/240/200",
        hasVR: false,
        isNew: false,
        paymentMethod: "押一付三",
        utilities: "民水民电",
        rentType: "整租",
      },
      {
        id: "5",
        title: "整租3居 · 世茂维拉",
        community: "世茂维拉",
        layout: "3室2厅2卫",
        area: "120",
        floor: "中楼层",
        direction: "南北",
        price: "9500",
        location: "亦庄",
        tags: ["南北通透", "精装修", "电梯房"],
        image: "https://picsum.photos/seed/rent5/240/200",
        hasVR: true,
        isNew: false,
        paymentMethod: "押一付三",
        utilities: "民水民电",
        rentType: "整租",
      },
      {
        id: "6",
        title: "整租2居 · 香山南营66号",
        community: "香山南营66号",
        layout: "2室1厅1卫",
        area: "70",
        floor: "高楼层",
        direction: "南北",
        price: "4600",
        location: "西山",
        tags: ["南北通透", "高楼层", "步梯"],
        image: "https://picsum.photos/seed/rent1/240/200",
        hasVR: true,
        isNew: false,
        paymentMethod: "押一付一",
        utilities: "民水民电",
        rentType: "整租",
      },
      {
        id: "7",
        title: "整租2居 · 香山南营66号",
        community: "香山南营66号",
        layout: "2室1厅1卫",
        area: "70",
        floor: "高楼层",
        direction: "南北",
        price: "4600",
        location: "西山",
        tags: ["南北通透", "高楼层", "步梯"],
        image: "https://picsum.photos/seed/rent1/240/200",
        hasVR: true,
        isNew: false,
        paymentMethod: "押一付一",
        utilities: "民水民电",
        rentType: "整租",
      },
      {
        id: "8",
        title: "整租2居 · 香山南营66号",
        community: "香山南营66号",
        layout: "2室1厅1卫",
        area: "70",
        floor: "高楼层",
        direction: "南北",
        price: "4600",
        location: "西山",
        tags: ["南北通透", "高楼层", "步梯"],
        image: "https://picsum.photos/seed/rent1/240/200",
        hasVR: true,
        isNew: false,
        paymentMethod: "押一付一",
        utilities: "民水民电",
        rentType: "整租",
      },
    ];

    // 根据筛选条件过滤数据
    let filteredData = [...mockData];

    // 根据功能导航过滤
    const functionType = functionNavs[activeFunctionIndex.value].type;
    if (functionType === "shared") {
      filteredData = filteredData.filter((house) => house.rentType === "合租");
    } else if (functionType === "whole") {
      filteredData = filteredData.filter((house) => house.rentType === "整租");
    }

    // 根据筛选参数过滤
    if (filterParams.area) {
      filteredData = filteredData.filter((house) =>
        house.location.includes(filterParams.area)
      );
    }

    if (filterParams.price) {
      const [min, max] = filterParams.price.split("-").map(Number);
      if (min && max) {
        filteredData = filteredData.filter((house) => {
          const price = Number(house.price);
          return price >= min && price <= max;
        });
      } else if (min) {
        filteredData = filteredData.filter(
          (house) => Number(house.price) >= min
        );
      } else if (max) {
        filteredData = filteredData.filter(
          (house) => Number(house.price) <= max
        );
      }
    }

    if (filterParams.layout) {
      filteredData = filteredData.filter((house) =>
        house.layout.startsWith(`${filterParams.layout}室`)
      );
    }

    if (filterParams.rentType) {
      filteredData = filteredData.filter(
        (house) => house.rentType === filterParams.rentType
      );
    }

    if (filterParams.more && filterParams.more.length > 0) {
      filteredData = filteredData.filter((house) => {
        return filterParams.more.some((tag) => {
          if (tag === "south-north") return house.direction === "南北";
          if (tag === "south") return house.direction === "南";
          if (tag === "east") return house.direction === "东";
          if (tag === "west") return house.direction === "西";
          if (tag === "north") return house.direction === "北";
          if (tag === "low") return house.floor.includes("低");
          if (tag === "middle") return house.floor.includes("中");
          if (tag === "high") return house.floor.includes("高");
          return house.tags.some((t) => t.includes(tag));
        });
      });
    }

    // 返回分页数据
    paging.value.complete(filteredData);
  }, 1000);
};

// 下拉刷新
const onRefresh = () => {
  if (paging.value) {
    paging.value.reload();
  }
};

// 加载更多 - 这个方法在z-paging中已自动处理
const loadMore = () => {
  // z-paging会自动处理加载更多逻辑
  console.log("加载更多");
};

// 格式化标题
const formatTitle = (house: RentHouse) => {
  const rentType = house.rentType || "整租";
  const rooms = house.layout.match(/(\d+)室/)
    ? house.layout.match(/(\d+)室/)![1]
    : "";
  return `${rentType}${rooms}居 · ${house.community}`;
};

// 格式化详情
const formatDetails = (house: RentHouse) => {
  return `${house.layout} | ${house.area}㎡ | ${house.direction} | ${house.floor}`;
};

// 处理筛选菜单打开事件
const handleFilterMenuOpened = () => {
  if (filterPanelRef.value) {
    const query = uni.createSelectorQuery();
    // @ts-ignore
    query
      .select(".filter-menu")
      .boundingClientRect((data) => {
        // @ts-ignore
        const rect = Array.isArray(data) ? data[0] : data;
        if (rect && rect.top !== 0) {
          uni.pageScrollTo({
            scrollTop: scrollTop.value + rect.top,
            duration: 300,
          });
        }
      })
      .exec();
  }
};

// 页面加载时初始化数据
onLoad(() => {
  // z-paging会自动在挂载后调用queryHouseList
});
const onPageScroll = (e) => {
  scrollTop.value = e.detail.scrollTop;
};


</script>

<style lang="scss" scoped>
.container {
  position: relative;
  background: linear-gradient(180deg, #f0e9dd 0%, #f7f8fa 400rpx), var(--bg-page);
  background-color: var(--bg-page);
  background-size: 100% 400rpx;
  background-repeat: no-repeat;
  min-height: 100vh;
  
  /* 顶部搜索栏 */
  .search-header {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background-color: transparent; // 确保搜索栏是透明的，显示背景渐变
  }
  
  // 给导航栏透明背景
  :deep(.uni-navbar) {
    background-color: transparent !important;
  }
  
  :deep(.uni-navbar__header) {
    background-color: transparent !important;
  }

  .search-box {
    flex: 1;
    display: flex;
    align-items: center;
    height: 72rpx;
    background-color: var(--bg-card);
    border-radius: 32rpx;
    padding: 0 24rpx;
    margin: 0 20rpx;
    transition: all 0.3s ease;

    &:active {
      background-color: #e9ecef;
    }
  }

  .search-placeholder {
    font-size: 28rpx;
    color: #999;
    margin-left: 12rpx;
  }

  // 筛选面板样式
  .filter-panel-wrapper {
    position: sticky;
    top: 0;
    z-index: 99;
  }

  .nav-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16rpx;
    padding: 24rpx 32rpx 16rpx;
    margin-top: 8rpx;
    border-radius: 16rpx 16rpx 0 0;
  }

  .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12rpx 0;
    position: relative;
    transition: all 0.3s ease;

    &.active {
      .nav-text {
        font-weight: 600;
      }
    }
  }

  .nav-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8rpx;
  }

  .nav-icon {
    font-size: 44rpx;
  }

  .nav-text {
    font-size: 24rpx;
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }

  .nav-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 24rpx;
    height: 4rpx;
    background-color: var(--primary);
    border-radius: 4rpx;
  }

  /* 房源列表 */
  .house-list {
    display: flex;
    flex-direction: column;
    padding: 20rpx;
    gap: 20rpx;
  }

  /* 房源卡片 */
  .house-card {
    display: flex;
    padding: 0 !important;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }
  }

  .image-container {
    position: relative;
    width: 240rpx;
    height: 240rpx;
    flex-shrink: 0;
  }

  .house-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-top-left-radius: 16rpx;
    border-bottom-left-radius: 16rpx;
  }

  .vr-tag {
    position: absolute;
    bottom: 12rpx;
    left: 12rpx;
    display: flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 4rpx 10rpx;
    border-radius: 4rpx;
    font-size: 20rpx;
  }

  .vr-text {
    font-size: 20rpx;
  }

  .new-tag {
    position: absolute;
    top: 12rpx;
    left: 12rpx;
    background-color: var(--primary);
    color: white;
    font-size: 20rpx;
    padding: 4rpx 12rpx;
    border-radius: 4rpx;
  }

  /* 房源信息 */
  .house-info {
    flex: 1;
    padding: 16rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .info-top {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10rpx;
  }

  .info-bottom {
    margin-top: 8rpx;
  }

  .house-title {
    font-size: 32rpx;
    font-weight: 500;
    line-height: 1.5;
  }

  .price-row {
    display: flex;
    align-items: baseline;
  }

  .price {
    font-size: 36rpx;
    font-weight: 500;
    color: var(--primary);
  }

  .price-unit {
    font-size: 26rpx;
    color: var(--primary);
    margin-left: 6rpx;
  }

  .details {
    font-size: 24rpx;
    color: var(--text-info);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500;
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4rpx;
    margin-top: 8rpx;
  }

  .tag {
    padding: 6rpx 12rpx;
    background-color: var(--bg-tag);
    color: var(--text-secondary);
    font-size: 24rpx;
    border-radius: 8rpx;
    line-height: 1.4;
  }

}
</style>

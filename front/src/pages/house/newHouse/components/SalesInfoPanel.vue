<template>
  <view class="sales-info-panel">
    <!-- 信息项列表 -->
    <view
      class="info-item"
      v-for="(item, index) in formattedInfoList"
      :key="index"
    >
      <view class="info-label text-info">{{ item.label }}</view>
      <view class="info-value" v-if="item.type === 'text'">
        <text>{{ item.value || "暂无数据" }}</text>
      </view>
      <view class="info-value info-link" v-else-if="item.type === 'layout'">
        <text>{{ item.value }}</text>
        <text class="link-more i-carbon-arrow-right"></text>
      </view>
      <view class="info-value info-address" v-else-if="item.type === 'address'">
        <text>{{ item.value }}</text>
        <text class="address-more i-carbon-arrow-right"></text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";

// 定义组件属性
interface Props {
  salesInfo: {
    status?: string; // 售卖状态
    buildings?: string; // 售卖楼栋
    layouts?: string; // 售卖户型
    openingDate?: string; // 最新开盘
    deliveryDate?: string; // 最近交房
    salesAddress?: string; // 售楼地址
    businessHours?: string; // 接待时间
    requireDeposit?: string; // 是否验资
  };
}

const props = defineProps<Props>();

// 格式化信息列表，用于展示
const formattedInfoList = computed(() => {
  const { salesInfo } = props;

  return [
    { label: "售卖状态", value: salesInfo.status, type: "text" },
    { label: "售卖楼栋", value: salesInfo.buildings, type: "text" },
    { label: "售卖户型", value: salesInfo.layouts, type: "layout" },
    { label: "最新开盘", value: salesInfo.openingDate, type: "text" },
    { label: "最近交房", value: salesInfo.deliveryDate, type: "text" },
    { label: "售楼地址", value: salesInfo.salesAddress, type: "address" },
    { label: "接待时间", value: salesInfo.businessHours, type: "text" },
    { label: "是否验资", value: salesInfo.requireDeposit, type: "text" },
  ].filter((item) => item.value !== undefined && item.value !== null);
});
</script>

<style lang="scss" scoped>
.sales-info-panel {
  .info-item {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    margin: 14rpx 0;

    .info-label {
      width: 160rpx;
      flex-shrink: 0;
      padding-right: 24rpx;
      line-height: 1.5;
    }

    .info-value {
      flex: 1;
      text-align: left;
      line-height: 1.5;

      &.info-link,
      &.info-address {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .link-text,
        .address-text {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .link-more,
        .address-more {
          color: #999;
          font-size: 24rpx;
          margin-left: 12rpx;
        }
      }
    }
  }
}
</style>

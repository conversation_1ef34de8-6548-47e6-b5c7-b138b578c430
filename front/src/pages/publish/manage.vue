<template>
  <view class="manage-page">
    <!-- 自定义导航栏 -->
    <uni-nav-bar
      :border="false"
      fixed
      background-color="transparent"
      :status-bar="true"
      title="我的发布"
      left-icon="back"
      @clickLeft="goBack"
    />

    <view class="content">
      <!-- 分类标签 -->
      <view class="category-tabs">
        <view 
          v-for="(category, index) in categories" 
          :key="category.type"
          class="tab-item"
          :class="{ active: activeCategory === category.type }"
          @tap="switchCategory(category.type)"
        >
          {{ category.name }}
        </view>
      </view>

      <!-- 房产管理 -->
      <view v-if="activeCategory === 'house'" class="category-content">
        <view class="manage-grid">
          <view 
            v-for="item in houseManageTypes" 
            :key="item.type"
            class="manage-item"
            @tap="navigateToManage(item.path)"
          >
            <view class="item-icon">
              <text :class="item.icon" class="text-48rpx"></text>
            </view>
            <text class="item-name">{{ item.name }}</text>
            <view v-if="item.count > 0" class="item-count">{{ item.count }}</view>
          </view>
        </view>

        <!-- 空状态插图 -->
        <view v-if="!hasHouseData" class="empty-state">
          <image src="/static/images/empty-house.png" class="empty-image" mode="aspectFit" />
          <view class="empty-content">
            <text class="empty-title">暂无房产发布</text>
            <text class="empty-desc">快去发布您的第一个房源吧</text>
            <view class="empty-action">
              <tui-button 
                type="primary"
                width="240rpx"
                height="72rpx"
                shape="circle"
                @click="goToPublish"
              >
                进入商铺管理
              </tui-button>
            </view>
          </view>
        </view>
      </view>

      <!-- 招聘管理 -->
      <view v-if="activeCategory === 'job'" class="category-content">
        <view class="manage-grid single-item">
          <view 
            class="manage-item"
            @tap="navigateToManage('/pages/job/manage')"
          >
            <view class="item-icon">
              <text class="i-carbon-user-multiple text-48rpx"></text>
            </view>
            <text class="item-name">职位管理</text>
            <view v-if="jobCount > 0" class="item-count">{{ jobCount }}</view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="jobCount === 0" class="empty-state">
          <image src="/static/images/empty-job.png" class="empty-image" mode="aspectFit" />
          <view class="empty-content">
            <text class="empty-title">暂无职位发布</text>
            <text class="empty-desc">发布职位信息，寻找合适人才</text>
            <view class="empty-action">
              <tui-button 
                type="primary"
                width="240rpx"
                height="72rpx"
                shape="circle"
                @click="publishJob"
              >
                发布职位
              </tui-button>
            </view>
          </view>
        </view>
      </view>

      <!-- 本地服务管理 -->
      <view v-if="activeCategory === 'service'" class="category-content">
        <view class="manage-grid">
          <view 
            v-for="item in serviceManageTypes" 
            :key="item.type"
            class="manage-item"
            @tap="navigateToManage(item.path)"
          >
            <view class="item-icon">
              <text :class="item.icon" class="text-48rpx"></text>
            </view>
            <text class="item-name">{{ item.name }}</text>
            <view v-if="item.count > 0" class="item-count">{{ item.count }}</view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="!hasServiceData" class="empty-state">
          <image src="/static/images/empty-service.png" class="empty-image" mode="aspectFit" />
          <view class="empty-content">
            <text class="empty-title">暂无服务发布</text>
            <text class="empty-desc">发布服务信息，扩展业务范围</text>
            <view class="empty-action">
              <tui-button 
                type="primary"
                width="240rpx"
                height="72rpx"
                shape="circle"
                @click="publishService"
              >
                发布服务
              </tui-button>
            </view>
          </view>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="stats-section">
        <view class="stats-title">发布统计</view>
        <view class="stats-grid">
          <view class="stats-item">
            <text class="stats-number">{{ totalPublished }}</text>
            <text class="stats-label">总发布</text>
          </view>
          <view class="stats-item">
            <text class="stats-number">{{ totalViews }}</text>
            <text class="stats-label">总浏览</text>
          </view>
          <view class="stats-item">
            <text class="stats-number">{{ totalContacts }}</text>
            <text class="stats-label">总咨询</text>
          </view>
        </view>
      </view>

      <!-- 快捷操作 -->
      <view class="quick-actions">
        <view class="action-title">快捷操作</view>
        <view class="action-list">
          <view class="action-item" @tap="refreshData">
            <view class="action-icon">
              <text class="i-carbon-refresh text-32rpx"></text>
            </view>
            <text class="action-text">刷新数据</text>
          </view>
          <view class="action-item" @tap="viewAnalytics">
            <view class="action-icon">
              <text class="i-carbon-analytics text-32rpx"></text>
            </view>
            <text class="action-text">数据分析</text>
          </view>
          <view class="action-item" @tap="contactSupport">
            <view class="action-icon">
              <text class="i-carbon-help text-32rpx"></text>
            </view>
            <text class="action-text">客服咨询</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// 分类数据
const categories = [
  { type: 'house', name: '房产' },
  { type: 'job', name: '招聘' },
  { type: 'service', name: '本地服务' }
];

// 当前激活的分类
const activeCategory = ref('house');

// 房产管理类型
const houseManageTypes = ref([
  {
    type: 'rent',
    name: '租房管理',
    icon: 'i-carbon-home',
    path: '/pages/house/manage/rent',
    count: 3
  },
  {
    type: 'second-hand',
    name: '卖房管理',
    icon: 'i-carbon-building',
    path: '/pages/house/manage/second-hand',
    count: 1
  },
  {
    type: 'commercial',
    name: '商铺管理',
    icon: 'i-carbon-building-insights',
    path: '/pages/house/manage/commercial',
    count: 0
  }
]);

// 本地服务管理类型
const serviceManageTypes = ref([
  {
    type: 'repair',
    name: '维修服务',
    icon: 'i-carbon-tools',
    path: '/pages/service/manage/repair',
    count: 2
  },
  {
    type: 'clean',
    name: '保洁服务',
    icon: 'i-carbon-clean',
    path: '/pages/service/manage/clean',
    count: 1
  },
  {
    type: 'decoration',
    name: '装修服务',
    icon: 'i-carbon-paint-brush',
    path: '/pages/service/manage/decoration',
    count: 0
  },
  {
    type: 'transfer',
    name: '生意转让',
    icon: 'i-carbon-partnership',
    path: '/pages/service/manage/transfer',
    count: 0
  }
]);

// 招聘统计
const jobCount = ref(2);

// 统计数据
const totalPublished = ref(9);
const totalViews = ref(1248);
const totalContacts = ref(36);

// 计算属性
const hasHouseData = computed(() => {
  return houseManageTypes.value.some(item => item.count > 0);
});

const hasServiceData = computed(() => {
  return serviceManageTypes.value.some(item => item.count > 0);
});

// 切换分类
const switchCategory = (type: string) => {
  activeCategory.value = type;
};

// 导航到管理页面
const navigateToManage = (path: string) => {
  uni.navigateTo({
    url: path,
    fail: () => {
      uni.showToast({
        title: '该功能开发中',
        icon: 'none'
      });
    }
  });
};

// 前往发布页面
const goToPublish = () => {
  uni.navigateTo({
    url: '/pages/publish/index'
  });
};

// 发布职位
const publishJob = () => {
  uni.navigateTo({
    url: '/pages/job/publish'
  });
};

// 发布服务
const publishService = () => {
  uni.navigateTo({
    url: '/pages/publish/index'
  });
};

// 刷新数据
const refreshData = () => {
  uni.showLoading({ title: '刷新中...' });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    });
    
    // TODO: 重新加载数据
  }, 1500);
};

// 查看分析
const viewAnalytics = () => {
  uni.showToast({
    title: '数据分析功能开发中',
    icon: 'none'
  });
};

// 联系客服
const contactSupport = () => {
  uni.showToast({
    title: '客服功能开发中',
    icon: 'none'
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.manage-page {
  min-height: 100vh;
  background: #f8f9fa;
}

.content {
  padding: 120rpx 32rpx 40rpx;
}

.category-tabs {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.2s;
  
  &.active {
    background: #ff6d00;
    color: white;
    font-weight: 600;
  }
}

.category-content {
  margin-bottom: 40rpx;
}

.manage-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  
  &.single-item {
    grid-template-columns: 1fr;
    max-width: 300rpx;
    margin: 0 auto;
  }
}

.manage-item {
  position: relative;
  background: white;
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  &:active {
    transform: translateY(-4rpx);
    box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  }
}

.item-icon {
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6d00, #ff9500);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  color: white;
}

.item-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  font-weight: 500;
}

.item-count {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  font-weight: 600;
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin: 0 auto 40rpx;
  opacity: 0.6;
}

.empty-content {
  .empty-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 600;
    display: block;
    margin-bottom: 12rpx;
  }
  
  .empty-desc {
    font-size: 26rpx;
    color: #999;
    display: block;
    margin-bottom: 32rpx;
  }
}

.stats-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 24rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.stats-item {
  text-align: center;
  padding: 24rpx;
  background: #f8f9ff;
  border-radius: 16rpx;
}

.stats-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #ff6d00;
  display: block;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.quick-actions {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.action-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 24rpx;
}

.action-list {
  display: flex;
  justify-content: space-around;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 16rpx;
  transition: all 0.2s;
  
  &:active {
    background: #f5f5f5;
    transform: scale(0.95);
  }
}

.action-icon {
  width: 72rpx;
  height: 72rpx;
  background: #f0f2f5;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  color: #666;
}

.action-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}
</style> 
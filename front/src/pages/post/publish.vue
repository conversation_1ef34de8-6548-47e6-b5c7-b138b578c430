<template>
  <view class="publish-container">
    <view class="content-container">
      <!-- 输入区域 -->
      <view class="input-area card">
        <textarea
          class="content-input"
          v-model="postContent"
          placeholder="分享你的新鲜事..."
          maxlength="2000"
          :show-confirm-bar="false"
          :auto-height="true"
        ></textarea>
      </view>

      <!-- 已选话题 -->
      <view class="selected-topics-area card" v-if="selectedTopics.length > 0">
        <view
          v-for="(topic, index) in selectedTopics"
          :key="index"
          class="topic-tag"
        >
          <text>#{{ topic.name }}</text>
          <text class="i-carbon-close" @tap.stop="removeTopic(index)"></text>
        </view>
      </view>

      <!-- 图片/视频展示区 -->
      <view class="media-container card" v-if="images.length > 0 || videoPath">
        <!-- 图片展示 -->
        <view class="image-grid" v-if="images.length > 0 && !videoPath">
          <view
            class="image-item"
            v-for="(image, index) in images"
            :key="index"
          >
            <image :src="image" class="preview-image" mode="aspectFill"></image>
            <view class="delete-icon" @tap.stop="removeImage(index)">
              <text class="i-carbon-close-filled"></text>
            </view>
          </view>
          <view
            class="add-image-btn"
            v-if="images.length < 9"
            @tap="chooseImage"
          >
            <text class="i-carbon-add"></text>
          </view>
        </view>

        <!-- 视频展示 -->
        <view class="video-container" v-if="videoPath">
          <video
            :src="videoPath"
            class="preview-video"
            :poster="videoCover"
            object-fit="cover"
          ></video>
          <view class="video-delete-icon" @tap="removeVideo">
            <text class="i-carbon-close-filled"></text>
          </view>
        </view>
      </view>

      <!-- 位置信息 -->
      <view
        class="location-container card"
        v-if="location"
        @tap="chooseLocation"
      >
        <view class="location-info">
          <text class="i-carbon-location-current text-primary"></text>
          <text class="location-text">{{ location }}</text>
        </view>
        <text
          class="i-carbon-close-outline text-grey"
          @tap.stop="removeLocation"
        ></text>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="footer-bar">
      <view class="function-bar">
        <view class="function-item" @tap="chooseImage">
          <text class="i-carbon-image-copy text-36rpx"></text>
        </view>
        <view class="function-item" @tap="chooseVideo">
          <text class="i-carbon-video text-36rpx"></text>
        </view>
        <view class="function-item" @tap="chooseLocation">
          <text class="i-carbon-location text-36rpx"></text>
        </view>
        <view class="function-item" @tap="openTopicSelector">
          <text class="i-carbon-hashtag text-36rpx"></text>
        </view>
      </view>

      <view class="publish-btn-container">
        <view
          class="publish-btn"
          :class="{ disabled: !canSubmit }"
          @tap="submitPost"
        >
          <text>发布</text>
        </view>
      </view>
    </view>

    <!-- 话题选择器 -->
    <topic-selector
      ref="topicSelectorRef"
      @confirm="onTopicsConfirm"
    ></topic-selector>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import TopicSelector from "@/components/post/TopicSelector.vue";

// 动态内容
const postContent = ref("");

// 图片列表
const images = ref<string[]>([]);

// 视频路径
const videoPath = ref("");

// 视频封面
const videoCover = ref("");

// 位置信息
const location = ref("");

// 话题列表
const selectedTopics = ref<{ id: number; name: string }[]>([]);

// 话题选择器引用
const topicSelectorRef = ref();

const canSubmit = computed(() => {
  return (
    postContent.value.trim().length > 0 ||
    images.value.length > 0 ||
    videoPath.value
  );
});

// 选择图片
const chooseImage = () => {
  if (videoPath.value) {
    uni.showToast({
      title: "已添加视频，不能同时添加图片",
      icon: "none",
    });
    return;
  }

  uni.chooseImage({
    count: 9 - images.value.length,
    sizeType: ["compressed"],
    sourceType: ["album", "camera"],
    success: (res) => {
      const selectedImages = res.tempFilePaths as string[];
      images.value = [...images.value, ...selectedImages];
    },
  });
};

// 移除图片
const removeImage = (index: number) => {
  images.value.splice(index, 1);
};

// 选择视频
const chooseVideo = () => {
  if (images.value.length > 0) {
    uni.showToast({
      title: "已添加图片，不能同时添加视频",
      icon: "none",
    });
    return;
  }

  uni.chooseVideo({
    sourceType: ["album", "camera"],
    maxDuration: 60,
    camera: "back",
    compressed: true,
    success: (res) => {
      videoPath.value = res.tempFilePath;
      // 生成视频封面
      videoCover.value = "";
      // 这里应该调用接口生成视频封面，但这里简化处理
      setTimeout(() => {
        videoCover.value = "https://picsum.photos/seed/video/400/300";
      }, 1000);
    },
  });
};

// 移除视频
const removeVideo = () => {
  videoPath.value = "";
  videoCover.value = "";
};

// 选择位置
const chooseLocation = () => {
  uni.chooseLocation({
    success: (res) => {
      location.value = res.name || res.address;
    },
    fail: () => {
      uni.showToast({
        title: "位置获取失败",
        icon: "none",
      });
    },
  });
};

// 移除位置
const removeLocation = () => {
  location.value = "";
};

// 打开话题选择器
const openTopicSelector = () => {
  if (topicSelectorRef.value) {
    topicSelectorRef.value.open(selectedTopics.value);
  }
};

// 确认选择话题
const onTopicsConfirm = (topics: { id: number; name: string }[]) => {
  selectedTopics.value = topics;
};

// 移除话题
const removeTopic = (index: number) => {
  selectedTopics.value.splice(index, 1);
};

// 选择话题
const chooseTag = () => {
  uni.showToast({
    title: "话题功能开发中",
    icon: "none",
  });
};

// 提交动态
const submitPost = () => {
  if (
    !postContent.value.trim() &&
    images.value.length === 0 &&
    !videoPath.value
  ) {
    uni.showToast({
      title: "请输入内容或添加图片/视频",
      icon: "none",
    });
    return;
  }

  // 提交前显示loading
  uni.showLoading({
    title: "发布中...",
  });

  // 模拟提交
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: "发布成功",
      icon: "success",
      duration: 2000,
      success: () => {
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      },
    });
  }, 2000);

  // 实际项目中应该上传图片和视频到服务器，然后提交动态内容
  // 这里省略上传逻辑
};
</script>

<style lang="scss" scoped>
.publish-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
}

.content-container {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 220rpx; /* 为底部操作栏留出空间 */
}

.card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
}

.input-area .content-input {
  width: 100%;
  min-height: 250rpx;
  font-size: 30rpx;
  line-height: 1.6;
}

.media-container {
  padding: 12rpx;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.image-item,
.add-image-btn {
  width: 100%;
  aspect-ratio: 1 / 1;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.delete-icon,
.video-delete-icon {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  font-size: 24rpx;
}

.add-image-btn {
  border: 2rpx dashed #dcdfe6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 50rpx;
  color: #dcdfe6;
}

.video-container {
  width: 60%;
  aspect-ratio: 16 / 9;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
}

.preview-video {
  width: 100%;
  height: 100%;
}

.selected-topics-area {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.topic-tag {
  display: flex;
  align-items: center;
  background-color: #eef3ff;
  color: #409eff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;

  .i-carbon-close {
    font-size: 24rpx;
    margin-left: 8rpx;
  }
}

.location-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.location-info {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.location-text {
  margin-left: 10rpx;
}

.footer-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-top: 1rpx solid #f0f0f0;
}

.function-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.function-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  color: var(--text-secondary);
}

.publish-btn-container {
  width: 100%;
}

.publish-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 88rpx;
  background: var(--primary);
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: background-color 0.3s;
}

.publish-btn.disabled {
  background-color: #fabda1;
  color: #fff;
}
</style>

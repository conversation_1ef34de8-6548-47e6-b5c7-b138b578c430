<template>
  <view class="my-posts-page">
    <CustomNavBar title="我的发布" />
    <view class="tabs-container">
      <tui-tabs
        :tabs="tabs"
        :currentTab="currentTab"
        item-width="33.33%"
        :slider-width="120"
        :slider-height="6"
        slider-bg-color="var(--primary)"
        selected-color="var(--primary)"
        color="var(--text-secondary)"
        @change="handleTabChange"
      ></tui-tabs>
    </view>

    <view class="content-wrapper">
      <view v-if="currentTab === 0" class="filter-section">
        <view
          v-for="(filter, index) in houseFilters"
          :key="filter.value"
          class="filter-chip"
          :class="{ active: activeHouseFilter === filter.value }"
          @click="handleHouseFilterChange(filter.value)"
        >
          {{ filter.label }}
        </view>
      </view>

      <view class="posts-list">
        <!-- 这里可以根据 currentTab 和 activeHouseFilter 来渲染不同的列表 -->
        <!-- 现在暂时都显示空状态 -->
        <EmptyResult
          message="您还没有发布任何内容"
          sub-message="去发布您的第一条信息吧"
          icon="i-carbon-add-comment"
          action-text="去发布"
          @action="goToPublish"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import CustomNavBar from '@/components/CustomNavBar.vue'
import EmptyResult from '@/components/common/EmptyResult.vue'

// 主分类Tabs
const tabs = [{ name: '房产' }, { name: '招聘' }, { name: '本地服务' }]
const currentTab = ref(0)

const handleTabChange = (e: any) => {
  currentTab.value = e.index
}

// 房产次级筛选
const houseFilters = [
  { label: '租房管理', value: 'rent' },
  { label: '卖房管理', value: 'sell' },
  { label: '商铺管理', value: 'shop' },
]
const activeHouseFilter = ref('rent')

const handleHouseFilterChange = (value: string) => {
  activeHouseFilter.value = value
}

const goToPublish = () => {
  uni.navigateTo({
    url: '/pages/publish/index',
  })
}
</script>

<style lang="scss" scoped>
.my-posts-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.tabs-container {
  background-color: var(--bg-card);
  border-bottom: 1rpx solid var(--border-color);
}

.content-wrapper {
  padding: 0 32rpx;
}

.filter-section {
  display: flex;
  gap: 24rpx;
  padding: 24rpx 0;

  .filter-chip {
    padding: 12rpx 28rpx;
    font-size: 26rpx;
    border-radius: 100rpx;
    background-color: var(--bg-tag);
    color: var(--text-secondary);
    transition: all 0.2s ease;

    &.active {
      background-color: var(--primary);
      color: #fff;
      font-weight: 500;
    }
  }
}

.posts-list {
  padding-top: 80rpx;
}
</style> 
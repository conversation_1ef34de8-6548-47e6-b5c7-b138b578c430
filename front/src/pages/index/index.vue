<template>
  <PageLayout title="首页" bg-color="#ffffff">
    <view class="home-container">
      <!-- 轮播图 -->
      <swiper
        class="carousel"
        circular
        autoplay
        :interval="3000"
        :duration="500"
      >
        <swiper-item v-for="item in swiperList" :key="item.id">
          <image class="carousel-image" :src="item.imgUrl" mode="aspectFill" />
        </swiper-item>
      </swiper>
    </view>
  </PageLayout>
</template>

<script setup lang="ts">
import { ref } from "vue";
import PageLayout from "@/components/common/PageLayout.vue";

// 模拟轮播图数据
const swiperList = ref([
  { id: 1, imgUrl: "/static/images/home/<USER>" },
  { id: 2, imgUrl: "/static/images/home/<USER>" },
]);
</script>

<style scoped>
.home-container {
  /* 在这里定义首页的样式 */
}
.carousel {
  width: 100%;
  height: 350rpx;
}
.carousel-image {
  width: 100%;
  height: 100%;
}
</style>

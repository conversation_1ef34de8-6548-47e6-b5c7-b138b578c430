<template>
  <view
    class="voice-recorder"
    @touchstart="startRecord"
    @touchend="endRecord"
    @touchcancel="cancelRecord"
    @touchmove="moveRecord"
  >
    <text class="record-text">{{ recordText }}</text>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 组件事件
const emit = defineEmits<{
  (e: 'start'): void
  (e: 'finish', duration: number): void
  (e: 'cancel'): void
  (e: 'update-status', isCanceled: boolean): void
}>()

// 状态
const isRecording = ref(false)
const isCancelMode = ref(false)
const recordStartY = ref(0)
const recordDuration = ref(0)
const recordTimer = ref<number | null>(null)

// 计算录音按钮文本
const recordText = computed(() => {
  if (isRecording.value) {
    return '松开 结束'
  }
  return '按住 说话'
})

// 开始录音
const startRecord = (event: TouchEvent) => {
  isRecording.value = true
  isCancelMode.value = false
  recordStartY.value = event.touches[0].clientY
  recordDuration.value = 0

  // 开始录音时添加轻微震动反馈
  uni.vibrateShort({
    success: () => {
      console.log('开始录音震动成功')
    },
  })

  // 开始计时
  recordTimer.value = setInterval(() => {
    recordDuration.value++
  }, 1000) as unknown as number

  emit('start')
}

// 结束录音
const endRecord = () => {
  if (!isRecording.value) return

  clearInterval(recordTimer.value as number)

  // 结束录音时添加轻微震动反馈
  uni.vibrateShort({
    success: () => {
      console.log('结束录音震动成功')
    },
  })

  if (isCancelMode.value) {
    cancelRecord()
    return
  }

  isRecording.value = false
  emit('finish', recordDuration.value)
}

// 取消录音
const cancelRecord = () => {
  if (!isRecording.value) return

  clearInterval(recordTimer.value as number)
  isRecording.value = false
  isCancelMode.value = false
  emit('cancel')
}

// 手指移动时判断是否取消
const moveRecord = (event: TouchEvent) => {
  if (!isRecording.value) return

  const currentY = event.touches[0].clientY
  const moveDistance = recordStartY.value - currentY

  // 检测是否改变了取消状态
  const wasInCancelMode = isCancelMode.value

  // 上滑超过50像素进入取消模式
  if (moveDistance > 50) {
    isCancelMode.value = true
  } else {
    isCancelMode.value = false
  }

  // 状态改变时添加震动反馈
  if (wasInCancelMode !== isCancelMode.value) {
    uni.vibrateShort({
      success: () => {
        console.log('取消状态改变震动成功')
      },
    })
  }

  // 通知父组件状态更新
  emit('update-status', isCancelMode.value)
}

// 格式化时长
const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}
</script>

<style lang="scss" scoped>
.voice-recorder {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.record-text {
  font-size: 30rpx;
  font-weight: 500;
  color: #666;
}
</style>

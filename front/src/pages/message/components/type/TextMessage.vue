<template>
  <view class="text-message" :class="{ self }" @longpress="onLongPress">
    <text class="text-content">{{ trText(content) }}</text>
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{
  content: string;
  self?: boolean; // 是否为自己发送的消息
}>();

const emit = defineEmits<{
  (e: "copy", content: string): void;
}>();

const trText = (str: string) => {
  // TODO：临时方案，解决 \n 被转义的问题
  return str.replace(/\\n/g, "\\\\n");
};

// 长按复制文本
const onLongPress = () => {
  uni.showActionSheet({
    itemList: ["复制"],
    success: (res) => {
      if (res.tapIndex === 0) {
        uni.setClipboardData({
          data: props.content,
          success: () => {
            uni.showToast({
              title: "复制成功",
              icon: "none",
            });
            emit("copy", props.content);
          },
        });
      }
    },
  });
};
</script>

<style lang="scss" scoped>
.text-message {
  position: relative;
  min-width: 60px;

  .text-content {
    display: inline-block;
    font-size: 32rpx;
    line-height: 1.5;
    word-wrap: break-word;
    letter-spacing: 0.2rpx;
  }

  &.self {
    .text-content {
      // color: #ffffff; // 保持文本颜色不变，或根据设计调整
    }
  }
}
</style>

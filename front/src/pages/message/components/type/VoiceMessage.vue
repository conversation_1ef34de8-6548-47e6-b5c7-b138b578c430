<template>
  <view class="voice-message" :class="{ self: self }" @click="playVoice">
    <view class="voice-wrapper" :class="{ playing: isPlaying }">
      <view class="voice-icon-box">
        <image
          v-if="isPlaying"
          :src="sounding"
          style="width: 36rpx; height: 36rpx"
          mode="widthFix"
        />
        <!-- <uni-icons
          :style="{ transform: self ? 'rotate(180deg)' : 'rotate(0deg)' }"
          v-else
          type="sound"
          size="22"
          :color="self ? '#ffffff' : '#666666'"
        /> -->

        <image
          :style="{ transform: self ? 'rotate(180deg)' : 'rotate(0deg)' }"
          v-else
          class="voice-icon"
          src="/src/static/images/chat/voice.png"
          mode="aspectFill"
        />
      </view>
      <view class="duration">{{ duration }}"</view>
      <!-- <view v-if="self" class="voice-icon-box">
        <image
          v-if="isPlaying"
          :src="sounding"
          style="width: 36rpx; height: 36rpx"
          mode="widthFix"
        />
        <uni-icons v-else type="sound" size="22" color="#ffffff" />
      </view> -->
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onUnmounted, computed } from "vue";
import sounding from "@/static/images/sound-ing.gif";

const props = defineProps<{
  src: string;
  duration: number;
  self?: boolean; // 是否为自己发送的消息
}>();

const self = computed(() => props.self !== false);
const isPlaying = ref(false);
let innerAudioContext: UniApp.InnerAudioContext | null = null;

// 播放语音
const playVoice = () => {
  if (isPlaying.value) {
    stopVoice();
    return;
  }

  // 先停止所有正在播放的音频
  if (window.currentAudioContext) {
    window.currentAudioContext.stop();
    window.currentAudioContext = null;
  }

  // 创建音频上下文
  innerAudioContext = uni.createInnerAudioContext();
  innerAudioContext.src = props.src;
  innerAudioContext.autoplay = true;

  // 保存当前播放的语音到全局变量
  window.currentAudioContext = innerAudioContext;

  isPlaying.value = true;

  // 语音播放完成事件
  innerAudioContext.onEnded(() => {
    handlePlayEnd();
  });

  // 语音播放错误事件
  innerAudioContext.onError(() => {
    uni.showToast({
      title: "语音播放失败",
      icon: "none",
    });
    handlePlayEnd();
  });

  // 添加其他可能的事件处理
  innerAudioContext.onStop(() => {
    handlePlayEnd();
  });

  innerAudioContext.onPause(() => {
    handlePlayEnd();
  });
};

// 停止播放
const stopVoice = () => {
  if (innerAudioContext) {
    innerAudioContext.stop();
    innerAudioContext = null;
  }
  isPlaying.value = false;
};

// 处理播放结束
const handlePlayEnd = () => {
  isPlaying.value = false;
  if (innerAudioContext) {
    innerAudioContext = null;
  }
  if (window.currentAudioContext === innerAudioContext) {
    window.currentAudioContext = null;
  }
};

// 组件销毁前停止播放，防止内存泄漏
onUnmounted(() => {
  stopVoice();
});

// 声明全局变量类型，确保TypeScript能识别
declare global {
  interface Window {
    currentAudioContext: UniApp.InnerAudioContext | null;
  }
}

// 确保全局变量存在
if (typeof window !== "undefined" && !window.currentAudioContext) {
  window.currentAudioContext = null;
}
</script>

<style lang="scss" scoped>
.voice-message {
  align-self: flex-start;
  position: relative; // 移除气泡尾巴相关定位
  margin-right: 0; // 移除气泡尾巴相关间距

  &.self {
    align-self: flex-end;
    margin-left: 0; // 移除气泡尾巴相关间距
    margin-right: 0; // 覆盖接收方样式
  }

  .voice-wrapper {
    display: flex;
    align-items: center;
    min-width: 200rpx;
    max-width: 480rpx;
    padding: 24rpx;
    background-color: #ffffff; // 接收方气泡背景色
    border-radius: 24rpx 24rpx 24rpx 8rpx; // 接收方气泡圆角
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    border: 1rpx solid rgba(0, 0, 0, 0.04);
    transition: all 0.2s ease;

    &.playing {
      background-color: #f8fafc;
      transform: scale(1.02);
    }
  }

  .voice-icon-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36rpx;
    height: 36rpx;
    margin-right: 20rpx;

    .voice-icon {
      width: 36rpx;
      height: 36rpx;
      opacity: 0.8;
    }
  }

  .duration {
    font-size: 28rpx;
    font-weight: 500;
    letter-spacing: 0.2rpx;
  }

  // 自己发送的语音消息样式
  &.self .voice-wrapper {
    flex-direction: row-reverse;
    background: linear-gradient(
      135deg,
      var(--primary-50) 0%,
      var(--primary-100) 100%
    );
    border-radius: 20rpx 20rpx 8rpx 20rpx; // 发送方气泡圆角

    .voice-icon-box {
      margin-right: 0;
      margin-left: 20rpx;
    }

    .duration {
      // color: #ffffff;
    }

    &.playing {
      // background: linear-gradient(135deg, #0066cc 0%, #4a47a3 100%);
    }
  }
}
</style>

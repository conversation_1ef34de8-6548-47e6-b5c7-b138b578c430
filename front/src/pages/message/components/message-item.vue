<template>
  <view class="bubble-box" :class="{ self }">
    <!-- 发送者头像容器 -->
    <view class="avatar-container">
      <image class="avatar" :src="avatarUrl" mode="aspectFill" />
    </view>

    <view class="message-content">
      <!-- 文本消息 -->
      <text-message
        v-if="message.type === 'text'"
        :content="message.content"
        :self="message.from === 'self'"
        @copy="handleCopy"
      />

      <!-- 图片消息 -->
      <image-message
        v-else-if="message.type === 'image'"
        :src="message.content"
        :width="message.ext?.width"
        :height="message.ext?.height"
        :self="message.from === 'self'"
        @preview="handlePreviewImage"
      />

      <!-- 语音消息 -->
      <voice-message
        v-else-if="message.type === 'voice'"
        :src="message.content"
        :duration="message.ext?.duration || 0"
        :self="message.from === 'self'"
        @play="handlePlayVoice"
      />

      <!-- 视频消息 -->
      <video-message
        v-else-if="message.type === 'video'"
        :src="message.content"
        :poster="message.ext?.thumbnail"
        :width="message.ext?.width"
        :height="message.ext?.height"
        :duration="message.ext?.duration"
      />

      <!-- 位置消息 -->
      <location-message
        v-else-if="message.type === 'location'"
        :name="message.content.name"
        :address="message.content.address"
        :latitude="message.content.latitude"
        :longitude="message.content.longitude"
        :imageUrl="message.content.image"
        @open="handleOpenLocation"
      />

      <!-- 系统消息 -->
      <view v-else-if="message.type === 'system'" class="system-message">
        <text>{{ message.content }}</text>
      </view>

      <!-- 未知消息类型 -->
      <!-- <view v-else class="unknown-message">
        <text>{{ message.content }}</text>
      </view> -->

      <!-- 消息状态 -->
      <view v-if="message.from === 'self'" class="message-status">
        <text v-if="message.status === 'sending'" class="status-sending"
          >发送中...</text
        >
        <view
          v-else-if="message.status === 'failed'"
          class="status-failed"
          @click="handleResend"
        >
          <uni-icons type="warn" size="16" color="#ff5151" />
        </view>
        <text
          v-else-if="message.status === 'sent' && !message.isRead"
          class="status-sent"
        >
          已发送
        </text>
        <text
          v-else-if="message.status === 'sent' && message.isRead"
          class="status-read"
        >
          已读
        </text>
      </view>
    </view>

    <!-- 当前用户头像容器 -->
    <!-- <view class="avatar-container">
      <image v-if="message.from === 'self'" class="avatar" :src="selfAvatar" mode="aspectFill" />
    </view> -->
  </view>
</template>

<script setup lang="ts">
import TextMessage from "./type/TextMessage.vue";
import ImageMessage from "./type/ImageMessage.vue";
import VoiceMessage from "./type/VoiceMessage.vue";
import VideoMessage from "./type/VideoMessage.vue";
import LocationMessage from "./type/LocationMessage.vue";
import type { ChatMsg } from "@/types/chatMsg";

// 组件属性
const props = defineProps<{
  message: ChatMsg.Message;
  otherAvatar: string;
  selfAvatar: string;
}>();

console.log(props.message);

// 组件事件
const emit = defineEmits<{
  (e: "previewImage", url: string): void;
  (e: "playVoice", message: ChatMsg.VoiceMessage): void;
  (e: "openLocation", location: ChatMsg.LocationMessage["content"]): void;
  (e: "resend", message: ChatMsg.Message): void;
  (e: "copy", content: string): void;
}>();

const self = computed(() => {
  return props.message.from === "self";
});

const avatarUrl = computed(() => {
  return props.message.from === "self" ? props.selfAvatar : props.otherAvatar;
});

// 处理图片预览
const handlePreviewImage = (url: string) => {
  emit("previewImage", url);
};

// 处理播放语音
const handlePlayVoice = () => {
  if (props.message.type === "voice") {
    emit("playVoice", props.message);
  }
};

// 处理打开位置
const handleOpenLocation = () => {
  if (props.message.type === "location") {
    emit("openLocation", props.message.content);
  }
};

// 处理重新发送
const handleResend = () => {
  emit("resend", props.message);
};

// 处理复制文本
const handleCopy = (content: string) => {
  emit("copy", content);
};
</script>

<style lang="scss" scoped>
.bubble-box {
  display: flex;
  align-items: flex-start;
  padding: 0 24rpx;
  margin-bottom: 32rpx; // 增加消息间距

  .avatar-container {
    flex: 0 0 88rpx; /* 固定宽度 */
    width: 88rpx;
    height: 88rpx;
    margin-right: 20rpx; // 头像与气泡的间距
  }

  .avatar {
    display: block;
    width: 88rpx;
    height: 88rpx;
    border-radius: 50%;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  }

  .message-content {
    position: relative; // 用于气泡尾巴定位
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    max-width: calc(100% - 200rpx); // 限制气泡最大宽度
    padding: 20rpx 24rpx; // 气泡内边距
    border-radius: 16rpx; // 气泡圆角
    word-break: break-word; // 文本自动换行
    line-height: 1.5; // 优化行高
    font-size: 30rpx; // 消息字体大小

    // 默认（接收方）气泡样式
    background-color: var(--bg-card);
    color: var(--text-base);
    margin-right: 10rpx; // 为气泡尾巴留出空间

    // 接收方气泡尾巴
    &::after {
      content: "";
      position: absolute;
      bottom: 10rpx; // 调整位置，使其与气泡底部对齐
      left: -10rpx; // 调整位置，使其与气泡左侧对齐
      width: 0;
      height: 0;
      border-top: 10rpx solid transparent;
      border-bottom: 10rpx solid transparent;
      border-right: 10rpx solid var(--bg-card); // 匹配气泡背景色
    }

    // 消息状态的定位调整
    .message-status {
      position: absolute;
      bottom: -40rpx;
      font-size: 24rpx;
      // 初始定位，后续根据气泡方向调整
    }
  }
}

.self {
  flex-direction: row-reverse; // 自身消息靠右
  .avatar-container {
    margin-right: 0;
    margin-left: 20rpx; // 头像与气泡的间距
  }

  .message-content {
    background-color: var(--text-green); // 发送方气泡颜色
    color: var(--text-base); // 发送方文本颜色
    margin-left: 10rpx; // 为气泡尾巴留出空间
    margin-right: 0; // 覆盖接收方样式
    align-items: flex-end; // 文本靠右对齐

    // 发送方气泡尾巴
    &::after {
      content: "";
      position: absolute;
      bottom: 10rpx; // 调整位置，使其与气泡底部对齐
      right: -10rpx; // 调整位置，使其与气泡右侧对齐
      left: auto; // 确保在右侧
      width: 0;
      height: 0;
      border-top: 10rpx solid transparent;
      border-bottom: 10rpx solid transparent;
      border-left: 10rpx solid var(--text-green); // 匹配气泡背景色
      border-right: none; // 确保只在左侧有边框
    }

    .message-status {
      right: 0; // 自身消息状态靠右
    }
  }
}

.system-message {
  padding: 8rpx 20rpx;
  margin: 0 auto;
  font-size: 24rpx;
  color: var(--text-info);
  text-align: center;
  background-color: var(--bg-tag);
  border-radius: 10rpx;
}

.unknown-message {
  padding: 16rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
  background-color: var(--bg-secondary);
  border-radius: 12rpx;
}

.message-status {
  position: absolute;
  bottom: -40rpx;
  font-size: 24rpx;

  .status-sending {
    color: var(--text-info);
  }

  .status-failed {
    color: var(--text-red);
  }

  .status-sent {
    color: var(--text-info);
  }

  .status-read {
    color: var(--text-blue);
  }

  .loading-icon {
    animation: rotate 1s linear infinite;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>

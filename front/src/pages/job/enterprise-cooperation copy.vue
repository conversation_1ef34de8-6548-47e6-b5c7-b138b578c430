<template>
  <view class="enterprise-cooperation">
    <!-- 头部信息 -->
    <view class="header-section">
      <uni-nav-bar
        fixed
        statusBar="true"
        :border="false"
        left-icon="back"
        backgroundColor="transparent"
        color="#292421"
        @clickLeft="goBack"
      >
      </uni-nav-bar>
      <!-- 套餐选择标签 -->
      <view class="plan-tabs">
        <view
          v-for="(plan, index) in enterprisePlans"
          :key="index"
          class="plan-tab"
          :class="{ active: selectedPlan === index }"
          @tap="selectPlan(index)"
        >
          <text class="tab-name">{{ plan.name }}</text>
        </view>
      </view>
    </view>

    <!-- 设备支持说明 -->
    <view class="device-support">
      <text class="support-text">手机、电脑、平板均可使用</text>
    </view>

    <!-- 价格套餐容器 -->
    <view class="pricing-container">
      <!-- 套餐选项 -->
      <scroll-view class="pricing-plans-scroll" scroll-x>
        <view class="pricing-plans">
          <view
            v-for="(option, index) in currentPricingOptions"
            :key="index"
            class="pricing-plan"
            :class="{ highlighted: selectedPriceIndex === index }"
            @tap="selectPrice(index)"
          >
            <view v-if="option.badge" class="plan-badge">
              <text class="badge-text">{{ option.badge }}</text>
            </view>
            <text class="plan-title">{{ option.title }}</text>
            <view class="price-display">
              <text v-if="option.price !== '定制'" class="price-symbol">¥</text>
              <text class="price-amount">{{ option.price }}</text>
            </view>
            <view v-if="option.subtitle" class="price-subtitle">
              <text class="subtitle-text">{{ option.subtitle }}</text>
            </view>
            <!-- <view v-if="option.renewal" class="renewal-info">
              <text class="renewal-text">{{ option.renewal }}</text>
            </view> -->
          </view>
        </view>
      </scroll-view>

      <!-- 支付说明 -->
      <view class="payment-info">
        <text class="info-text">到期按套餐费用自动续费，可随时取消</text>
      </view>
    </view>

    <!-- 确认协议并支付按钮 -->
    <view class="confirm-payment-box">
      <view class="pay-button" @tap="handlePayment">
        <text class="pay-text">确认协议并支付</text>
      </view>
      <view class="agreement-row">
        <text class="agreement-text">开通前请阅读</text>
        <text class="agreement-link">《会员服务协议》</text>
        <text class="agreement-text">(含自动续费条款)</text>
      </view>
    </view>

    <!-- 权益展示 -->
    <view class="benefits-container">
      <view class="benefits-header">
        <text class="benefits-title">{{ selectedPlanInfo.name }}会员特权</text>
        <text class="benefits-link">更多特权 ></text>
      </view>

      <view class="benefits-grid">
        <view
          v-for="(service, index) in selectedPlanInfo.services"
          :key="index"
          class="benefit-item"
        >
          <view class="benefit-icon-wrapper">
            <view class="icon" :class="getBenefitIcon(service)"></view>
          </view>
          <text class="benefit-name">{{ service }}</text>
        </view>
      </view>
    </view>

    <!-- 支付成功弹窗 -->
    <tui-modal
      :show="showSuccessModal"
      :mask="true"
      @close="showSuccessModal = false"
    >
      <view class="success-modal">
        <view class="success-icon-wrapper">
          <text class="i-carbon-checkmark-outline"></text>
        </view>
        <text class="success-title">开通成功</text>
        <text class="success-desc">企业合作服务已为您开通</text>
        <view class="success-button" @tap="showSuccessModal = false">
          <text>好的</text>
        </view>
      </view>
    </tui-modal>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import plansData from "./taocan.json";

const cooperationPlans = plansData.enterprise_cooperation_plans;

const selectedPlan = ref(1);
const selectedPriceIndex = ref(3); // 默认选中年付
const showSuccessModal = ref(false);
const navBackground = "#2c2c2e";

const enterprisePlans = ref([
  {
    name: "星火计划",
    key: "spark_plan",
  },
  {
    name: "燎原计划",
    key: "prairie_fire_plan",
  },
  {
    name: "鲲鹏计划",
    key: "roc_plan",
  },
]);

const planPricing = computed(() => {
  const planKey = enterprisePlans.value[selectedPlan.value]
    .key as keyof typeof cooperationPlans;
  const planInfo = cooperationPlans[planKey] as any;
  const pricing = planInfo.pricing_tiers[0];

  return {
    [planKey]: [
      {
        badge: "月度尝鲜",
        title: "月付",
        price: pricing.standard_price.toFixed(0),
        subtitle: ``,
        renewal: "",
      },
      {
        badge: "季度特惠",
        title: "季付",
        price: pricing.quarterly_price.toFixed(0),
        subtitle: `折合¥${(pricing.quarterly_price / 3).toFixed(1)}/月`,
        renewal: `次季续费 ¥${pricing.quarterly_price.toFixed(0)}`,
      },
      {
        badge: "半年优选",
        title: "半年付",
        price: pricing.semi_annual_price.toFixed(0),
        subtitle: `折合¥${(pricing.semi_annual_price / 6).toFixed(1)}/月`,
        renewal: `次半年续费 ¥${pricing.semi_annual_price.toFixed(0)}`,
      },
      {
        badge: "年度最省",
        title: "年付",
        price: pricing.annual_price.toFixed(0),
        subtitle: `折合¥${(pricing.annual_price / 12).toFixed(1)}/月`,
        renewal: `次年续费 ¥${pricing.annual_price.toFixed(0)}`,
      },
    ],
  };
});

const enterprisePlansDetail = computed(() => {
  const planKey = enterprisePlans.value[selectedPlan.value]
    .key as keyof typeof cooperationPlans;
  const planInfo = cooperationPlans[planKey] as any;
  return {
    name: enterprisePlans.value[selectedPlan.value].name,
    services: planInfo.core_features,
  };
});

const benefitIcons: { [key: string]: string } = {
  职位: "i-carbon-add-filled",
  刷新: "i-carbon-update-now",
  匹配: "i-carbon-user-multiple",
  认证: "i-carbon-badge",
  加速: "i-carbon-rocket",
  人才库: "i-carbon-data-base",
  API: "i-carbon-api",
  客户经理: "i-carbon-customer-service",
  门户: "i-carbon-page-break", // 对应 定制招聘门户
  管理: "i-carbon-tool-box",
  补贴: "i-carbon-money",
  测评: "i-carbon-result-old", // 对应 人才测评系统
  报告: "i-carbon-report", // 对应 竞品分析报告
};

const selectedPlanInfo = computed(() => {
  return enterprisePlansDetail.value;
});

const currentPricingOptions = computed(() => {
  const planKey = enterprisePlans.value[selectedPlan.value].key;
  return planPricing.value[planKey];
});

const goBack = () => uni.navigateBack();
const selectPlan = (index: number) => {
  selectedPlan.value = index;
  selectedPriceIndex.value = 3; // 切换套餐时，默认选中年付
};

const selectPrice = (index: number) => {
  selectedPriceIndex.value = index;
};

const getBenefitIcon = (serviceName: string) => {
  const keyword = Object.keys(benefitIcons).find((key) =>
    serviceName.includes(key)
  );
  return keyword ? benefitIcons[keyword] : "i-carbon-help";
};

const handlePayment = () => {
  const currentOption = currentPricingOptions.value[selectedPriceIndex.value];
  if (currentOption?.price === "定制") {
    uni.showToast({ title: "请联系客服获取定制方案", icon: "none" });
    return;
  }
  uni.showLoading({ title: "支付中..." });
  setTimeout(() => {
    uni.hideLoading();
    showSuccessModal.value = true;
  }, 1500);
};
</script>

<style lang="scss" scoped>
.enterprise-cooperation {
  min-height: 100vh;
  background: linear-gradient(180deg, #fdf6f1 0%, #f7f8fa 100%);
  color: #333;
  padding-bottom: 40rpx;
}

.header-section {
  // background: linear-gradient(180deg, #3a3a3a 0%, #1e1e1e 100%);
  padding-bottom: 40rpx;
  border-bottom-left-radius: 40rpx;
  border-bottom-right-radius: 40rpx;
}

.plan-tabs {
  display: flex;
  margin: 20rpx 40rpx 0;
  justify-content: space-around;
  background-color: #3e3e3e;
  border-radius: 46rpx;
  padding: 8rpx;
}

.plan-tab {
  flex: 1;
  position: relative;
  border-radius: 36rpx;
  padding: 16rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: #e0e0e0;

  &.active {
    background: linear-gradient(135deg, #fdeadd, #f5d6c2);
    color: #603813;
    font-weight: bold;
  }
}

.tab-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
  color: #b2b2b2;
}

.tab-name {
  font-size: 28rpx;
}

.device-support {
  text-align: center;
  margin: 40rpx 0;
}

.support-text {
  font-size: 26rpx;
  color: #999;
}

.pricing-container {
  margin: 0 32rpx;
}

.pricing-plans-scroll {
  white-space: nowrap;
}

.pricing-plans {
  display: flex;
  gap: 20rpx;
  padding: 10rpx 0;
}

.pricing-plan {
  display: inline-block;
  flex: 0 0 auto;
  width: 260rpx;
  text-align: center;
  padding: 32rpx 16rpx;
  border-radius: 24rpx;
  background: #f9fafb;
  border: 3rpx solid var(--bg-tag);
  position: relative;
  transition: all 0.3s ease;

  &.highlighted {
    // background: #fdf5e6;
    border-color: #926623;
    background: linear-gradient(110deg, #fdfbf9 0%, #fff5e6 100%);
  }
}

.plan-badge {
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #4c4c4c, #333333);
  border-radius: 24rpx 0 24rpx 0;
  padding: 6rpx 20rpx;
}

.badge-text {
  font-size: 22rpx;
  color: #f0dcb5;
  font-weight: 500;
}

.plan-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-top: 20rpx;
  margin-bottom: 24rpx;
}

.price-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 8rpx;
  color: #d89f58;
}

.price-symbol {
  font-size: 32rpx;
  font-weight: 500;
}

.price-amount {
  font-size: 64rpx;
  font-weight: bold;
}

.price-subtitle {
  margin-bottom: 12rpx;
}

.subtitle-text {
  font-size: 24rpx;
  color: #999999;
}

.renewal-info {
  background: #f5f5f5;
  border-radius: 16rpx;
  padding: 8rpx;
  margin: 16rpx -16rpx -32rpx -16rpx;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.renewal-text {
  font-size: 22rpx;
  color: #666666;
}

.payment-info {
  text-align: center;
  margin-top: 32rpx;
}

.info-text {
  font-size: 24rpx;
  color: #b2b2b2;
}

.confirm-payment-box {
  margin: 40rpx 32rpx;
}

.pay-button {
  background: linear-gradient(135deg, #f9d692, #e6cba6);
  color: #3e3e3e;
  height: 90rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(230, 203, 166, 0.4);
}

.pay-text {
  font-size: 32rpx;
  font-weight: 600;
}

.agreement-row {
  text-align: center;
  margin-top: 32rpx;
  font-size: 24rpx;
  color: #999999;
}

.agreement-link {
  color: #555;
}

.benefits-container {
  background: #ffffff;
  margin: 0 32rpx 32rpx;
  padding: 32rpx;
  border-radius: 24rpx;
}

.benefits-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.benefits-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.benefits-link {
  font-size: 26rpx;
  color: #999;
}

.benefits-grid {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.benefit-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  text-align: left;
}

.benefit-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #fdf5e6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.benefit-icon-wrapper .icon {
  font-size: 40rpx;
  color: #d89f58;
}

.benefit-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  font-weight: 500;
}

.payment-section {
  display: none; // Hiding the old fixed footer
}

.pay-button-bottom {
  display: none;
}

.success-modal {
  background: #fff;
  border-radius: 32rpx;
  padding: 64rpx 48rpx;
  text-align: center;
}

.success-icon-wrapper {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #d89f58;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 64rpx;
  margin: 0 auto 32rpx;
}

.success-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.success-desc {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 48rpx;
}

.success-button {
  background: #d89f58;
  color: #fff;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 600;
}
</style>

<template>
  <view>
    <!-- 人才筛选 -->
    <view class="talent-filter px-20rpx mb-20rpx">
      <view class="talent-filter-tabs flex">
        <view
          v-for="(tab, index) in talentFilterTabs"
          :key="index"
          class="talent-filter-tab"
          :class="{ 'active-tab': tab.active }"
          @click="handleTalentFilterTabClick(index)"
        >
          <text class="text-28rpx">{{ tab.name }}</text>
        </view>
      </view>
      <view
        class="filter-more flex items-center"
        @click="openTalentFilterPopup"
      >
        <text class="text-secondary">筛选</text>
        <text class="i-carbon-filter ml-4rpx text-secondary"></text>
      </view>
    </view>

    <!-- 人才列表 - 使用z-paging -->
    <view class="talent-list">
      <z-paging
        ref="paging"
        v-model="talentList"
        @query="queryTalentList"
        use-page-scroll
        :scrollViewStyle="{ padding: '0 20rpx' }"
      >
        <view
          v-for="(talent, index) in talentList"
          :key="index"
          class="mb-20rpx"
        >
          <TalentItem
            :talent="talent"
            @click="goToTalentDetail(talent)"
            @contact="contactTalent(talent)"
          />
        </view>
      </z-paging>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { onPageScroll, onReachBottom } from "@dcloudio/uni-app";
import useZPaging from "z-paging/components/z-paging/js/hooks/useZPaging.js";
import TalentItem from "@/components/job/TalentItem.vue";

const emit = defineEmits(["openTalentFilterPopup"]);

// z-paging ref
const paging = ref(null);

useZPaging(paging);
// 人才筛选相关
const talentFilterTabs = ref([{ name: "推荐", active: true }]);

const talentList = ref([]);

// 模拟人才数据生成
const generateTalentData = (page: number, pageSize: number) => {
  const data = [];
  const startIndex = (page - 1) * pageSize;

  const names = [
    "张三",
    "李女士",
    "王五",
    "赵六",
    "陈七",
    "刘八",
    "孙九",
    "周十",
  ];
  const jobTitles = [
    "前端开发工程师",
    "UI/UX 设计师",
    "Java 后端开发",
    "产品总监",
    "运营专员",
    "销售经理",
    "人事主管",
    "财务分析师",
  ];
  const locations = [
    "广州市天河区",
    "深圳市南山区",
    "上海市浦东新区",
    "北京市朝阳区",
  ];
  const experiences = ["1年", "2年", "3年", "5年", "8年", "10年以上"];
  const educations = ["大专", "本科", "硕士"];
  const statuses = ["active", "interview", "pending", "rejected"];

  for (let i = 0; i < pageSize; i++) {
    const index = startIndex + i;
    if (index >= 50) break; // 最多50条数据

    data.push({
      id: index + 1,
      name:
        names[index % names.length] +
        (index > 7 ? `_${Math.floor(index / 8)}` : ""),
      gender: index % 2 === 0 ? "男" : "女",
      age: 22 + (index % 20),
      jobTitle: jobTitles[index % jobTitles.length],
      experience: experiences[index % experiences.length],
      education: educations[index % educations.length],
      location: locations[index % locations.length],
      lastActivity:
        index < 5
          ? "刚刚"
          : index < 10
          ? "2小时前"
          : index < 20
          ? "昨天"
          : "3天前",
      skills: [
        ["Vue", "React", "TypeScript", "Node.js"],
        ["Figma", "Sketch", "Illustrator", "用户体验"],
        ["Java", "Spring Boot", "MySQL", "Redis"],
        ["产品规划", "团队管理", "数据分析", "商业需求"],
        ["新媒体运营", "活动策划", "数据分析", "用户增长"],
        ["销售技巧", "客户维护", "市场开拓", "团队协作"],
        ["招聘管理", "薪酬设计", "员工关系", "培训发展"],
        ["财务分析", "成本控制", "预算管理", "风险评估"],
      ][index % 8],
      status: statuses[index % statuses.length],
    });
  }

  return data;
};

// 查询人才列表
const queryTalentList = async (pageNo: number, pageSize: number) => {
  console.log("查询人才列表", pageNo, pageSize);

  try {
    // 模拟数据
    const data = generateTalentData(pageNo, pageSize);
    console.log("data", data);
    // 完成分页查询
    paging.value.complete(data);

    // 如果是最后一页，调用noMore
    if (pageNo * pageSize >= 50) {
      paging.value.noMore();
    }
  } catch (error) {
    console.error("加载人才列表失败:", error);
    paging.value.complete(false);
    uni.showToast({
      title: "加载失败，请重试",
      icon: "none",
    });
  }
};

// 初始化数据
onMounted(() => {
  // 延迟一点开始加载，确保组件完全挂载
  setTimeout(() => {
    paging.value.reload();
  }, 100);
});

const handleTalentFilterTabClick = (index: number) => {
  talentFilterTabs.value.forEach((tab, idx) => {
    tab.active = idx === index;
  });
  // 切换筛选条件时重新加载数据
  paging.value.reload();
};

const openTalentFilterPopup = () => {
  console.log("RecruiterView: 点击筛选按钮");
  emit("openTalentFilterPopup");
};

// 跳转到人才详情页
const goToTalentDetail = (talent: any) => {
  uni.navigateTo({
    url: `/pages/job/recruiter/talent-detail?id=${talent.id}`,
  });
};

// 联系人才
const contactTalent = (talent: any) => {
  uni.showModal({
    title: "联系人才",
    content: `确定要联系 ${talent.name} 吗？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: "联系请求已发送",
          icon: "success",
        });
      }
    },
  });
};

// 刷新数据
const refreshData = () => {
  paging.value.reload();
};

// 暴露方法给父组件
defineExpose({
  refreshData,
});
</script>

<style lang="scss" scoped>
// 人才筛选 - 现代化设计
.talent-filter {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .filter-header {
    margin-bottom: 20rpx;
  }

  .talent-filter-tabs {
    display: flex;
    gap: 12rpx;

    .talent-filter-tab {
      padding: 12rpx 20rpx;
      background: #f8f9fa;
      border-radius: 16rpx;
      color: #666;
      font-size: 26rpx;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      border: 1rpx solid #e9ecef;

      &.active-tab {
        background: linear-gradient(135deg, var(--primary), var(--primary-400));
        color: #ffffff;
        border-color: var(--primary);
        box-shadow: 0 4rpx 12rpx rgba(var(--primary), 0.25);
      }

      &:active {
        transform: translateY(1rpx) scale(0.95);
      }
    }
  }

  .filter-more {
    padding: 10rpx 16rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    color: #666;
    font-size: 24rpx;
    transition: all 0.2s;
    border: 1rpx solid #e9ecef;

    &:active {
      background: #e9ecef;
      transform: scale(0.98);
    }
  }
}

// 人才列表样式
.talent-list {
  margin: 0 20rpx;
  flex: 1;
}
</style>

<template>
  <view class="analytics-container">
    <uni-nav-bar
      fixed
      statusBar="true"
      :border="false"
      title="数据分析"
      left-icon="back"
      backgroundColor="#ffffff"
      @clickLeft="goBack"
    />

    <scroll-view scroll-y class="analytics-content">
      <!-- 数据概览卡片 -->
      <view class="overview-section">
        <view class="section-title">
          <text class="title-text">数据概览</text>
          <view class="time-selector" @tap="showTimeSelector">
            <text class="time-text">{{ selectedTimeRange }}</text>
            <text class="i-carbon-chevron-down time-icon"></text>
          </view>
        </view>

        <view class="overview-grid">
          <view
            v-for="(stat, index) in overviewStats"
            :key="index"
            class="stat-card"
            :class="stat.colorClass"
          >
            <view class="stat-icon">
              <text :class="stat.icon"></text>
            </view>
            <view class="stat-content">
              <text class="stat-value">{{ stat.value }}</text>
              <text class="stat-label">{{ stat.label }}</text>
              <view class="stat-change" :class="stat.change.type">
                <text :class="stat.change.icon"></text>
                <text class="change-text">{{ stat.change.text }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 趋势图表 -->
      <view class="chart-section">
        <view class="section-title">
          <text class="title-text">趋势分析</text>
        </view>

        <view class="chart-tabs">
          <view
            v-for="(tab, index) in chartTabs"
            :key="index"
            class="chart-tab"
            :class="{ active: activeChartTab === index }"
            @tap="switchChartTab(index)"
          >
            {{ tab.name }}
          </view>
        </view>

        <view class="chart-container">
          <!-- 这里可以集成图表组件，暂时用模拟数据 -->
          <view class="chart-placeholder">
            <text class="placeholder-text"
              >{{ chartTabs[activeChartTab].name }}趋势图</text
            >
            <view class="chart-mockup">
              <view
                v-for="(bar, index) in mockChartData"
                :key="index"
                class="chart-bar"
                :style="{ height: bar.height + '%' }"
              >
                <text class="bar-label">{{ bar.label }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 职位分析 -->
      <view class="position-analysis">
        <view class="section-title">
          <text class="title-text">职位分析</text>
        </view>

        <view class="position-list">
          <view
            v-for="(position, index) in positionAnalytics"
            :key="index"
            class="position-item"
          >
            <view class="position-header">
              <view class="position-info">
                <text class="position-title">{{ position.title }}</text>
                <text class="position-status" :class="position.statusClass">{{
                  position.status
                }}</text>
              </view>
              <view class="position-stats">
                <text class="views-count">{{ position.views }} 浏览</text>
              </view>
            </view>

            <view class="position-metrics">
              <view class="metric-item">
                <text class="metric-label">投递简历</text>
                <text class="metric-value">{{ position.applications }}</text>
              </view>
              <view class="metric-item">
                <text class="metric-label">面试邀请</text>
                <text class="metric-value">{{ position.interviews }}</text>
              </view>
              <view class="metric-item">
                <text class="metric-label">通过率</text>
                <text class="metric-value success"
                  >{{ position.passRate }}%</text
                >
              </view>
            </view>

            <view class="position-progress">
              <view class="progress-bar">
                <view
                  class="progress-fill"
                  :style="{ width: position.progressPercent + '%' }"
                ></view>
              </view>
              <text class="progress-text"
                >招聘进度 {{ position.progressPercent }}%</text
              >
            </view>
          </view>
        </view>
      </view>

      <!-- 人才洞察 -->
      <view class="talent-insights">
        <view class="section-title">
          <text class="title-text">人才洞察</text>
        </view>

        <view class="insights-grid">
          <view class="insight-card">
            <view class="insight-header">
              <text class="insight-title">热门技能</text>
            </view>
            <view class="skill-tags">
              <view
                v-for="(skill, index) in popularSkills"
                :key="index"
                class="skill-tag"
                :style="{ fontSize: skill.size + 'rpx' }"
              >
                {{ skill.name }}
              </view>
            </view>
          </view>

          <view class="insight-card">
            <view class="insight-header">
              <text class="insight-title">学历分布</text>
            </view>
            <view class="education-stats">
              <view
                v-for="(edu, index) in educationDistribution"
                :key="index"
                class="education-item"
              >
                <text class="education-label">{{ edu.name }}</text>
                <view class="education-bar">
                  <view
                    class="education-fill"
                    :style="{ width: edu.percent + '%' }"
                  ></view>
                </view>
                <text class="education-percent">{{ edu.percent }}%</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 时间选择器弹窗 -->
    <tui-bottom-popup
      :show="showTimePopup"
      :height="600"
      @close="showTimePopup = false"
    >
      <view class="time-selector-popup">
        <view class="popup-header">
          <text class="popup-title">选择时间范围</text>
        </view>
        <view class="time-options">
          <view
            v-for="(option, index) in timeOptions"
            :key="index"
            class="time-option"
            :class="{ active: selectedTimeRange === option }"
            @tap="selectTimeRange(option)"
          >
            {{ option }}
          </view>
        </view>
      </view>
    </tui-bottom-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

const activeChartTab = ref(0);
const showTimePopup = ref(false);
const selectedTimeRange = ref("最近7天");

// 数据概览
const overviewStats = ref([
  {
    label: "总浏览量",
    value: "1,240",
    icon: "i-carbon-view",
    colorClass: "blue",
    change: {
      type: "increase",
      icon: "i-carbon-trending-up",
      text: "+12.5%",
    },
  },
  {
    label: "收到简历",
    value: "87",
    icon: "i-carbon-document",
    colorClass: "green",
    change: {
      type: "increase",
      icon: "i-carbon-trending-up",
      text: "+8.3%",
    },
  },
  {
    label: "面试安排",
    value: "23",
    icon: "i-carbon-user-multiple",
    colorClass: "purple",
    change: {
      type: "increase",
      icon: "i-carbon-trending-up",
      text: "+15.2%",
    },
  },
  {
    label: "已录用",
    value: "5",
    icon: "i-carbon-checkmark-filled",
    colorClass: "orange",
    change: {
      type: "decrease",
      icon: "i-carbon-trending-down",
      text: "-2.1%",
    },
  },
]);

// 图表标签
const chartTabs = ref([
  { name: "浏览量" },
  { name: "简历投递" },
  { name: "面试安排" },
  { name: "录用情况" },
]);

// 模拟图表数据
const mockChartData = ref([
  { label: "周一", height: 60 },
  { label: "周二", height: 80 },
  { label: "周三", height: 45 },
  { label: "周四", height: 90 },
  { label: "周五", height: 75 },
  { label: "周六", height: 30 },
  { label: "周日", height: 25 },
]);

// 职位分析数据
const positionAnalytics = ref([
  {
    title: "前端开发工程师",
    status: "招聘中",
    statusClass: "active",
    views: 342,
    applications: 28,
    interviews: 8,
    passRate: 35,
    progressPercent: 60,
  },
  {
    title: "产品经理",
    status: "已满员",
    statusClass: "completed",
    views: 256,
    applications: 19,
    interviews: 6,
    passRate: 50,
    progressPercent: 100,
  },
  {
    title: "UI设计师",
    status: "招聘中",
    statusClass: "active",
    views: 189,
    applications: 15,
    interviews: 4,
    passRate: 40,
    progressPercent: 30,
  },
]);

// 热门技能
const popularSkills = ref([
  { name: "Vue", size: 36 },
  { name: "React", size: 32 },
  { name: "JavaScript", size: 40 },
  { name: "TypeScript", size: 30 },
  { name: "Node.js", size: 28 },
  { name: "Python", size: 26 },
  { name: "UI设计", size: 34 },
  { name: "产品设计", size: 24 },
]);

// 学历分布
const educationDistribution = ref([
  { name: "本科", percent: 65 },
  { name: "硕士", percent: 25 },
  { name: "大专", percent: 8 },
  { name: "博士", percent: 2 },
]);

// 时间选项
const timeOptions = ref([
  "最近7天",
  "最近30天",
  "最近3个月",
  "最近6个月",
  "最近1年",
]);

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示时间选择器
const showTimeSelector = () => {
  showTimePopup.value = true;
};

// 选择时间范围
const selectTimeRange = (range: string) => {
  selectedTimeRange.value = range;
  showTimePopup.value = false;
  // 这里可以根据时间范围重新获取数据
};

// 切换图表标签
const switchChartTab = (index: number) => {
  activeChartTab.value = index;
  // 这里可以切换图表数据
};

onMounted(() => {
  // 页面加载时获取数据
});
</script>

<style lang="scss" scoped>
.analytics-container {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.analytics-content {
  padding: 120rpx 20rpx 40rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;

  .title-text {
    font-size: 32rpx;
    font-weight: 600;
  }

  .time-selector {
    display: flex;
    align-items: center;
    padding: 12rpx 16rpx;
    background-color: var(--bg-secondary);
    border-radius: 20rpx;

    .time-text {
      font-size: 26rpx;
      color: var(--text-secondary);
      margin-right: 8rpx;
    }

    .time-icon {
      font-size: 20rpx;
      color: var(--text-secondary);
    }
  }
}

.overview-section {
  margin-bottom: 40rpx;

  .overview-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;

    .stat-card {
      background-color: #fff;
      border-radius: 20rpx;
      padding: 32rpx 24rpx;
      position: relative;
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 8rpx;
      }

      &.blue::before {
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
      }

      &.green::before {
        background: linear-gradient(90deg, #10b981, #047857);
      }

      &.purple::before {
        background: linear-gradient(90deg, #8b5cf6, #6d28d9);
      }

      &.orange::before {
        background: linear-gradient(90deg, #f59e0b, #d97706);
      }

      .stat-icon {
        width: 56rpx;
        height: 56rpx;
        border-radius: 28rpx;
        background-color: var(--bg-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16rpx;

        text {
          font-size: 28rpx;
          color: var(--text-secondary);
        }
      }

      .stat-content {
        .stat-value {
          display: block;
          font-size: 36rpx;
          font-weight: 700;

          margin-bottom: 8rpx;
        }

        .stat-label {
          display: block;
          font-size: 24rpx;
          color: var(--text-secondary);
          margin-bottom: 12rpx;
        }

        .stat-change {
          display: flex;
          align-items: center;
          gap: 4rpx;

          text {
            font-size: 22rpx;
          }

          &.increase {
            color: var(--success);
          }

          &.decrease {
            color: var(--error);
          }
        }
      }
    }
  }
}

.chart-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  margin-bottom: 40rpx;

  .chart-tabs {
    display: flex;
    gap: 24rpx;
    margin-bottom: 32rpx;

    .chart-tab {
      padding: 12rpx 24rpx;
      background-color: var(--bg-secondary);
      border-radius: 20rpx;
      font-size: 26rpx;
      color: var(--text-secondary);
      transition: all 0.2s;

      &.active {
        background-color: var(--primary);
        color: #fff;
      }
    }
  }

  .chart-container {
    .chart-placeholder {
      text-align: center;

      .placeholder-text {
        font-size: 28rpx;
        color: var(--text-secondary);
        margin-bottom: 32rpx;
      }

      .chart-mockup {
        display: flex;
        align-items: end;
        justify-content: space-between;
        height: 300rpx;
        padding: 0 20rpx;

        .chart-bar {
          flex: 1;
          background: linear-gradient(
            to top,
            var(--primary),
            var(--primary-400)
          );
          margin: 0 4rpx;
          border-radius: 8rpx 8rpx 0 0;
          position: relative;
          min-height: 20rpx;

          .bar-label {
            position: absolute;
            bottom: -40rpx;
            left: 50%;
            transform: translateX(-50%);
            font-size: 20rpx;
            color: var(--text-secondary);
          }
        }
      }
    }
  }
}

.position-analysis {
  margin-bottom: 40rpx;

  .position-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .position-item {
      background-color: #fff;
      border-radius: 20rpx;
      padding: 32rpx 24rpx;

      .position-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24rpx;

        .position-info {
          display: flex;
          align-items: center;
          gap: 16rpx;

          .position-title {
            font-size: 30rpx;
            font-weight: 600;
          }

          .position-status {
            padding: 8rpx 16rpx;
            border-radius: 12rpx;
            font-size: 22rpx;

            &.active {
              background-color: rgba(var(--success-rgb), 0.1);
              color: var(--success);
            }

            &.completed {
              background-color: rgba(var(--primary-rgb), 0.1);
              color: var(--primary);
            }
          }
        }

        .position-stats {
          .views-count {
            font-size: 26rpx;
            color: var(--text-secondary);
          }
        }
      }

      .position-metrics {
        display: flex;
        justify-content: space-between;
        margin-bottom: 24rpx;

        .metric-item {
          text-align: center;

          .metric-label {
            display: block;
            font-size: 24rpx;
            color: var(--text-secondary);
            margin-bottom: 8rpx;
          }

          .metric-value {
            font-size: 32rpx;
            font-weight: 600;

            &.success {
              color: var(--success);
            }
          }
        }
      }

      .position-progress {
        .progress-bar {
          height: 12rpx;
          background-color: var(--bg-secondary);
          border-radius: 6rpx;
          overflow: hidden;
          margin-bottom: 12rpx;

          .progress-fill {
            height: 100%;
            background: linear-gradient(
              90deg,
              var(--primary),
              var(--primary-400)
            );
            transition: width 0.3s;
          }
        }

        .progress-text {
          font-size: 24rpx;
          color: var(--text-secondary);
        }
      }
    }
  }
}

.talent-insights {
  .insights-grid {
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .insight-card {
      background-color: #fff;
      border-radius: 20rpx;
      padding: 32rpx 24rpx;

      .insight-header {
        margin-bottom: 24rpx;

        .insight-title {
          font-size: 30rpx;
          font-weight: 600;
        }
      }

      .skill-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .skill-tag {
          padding: 12rpx 20rpx;
          background-color: var(--bg-secondary);
          border-radius: 20rpx;
          color: var(--text-secondary);
        }
      }

      .education-stats {
        .education-item {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .education-label {
            width: 80rpx;
            font-size: 26rpx;
            color: var(--text-secondary);
          }

          .education-bar {
            flex: 1;
            height: 12rpx;
            background-color: var(--bg-secondary);
            border-radius: 6rpx;
            overflow: hidden;
            margin: 0 16rpx;

            .education-fill {
              height: 100%;
              background: linear-gradient(
                90deg,
                var(--primary),
                var(--primary-400)
              );
              transition: width 0.3s;
            }
          }

          .education-percent {
            width: 60rpx;
            font-size: 24rpx;
            color: var(--text-secondary);
            text-align: right;
          }
        }
      }
    }
  }
}

.time-selector-popup {
  padding: 32rpx;

  .popup-header {
    text-align: center;
    margin-bottom: 32rpx;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
    }
  }

  .time-options {
    .time-option {
      padding: 24rpx;
      text-align: center;
      font-size: 28rpx;
      color: var(--text-secondary);
      border-radius: 12rpx;
      margin-bottom: 16rpx;
      transition: all 0.2s;

      &.active {
        background-color: var(--primary);
        color: #fff;
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}
</style>

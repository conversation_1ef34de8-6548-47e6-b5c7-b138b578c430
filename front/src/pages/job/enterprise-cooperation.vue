<template>
  <view class="vip-page-container">
    <!-- 头部自定义导航 -->
    <view class="header-section">
      <uni-nav-bar
        fixed
        statusBar="true"
        :border="false"
        left-icon="back"
        backgroundColor="transparent"
        color="#1F2329"
        @clickLeft="goBack"
      >
        <template #default>
          <view class="nav-title">企业会员</view>
        </template>
      </uni-nav-bar>
      <!-- 套餐选择 -->
      <view class="plan-tabs">
        <view
          v-for="(plan, index) in enterprisePlans"
          :key="index"
          class="plan-tab"
          :class="{ active: selectedPlan === index }"
          @tap="selectPlan(index)"
        >
          <text class="tab-name">{{ plan.name }}</text>
        </view>
      </view>
    </view>

    <!-- 主内容区域 -->
    <scroll-view scroll-y class="content-scroll">
      <!-- 价格卡片 -->
      <view class="pricing-card">
        <view class="card-header">
          <text class="current-plan-name">{{ selectedPlanInfo.name }}</text>
          <text class="plan-slogan">解锁全部专属特权</text>
        </view>

        <!-- 价格选项 -->
        <view class="pricing-options">
          <view
            v-for="(option, index) in currentPricingOptions"
            :key="index"
            class="pricing-option"
            :class="{ highlighted: selectedPriceIndex === index }"
            @tap="selectPrice(index)"
          >
            <view v-if="option.badge" class="plan-badge">
              <text class="badge-text">{{ option.badge }}</text>
            </view>
            <text class="plan-title">{{ option.title }}</text>
            <view class="price-display">
              <text v-if="option.price !== '定制'" class="price-symbol">¥</text>
              <text class="price-amount">{{ option.price }}</text>
            </view>
            <view v-if="option.subtitle" class="price-subtitle">
              <text class="subtitle-text">{{ option.subtitle }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 权益展示 -->
      <view class="benefits-container">
        <view class="benefits-header">
          <text class="benefits-title">会员尊享特权</text>
          <text class="benefits-link">更多 ></text>
        </view>

        <view class="benefits-grid">
          <view
            v-for="(service, index) in selectedPlanInfo.services"
            :key="index"
            class="benefit-item"
          >
            <view class="icon" :class="getBenefitIcon(service)"></view>
            <text class="benefit-name">{{ service }}</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部支付栏 -->
    <view class="payment-footer">
      <view class="price-summary">
        <text class="summary-label">合计：</text>
        <text class="summary-amount"
          >¥{{
            currentPricingOptions[selectedPriceIndex]
              ? currentPricingOptions[selectedPriceIndex].price
              : ""
          }}</text
        >
      </view>
      <view class="pay-button" @tap="handlePayment">
        <text class="pay-text">立即开通</text>
      </view>
    </view>
    <view class="agreement-bar">
      <text class="agreement-text">开通前请阅读</text>
      <text class="agreement-link">《会员服务协议》</text>
      <text class="agreement-text">(含自动续费条款)</text>
    </view>

    <!-- 支付成功弹窗 -->
    <tui-modal
      :show="showSuccessModal"
      :mask="true"
      @close="showSuccessModal = false"
    >
      <view class="success-modal">
        <view class="success-icon-wrapper">
          <text class="i-carbon-checkmark-outline"></text>
        </view>
        <text class="success-title">开通成功</text>
        <text class="success-desc">企业合作服务已为您开通</text>
        <view class="success-button" @tap="showSuccessModal = false">
          <text>好的</text>
        </view>
      </view>
    </tui-modal>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import plansData from "./taocan.json";

const cooperationPlans = plansData.enterprise_cooperation_plans;

const selectedPlan = ref(1);
const selectedPriceIndex = ref(3); // 默认选中年付
const showSuccessModal = ref(false);

const enterprisePlans = ref([
  {
    name: "星火计划",
    key: "spark_plan",
  },
  {
    name: "燎原计划",
    key: "prairie_fire_plan",
  },
  {
    name: "鲲鹏计划",
    key: "roc_plan",
  },
]);

const planPricing = computed(() => {
  const planKey = enterprisePlans.value[selectedPlan.value]
    .key as keyof typeof cooperationPlans;
  const planInfo = cooperationPlans[planKey] as any;
  const pricing = planInfo.pricing_tiers[0];

  return {
    [planKey]: [
      {
        badge: "",
        title: "月付",
        price: pricing.standard_price.toFixed(0),
        subtitle: ``,
        renewal: "",
      },
      {
        badge: "特惠",
        title: "季付",
        price: pricing.quarterly_price.toFixed(0),
        subtitle: `折合¥${(pricing.quarterly_price / 3).toFixed(1)}/月`,
        renewal: `次季续费 ¥${pricing.quarterly_price.toFixed(0)}`,
      },
      {
        badge: "优选",
        title: "半年付",
        price: pricing.semi_annual_price.toFixed(0),
        subtitle: `折合¥${(pricing.semi_annual_price / 6).toFixed(1)}/月`,
        renewal: `次半年续费 ¥${pricing.semi_annual_price.toFixed(0)}`,
      },
      {
        badge: "最省",
        title: "年付",
        price: pricing.annual_price.toFixed(0),
        subtitle: `折合¥${(pricing.annual_price / 12).toFixed(1)}/月`,
        renewal: `次年续费 ¥${pricing.annual_price.toFixed(0)}`,
      },
    ],
  };
});

const enterprisePlansDetail = computed(() => {
  const planKey = enterprisePlans.value[selectedPlan.value]
    .key as keyof typeof cooperationPlans;
  const planInfo = cooperationPlans[planKey] as any;
  return {
    name: enterprisePlans.value[selectedPlan.value].name,
    services: planInfo.core_features,
  };
});

const benefitIcons: { [key: string]: string } = {
  职位: "i-carbon-add-filled",
  刷新: "i-carbon-update-now",
  匹配: "i-carbon-user-multiple",
  认证: "i-carbon-badge",
  加速: "i-carbon-rocket",
  人才库: "i-carbon-data-base",
  API: "i-carbon-api",
  客户经理: "i-carbon-customer-service",
  门户: "i-carbon-page-break", // 对应 定制招聘门户
  管理: "i-carbon-tool-box",
  补贴: "i-carbon-money",
  测评: "i-carbon-result-old", // 对应 人才测评系统
  报告: "i-carbon-report", // 对应 竞品分析报告
};

const selectedPlanInfo = computed(() => {
  return enterprisePlansDetail.value;
});

const currentPricingOptions = computed(() => {
  const planKey = enterprisePlans.value[selectedPlan.value].key;
  return planPricing.value[planKey];
});

const goBack = () => uni.navigateBack();
const selectPlan = (index: number) => {
  selectedPlan.value = index;
  selectedPriceIndex.value = 3; // 切换套餐时，默认选中年付
};

const selectPrice = (index: number) => {
  selectedPriceIndex.value = index;
};

const getBenefitIcon = (serviceName: string) => {
  const keyword = Object.keys(benefitIcons).find((key) =>
    serviceName.includes(key)
  );
  return keyword ? benefitIcons[keyword] : "i-carbon-help";
};

const handlePayment = () => {
  const currentOption = currentPricingOptions.value[selectedPriceIndex.value];
  if (currentOption?.price === "定制") {
    uni.showToast({ title: "请联系客服获取定制方案", icon: "none" });
    return;
  }
  uni.showLoading({ title: "支付中..." });
  setTimeout(() => {
    uni.hideLoading();
    showSuccessModal.value = true;
  }, 1500);
};
</script>

<style lang="scss" scoped>
// --- 色彩变量 ---
// $gold-gradient: linear-gradient(135deg, #f0dcb5 0%, #c89b6d 100%); // 替换为CSS变量
// $bg-color: #ffffff; // 替换为CSS变量
// $card-bg-color: #ffffff; // 替换为CSS变量
// $text-primary-color: #1f2329; // 替换为CSS变量
// var(--text-secondary)-color: #8a8d93; // 替换为CSS变量
// $gold-text-color: #c89b6d; // 替换为CSS变量
// $border-color: #ebeef5; // 替换为CSS变量
// $highlight-bg-color: #fdfbf8; // 替换为CSS变量

.vip-page-container {
  min-height: 100vh;
  background-color: var(--bg-page);

  display: flex;
  flex-direction: column;
}

.header-section {
  background: linear-gradient(180deg, #fdf6f1 0%, #f7f8fa 100%);
  position: sticky;
  top: 0;
  z-index: 10;
  padding-bottom: 20rpx;
}

.nav-title {
  font-size: 34rpx;
  font-weight: 600;
}

.plan-tabs {
  display: flex;
  margin: 20rpx 40rpx 0;
  justify-content: center;
  gap: 40rpx;
}

.plan-tab {
  position: relative;
  padding: 16rpx 0;
  transition: all 0.3s ease;
  color: var(--text-secondary);

  .tab-name {
    font-size: 30rpx;
  }

  &.active {
    color: #c89b6d; // 保持金色，因为app.css中没有直接对应的金色变量
    font-weight: bold;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 6rpx;
      background: linear-gradient(135deg, #f0dcb5 0%, #c89b6d 100%);
      border-radius: 3rpx;
    }
  }
}

.content-scroll {
  flex: 1;
  padding-bottom: 220rpx; // 为底部支付栏留出空间
}

.pricing-card {
  background: var(--bg-card);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-top: 20rpx;
  border: 1px solid var(--border-color);
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.current-plan-name {
  font-size: 44rpx;
  font-weight: bold;

  display: block;
}

.plan-slogan {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-top: 8rpx;
}

.pricing-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.pricing-option {
  text-align: center;
  padding: 32rpx 16rpx;
  border-radius: 24rpx;
  background: #f7f8fa;
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;

  .plan-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-info);
    margin-bottom: 16rpx;
  }

  &.highlighted {
    background: #fdfbf8; // 保持高亮背景色
    border: 2px solid #c89b6d; // 保持金色边框

    .plan-title {
      color: #3a3a38;
    }
  }
}

.plan-badge {
  position: absolute;
  top: -2rpx;
  right: -2rpx;
  background: linear-gradient(135deg, #f0dcb5 0%, #c89b6d 100%);
  border-radius: 0 22rpx 0 16rpx;
  padding: 4rpx 16rpx;
}

.badge-text {
  font-size: 22rpx;
  color: #333;
  font-weight: bold;
}

.price-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 8rpx;
  color: #c89b6d; // 保持金色
}

.price-symbol {
  font-size: 30rpx;
  font-weight: 500;
}

.price-amount {
  font-size: 56rpx;
  font-weight: bold;
}

.price-subtitle {
  margin-bottom: 12rpx;
}

.subtitle-text {
  font-size: 22rpx;
  color: var(--text-secondary);
}

.benefits-container {
  background: var(--bg-card);
  margin: 40rpx 0;
  padding: 32rpx;
  border-radius: 24rpx;
  border: 1px solid var(--border-color);
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
}

.benefits-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.benefits-title {
  font-size: 32rpx;
  font-weight: 600;
}

.benefits-link {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40rpx 20rpx;
}

.benefit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.benefit-item .icon {
  font-size: 56rpx;
  color: #c89b6d; // 保持金色
  margin-bottom: 16rpx;
}

.benefit-name {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.payment-footer {
  position: fixed;
  bottom: 60rpx;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 40rpx;
  background: var(--bg-card);
  border-top: 1px solid var(--border-color);
  z-index: 20;
}

.price-summary {
  display: flex;
  align-items: baseline;
}

.summary-label {
  font-size: 28rpx;
}

.summary-amount {
  font-size: 44rpx;
  font-weight: bold;
  color: #c89b6d; // 保持金色
}

.pay-button {
  background: linear-gradient(135deg, #f0dcb5 0%, #c89b6d 100%);
  color: #3a3a38;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 60rpx;
  box-shadow: 0 8rpx 20rpx rgba(200, 155, 109, 0.4);
}

.pay-text {
  font-size: 30rpx;
  font-weight: 600;
}

.agreement-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  padding: 10rpx 0 20rpx;
  font-size: 22rpx;
  color: #a0a0a0;
  background: var(--bg-card);
  z-index: 20;
}

.agreement-link {
  color: #888;
  margin: 0 4rpx;
}

.success-modal {
  background: var(--bg-card);
  border-radius: 32rpx;
  padding: 64rpx 48rpx;
  text-align: center;
  border: 1px solid var(--border-color);
}

.success-icon-wrapper {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #f0dcb5 0%, #c89b6d 100%);
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 64rpx;
  margin: 0 auto 32rpx;
}

.success-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;

  margin-bottom: 16rpx;
}

.success-desc {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 48rpx;
}

.success-button {
  background: linear-gradient(135deg, #f0dcb5 0%, #c89b6d 100%);
  color: #333;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 600;
}
</style>

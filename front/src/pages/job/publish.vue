<template>
  <view class="container">
    <scroll-view scroll-y enable-flex class="content-scroll">
      <!-- 基本信息 -->
      <view class="form-section bg-white mx-20rpx mt-30rpx rounded-lg">
        <view class="form-content p-30rpx">
          <!-- 职位类型 -->
          <view class="form-item pb-20rpx mb-30rpx">
            <view class="form-label mb-20rpx">
              <text class="text-28rpx">职位类型</text>
            </view>
            <JobTypeSelector
              v-model="jobForm.jobType"
              @change="handleJobTypeChange"
            />
          </view>

          <view class="form-item pb-20rpx mb-30rpx">
            <view class="form-label mb-20rpx">
              <text class="text-28rpx">职位名称</text>
            </view>
            <input
              class="form-input text-30rpx"
              placeholder="请输入职位名称"
              placeholder-class="placeholder-style"
              v-model="jobForm.title"
            />
          </view>

          <view class="form-item pb-20rpx mb-30rpx">
            <view class="form-label mb-20rpx">
              <text class="text-28rpx">职位类别</text>
            </view>
            <view
              class="form-selector flex justify-between items-center"
              @tap="showCategoryPicker"
            >
              <text
                class="text-30rpx"
                :class="
                  jobForm.categoryText ? 'text-base' : 'color-placeholder'
                "
              >
                {{ jobForm.categoryText || "请选择职位类别" }}
              </text>
              <text class="i-carbon-chevron-right text-grey"></text>
            </view>
          </view>

          <SalarySelector
            :salary-type="jobForm.salaryType"
            :salary-text="jobForm.salaryText"
            :custom-salary-min="jobForm.customSalaryMin"
            :custom-salary-max="jobForm.customSalaryMax"
            :is-custom-salary="jobForm.isCustomSalary"
            @update:salary-type="(val) => (jobForm.salaryType = val)"
            @update:salary-text="(val) => (jobForm.salaryText = val)"
            @update:custom-salary-min="(val) => (jobForm.customSalaryMin = val)"
            @update:custom-salary-max="(val) => (jobForm.customSalaryMax = val)"
            @update:is-custom-salary="(val) => (jobForm.isCustomSalary = val)"
          />

          <view class="form-item pb-20rpx mb-30rpx">
            <view class="form-label mb-20rpx">
              <text class="text-28rpx">工作地点</text>
            </view>
            <view class="location-input-wrap">
              <!-- 位置选择区域 -->
              <view class="mb-20rpx">
                <view
                  class="location-current flex items-center justify-between"
                  @tap="openLocationChooser"
                >
                  <view class="flex-col" v-if="jobForm.addr_name">
                    <text class="text-main">{{ jobForm.addr_name }}</text>
                    <text class="text-gray text-26rpx">{{
                      jobForm.address
                    }}</text>
                  </view>
                  <view class="flex items-center text-gray" v-else>
                    <text
                      class="i-solar-map-point-linear text-30rpx mr-10rpx"
                    ></text>
                    <text class="text-gray">{{ "请选择工作地点" }}</text>
                  </view>
                  <text
                    class="i-carbon-chevron-right text-32rpx text-grey"
                  ></text>
                </view>
              </view>

              <!-- 门牌号输入区域 -->
              <view class="mb-10rpx">
                <view class="location-detail-input">
                  <input
                    class="form-input text-30rpx"
                    placeholder="请输入门牌号，如：A座302室"
                    placeholder-class="placeholder-style"
                    v-model="jobForm.locationDetail"
                    @focus="handleAddressInputFocus"
                  />
                </view>
              </view>
            </view>
          </view>

          <view class="form-item pb-20rpx mb-30rpx">
            <view class="form-label mb-20rpx">
              <text class="text-28rpx">联系电话</text>
            </view>
            <input
              class="form-input text-30rpx"
              type="number"
              maxlength="11"
              placeholder="请输入联系电话"
              placeholder-class="placeholder-style"
              v-model="jobForm.contactPhone"
            />
          </view>

          <EducationSelector
            :education-id="jobForm.educationId"
            :education-text="jobForm.educationText"
            @update:education-id="(val) => (jobForm.educationId = val)"
            @update:education-text="(val) => (jobForm.educationText = val)"
          />

          <view class="form-item pb-20rpx">
            <view class="form-label mb-20rpx">
              <text class="text-28rpx">经验要求</text>
            </view>
            <view class="experience-options flex flex-wrap">
              <view
                v-for="(item, index) in experienceOptions"
                :key="index"
                class="tag mb-20rpx mr-20rpx"
                :class="{
                  'active-option': jobForm.experienceText === item.name,
                }"
                @tap="selectExperience(item)"
              >
                {{ item.name }}
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 职位描述 -->
      <view class="form-section bg-white mx-20rpx mt-30rpx rounded-lg">
        <view class="form-content p-30rpx">
          <view class="form-item">
            <view class="form-label mb-20rpx">
              <text class="text-28rpx">详细描述</text>
            </view>
            <textarea
              class="description-textarea"
              placeholder="请详细描述岗位职责、任职要求等信息，吸引更多求职者"
              placeholder-class="placeholder-style"
              v-model="jobForm.description"
              maxlength="2000"
            ></textarea>
            <view class="text-right text-24rpx text-grey mt-10rpx">
              {{ jobForm.description.length }}/2000
            </view>
          </view>
        </view>
      </view>

      <!-- 职位福利 -->
      <view class="form-section bg-white mx-20rpx mt-30rpx rounded-lg">
        <view class="section-title p-30rpx flex justify-between items-center">
          <text class="text-32rpx font-500">职位福利</text>
          <text class="text-26rpx text-grey">可多选</text>
        </view>

        <view class="form-content p-30rpx">
          <view class="welfare-options flex flex-wrap">
            <view
              v-for="(item, index) in welfareOptions"
              :key="index"
              class="tag mb-20rpx mr-20rpx"
              :class="{ 'active-option': jobForm.welfare.includes(item) }"
              @tap="toggleWelfare(item)"
            >
              {{ item }}
            </view>
          </view>
        </view>
      </view>

      <!-- 发布选项 -->
      <view class="form-section bg-white mx-20rpx mt-30rpx rounded-lg">
        <view class="section-title p-30rpx">
          <text class="text-32rpx font-500">发布选项</text>
        </view>

        <view class="form-content p-30rpx">
          <!-- 发布时长 -->
          <view class="form-item pb-20rpx mb-30rpx">
            <view class="form-label mb-20rpx">
              <text class="text-28rpx">发布时长</text>
            </view>
            <view class="duration-options flex">
              <view
                v-for="(item, index) in durationOptions"
                :key="index"
                class="duration-option"
                :class="{
                  'active-duration-option': selectedDuration === item.value,
                }"
                @tap="selectDuration(item.value)"
              >
                <view class="duration-option-name">{{ item.name }}</view>
                <view class="duration-option-price">{{ item.price }}</view>
              </view>
            </view>
          </view>

          <!-- 附加功能 -->
          <view class="form-item pb-20rpx mb-30rpx">
            <view class="form-label mb-20rpx">
              <text class="text-28rpx">附加功能</text>
            </view>

            <view class="premium-options">
              <view
                v-for="(service, key) in premiumServices"
                :key="key"
                class="premium-option flex items-center justify-between pb-20rpx mb-20rpx"
              >
                <view class="flex items-center">
                  <view :class="['premium-icon', service.bgColor, 'mr-16rpx']">
                    <text :class="[service.icon, 'text-inverse']"></text>
                  </view>
                  <view>
                    <view class="text-28rpx font-500">{{ service.name }}</view>
                    <view class="text-24rpx text-grey">{{
                      service.description
                    }}</view>
                  </view>
                </view>
                <switch
                  color="#ff6d00"
                  :checked="jobForm.premium[key]"
                  @change="(e) => togglePremium(key, e)"
                />
              </view>
            </view>
          </view>

          <!-- 费用计算 -->
          <view class="form-item">
            <view class="form-label mb-20rpx">
              <text class="text-28rpx">费用合计</text>
            </view>

            <view class="fee-detail bg-gray-50 p-20rpx rounded-lg">
              <view class="flex justify-between mb-16rpx" v-if="baseFee > 0">
                <text class="text-26rpx text-info">基础发布费用</text>
                <text class="text-26rpx">¥{{ baseFee }}</text>
              </view>
              <view
                v-for="item in activePremiumItems"
                :key="item.key"
                class="flex justify-between mb-16rpx"
              >
                <text class="text-26rpx text-info">{{ item.name }}</text>
                <text class="text-26rpx">¥{{ item.price }}</text>
              </view>
              <view
                class="border-top-light pt-16rpx flex justify-between items-center"
              >
                <text class="text-28rpx font-500">总计</text>
                <text class="text-36rpx font-500 text-primary"
                  >¥{{ totalFee }}</text
                >
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 身份认证提醒 -->
      <view
        class="auth-reminder mx-20rpx my-30rpx bg-yellow-50 p-30rpx rounded-lg"
        v-if="!isAuthenticated"
      >
        <view class="flex items-start">
          <text
            class="i-carbon-warning-alt text-yellow-500 text-44rpx mr-16rpx"
          ></text>
          <view class="flex-1">
            <view class="text-28rpx font-500 mb-10rpx">您尚未完成身份认证</view>
            <view class="text-26rpx text-info mb-20rpx">
              完成身份认证后可获得招聘认证标识，提高简历投递率
            </view>
            <view class="auth-btn" @tap="goToAuth">去认证</view>
          </view>
        </view>
      </view>

      <!-- 底部间距 -->
      <view class="bottom-padding"></view>
    </scroll-view>

    <!-- 底部提交按钮 -->
    <view class="submit-btn-container">
      <view class="flex items-center">
        <view class="price-display flex-1">
          <text class="text-26rpx text-grey">费用：</text>
          <text class="text-36rpx font-500 text-primary ml-10rpx"
            >¥{{ totalFee }}</text
          >
        </view>
        <view class="post-btn" @tap="submitJob">立即发布</view>
      </view>
    </view>

    <!-- 选择器弹出层 -->
    <uni-popup
      ref="popup"
      :safe-area="true"
      background-color="#fff"
      type="bottom"
    >
      <view class="popup-content">
        <view
          class="popup-header flex justify-between items-center px-30rpx py-20rpx"
        >
          <text class="text-32rpx font-500">{{ popupTitle }}</text>
          <text class="close-btn i-carbon-close" @tap="hidePopup"></text>
        </view>

        <!-- 职位类别选择器 -->
        <view
          v-if="pickerType === 'category'"
          class="category-picker-container"
        >
          <JobCategorySelector
            v-model="jobForm.categoryText"
            :category-id="jobForm.categoryId"
            @update:category-id="(val) => (jobForm.categoryId = val)"
            @change="handleCategoryChange"
          />
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  jobCategories,
  salaryRanges,
  durationOptions,
  premiumServices,
} from "@/constants/static/job";
import {
  experienceOptions,
  educationOptions,
  welfareOptions,
} from "@/constants/standards";
import JobTypeSelector from "@/components/job/JobTypeSelector.vue";
import SalarySelector from "@/components/job/SalarySelector.vue";
import EducationSelector from "@/components/job/EducationSelector.vue";
import JobCategorySelector from "@/components/job/JobCategorySelector.vue";

// uni-popup 组件引用
const popup = ref(null);
const pickerType = ref("");
const popupTitle = ref("");

// 是否已认证
const isAuthenticated = ref(false);

// 职位表单数据
const jobForm = reactive({
  title: "",
  jobType: 1, // 默认全职
  categoryId: 0,
  categoryText: "",
  salaryId: 0,
  salaryText: "",
  salaryType: "",
  isCustomSalary: false, // 是否使用自定义薪资
  customSalaryMin: "", // 自定义最低薪资
  customSalaryMax: "", // 自定义最高薪资
  location: "", // 定位地址
  latitude: 0, // 纬度
  longitude: 0, // 经度
  addr_name: "", // 地址名称
  address: "", // 地址
  locationDetail: "", // 详细地址
  contactPhone: "",
  educationId: 0,
  educationText: "",
  experienceId: 0,
  experienceText: "",
  description: "",
  welfare: [] as string[],
  premium: {
    topPlacement: false,
    urgent: false,
    analytics: false,
  },
});

// 选中的发布时长
const selectedDuration = ref(7);

// 处理职位类别变更
const handleCategoryChange = (category) => {
  if (category) {
    jobForm.categoryId = category.id;
    jobForm.categoryText = category.fullName;
    hidePopup();
  }
};

// 打开地图选择位置
const openLocationChooser = () => {
  uni.showLoading({
    title: "加载位置中...",
  });

  uni.getLocation({
    type: "gcj02",
    success: (res) => {
      uni.hideLoading();
      // 打开地图选择页面
      uni.chooseLocation({
        latitude: res.latitude,
        longitude: res.longitude,
        success: (result) => {
          console.log("选择位置结果:", result);
          // 设置位置信息
          jobForm.latitude = res.latitude;
          jobForm.longitude = res.longitude;
          jobForm.addr_name = result.name;
          jobForm.address = result.address;
        },
      });
    },
    fail: (err) => {
      uni.hideLoading();
      console.log("获取位置失败:", err);
      // 即使获取当前位置失败，也允许用户手动选择位置
      uni.chooseLocation({
        success: (result) => {
          jobForm.location = result.address;
        },
      });
    },
  });
};

// 处理地址输入框聚焦
const handleAddressInputFocus = () => {
  // 确保详细地址输入区域可见
  setTimeout(() => {
    uni.pageScrollTo({
      scrollTop: 300, // 减小滚动位置，避免输入框被遮挡
      duration: 300,
    });
  }, 100);
};

// 选择工作经验
const selectExperience = (experience: any) => {
  jobForm.experienceId = experience.id;
  jobForm.experienceText = experience.name;
};

// 切换福利选项
const toggleWelfare = (welfare: string) => {
  const index = jobForm.welfare.indexOf(welfare);
  if (index === -1) {
    jobForm.welfare.push(welfare);
  } else {
    jobForm.welfare.splice(index, 1);
  }
};

// 选择发布时长
const selectDuration = (duration: number) => {
  selectedDuration.value = duration;
};

// 切换高级功能
const togglePremium = (key: string, e: any) => {
  jobForm.premium[key] = e.detail.value;
};

// 基础费用
const baseFee = computed(() => {
  if (selectedDuration.value === 7) return 0;
  if (selectedDuration.value === 15) return 29;
  if (selectedDuration.value === 30) return 49;
  if (selectedDuration.value === 90) return 99;
  return 0;
});

// 计算各项增值服务费用
const premiumFees = computed<Record<string, number>>(() => {
  return {
    topPlacement: jobForm.premium.topPlacement
      ? premiumServices.topPlacement.price
      : 0,
    urgent: jobForm.premium.urgent ? premiumServices.urgent.price : 0,
    analytics: jobForm.premium.analytics ? premiumServices.analytics.price : 0,
  };
});

// 活跃的增值服务项目
const activePremiumItems = computed(() => {
  const result = [];
  if (premiumFees.value.topPlacement > 0) {
    result.push({
      key: "topPlacement",
      name: premiumServices.topPlacement.name,
      price: premiumFees.value.topPlacement,
    });
  }
  if (premiumFees.value.urgent > 0) {
    result.push({
      key: "urgent",
      name: premiumServices.urgent.name,
      price: premiumFees.value.urgent,
    });
  }
  if (premiumFees.value.analytics > 0) {
    result.push({
      key: "analytics",
      name: premiumServices.analytics.name,
      price: premiumFees.value.analytics,
    });
  }
  return result;
});

// 总费用
const totalFee = computed(() => {
  return (
    baseFee.value +
    premiumFees.value.topPlacement +
    premiumFees.value.urgent +
    premiumFees.value.analytics
  );
});

// 显示职位类别选择器
const showCategoryPicker = () => {
  pickerType.value = "category";
  popupTitle.value = "选择职位类别";
  if (popup.value) {
    popup.value.open();
  }
};
// 隐藏弹出层
const hidePopup = () => {
  if (popup.value) {
    popup.value.close();
  }
};

// 跳转到认证页面
const goToAuth = () => {
  uni.navigateTo({
    url: "/pages/job/company-auth",
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 提交职位
const submitJob = () => {
  // 表单验证
  if (!jobForm.title) {
    uni.showToast({
      title: "请输入职位名称",
      icon: "none",
    });
    return;
  }

  if (!jobForm.categoryText) {
    uni.showToast({
      title: "请选择职位类别",
      icon: "none",
    });
    return;
  }

  if (!jobForm.salaryText) {
    uni.showToast({
      title: "请选择薪资范围",
      icon: "none",
    });
    return;
  }

  if (!jobForm.salaryType) {
    uni.showToast({
      title: "请选择薪资类型",
      icon: "none",
    });
    return;
  }

  if (!jobForm.location) {
    uni.showToast({
      title: "请选择工作地点",
      icon: "none",
    });
    return;
  }

  if (!jobForm.locationDetail) {
    uni.showToast({
      title: "请输入门牌号",
      icon: "none",
    });
    return;
  }

  if (!jobForm.contactPhone) {
    uni.showToast({
      title: "请输入联系电话",
      icon: "none",
    });
    return;
  }

  if (!jobForm.educationText) {
    uni.showToast({
      title: "请选择学历要求",
      icon: "none",
    });
    return;
  }

  if (!jobForm.experienceText) {
    uni.showToast({
      title: "请选择经验要求",
      icon: "none",
    });
    return;
  }

  if (!jobForm.description || jobForm.description.length < 50) {
    uni.showToast({
      title: "请详细描述职位信息，不少于50字",
      icon: "none",
    });
    return;
  }

  // TODO: 提交职位信息到服务器

  uni.showToast({
    title: "职位发布成功",
    icon: "success",
    duration: 2000,
    success: () => {
      setTimeout(() => {
        if (isAuthenticated.value) {
          // 如果已认证，跳转到职位管理页面
          uni.navigateTo({
            url: "/pages/job/manage",
          });
        } else {
          // 未认证，跳转回上一页
          uni.navigateBack();
        }
      }, 2000);
    },
  });
};

// 检查认证状态
onMounted(() => {
  // TODO: 实际项目中应从服务器或本地缓存获取认证状态
  isAuthenticated.value = false;
});

// 处理职位类型变更
const handleJobTypeChange = (type) => {
  console.log("职位类型变更为:", type.label);
  // 可以在这里根据职位类型做一些额外处理
};
</script>

<style lang="scss" scoped>
.container {
  // margin-top: 20rpx;

  .form-section {
    .form-item {
      border-bottom: 1rpx solid var(--border-color);

      .form-label {
        color: var(--text-info);
      }
    }
  }

  .border-bottom {
    border-bottom: 1rpx solid #f0f0f0;
  }

  .border-bottom-light {
    border-bottom: 1rpx dashed #f0f0f0;
  }

  .border-top-light {
    border-top: 1rpx solid #f0f0f0;
  }

  .form-input {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 20rpx;
    background-color: var(--bg-input);
    border-radius: var(--radius-2);
    border: 1rpx solid var(--border-color);
    box-sizing: border-box;
  }

  .form-selector {
    width: 100%;
    height: 80rpx;
    padding: 0 20rpx;
    background-color: var(--bg-input);
    border-radius: var(--radius-2);
    border: 1rpx solid var(--border-color);
  }

  .color-placeholder {
    color: var(--text-grey);
  }

  .placeholder-style {
    color: var(--text-grey);
  }

  .description-textarea {
    width: 100%;
    height: 250rpx;
    line-height: 1.6;
    background-color: var(--bg-input);
    border-radius: var(--radius-2);
    padding: 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
    border: 1rpx solid var(--border-color);
  }

  .active-option {
    background-color: rgba(255, 109, 0, 0.1);
    color: var(--primary);
  }

  .duration-options {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16rpx;
  }

  .duration-option {
    border: 1rpx solid var(--border-color);
    border-radius: var(--radius-2);
    padding: 16rpx 10rpx;
    text-align: center;
    transition: all 0.3s;
  }

  .active-duration-option {
    border-color: var(--primary);
    background-color: rgba(255, 109, 0, 0.05);
  }

  .duration-option-name {
    font-size: 28rpx;

    margin-bottom: 8rpx;
  }

  .duration-option-price {
    font-size: 24rpx;
    color: var(--primary);
  }

  .premium-icon {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
  }

  .auth-btn {
    display: inline-block;
    padding: 8rpx 24rpx;
    background-color: var(--primary);
    color: var(--text-inverse);
    border-radius: 30rpx;
    font-size: 24rpx;
  }

  .submit-btn-container {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    padding: 20rpx 30rpx;
    padding-bottom: 60rpx;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    z-index: 99;
  }

  .post-btn {
    width: 200rpx;
    height: 80rpx;
    line-height: 80rpx;
    background-color: var(--primary);
    color: var(--text-inverse);
    text-align: center;
    border-radius: 40rpx;
    font-size: 30rpx;
    font-weight: bold;
  }

  .bottom-padding {
    height: 180rpx; /* 增加底部内容的间距，避免被底部栏遮挡 */
  }

  .text-primary {
    color: var(--primary);
  }

  .text-inverse {
    color: var(--text-inverse);
  }

  /* 弹出层样式 */
  .popup-content {
    background-color: #fff;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    max-height: 80vh;
    overflow: hidden;
  }

  .popup-header {
    border-bottom: 1rpx solid #f0f0f0;
  }

  .close-btn {
    font-size: 40rpx;
    color: var(--text-grey);
  }

  /* 职位类别选择器容器 */
  .category-picker-container {
    height: 80vh;
  }

  /* 确保弹出层有足够高度显示选择器 */
  .tui-picker__box {
    height: auto !important;
  }

  .tui-picker__view {
    height: 480rpx !important;
    overflow: hidden;
  }

  /* 列表选择器样式 */
  .list-select {
    background-color: #fff;
  }

  .list-item {
    border-bottom: 1rpx solid #f0f0f0;
  }

  .selected-list-item {
    color: var(--primary);
    font-weight: bold;
    background-color: rgba(255, 109, 0, 0.05);
  }

  /* 地址输入相关样式 */
  .location-input-wrap {
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .location-current {
    padding: 20rpx 12rpx;
    border-radius: 8rpx;
    margin-bottom: 30rpx; /* 增加下方间距 */
  }

  .location-detail-input {
    width: 100%;
    margin-top: 10rpx;
    position: relative;
    z-index: 1;
  }

  .flex-col {
    display: flex;
    flex-direction: column;
  }

  .flex-x-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .flex-y {
    display: flex;
    flex-direction: column;
  }

  .flex-x {
    display: flex;
    align-items: center;
  }

  .hot-cities-section {
    padding: 20rpx 30rpx;
  }

  .hot-cities-title {
    font-size: 28rpx;
    font-weight: bold;

    margin-bottom: 20rpx;
  }

  .hot-cities-list {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10rpx;
  }
  .province-list-title {
    font-size: 28rpx;
    font-weight: bold;

    padding: 20rpx 30rpx;
  }
}
</style>

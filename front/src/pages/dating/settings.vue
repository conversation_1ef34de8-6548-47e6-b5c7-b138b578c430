<template>
  <view class="settings-container">
    <CustomNavBar title="偏好设置" />

    <view class="setting-group">
      <view class="setting-item">
        <text class="label">年龄范围</text>
        <view class="value">{{ ageRange[0] }} - {{ ageRange[1] }}岁</view>
      </view>
      <view class="slider-wrapper">
        <tui-slider
          :value="ageRange"
          :min="18"
          :max="60"
          :step="1"
          @change="onAgeRangeChange"
        />
      </view>
    </view>

    <view class="setting-group">
      <view class="setting-item">
        <text class="label">距离</text>
        <view class="value">{{ distance }}km</view>
      </view>
      <view class="slider-wrapper">
        <tui-slider
          v-model="distance"
          :min="0"
          :max="100"
          :step="1"
          @change="onDistanceChange"
        />
      </view>
    </view>

    <view class="setting-group">
      <tui-list-cell>
        <view class="flex-x-between">
          <text class="label">只看同城</text>
          <switch :checked="onlySameCity" @change="onSameCityChange" />
        </view>
      </tui-list-cell>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import CustomNavBar from "@/components/CustomNavBar.vue";

const ageRange = ref([22, 35]);
const distance = ref(50);
const onlySameCity = ref(true);

const onAgeRangeChange = (e: any) => {
  ageRange.value = e.detail.value;
};

const onDistanceChange = (e: any) => {
  distance.value = e.detail.value;
};

const onSameCityChange = (e: any) => {
  onlySameCity.value = e.detail.value;
};
</script>

<style scoped lang="scss">
.settings-container {
  background-color: #f4f5f9;
  min-height: 100vh;
}

.setting-group {
  background-color: #fff;
  margin-top: 20rpx;
  padding: 20rpx 30rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.label {
  font-size: 30rpx;
}

.value {
  font-size: 28rpx;
  color: #999;
}

.slider-wrapper {
  padding: 0 10rpx;
}
</style>

<template>
  <view class="user-detail-container">
    <!-- 自定义导航栏 -->
    <uni-nav-bar
      :fixed="true"
      :border="false"
      status-bar="true"
      background-color="rgba(255,255,255,0.95)"
      left-icon="back"
      @clickLeft="goBack"
    >
      <template #left>
        <view class="nav-icon">
          <uni-icons type="back" size="22" color="#333"></uni-icons>
        </view>
      </template>
      <template #right>
        <view class="nav-icon" @click="showMore">
          <uni-icons type="more-filled" size="22" color="#333"></uni-icons>
        </view>
      </template>
    </uni-nav-bar>

    <scroll-view scroll-y class="detail-content">
      <!-- 用户图片展示区 -->
      <view class="image-section">
        <swiper
          class="image-swiper"
          :indicator-dots="true"
          :autoplay="false"
          indicator-color="rgba(255,255,255,0.5)"
          indicator-active-color="#fff"
          :current="currentImageIndex"
          @change="onImageChange"
        >
          <swiper-item v-for="(image, index) in userInfo.pictures" :key="index">
            <image :src="image" class="detail-image" mode="aspectFill" />
          </swiper-item>
        </swiper>
      </view>

      <!-- 用户基本信息 -->
      <view class="info-section">
        <view class="basic-info">
          <text class="user-name">{{ userInfo.name }}, {{ userInfo.age }}</text>
          <view class="user-distance">
            <tui-icon name="position" color="#999" :size="16"></tui-icon>
            <text>距离你 {{ userInfo.distance }}km</text>
          </view>
        </view>

        <!-- 用户描述 -->
        <view class="description-card" v-if="userInfo.description">
          <text class="description-text">{{ userInfo.description }}</text>
        </view>

        <!-- 兴趣标签 -->
        <view
          class="interests-card"
          v-if="userInfo.tags && userInfo.tags.length"
        >
          <text class="card-title">兴趣爱好</text>
          <view class="tags-container">
            <view v-for="tag in userInfo.tags" :key="tag" class="interest-tag">
              {{ tag }}
            </view>
          </view>
        </view>

        <!-- 详细信息卡片 -->
        <view class="details-card">
          <text class="card-title">基本信息</text>
          <view class="detail-item">
            <text class="detail-label">年龄</text>
            <text class="detail-value">{{ userInfo.age }}岁</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">身高</text>
            <text class="detail-value">{{ userInfo.height || "未填写" }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">职业</text>
            <text class="detail-value">{{ userInfo.job || "未填写" }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">学历</text>
            <text class="detail-value">{{
              userInfo.education || "未填写"
            }}</text>
          </view>
        </view>

        <!-- 空白区域，防止被底部按钮遮挡 -->
        <view class="bottom-spacer"></view>
      </view>
    </scroll-view>

    <!-- 底部操作按钮 -->
    <view class="action-bar">
      <view class="action-btn dislike-btn" @click="handleAction('dislike')">
        <image src="/static/svg/dating/close.svg" class="action-icon" />
      </view>
      <view class="action-btn like-btn" @click="handleAction('like')">
        <image src="/static/svg/dating/like.svg" class="action-icon" />
      </view>
      <view class="action-btn superlike-btn" @click="handleAction('superlike')">
        <image src="/static/svg/dating/super-like.svg" class="action-icon" />
      </view>
      <view class="action-btn message-btn" @click="sendMessage">
        <uni-icons type="chat" size="30" color="#fff"></uni-icons>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

interface UserDetail {
  id: number;
  name: string;
  age: number;
  distance: number;
  pictures: string[];
  tags: string[];
  description?: string;
  height?: string;
  job?: string;
  education?: string;
}

const userInfo = ref<UserDetail>({
  id: 1,
  name: "Kristin",
  age: 23,
  distance: 5,
  pictures: [],
  tags: [],
});

const currentImageIndex = ref(0);

// 模拟用户数据 - 实际应用中应该从API获取
const mockUserData: Record<number, UserDetail> = {
  1: {
    id: 1,
    name: "Kristin",
    age: 23,
    distance: 5,
    pictures: [
      "https://images.unsplash.com/photo-1520466809213-7b9a56adcd45?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=880&q=80",
      "https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80",
      "https://images.unsplash.com/photo-1494790108755-2616b68e3e97?ixlib=rb-4.0.3&auto=format&fit=crop&w=687&q=80",
    ],
    tags: ["Music", "Anime", "Reading", "Photography"],
    description:
      "喜欢音乐和阅读，寻找有趣的灵魂。热爱生活，享受每一个美好的瞬间。",
    height: "165cm",
    job: "设计师",
    education: "本科",
  },
  2: {
    id: 2,
    name: "Jessica",
    age: 25,
    distance: 2,
    pictures: [
      "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=761&q=80",
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80",
    ],
    tags: ["Travel", "Foodie", "Yoga"],
    description: "环球旅行爱好者，美食探索家。希望找到可以一起看世界的人。",
    height: "168cm",
    job: "旅游博主",
    education: "硕士",
  },
  3: {
    id: 3,
    name: "Amanda",
    age: 21,
    distance: 8,
    pictures: [
      "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=688&q=80",
      "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80",
    ],
    tags: ["Movies", "Hiking", "Art"],
    description: "电影迷和户外运动爱好者，寻找志同道合的朋友。",
    height: "162cm",
    job: "影视编剧",
    education: "本科",
  },
  4: {
    id: 4,
    name: "Emma",
    age: 26,
    distance: 12,
    pictures: [
      "https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=687&q=80",
      "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1471&q=80",
    ],
    tags: ["Yoga", "Photography"],
    description: "瑜伽教练，摄影爱好者。致力于帮助他人保持身心健康。",
    height: "170cm",
    job: "瑜伽教练",
    education: "专科",
  },
};

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1] as any;
  const options = currentPage?.options;

  if (options?.userId) {
    const userId = parseInt(options.userId);
    userInfo.value = mockUserData[userId] || userInfo.value;
  }
});

const onImageChange = (e: any) => {
  currentImageIndex.value = e.detail.current;
};

const handleAction = (action: "like" | "dislike" | "superlike") => {
  let message = "";
  switch (action) {
    case "like":
      message = "喜欢 ❤️";
      break;
    case "dislike":
      message = "不喜欢";
      break;
    case "superlike":
      message = "超级喜欢 ⭐";
      break;
  }

  uni.showToast({
    title: message,
    icon: "none",
    duration: 1500,
  });

  setTimeout(() => {
    goBack();
  }, 1500);
};

const sendMessage = () => {
  uni.showModal({
    title: "发送消息",
    content: `向 ${userInfo.value.name} 发送消息？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: "消息已发送",
          icon: "success",
        });
      }
    },
  });
};

const showMore = () => {
  uni.showActionSheet({
    itemList: ["举报用户", "拉黑用户"],
    success: (res) => {
      if (res.tapIndex === 0) {
        uni.showToast({ title: "举报成功", icon: "success" });
      } else if (res.tapIndex === 1) {
        uni.showToast({ title: "已拉黑", icon: "success" });
      }
    },
  });
};

const goBack = () => {
  uni.navigateBack();
};
</script>

<style scoped lang="scss">
.user-detail-container {
  background-color: #f4f5f9;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.nav-icon {
  width: 68rpx;
  height: 68rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
}

.detail-content {
  flex: 1;
  height: calc(100vh - 88px - 140rpx);
}

.image-section {
  height: 60vh;
  position: relative;
}

.image-swiper {
  height: 100%;
  width: 100%;
}

.detail-image {
  width: 100%;
  height: 100%;
}

.info-section {
  background-color: #f4f5f9;
  margin-top: -40rpx;
  border-radius: 40rpx 40rpx 0 0;
  position: relative;
  z-index: 10;
  padding: 40rpx 30rpx 0;
}

.basic-info {
  margin-bottom: 30rpx;
}

.user-name {
  font-size: 56rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.user-distance {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #999;
  font-size: 28rpx;
}

.description-card,
.interests-card,
.details-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.description-text {
  font-size: 30rpx;
  line-height: 1.6;
  color: #666;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.interest-tag {
  background-color: #f0f8ff;
  color: #1890ff;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: 2rpx solid #e6f7ff;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.detail-label {
  font-size: 30rpx;
  color: #666;
}

.detail-value {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.bottom-spacer {
  height: 200rpx;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 30rpx);
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.9);
  }

  &.dislike-btn {
    background-color: #ff4d4f;
  }

  &.like-btn {
    background-color: #52c41a;
  }

  &.superlike-btn {
    background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  }

  &.message-btn {
    background-color: #1890ff;
  }
}

.action-icon {
  width: 50rpx;
  height: 50rpx;
}
</style>

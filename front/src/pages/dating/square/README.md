# 交友广场模块

## 功能概述

交友广场是一个完整的社区帖子模块，支持用户发布动态、浏览帖子、评论互动等功能。

## 页面结构

### 1. 广场首页 (`index.vue`)
- **功能**：帖子列表展示
- **特性**：
  - 使用 z-paging 实现高性能无限滚动
  - 瀑布流式帖子卡片布局
  - 悬浮发布按钮
  - 支持下拉刷新和上拉加载更多

### 2. 帖子详情页 (`detail.vue`)
- **功能**：查看帖子详情和评论
- **特性**：
  - 完整的帖子内容展示
  - 用户信息、关注、举报操作
  - 评论列表和回复功能
  - 点赞、评论交互
  - 图片预览功能

### 3. 发布页面 (`publish.vue`)
- **功能**：发布新动态
- **特性**：
  - 文本内容编辑（500字限制）
  - 图片上传（最多9张）
  - 话题选择（最多3个）
  - 位置信息添加
  - 隐私设置选择
  - 实时字符计数

### 4. 话题选择页 (`topic.vue`)
- **功能**：选择和搜索话题
- **特性**：
  - 热门话题推荐
  - 话题搜索功能
  - 最多选择3个话题
  - 已选话题管理
  - 实时搜索结果

## 组件说明

### PostItem 组件
- **位置**：`/components/dating/square/PostItem.vue`
- **功能**：帖子卡片展示
- **特性**：
  - 用户信息展示
  - 图片网格布局（1-4张图片）
  - 话题标签展示
  - 点赞评论交互
  - 关注和举报功能

### CommentList 组件
- **位置**：`/components/dating/square/CommentList.vue`
- **功能**：评论列表展示
- **特性**：
  - 多层嵌套回复支持
  - 评论点赞功能
  - 回复和举报操作
  - 用户头像点击跳转

### CommentInput 组件
- **位置**：`/components/dating/square/CommentInput.vue`
- **功能**：评论输入界面
- **特性**：
  - 自适应高度文本输入
  - 完整的 Emoji 表情选择器（100+表情）
  - 发送按钮状态控制
  - 现代化输入界面

### ReportModal 组件
- **位置**：`/components/dating/square/ReportModal.vue`
- **功能**：举报投诉弹窗
- **特性**：
  - 6种预设举报类型
  - 自定义举报原因输入
  - 字符计数和提交控制
  - 底部弹窗设计

## 技术特点

### 设计风格
- 现代化卡片设计
- 圆角、阴影、渐变效果
- 一致的色彩系统（主色 #8B5CF6）
- 流畅的微交互动画

### 性能优化
- z-paging 高性能列表
- 图片懒加载
- 组件化架构
- TypeScript 类型安全

### 交互体验
- 平滑动画过渡
- 即时反馈
- 响应式布局
- 用户友好的错误处理

## 使用方式

### 在交友模块中集成
```vue
<!-- 在 dating/index.vue 中 -->
<template>
  <view class="tab-content">
    <Square v-if="currentTab === 2" />
  </view>
</template>

<script>
import Square from './square/index.vue';
</script>
```

### 独立页面访问
```javascript
// 跳转到广场首页
uni.navigateTo({
  url: '/pages/dating/square/index'
});

// 跳转到发布页面
uni.navigateTo({
  url: '/pages/dating/square/publish'
});

// 跳转到帖子详情
uni.navigateTo({
  url: `/pages/dating/square/detail?id=${postId}`
});
```

## 数据流

### 帖子数据结构
```typescript
interface Post {
  id: number;
  user: User;
  content: string;
  images?: string[];
  topics: string[];
  likes: number;
  comments: number;
  isLiked: boolean;
  distance: string;
  timeAgo: string;
}
```

### 评论数据结构
```typescript
interface Comment {
  id: number;
  user: User;
  content: string;
  timeAgo: string;
  likes: number;
  isLiked: boolean;
  replies?: Comment[];
  replyTo?: string;
}
```

## 扩展功能

### 已实现
- ✅ 帖子发布和浏览
- ✅ 评论和回复
- ✅ 点赞功能
- ✅ 话题系统
- ✅ 图片上传和预览
- ✅ 举报投诉
- ✅ Emoji 表情
- ✅ 用户关注

### 待扩展
- 🔄 视频发布支持
- 🔄 帖子分享功能
- 🔄 私信功能
- 🔄 消息通知
- 🔄 内容审核
- 🔄 数据统计

## 注意事项

1. **图片处理**：确保图片压缩和格式转换
2. **内容审核**：建议接入内容安全检测
3. **性能监控**：关注列表滚动性能
4. **用户体验**：保持交互的即时反馈
5. **数据缓存**：合理使用本地缓存提升体验 
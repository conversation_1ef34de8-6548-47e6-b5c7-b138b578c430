<template>
  <view class="topic-container">
    <CustomNavBar title="选择话题" />
    
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <uni-icons type="search" size="20" color="#9CA3AF"></uni-icons>
        <input
          v-model="searchKeyword"
          class="search-input"
          placeholder="搜索话题..."
          @input="onSearchInput"
        />
        <view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
          <uni-icons type="clear" size="16" color="#9CA3AF"></uni-icons>
        </view>
      </view>
    </view>

    <scroll-view scroll-y class="content-scroll">
      <!-- 已选择的话题 -->
      <view v-if="selectedTopics.length" class="selected-section">
        <view class="section-header">
          <text class="section-title">已选择 ({{ selectedTopics.length }}/3)</text>
        </view>
        <view class="topic-list">
          <view 
            v-for="(topic, index) in selectedTopics" 
            :key="topic" 
            class="topic-item selected"
            @click="removeTopic(topic)"
          >
            <text class="topic-text">{{ topic }}</text>
            <uni-icons type="checkmarkempty" size="16" color="#8B5CF6"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 热门话题 -->
      <view v-if="!searchKeyword" class="hot-section">
        <view class="section-header">
          <text class="section-title">🔥 热门话题</text>
        </view>
        <view class="topic-list">
          <view 
            v-for="topic in hotTopics" 
            :key="topic" 
            class="topic-item"
            :class="{ selected: selectedTopics.includes(topic), disabled: !canSelectMore && !selectedTopics.includes(topic) }"
            @click="toggleTopic(topic)"
          >
            <text class="topic-text">{{ topic }}</text>
            <uni-icons 
              v-if="selectedTopics.includes(topic)"
              type="checkmarkempty" 
              size="16" 
              color="#8B5CF6"
            ></uni-icons>
          </view>
        </view>
      </view>

      <!-- 推荐话题 -->
      <view v-if="!searchKeyword" class="recommend-section">
        <view class="section-header">
          <text class="section-title">💡 推荐话题</text>
        </view>
        <view class="topic-list">
          <view 
            v-for="topic in recommendTopics" 
            :key="topic" 
            class="topic-item"
            :class="{ selected: selectedTopics.includes(topic), disabled: !canSelectMore && !selectedTopics.includes(topic) }"
            @click="toggleTopic(topic)"
          >
            <text class="topic-text">{{ topic }}</text>
            <uni-icons 
              v-if="selectedTopics.includes(topic)"
              type="checkmarkempty" 
              size="16" 
              color="#8B5CF6"
            ></uni-icons>
          </view>
        </view>
      </view>

      <!-- 搜索结果 -->
      <view v-if="searchKeyword" class="search-results">
        <view class="section-header">
          <text class="section-title">搜索结果</text>
        </view>
        <view v-if="searchResults.length" class="topic-list">
          <view 
            v-for="topic in searchResults" 
            :key="topic" 
            class="topic-item"
            :class="{ selected: selectedTopics.includes(topic), disabled: !canSelectMore && !selectedTopics.includes(topic) }"
            @click="toggleTopic(topic)"
          >
            <text class="topic-text">{{ topic }}</text>
            <uni-icons 
              v-if="selectedTopics.includes(topic)"
              type="checkmarkempty" 
              size="16" 
              color="#8B5CF6"
            ></uni-icons>
          </view>
        </view>
        <view v-else class="no-results">
          <text class="no-results-text">未找到相关话题</text>
          <text class="create-topic-hint">您可以在发布时创建新话题</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部确认按钮 -->
    <view class="footer">
      <button class="confirm-btn" @click="confirmSelection">
        确认选择 ({{ selectedTopics.length }})
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import CustomNavBar from '@/components/CustomNavBar.vue';

const searchKeyword = ref('');
const selectedTopics = ref<string[]>([]);
const searchResults = ref<string[]>([]);

const canSelectMore = computed(() => selectedTopics.value.length < 3);

// 热门话题
const hotTopics = [
  '#周末去哪儿', '#美食探店', '#健身打卡', '#旅行日记', '#摄影分享',
  '#读书笔记', '#电影推荐', '#音乐分享', '#宠物日常', '#工作日常',
  '#学习笔记', '#生活感悟', '#穿搭分享', '#化妆技巧', '#居家生活'
];

// 推荐话题
const recommendTopics = [
  '#户外运动', '#咖啡时光', '#夜景拍摄', '#早餐记录', '#下午茶',
  '#周末市集', '#艺术展览', '#音乐节', '#跑步日记', '#瑜伽练习',
  '#烘焙日记', '#花艺学习', '#手工制作', '#园艺生活', '#极简生活',
  '#数码科技', '#游戏时光', '#追剧日常', '#二次元', '#古风摄影'
];

// 所有话题（用于搜索）
const allTopics = [...hotTopics, ...recommendTopics, 
  '#街头摄影', '#日系风格', '#复古穿搭', '#minimalist', '#城市探索',
  '#深夜食堂', '#书店打卡', '#咖啡馆', '#独立书店', '#文艺青年',
  '#创业日记', '#职场生活', '#副业分享', '#理财记录', '#投资学习'
];

onLoad((options) => {
  // 获取已选择的话题
  if (options && options.selected) {
    try {
      const selected = JSON.parse(decodeURIComponent(options.selected));
      selectedTopics.value = selected;
    } catch (e) {
      console.error('解析已选话题失败:', e);
    }
  }
});

const onSearchInput = () => {
  if (!searchKeyword.value.trim()) {
    searchResults.value = [];
    return;
  }

  const keyword = searchKeyword.value.toLowerCase();
  searchResults.value = allTopics.filter(topic => 
    topic.toLowerCase().includes(keyword)
  );
};

const clearSearch = () => {
  searchKeyword.value = '';
  searchResults.value = [];
};

const toggleTopic = (topic: string) => {
  const index = selectedTopics.value.indexOf(topic);
  
  if (index > -1) {
    // 取消选择
    selectedTopics.value.splice(index, 1);
  } else {
    // 选择话题
    if (selectedTopics.value.length < 3) {
      selectedTopics.value.push(topic);
    } else {
      uni.showToast({
        title: '最多只能选择3个话题',
        icon: 'none'
      });
    }
  }
};

const removeTopic = (topic: string) => {
  const index = selectedTopics.value.indexOf(topic);
  if (index > -1) {
    selectedTopics.value.splice(index, 1);
  }
};

const confirmSelection = () => {
  // 通过事件通信传递选中的话题
  uni.$emit('topicSelected', selectedTopics.value);
  uni.navigateBack();
};
</script>

<style scoped lang="scss">
.topic-container {
  background-color: #f3f4f6;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.search-section {
  background-color: #fff;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F3F4F6;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #F9FAFB;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
  gap: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #374151;
  background: transparent;
  border: none;
  outline: none;

  &::placeholder {
    color: #9CA3AF;
  }
}

.clear-btn {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  
  &:active {
    background-color: #E5E7EB;
  }
}

.content-scroll {
  flex: 1;
  padding: 0 32rpx;
}

.selected-section,
.hot-section,
.recommend-section,
.search-results {
  margin: 24rpx 0;
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1F2937;
}

.topic-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.topic-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  background-color: #fff;
  border: 2rpx solid #E5E7EB;
  border-radius: 24rpx;
  padding: 16rpx 24rpx;
  transition: all 0.2s ease;

  &:active:not(.disabled) {
    transform: scale(0.98);
  }

  &.selected {
    background-color: #EEF2FF;
    border-color: #8B5CF6;
  }

  &.disabled {
    opacity: 0.5;
  }
}

.topic-text {
  font-size: 28rpx;
  color: #374151;
}

.selected .topic-text {
  color: #8B5CF6;
}

.no-results {
  text-align: center;
  padding: 80rpx 0;
}

.no-results-text {
  font-size: 30rpx;
  color: #6B7280;
  display: block;
  margin-bottom: 16rpx;
}

.create-topic-hint {
  font-size: 26rpx;
  color: #9CA3AF;
}

.footer {
  padding: 32rpx;
  background-color: #fff;
  border-top: 1rpx solid #F3F4F6;
}

.confirm-btn {
  width: 100%;
  background-color: #8B5CF6;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  padding: 28rpx;
  border-radius: 16rpx;
  line-height: 1;
  margin: 0;
  border: none;
  transition: all 0.3s ease;

  &:active {
    background-color: #7C3AED;
    transform: scale(0.98);
  }
}
</style> 
<template>
  <view class="dating-container">
    <!-- Content Area -->
    <view class="content">
      <Match v-show="currentTab === 0" />
      <Likes v-if="currentTab === 1" />
      <Square v-if="currentTab === 2" />
    </view>

    <!-- Custom Tab Bar -->
    <tui-tabbar
      :is-fixed="true"
      :tab-bar="tabbar"
      :current="currentTab"
      unlined
      @click="onTabClick"
      background-color="#fff"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import CustomNavBar from "@/components/CustomNavBar.vue";
import Match from "@/components/dating/Match.vue";
import Likes from "@/components/dating/Likes.vue";
import Square from "./square/index.vue";

const currentTab = ref(0);

const isMatchPage = computed(() => currentTab.value === 0);

const navBarTitle = computed(() => {
  if (currentTab.value === 1) return "我收到的喜欢";
  if (currentTab.value === 2) return "动态广场";
  return "";
});

const tabbar = ref([
  {
    pagePath: "",
    text: "匹配",
    iconPath: "/static/svg/dating/match.svg",
    selectedIconPath: "/static/svg/dating/match-selected.svg",
  },
  {
    pagePath: "",
    text: "喜欢",
    iconPath: "/static/svg/dating/likes.svg",
    selectedIconPath: "/static/svg/dating/likes-selected.svg",
  },
  {
    pagePath: "",
    text: "广场",
    iconPath: "/static/svg/dating/square.svg",
    selectedIconPath: "/static/svg/dating/square-selected.svg",
  },
  {
    pagePath: "/pages/message/conversation",
    text: "消息",
    iconPath: "/static/tabbar/chat.png",
    selectedIconPath: "/static/tabbar/chat-selected.png",
  },
  {
    pagePath: "/pages/dating/profile",
    text: "我的",
    iconPath: "/static/tabbar/mine.png",
    selectedIconPath: "/static/tabbar/mine-selected.png",
  },
]);

const onTabClick = (e: any) => {
  const index = e.index;
  const item = tabbar.value[index];

  if (item.pagePath) {
    uni.navigateTo({ url: item.pagePath });
  } else {
    currentTab.value = index;
  }
};

const goBack = () => {
  uni.navigateBack();
};
</script>

<style scoped lang="scss">
.dating-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  transition: background-color 0.3s ease;
}

.content {
  padding: 0;
  margin: 0;
  flex: 1;
  overflow: hidden; /* Hide overflow to prevent scroll issues with absolute children */
  height: calc(100vh - 200rpx);
}
</style>


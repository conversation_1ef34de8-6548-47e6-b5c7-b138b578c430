/**
 * Job Categories Static Data
 * 职位分类静态数据
 */

// 职位分类数据
export const jobCategories = [
    {
        id: 1,
        name: '互联网/IT',
        subCategories: [
            { id: 101, name: '程序开发' },
            { id: 102, name: '网络工程师' },
            { id: 103, name: '产品经理' },
            { id: 104, name: 'UI设计师' },
            { id: 105, name: '平面设计师' },
            { id: 106, name: '网站设计' },
            { id: 107, name: '网站运营' },
            { id: 108, name: '游戏开发' },
            { id: 109, name: '数据分析' },
            { id: 110, name: '电脑维修' }
        ]
    },
    {
        id: 2,
        name: '金融/财会',
        subCategories: [
            { id: 201, name: '会计' },
            { id: 202, name: '出纳' },
            { id: 203, name: '银行柜员' },
            { id: 204, name: '保险代理' },
            { id: 205, name: '贷款顾问' },
            { id: 206, name: '理财顾问' },
            { id: 207, name: '证券经纪' },
            { id: 208, name: '财务经理' },
            { id: 209, name: '审计/税务' }
        ]
    },
    {
        id: 3,
        name: '行政/人事',
        subCategories: [
            { id: 301, name: '行政专员' },
            { id: 302, name: '人事专员' },
            { id: 303, name: '前台接待' },
            { id: 304, name: '文员' },
            { id: 305, name: '招聘专员' },
            { id: 306, name: '行政助理' },
            { id: 307, name: '人力资源' },
            { id: 308, name: '培训专员' },
            { id: 309, name: '行政主管' }
        ]
    },
    {
        id: 4,
        name: '设计/创意',
        subCategories: [
            { id: 401, name: '平面设计' },
            { id: 402, name: '室内设计' },
            { id: 403, name: '广告设计' },
            { id: 404, name: '工业设计' },
            { id: 405, name: '美术设计' },
            { id: 406, name: '插画师' },
            { id: 407, name: '视觉设计' },
            { id: 408, name: '摄影师' }
        ]
    },
    {
        id: 5,
        name: '运营/编辑',
        subCategories: [
            { id: 501, name: '新媒体运营' },
            { id: 502, name: '电商运营' },
            { id: 503, name: '编辑/文案' },
            { id: 504, name: '市场推广' },
            { id: 505, name: '内容创作' },
            { id: 506, name: '活动策划' },
            { id: 507, name: '直播运营' },
            { id: 508, name: '视频剪辑' }
        ]
    },
    {
        id: 6,
        name: '房产/中介',
        subCategories: [
            { id: 601, name: '房产经纪人' },
            { id: 602, name: '置业顾问' },
            { id: 603, name: '房屋评估师' },
            { id: 604, name: '物业管理' },
            { id: 605, name: '小区保安' },
            { id: 606, name: '保洁绿化' },
            { id: 607, name: '房产销售' },
            { id: 608, name: '二手房销售' }
        ]
    },
    {
        id: 7,
        name: '销售/营业员',
        subCategories: [
            { id: 701, name: '销售代表' },
            { id: 702, name: '电话销售' },
            { id: 703, name: '导购员' },
            { id: 704, name: '销售顾问' },
            { id: 705, name: '业务员' },
            { id: 706, name: '销售主管' },
            { id: 707, name: '客户经理' },
            { id: 708, name: '门店店员' }
        ]
    },
    {
        id: 8,
        name: '教育/培训',
        subCategories: [
            { id: 801, name: '幼教/托管' },
            { id: 802, name: '小学老师' },
            { id: 803, name: '初高中老师' },
            { id: 804, name: '课后辅导' },
            { id: 805, name: '英语老师' },
            { id: 806, name: '音乐老师' },
            { id: 807, name: '美术老师' },
            { id: 808, name: '体育老师' },
            { id: 809, name: '舞蹈老师' },
            { id: 810, name: '培训班教师' }
        ]
    },
    {
        id: 9,
        name: '医疗/健康',
        subCategories: [
            { id: 901, name: '护士/护理' },
            { id: 902, name: '医生/医师' },
            { id: 903, name: '药店店员' },
            { id: 904, name: '理疗师' },
            { id: 905, name: '营养师' },
            { id: 906, name: '针灸推拿' },
            { id: 907, name: '医院前台' },
            { id: 908, name: '诊所助理' }
        ]
    },
    {
        id: 10,
        name: '酒店/旅游',
        subCategories: [
            { id: 1001, name: '前台接待' },
            { id: 1002, name: '客房服务' },
            { id: 1003, name: '酒店保安' },
            { id: 1004, name: '行李员' },
            { id: 1005, name: '导游' },
            { id: 1006, name: '旅游顾问' },
            { id: 1007, name: '酒店经理' },
            { id: 1008, name: '民宿管家' }
        ]
    },
    {
        id: 11,
        name: '餐饮/烹饪',
        subCategories: [
            { id: 1101, name: '厨师' },
            { id: 1102, name: '烧烤师傅' },
            { id: 1103, name: '面点师' },
            { id: 1104, name: '切配/打荷' },
            { id: 1105, name: '传菜员' },
            { id: 1106, name: '洗碗工' },
            { id: 1107, name: '服务员' },
            { id: 1108, name: '收银员' },
            { id: 1109, name: '店长/经理' },
            { id: 1110, name: '调酒师/茶艺师' },
            { id: 1111, name: '食堂员工' },
            { id: 1112, name: '配菜/打包' }
        ]
    },
    {
        id: 12,
        name: '零售/超市',
        subCategories: [
            { id: 1201, name: '营业员' },
            { id: 1202, name: '收银员' },
            { id: 1203, name: '导购员' },
            { id: 1204, name: '理货员' },
            { id: 1205, name: '促销/导购' },
            { id: 1206, name: '店长/主管' },
            { id: 1207, name: '防损员/保安' },
            { id: 1208, name: '采购员' },
            { id: 1209, name: '食品加工' }
        ]
    },
    {
        id: 13,
        name: '家政/保洁',
        subCategories: [
            { id: 1301, name: '保洁员' },
            { id: 1302, name: '保姆/护工' },
            { id: 1303, name: '月嫂/育婴师' },
            { id: 1304, name: '家电维修' },
            { id: 1305, name: '钟点工' },
            { id: 1306, name: '保安' },
            { id: 1307, name: '家政经理' },
            { id: 1308, name: '养老院护工' },
            { id: 1309, name: '家庭保洁' }
        ]
    },
    {
        id: 14,
        name: '美容/美发',
        subCategories: [
            { id: 1401, name: '美发师' },
            { id: 1402, name: '美容师' },
            { id: 1403, name: '美甲师' },
            { id: 1404, name: '按摩师' },
            { id: 1405, name: '美睫师' },
            { id: 1406, name: '洗头工' },
            { id: 1407, name: '发型助理' },
            { id: 1408, name: '美容顾问' },
            { id: 1409, name: '店长' }
        ]
    },
    {
        id: 15,
        name: '司机/物流',
        subCategories: [
            { id: 1501, name: '快递员' },
            { id: 1502, name: '送餐员' },
            { id: 1503, name: '货运司机' },
            { id: 1504, name: '出租车司机' },
            { id: 1505, name: '网约车司机' },
            { id: 1506, name: '物流仓管' },
            { id: 1507, name: '装卸工' },
            { id: 1508, name: '搬运工' },
            { id: 1509, name: '分拣员' }
        ]
    },
    {
        id: 16,
        name: '工厂/生产',
        subCategories: [
            { id: 1601, name: '普工/操作工' },
            { id: 1602, name: '组装工' },
            { id: 1603, name: '包装工' },
            { id: 1604, name: '质检员' },
            { id: 1605, name: '搬运工' },
            { id: 1606, name: '叉车工' },
            { id: 1607, name: '车间主管' },
            { id: 1608, name: '维修工' },
            { id: 1609, name: '电工' },
            { id: 1610, name: '木工/油漆工' }
        ]
    },
    {
        id: 17,
        name: '建筑/装修',
        subCategories: [
            { id: 1701, name: '泥水工' },
            { id: 1702, name: '木工' },
            { id: 1703, name: '油漆工' },
            { id: 1704, name: '电工' },
            { id: 1705, name: '水电工' },
            { id: 1706, name: '瓦工' },
            { id: 1707, name: '钢筋工' },
            { id: 1708, name: '施工员' },
            { id: 1709, name: '装修工' },
            { id: 1710, name: '小工/学徒' }
        ]
    },
    {
        id: 18,
        name: '农林牧渔',
        subCategories: [
            { id: 1801, name: '农场工人' },
            { id: 1802, name: '果园工人' },
            { id: 1803, name: '养殖人员' },
            { id: 1804, name: '饲养员' },
            { id: 1805, name: '花卉工人' },
            { id: 1806, name: '农业技术员' },
            { id: 1807, name: '畜牧兽医' },
            { id: 1808, name: '渔业水产' }
        ]
    },
    {
        id: 19,
        name: '能源/环保',
        subCategories: [
            { id: 1901, name: '电力工人' },
            { id: 1902, name: '水务工人' },
            { id: 1903, name: '燃气工人' },
            { id: 1904, name: '电工/电器维修' },
            { id: 1905, name: '环卫工人' },
            { id: 1906, name: '垃圾分类员' },
            { id: 1907, name: '回收站工作' },
            { id: 1908, name: '能源设备维护' }
        ]
    },
    {
        id: 20,
        name: '娱乐/休闲',
        subCategories: [
            { id: 2001, name: 'KTV服务员' },
            { id: 2002, name: '足浴按摩' },
            { id: 2003, name: '洗浴服务' },
            { id: 2004, name: '游戏陪玩' },
            { id: 2005, name: '网咖网管' },
            { id: 2006, name: '电影院服务员' },
            { id: 2007, name: '游乐园工作人员' },
            { id: 2008, name: '棋牌室服务员' }
        ]
    },
    {
        id: 21,
        name: '传媒/艺术',
        subCategories: [
            { id: 2101, name: '摄影师' },
            { id: 2102, name: '摄像师' },
            { id: 2103, name: '主持人' },
            { id: 2104, name: '播音员' },
            { id: 2105, name: '编剧' },
            { id: 2106, name: '演员/模特' },
            { id: 2107, name: '化妆师' },
            { id: 2108, name: '视频剪辑' }
        ]
    },
    {
        id: 22,
        name: '其他职业',
        subCategories: [
            { id: 2201, name: '保安' },
            { id: 2202, name: '传单派发' },
            { id: 2203, name: '临时工' },
            { id: 2204, name: '婚礼/庆典' },
            { id: 2205, name: '志愿者' },
            { id: 2206, name: '主播/直播' },
            { id: 2207, name: '家教' },
            { id: 2208, name: '其他' }
        ]
    }
];

// 职位类型数据
export const jobTypes = [
    {
        id: 'fulltime',
        label: '全职',
        value: 1,
        description: '长期稳定工作，通常每周工作40小时，享受完整福利待遇',
    },
    {
        id: 'parttime',
        label: '兼职',
        value: 2,
        description: '灵活时间安排，按小时计薪，适合学生或需要兼顾其他事务的人',
    },
    {
        id: 'temporary',
        label: '临时工',
        value: 3,
        description: '短期工作机会，通常用于应对季节性需求或特定项目',
    }
];

// 结算方式
export const salaryTypeOptions = [
    { id: 1, name: '月结' },
    { id: 2, name: '周结' },
    { id: 3, name: '日结' },
    { id: 4, name: '小时结' },
];

// 工作性质
export const jobNatureOptions = [
    { id: 1, name: '线下工作', description: '需要到固定工作地点办公' },
    { id: 2, name: '远程工作', description: '可以在任意地点办公，通过网络协作' },
    { id: 3, name: '混合办公', description: '线下和远程工作相结合的方式' },
];

// 工作方式
export const workModeOptions = [
    { id: 1, name: '做五休二', description: '周一至周五工作，周末休息' },
    { id: 2, name: '排班制', description: '按照排班表安排工作时间' },
    { id: 3, name: '倒班制', description: '轮流工作的班次制度' },
    { id: 4, name: '弹性工作制', description: '在规定范围内自由安排工作时间' },
    { id: 5, name: '大小周', description: '隔周单休和双休交替的工作制度' },
    { id: 6, name: '做六休一', description: '一周工作六天，休息一天' },
];

// 薪资范围
export const salaryRanges = [
    { id: 1, name: '3k以下' },
    { id: 2, name: '3k-5k' },
    { id: 3, name: '5k-7k' },
    { id: 4, name: '7k-10k' },
    { id: 5, name: '10k-15k' },
    { id: 6, name: '15k-20k' },
    { id: 7, name: '20k-30k' },
    { id: 8, name: '30k-50k' },
    { id: 9, name: '50k以上' },
    { id: 10, name: '面议' },
];

// 工作经验 - 已移至 @/constants/standards.ts
// import { experienceOptions } from '@/constants/standards'

// 最低学历 - 已移至 @/constants/standards.ts
// import { educationOptions } from '@/constants/standards'

// 福利标签 - 已移至 @/constants/standards.ts
// import { welfareOptions } from '@/constants/standards'

// 发布时长选项
export const durationOptions = [
    { value: 7, name: '7天', price: '免费' },
    { value: 15, name: '15天', price: '¥29' },
    { value: 30, name: '30天', price: '¥49' },
    { value: 90, name: '90天', price: '¥99' },
];

// 高级服务选项
export const premiumServices = {
    topPlacement: {
        name: '置顶推广',
        description: '让您的职位出现在搜索结果的前列',
        price: 199,
        icon: 'i-carbon-arrow-up',
        bgColor: 'bg-orange-500'
    },
    urgent: {
        name: '紧急招聘',
        description: '高亮显示，吸引更多求职者关注',
        price: 99,
        icon: 'i-carbon-warning',
        bgColor: 'bg-red-500'
    },
    analytics: {
        name: '数据分析',
        description: '查看职位浏览和应聘的详细数据',
        price: 49,
        icon: 'i-carbon-chart-line',
        bgColor: 'bg-blue-500'
    }
}; 
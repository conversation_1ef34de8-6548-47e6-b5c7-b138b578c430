/**
 * House Data Static Data
 * 房产相关静态数据
 */

import type { BaseHouseItem, SecondHandHouseItem, NewHouseItem, RentHouseItem, CommercialHouseItem } from '@/types/house';

// 城市数据
export const cityData = [
    { id: 1, name: '杭州市', hot: true },
    { id: 2, name: '温州市', hot: true },
    { id: 3, name: '宁波市', hot: true },
    { id: 4, name: '金华市', hot: false },
    { id: 5, name: '嘉兴市', hot: false },
    { id: 6, name: '台州市', hot: false },
    { id: 7, name: '绍兴市', hot: false },
    { id: 8, name: '湖州市', hot: false },
    { id: 9, name: '丽水市', hot: false },
    { id: 10, name: '衢州市', hot: false },
    { id: 11, name: '舟山市', hot: false },
];

// 区域数据（以杭州为例）
export const areaData = [
    {
        id: 1,
        name: '全城',
        subAreas: []
    },
    {
        id: 2,
        name: '热门商圈',
        subAreas: [
            { id: 21, name: '城西银泰', parentId: 2 },
            { id: 22, name: '西湖文化广场', parentId: 2 },
            { id: 23, name: '武林广场', parentId: 2 },
            { id: 24, name: '钱江新城', parentId: 2 },
            { id: 25, name: '滨江区政府', parentId: 2 },
        ]
    },
    {
        id: 3,
        name: '上城区',
        subAreas: [
            { id: 31, name: '清波街道', parentId: 3 },
            { id: 32, name: '湖滨街道', parentId: 3 },
            { id: 33, name: '小营街道', parentId: 3 },
        ]
    },
    {
        id: 4,
        name: '下城区',
        subAreas: [
            { id: 41, name: '武林街道', parentId: 4 },
            { id: 42, name: '天水街道', parentId: 4 },
            { id: 43, name: '长庆街道', parentId: 4 },
        ]
    },
    {
        id: 5,
        name: '西湖区',
        subAreas: [
            { id: 51, name: '灵隐街道', parentId: 5 },
            { id: 52, name: '西溪街道', parentId: 5 },
            { id: 53, name: '文新街道', parentId: 5 },
        ]
    },
    {
        id: 6,
        name: '拱墅区',
        subAreas: [
            { id: 61, name: '大关街道', parentId: 6 },
            { id: 62, name: '拱宸桥街道', parentId: 6 },
            { id: 63, name: '祥符街道', parentId: 6 },
        ]
    },
    {
        id: 7,
        name: '江干区',
        subAreas: [
            { id: 71, name: '笕桥街道', parentId: 7 },
            { id: 72, name: '凯旋街道', parentId: 7 },
            { id: 73, name: '钱江新城', parentId: 7 },
        ]
    },
    {
        id: 8,
        name: '滨江区',
        subAreas: [
            { id: 81, name: '西兴街道', parentId: 8 },
            { id: 82, name: '长河街道', parentId: 8 },
            { id: 83, name: '浦沿街道', parentId: 8 },
        ]
    },
    {
        id: 9,
        name: '余杭区',
        subAreas: [
            { id: 91, name: '临平街道', parentId: 9 },
            { id: 92, name: '南苑街道', parentId: 9 },
            { id: 93, name: '星桥街道', parentId: 9 },
        ]
    },
    {
        id: 10,
        name: '萧山区',
        subAreas: [
            { id: 101, name: '北干街道', parentId: 10 },
            { id: 102, name: '蜀山街道', parentId: 10 },
            { id: 103, name: '城厢街道', parentId: 10 },
        ]
    },
    {
        id: 11,
        name: '富阳区',
        subAreas: [
            { id: 111, name: '富春街道', parentId: 11 },
            { id: 112, name: '春江街道', parentId: 11 },
            { id: 113, name: '东洲街道', parentId: 11 },
        ]
    },
    {
        id: 12,
        name: '临安区',
        subAreas: [
            { id: 121, name: '锦城街道', parentId: 12 },
            { id: 122, name: '玲珑街道', parentId: 12 },
            { id: 123, name: '青山湖街道', parentId: 12 },
        ]
    },
];

// 租金范围
export const priceRanges = [
    { id: 1, name: '不限', min: 0, max: 999999 },
    { id: 2, name: '1000元以下', min: 0, max: 1000 },
    { id: 3, name: '1000-2000元', min: 1000, max: 2000 },
    { id: 4, name: '2000-3000元', min: 2000, max: 3000 },
    { id: 5, name: '3000-5000元', min: 3000, max: 5000 },
    { id: 6, name: '5000-8000元', min: 5000, max: 8000 },
    { id: 7, name: '8000元以上', min: 8000, max: 999999 },
    { id: 8, name: '自定义', min: 0, max: 0, custom: true }
];

// 户型选项
export const houseTypes = [
    { id: 1, name: '不限' },
    { id: 2, name: '一室' },
    { id: 3, name: '二室' },
    { id: 4, name: '三室' },
    { id: 5, name: '四室' },
    { id: 6, name: '四室以上' },
    { id: 7, name: '公寓' },
    { id: 8, name: '商住楼' },
    { id: 9, name: '别墅' }
];

// 房屋类型
export const propertyTypes = [
    { id: 1, name: '不限' },
    { id: 2, name: '普通住宅' },
    { id: 3, name: '公寓' },
    { id: 4, name: '别墅' },
    { id: 5, name: '农房' },
    { id: 6, name: '平房' },
    { id: 7, name: '四合院' },
];

// 租赁方式
export const rentTypes = [
    { id: 1, name: '不限' },
    { id: 2, name: '整租' },
    { id: 3, name: '合租' },
    { id: 4, name: '房东直租' },
    { id: 5, name: '中介租赁' }
];

// 装修情况
export const decorationTypes = [
    { id: 1, name: '不限' },
    { id: 2, name: '精装修' },
    { id: 3, name: '简装' },
    { id: 4, name: '毛坯' },
    { id: 5, name: '豪华装修' }
];

// 朝向选择
export const orientations = [
    { id: 1, name: '不限' },
    { id: 2, name: '东' },
    { id: 3, name: '南' },
    { id: 4, name: '西' },
    { id: 5, name: '北' },
    { id: 6, name: '东南' },
    { id: 7, name: '东北' },
    { id: 8, name: '西南' },
    { id: 9, name: '西北' },
    { id: 10, name: '南北通透' }
];

// 楼层
export const floorTypes = [
    { id: 1, name: '不限' },
    { id: 2, name: '低楼层', desc: '1-3层' },
    { id: 3, name: '中楼层', desc: '4-6层' },
    { id: 4, name: '高楼层', desc: '7层以上' },
];

// 房龄
export const houseAges = [
    { id: 1, name: '不限' },
    { id: 2, name: '5年以内' },
    { id: 3, name: '10年以内' },
    { id: 4, name: '15年以内' },
    { id: 5, name: '20年以内' },
    { id: 6, name: '20年以上' },
];

// 设施配置
export const facilities = [
    { id: 1, name: '无线网络', icon: 'i-carbon-wifi' },
    { id: 2, name: '电视', icon: 'i-carbon-tv' },
    { id: 3, name: '冰箱', icon: 'i-carbon-ice-vision' },
    { id: 4, name: '洗衣机', icon: 'i-carbon-machine-learning' },
    { id: 5, name: '空调', icon: 'i-carbon-air-conditioner' },
    { id: 6, name: '热水器', icon: 'i-carbon-temperature-hot' },
    { id: 7, name: '燃气灶', icon: 'i-carbon-fire' },
    { id: 8, name: '微波炉', icon: 'i-carbon-checkbox' },
    { id: 9, name: '油烟机', icon: 'i-carbon-map' },
    { id: 10, name: '电磁炉', icon: 'i-carbon-battery-charging' },
    { id: 11, name: '床', icon: 'i-carbon-sleeping' },
    { id: 12, name: '沙发', icon: 'i-carbon-template' },
    { id: 13, name: '衣柜', icon: 'i-carbon-folder' },
    { id: 14, name: '书桌', icon: 'i-carbon-desk' },
    { id: 15, name: '餐桌', icon: 'i-carbon-table' },
    { id: 16, name: '阳台', icon: 'i-carbon-sun' },
    { id: 17, name: '独卫', icon: 'i-carbon-toilet' },
    { id: 18, name: '车位', icon: 'i-carbon-car' }
];

// 租房租期
export const leaseTerms = [
    { id: 1, name: '不限' },
    { id: 2, name: '月租' },
    { id: 3, name: '1-3个月' },
    { id: 4, name: '4-6个月' },
    { id: 5, name: '7个月-1年' },
    { id: 6, name: '一年以上' },
];

// 房源特色标签
export const houseTags = [
    { id: 1, name: '近地铁', color: '#FF7D41' },
    { id: 2, name: '拎包入住', color: '#3B7CFF' },
    { id: 3, name: '押一付一', color: '#00B578' },
    { id: 4, name: '新上房源', color: '#F74F55' },
    { id: 5, name: '随时看房', color: '#6A36CA' },
    { id: 6, name: '可短租', color: '#FF7D41' },
    { id: 7, name: '带阳台', color: '#3B7CFF' },
    { id: 8, name: '独立卫生间', color: '#00B578' },
    { id: 9, name: '可养宠物', color: '#F74F55' },
    { id: 10, name: '首次出租', color: '#6A36CA' },
    { id: 11, name: '独立厨房', color: '#FF7D41' },
    { id: 12, name: '免押金', color: '#3B7CFF' },
    { id: 13, name: '南北通透', color: '#00B578' },
    { id: 14, name: '有电梯', color: '#F74F55' },
    { id: 15, name: '落地窗', color: '#6A36CA' },
];

// 支付方式
export const paymentMethods = [
    { id: 1, name: '不限' },
    { id: 2, name: '押一付一' },
    { id: 3, name: '押一付三' },
    { id: 4, name: '押二付一' },
    { id: 5, name: '押二付三' },
    { id: 6, name: '押一付六' },
    { id: 7, name: '半年付' },
    { id: 8, name: '年付' }
];

// 排序选项
export const sortOptions = [
    { id: 1, name: '默认排序' },
    { id: 2, name: '发布时间降序' },
    { id: 3, name: '价格升序' },
    { id: 4, name: '价格降序' },
    { id: 5, name: '面积升序' },
    { id: 6, name: '面积降序' }
];

// 标签选项
export const tagOptions = [
    { id: 1, name: '近地铁' },
    { id: 2, name: '精装修' },
    { id: 3, name: '押一付一' },
    { id: 4, name: '独卫' },
    { id: 5, name: '近商圈' },
    { id: 6, name: '近学校' },
    { id: 7, name: '安静' },
    { id: 8, name: '复式' },
    { id: 9, name: '南北通透' },
    { id: 10, name: '带阳台' },
    { id: 11, name: '可短租' }
];

// 租房攻略分类
export const rentalGuideCategories = [
    { id: 1, name: '新手租房' },
    { id: 2, name: '租房陷阱' },
    { id: 3, name: '合同签订' },
    { id: 4, name: '房屋维修' },
    { id: 5, name: '租客权益' },
];

// 模拟地铁线路数据
export const metroLines = [
    { id: 1, name: '1号线', color: '#D4237A' },
    { id: 2, name: '2号线', color: '#008B95' },
    { id: 3, name: '3号线', color: '#DE5203' },
    { id: 4, name: '4号线', color: '#008F3B' },
    { id: 5, name: '5号线', color: '#A13A94' },
    { id: 6, name: '6号线', color: '#8B5AAF' },
    { id: 7, name: '7号线', color: '#E5171D' },
    { id: 8, name: '8号线', color: '#0075C1' },
    { id: 9, name: '9号线', color: '#E2B656' },
    { id: 10, name: '10号线', color: '#B481BD' },
];

// 常用地点类型
export const placeTypes = [
    { id: 1, name: '地铁站', icon: 'i-carbon-train' },
    { id: 2, name: '学校', icon: 'i-carbon-education' },
    { id: 3, name: '医院', icon: 'i-carbon-hospital' },
    { id: 4, name: '商场', icon: 'i-carbon-shopping-bag' },
    { id: 5, name: '写字楼', icon: 'i-carbon-building' },
    { id: 6, name: '公园', icon: 'i-carbon-tree' },
    { id: 7, name: '银行', icon: 'i-carbon-money' },
];

// 房源配置图标
export const houseIcons = {
    area: 'i-carbon-area',
    bedroom: 'i-carbon-sleeping',
    bathroom: 'i-carbon-bath',
    livingroom: 'i-carbon-sofa',
    orientation: 'i-carbon-compass',
    floor: 'i-carbon-building',
};

// 模拟热门搜索
export const hotSearches = [
    '三室两厅',
    '押一付一',
    '精装修',
    '西湖边',
    '地铁口',
    '2000元以下',
    '可养宠物',
    '拎包入住'
];

// 模拟历史搜索
export const historySearches = [
    '城西银泰附近',
    '单身公寓',
    '西湖区两室',
    '地铁1号线',
    '朝南主卧',
    '武林广场附近'
];

// 房源列表数据
export const houseListData = [
    {
        id: 'h001',
        title: '阳光花园 精装修两居室 南北通透 拎包入住',
        images: [
            'https://picsum.photos/seed/house101/800/600',
            'https://picsum.photos/seed/house102/800/600',
            'https://picsum.photos/seed/house103/800/600',
            'https://picsum.photos/seed/house104/800/600',
        ],
        tags: ['整租', '精装', '押一付一'],
        location: '海淀区 - 五道口',
        distance: '距地铁13号线五道口站500米',
        price: 5800,
        priceUnit: '元/月',
        houseType: '2室1厅',
        area: 85,
        orientation: '南北',
        floor: '8/18层',
        decoration: '精装修',
        facilities: [1, 2, 3, 4, 5, 6, 7],
        releaseTime: '2023-10-25',
        viewCount: 156,
        isVip: true,
        isNew: true,
        description: '此房坐落于阳光花园小区，紧邻五道口地铁站，交通便利。小区环境优美，物业管理完善，安全性高。房屋精装修，家具家电齐全，拎包入住。采光充足，通风良好，是理想的居住选择。',
        rentType: '整租',
        paymentMethod: '押一付一',
        contactPerson: '王先生',
        contactPhone: '138****5678',
        viewingTime: '随时可看',
        checkInTime: '随时入住',
        addressDetail: '海淀区成府路28号阳光花园小区8号楼8层802室',
        estateInfo: {
            name: '阳光花园',
            buildYear: 2012,
            totalBuildings: 12,
            propertyFee: 2.5,
            propertyCompany: '海淀物业管理有限公司',
            developers: '北京建设开发公司',
        },
        surroundings: [
            { type: '交通', name: '五道口地铁站', distance: '500米' },
            { type: '教育', name: '清华大学', distance: '1.2公里' },
            { type: '教育', name: '五道口小学', distance: '800米' },
            { type: '购物', name: '五道口购物中心', distance: '600米' },
            { type: '餐饮', name: '海底捞火锅', distance: '700米' },
            { type: '医疗', name: '海淀社区医院', distance: '1公里' },
        ],
        location_coordinates: {
            latitude: 39.992736,
            longitude: 116.338639,
        }
    },
    {
        id: 'h002',
        title: '望京SOHO附近 一居室 近地铁 随时看房',
        images: [
            'https://picsum.photos/seed/house201/800/600',
            'https://picsum.photos/seed/house202/800/600',
            'https://picsum.photos/seed/house203/800/600',
        ],
        tags: ['合租', '押一付三'],
        location: '朝阳区 - 望京',
        distance: '距地铁15号线望京站800米',
        price: 3200,
        priceUnit: '元/月',
        houseType: '1室1厅',
        area: 45,
        orientation: '南',
        floor: '10/22层',
        decoration: '简装',
        facilities: [1, 3, 4, 5, 6],
        releaseTime: '2023-10-20',
        viewCount: 89,
        isVip: false,
        isNew: false,
        description: '房源位于望京SOHO附近，交通便利，生活设施完善。房间朝南，采光好。配有基础家具，可拎包入住。',
        rentType: '整租',
        paymentMethod: '押一付三',
        contactPerson: '李女士',
        contactPhone: '139****4321',
        viewingTime: '随时可看',
        checkInTime: '11月1日起',
        addressDetail: '朝阳区望京街道望京SOHO T3座1008室',
        estateInfo: {
            name: '望京SOHO',
            buildYear: 2014,
            totalBuildings: 3,
            propertyFee: 4.5,
            propertyCompany: '朝阳物业管理有限公司',
            developers: '北京SOHO中国',
        },
        surroundings: [
            { type: '交通', name: '望京地铁站', distance: '800米' },
            { type: '购物', name: '望京购物中心', distance: '500米' },
            { type: '餐饮', name: '星巴克咖啡', distance: '100米' },
            { type: '医疗', name: '望京医院', distance: '1.5公里' },
        ],
        location_coordinates: {
            latitude: 39.993537,
            longitude: 116.467377,
        }
    },
    {
        id: 'h003',
        title: '自如友家 · 天通苑北 三家合住 主卧朝南',
        images: [
            'https://picsum.photos/seed/house301/800/600',
            'https://picsum.photos/seed/house302/800/600',
            'https://picsum.photos/seed/house303/800/600',
        ],
        tags: ['合租', '自如管理', '独立卫浴'],
        location: '昌平区 - 天通苑',
        distance: '距地铁5号线天通苑站300米',
        price: 2600,
        priceUnit: '元/月',
        houseType: '3室1厅主卧',
        area: 20,
        orientation: '南',
        floor: '6/12层',
        decoration: '精装修',
        facilities: [1, 2, 3, 4, 5, 6, 7, 11, 14],
        releaseTime: '2023-10-24',
        viewCount: 112,
        isVip: true,
        isNew: true,
        description: '此房为自如友家，三居室中的主卧室，朝南采光好。室内配有独立卫生间，家具家电齐全，拎包入住。公共区域有客厅和厨房，合住氛围良好。',
        rentType: '合租',
        paymentMethod: '押一付三',
        contactPerson: '自如管家',
        contactPhone: '************',
        viewingTime: '预约看房',
        checkInTime: '随时入住',
        addressDetail: '昌平区天通苑北一区10号楼6层602室',
        estateInfo: {
            name: '天通苑北一区',
            buildYear: 2008,
            totalBuildings: 24,
            propertyFee: 1.8,
            propertyCompany: '天通苑物业',
            developers: '北京建工集团',
        },
        surroundings: [
            { type: '交通', name: '天通苑地铁站', distance: '300米' },
            { type: '购物', name: '华联商场', distance: '500米' },
            { type: '餐饮', name: '肯德基', distance: '400米' },
            { type: '教育', name: '天通苑小学', distance: '600米' },
        ],
        location_coordinates: {
            latitude: 40.063564,
            longitude: 116.419381,
        }
    }
];

/**
 * 生成指定数量的随机房源数据
 * @param count 需要生成的数量
 * @param type 房源类型: 'second'(二手房), 'new'(新房), 'rent'(租房), 'commercial'(商铺办公)
 * @returns 房源数据数组
 */
export function generateMoreHouses(count: number, type: string = 'second'): any[] {
    const result = [];

    for (let i = 0; i < count; i++) {
        let house;

        switch (type) {
            case 'second':
                house = generateSecondHouse(i);
                break;
            case 'new':
                house = generateNewHouse(i);
                break;
            case 'rent':
                house = generateRentHouse(i);
                break;
            case 'commercial':
                house = generateCommercialHouse(i);
                break;
            default:
                house = generateSecondHouse(i);
        }

        result.push(house);
    }

    return result;
}

/**
 * 生成二手房数据
 */
function generateSecondHouse(index: number): SecondHandHouseItem {
    const id = `second${index}`;
    const layout = `${Math.floor(Math.random() * 3) + 1}室${Math.floor(Math.random() * 2) + 1}厅`;
    const area = Math.floor(Math.random() * 100) + 50;
    const price = Math.floor(Math.random() * 500) + 300;

    return {
        id,
        title: `精装${layout} 南北通透 采光好`,
        image: `https://picsum.photos/seed/second${index}/300/200`,
        layout,
        area: area.toString(),
        direction: ["南北通透", "东西向", "朝南", "朝东"][Math.floor(Math.random() * 4)],
        floor: `${Math.floor(Math.random() * 20) + 1}层/${Math.floor(Math.random() * 30) + 20}层`,
        price,
        unitPrice: `${Math.floor(Math.random() * 20000) + 30000}元/㎡`,
        tags: ["满五年", "近地铁", "学区房", "南北通透", "精装修"].slice(0, Math.floor(Math.random() * 3) + 1),
        community: ["阳光小区", "丽都花园", "蓝天家园", "翠湖园"][Math.floor(Math.random() * 4)],
        rooms: parseInt(layout.split('室')[0]),
        halls: parseInt(layout.split('室')[1].split('厅')[0]),
    };
}

/**
 * 生成新房数据
 */
function generateNewHouse(index: number): NewHouseItem {
    const id = `new${index}`;
    const area = Math.floor(Math.random() * 50) + 80;

    return {
        id,
        name: `碧桂园·翡翠天境${Math.floor(Math.random() * 10) + 1}期`,
        image: `https://picsum.photos/seed/new${index}/300/200`,
        area: `${area}-${area + 50}`,
        location: ["朝阳区·CBD", "海淀区·中关村", "丰台区·科技园", "昌平区·回龙观"][Math.floor(Math.random() * 4)],
        price: Math.floor(Math.random() * 30000) + 20000,
        priceRange: `${Math.floor(Math.random() * 30000) + 20000}-${Math.floor(Math.random() * 20000) + 40000}元/㎡`,
        tags: ["近地铁", "学区房", "公园旁", "精装修", "品牌开发商"].slice(0, Math.floor(Math.random() * 3) + 1),
        status: ["在售", "即将开盘", "售罄"][Math.floor(Math.random() * 3)],
        buildTypes: ["高层", "洋房", "别墅"].slice(0, Math.floor(Math.random() * 2) + 1),
        openTime: `${Math.floor(Math.random() * 12) + 1}月${Math.floor(Math.random() * 28) + 1}日`,
        developer: ["碧桂园", "万科", "保利", "华润"][Math.floor(Math.random() * 4)],
        extraInfo: `关注${Math.floor(Math.random() * 1000) + 100}人`,
    };
}

/**
 * 生成租房数据
 */
function generateRentHouse(index: number): RentHouseItem {
    const id = `rent${index}`;
    const layout = `${Math.floor(Math.random() * 3) + 1}室${Math.floor(Math.random() * 2) + 1}厅`;
    const area = Math.floor(Math.random() * 50) + 30;
    const rentType = ["整租", "合租", "公寓"][Math.floor(Math.random() * 3)];

    return {
        id,
        title: `${rentType}·${["阳光嘉园", "丽都花园", "蓝天家园"][Math.floor(Math.random() * 3)]} ${layout}`,
        image: `https://picsum.photos/seed/rent${index}/300/200`,
        layout,
        area: area.toString(),
        direction: ["南北通透", "东西向", "朝南", "朝东"][Math.floor(Math.random() * 4)],
        floor: `${Math.floor(Math.random() * 20) + 1}层/${Math.floor(Math.random() * 30) + 20}层`,
        price: Math.floor(Math.random() * 3000) + 1000,
        rentType,
        paymentMethod: ["押一付一", "押一付三", "押二付三"][Math.floor(Math.random() * 3)],
        tags: ["拎包入住", "近地铁", "精装修", "家电齐全", "随时看房"].slice(0, Math.floor(Math.random() * 3) + 1),
        rooms: parseInt(layout.split('室')[0]),
        halls: parseInt(layout.split('室')[1].split('厅')[0]),
        extraInfo: `${Math.floor(Math.random() * 100) + 1}人看过`,
    };
}

/**
 * 生成商业地产数据
 */
function generateCommercialHouse(index: number): CommercialHouseItem {
    const id = `comm${index}`;
    const area = Math.floor(Math.random() * 500) + 50;
    const type = ["商铺", "写字楼", "厂房", "仓库"][Math.floor(Math.random() * 4)];
    const priceType = Math.random() > 0.5 ? 'rent' : 'sale';

    // 根据类型设置不同的标题模板
    let title;
    switch (type) {
        case "商铺":
            title = ["临街旺铺", "购物中心商铺", "社区底商", "商业街店面"][Math.floor(Math.random() * 4)];
            break;
        case "写字楼":
            title = ["甲级写字楼", "商务中心", "创意园区", "独栋办公"][Math.floor(Math.random() * 4)];
            break;
        case "厂房":
            title = ["标准厂房", "钢构厂房", "生产车间", "工业园区"][Math.floor(Math.random() * 4)];
            break;
        case "仓库":
            title = ["标准仓库", "物流中心", "冷库", "保税仓"][Math.floor(Math.random() * 4)];
            break;
        default:
            title = "商业地产";
    }

    return {
        id,
        title: `${["朝阳区", "海淀区", "丰台区", "昌平区"][Math.floor(Math.random() * 4)]} ${title}`,
        image: `https://picsum.photos/seed/comm${index}/300/200`,
        type,
        area: area.toString(),
        location: `${["商圈", "开发区", "产业园", "CBD"][Math.floor(Math.random() * 4)]}附近`,
        price: priceType === 'sale' ? (Math.floor(Math.random() * 500) + 100) : (Math.floor(Math.random() * 20000) + 3000),
        priceType,
        unitPrice: priceType === 'sale' ? `${Math.floor(Math.random() * 20000) + 10000}元/㎡` : null,
        tags: ["临街", "独立产权", "精装修", "近地铁", "高层", "配套齐全"].slice(0, Math.floor(Math.random() * 3) + 1),
        extraInfo: `${Math.floor(Math.random() * 100) + 1}人看过`,
    };
} 
// =====================================================
// 数据标准化常量定义
// =====================================================

// 性别标准
export const GENDER = {
  UNLIMITED: 0, // 不限
  MALE: 1,      // 男
  FEMALE: 2     // 女
} as const

// 年龄范围标准 (简化版)
export const AGE_RANGE = {
  UNLIMITED: 0, // 不限
  AGE_18_TO_30: 1, // 18-30岁
  AGE_31_TO_40: 2, // 31-40岁
  AGE_41_TO_50: 3, // 41-50岁
  AGE_50_PLUS: 4   // 50岁以上
} as const

// 学历标准 (移除博士)
export const EDUCATION = {
  UNLIMITED: 0,   // 不限
  JUNIOR_HIGH: 1, // 初中及以下
  HIGH_SCHOOL: 2, // 高中/中专
  ASSOCIATE: 3,   // 大专
  BACHELOR: 4,    // 本科
  MASTER: 5       // 硕士
} as const

// 工作经验标准
export const EXPERIENCE = {
  UNLIMITED: 0,       // 不限
  FRESH_GRADUATE: 1,  // 应届毕业生
  UNDER_1_YEAR: 2,    // 1年以下
  YEARS_1_TO_3: 3,    // 1-3年
  YEARS_3_TO_5: 4,    // 3-5年
  YEARS_5_TO_10: 5,   // 5-10年
  OVER_10_YEARS: 6    // 10年以上
} as const



// 正职薪资标准 (按月，单位：元)
export const JOB_SALARY = {
  UNLIMITED: 0,      // 不限
  RANGE_3K_TO_6K: 1, // 3K-6K (3000-6000元)
  RANGE_6K_TO_10K: 2, // 6K-10K (6000-10000元)
  RANGE_10K_TO_15K: 3, // 10K-15K (10000-15000元)
  RANGE_15K_TO_25K: 4, // 15K-25K (15000-25000元)
  OVER_25K: 5        // 25K以上 (25000元以上)
} as const

// 公司规模标准 (使用数值编码)
export const COMPANY_SIZE = {
  UNLIMITED: 0,  // 不限
  STARTUP: 1,    // 1-20人
  SMALL: 2,      // 21-100人
  MEDIUM: 3,     // 101-500人
  LARGE: 4,      // 501-1000人
  ENTERPRISE: 5  // 1000人以上
} as const

// 福利待遇标准 (JSON数组，适合中低端岗位)
export const WELFARE_BENEFITS = {
  // 基础保障类
  SOCIAL_INSURANCE: 1,        // 五险一金
  INSURANCE: 2,               // 五险
  ACCOMMODATION: 3,           // 包食宿
  FOOD: 4,                   // 包吃
  HOUSING: 5,                // 包住
  MEAL_ALLOWANCE: 6,         // 餐补

  // 薪酬奖励类
  ANNUAL_BONUS: 7,           // 年终奖
  PERFORMANCE_BONUS: 8,      // 绩效奖金
  FULL_ATTENDANCE: 9,        // 全勤奖
  OVERTIME_PAY: 10,          // 加班费

  // 时间福利类
  PAID_LEAVE: 11,            // 带薪年假
  FLEXIBLE_WORK: 12,         // 弹性工作
  WEEKEND_OFF: 13,           // 双休
  LEGAL_HOLIDAYS: 14,        // 法定节假日

  // 交通住宿类
  TRANSPORT_ALLOWANCE: 15,   // 交通补贴
  DORMITORY: 16,             // 宿舍
  SHUTTLE_BUS: 17,           // 班车接送

  // 发展培训类
  TRAINING: 18,              // 培训机会
  PROMOTION_PATH: 19,        // 晋升通道
  SKILL_TRAINING: 20,        // 技能培训

  // 生活关怀类
  HOLIDAY_BENEFITS: 21,      // 节日福利
  BIRTHDAY_BENEFIT: 22,      // 生日福利
  TEAM_BUILDING: 23,         // 团建活动
  EMPLOYEE_DISCOUNT: 24,     // 员工优惠

  // 健康医疗类
  MEDICAL_EXAM: 25,          // 体检
  COMMERCIAL_INSURANCE: 26,  // 商业保险

} as const

// =====================================================
// 标准选项数据结构
// =====================================================

export interface StandardOption {
  code: number
  label: string
  value: number
  min?: number  // 最小值（用于范围类型）
  max?: number  // 最大值（用于范围类型）
}

export interface SalaryRange {
  code: number
  min: number  // 最小值（元）
  max: number  // 最大值（元）
}

export interface AgeRange {
  code: number
  min: number  // 最小年龄
  max: number  // 最大年龄
}

export interface CompanySizeRange {
  code: number
  min: number  // 最小人数
  max: number  // 最大人数
}

// =====================================================
// 预定义选项数据
// =====================================================

// 性别选项
export const genderOptions: StandardOption[] = [
  { code: GENDER.UNLIMITED, label: '不限', value: GENDER.UNLIMITED },
  { code: GENDER.MALE, label: '男', value: GENDER.MALE },
  { code: GENDER.FEMALE, label: '女', value: GENDER.FEMALE }
]

// 年龄范围选项
export const ageRangeOptions: StandardOption[] = [
  { code: AGE_RANGE.UNLIMITED, label: '不限', value: AGE_RANGE.UNLIMITED },
  { code: AGE_RANGE.AGE_18_TO_30, label: '18-30岁', value: AGE_RANGE.AGE_18_TO_30 },
  { code: AGE_RANGE.AGE_31_TO_40, label: '31-40岁', value: AGE_RANGE.AGE_31_TO_40 },
  { code: AGE_RANGE.AGE_41_TO_50, label: '41-50岁', value: AGE_RANGE.AGE_41_TO_50 },
  { code: AGE_RANGE.AGE_50_PLUS, label: '50岁以上', value: AGE_RANGE.AGE_50_PLUS }
]

// 学历选项
export const educationOptions: StandardOption[] = [
  { code: EDUCATION.UNLIMITED, label: '不限', value: EDUCATION.UNLIMITED },
  { code: EDUCATION.JUNIOR_HIGH, label: '初中及以下', value: EDUCATION.JUNIOR_HIGH },
  { code: EDUCATION.HIGH_SCHOOL, label: '高中/中专', value: EDUCATION.HIGH_SCHOOL },
  { code: EDUCATION.ASSOCIATE, label: '大专', value: EDUCATION.ASSOCIATE },
  { code: EDUCATION.BACHELOR, label: '本科', value: EDUCATION.BACHELOR },
  { code: EDUCATION.MASTER, label: '硕士', value: EDUCATION.MASTER }
]

// 工作经验选项
export const experienceOptions: StandardOption[] = [
  { code: EXPERIENCE.UNLIMITED, label: '不限', value: EXPERIENCE.UNLIMITED },
  { code: EXPERIENCE.FRESH_GRADUATE, label: '应届毕业生', value: EXPERIENCE.FRESH_GRADUATE },
  { code: EXPERIENCE.UNDER_1_YEAR, label: '1年以下', value: EXPERIENCE.UNDER_1_YEAR },
  { code: EXPERIENCE.YEARS_1_TO_3, label: '1-3年', value: EXPERIENCE.YEARS_1_TO_3 },
  { code: EXPERIENCE.YEARS_3_TO_5, label: '3-5年', value: EXPERIENCE.YEARS_3_TO_5 },
  { code: EXPERIENCE.YEARS_5_TO_10, label: '5-10年', value: EXPERIENCE.YEARS_5_TO_10 },
  { code: EXPERIENCE.OVER_10_YEARS, label: '10年以上', value: EXPERIENCE.OVER_10_YEARS }
]

// 公司规模选项
export const companySizeOptions: StandardOption[] = [
  { code: COMPANY_SIZE.UNLIMITED, label: '不限', value: COMPANY_SIZE.UNLIMITED },
  { code: COMPANY_SIZE.STARTUP, label: '1-20人', value: COMPANY_SIZE.STARTUP, min: 1, max: 20 },
  { code: COMPANY_SIZE.SMALL, label: '21-100人', value: COMPANY_SIZE.SMALL, min: 21, max: 100 },
  { code: COMPANY_SIZE.MEDIUM, label: '101-500人', value: COMPANY_SIZE.MEDIUM, min: 101, max: 500 },
  { code: COMPANY_SIZE.LARGE, label: '501-1000人', value: COMPANY_SIZE.LARGE, min: 501, max: 1000 },
  { code: COMPANY_SIZE.ENTERPRISE, label: '1000人以上', value: COMPANY_SIZE.ENTERPRISE, min: 1001, max: 999999 }
]

// 福利待遇选项（多选，JSON数组）
export const welfareOptions: StandardOption[] = [
  // 基础保障类
  { code: WELFARE_BENEFITS.SOCIAL_INSURANCE, label: '五险一金', value: WELFARE_BENEFITS.SOCIAL_INSURANCE },
  { code: WELFARE_BENEFITS.INSURANCE, label: '五险', value: WELFARE_BENEFITS.INSURANCE },
  { code: WELFARE_BENEFITS.ACCOMMODATION, label: '包食宿', value: WELFARE_BENEFITS.ACCOMMODATION },
  { code: WELFARE_BENEFITS.FOOD, label: '包吃', value: WELFARE_BENEFITS.FOOD },
  { code: WELFARE_BENEFITS.HOUSING, label: '包住', value: WELFARE_BENEFITS.HOUSING },
  { code: WELFARE_BENEFITS.MEAL_ALLOWANCE, label: '餐补', value: WELFARE_BENEFITS.MEAL_ALLOWANCE },

  // 薪酬奖励类
  { code: WELFARE_BENEFITS.ANNUAL_BONUS, label: '年终奖', value: WELFARE_BENEFITS.ANNUAL_BONUS },
  { code: WELFARE_BENEFITS.PERFORMANCE_BONUS, label: '绩效奖金', value: WELFARE_BENEFITS.PERFORMANCE_BONUS },
  { code: WELFARE_BENEFITS.FULL_ATTENDANCE, label: '全勤奖', value: WELFARE_BENEFITS.FULL_ATTENDANCE },
  { code: WELFARE_BENEFITS.OVERTIME_PAY, label: '加班费', value: WELFARE_BENEFITS.OVERTIME_PAY },

  // 时间福利类
  { code: WELFARE_BENEFITS.PAID_LEAVE, label: '带薪年假', value: WELFARE_BENEFITS.PAID_LEAVE },
  { code: WELFARE_BENEFITS.FLEXIBLE_WORK, label: '弹性工作', value: WELFARE_BENEFITS.FLEXIBLE_WORK },
  { code: WELFARE_BENEFITS.WEEKEND_OFF, label: '双休', value: WELFARE_BENEFITS.WEEKEND_OFF },
  { code: WELFARE_BENEFITS.LEGAL_HOLIDAYS, label: '法定节假日', value: WELFARE_BENEFITS.LEGAL_HOLIDAYS },

  // 交通住宿类
  { code: WELFARE_BENEFITS.TRANSPORT_ALLOWANCE, label: '交通补贴', value: WELFARE_BENEFITS.TRANSPORT_ALLOWANCE },
  { code: WELFARE_BENEFITS.DORMITORY, label: '宿舍', value: WELFARE_BENEFITS.DORMITORY },
  { code: WELFARE_BENEFITS.SHUTTLE_BUS, label: '班车接送', value: WELFARE_BENEFITS.SHUTTLE_BUS },

  // 发展培训类
  { code: WELFARE_BENEFITS.TRAINING, label: '培训机会', value: WELFARE_BENEFITS.TRAINING },
  { code: WELFARE_BENEFITS.PROMOTION_PATH, label: '晋升通道', value: WELFARE_BENEFITS.PROMOTION_PATH },
  { code: WELFARE_BENEFITS.SKILL_TRAINING, label: '技能培训', value: WELFARE_BENEFITS.SKILL_TRAINING },

  // 生活关怀类
  { code: WELFARE_BENEFITS.HOLIDAY_BENEFITS, label: '节日福利', value: WELFARE_BENEFITS.HOLIDAY_BENEFITS },
  { code: WELFARE_BENEFITS.BIRTHDAY_BENEFIT, label: '生日福利', value: WELFARE_BENEFITS.BIRTHDAY_BENEFIT },
  { code: WELFARE_BENEFITS.TEAM_BUILDING, label: '团建活动', value: WELFARE_BENEFITS.TEAM_BUILDING },
  { code: WELFARE_BENEFITS.EMPLOYEE_DISCOUNT, label: '员工优惠', value: WELFARE_BENEFITS.EMPLOYEE_DISCOUNT },

  // 健康医疗类
  { code: WELFARE_BENEFITS.MEDICAL_EXAM, label: '体检', value: WELFARE_BENEFITS.MEDICAL_EXAM },
  { code: WELFARE_BENEFITS.COMMERCIAL_INSURANCE, label: '商业保险', value: WELFARE_BENEFITS.COMMERCIAL_INSURANCE },
]

// 正职薪资范围映射 (单位：元/月)
export const jobSalaryRanges: Record<number, SalaryRange> = {
  [JOB_SALARY.UNLIMITED]: { code: JOB_SALARY.UNLIMITED, min: 0, max: 0 },
  [JOB_SALARY.RANGE_3K_TO_6K]: { code: JOB_SALARY.RANGE_3K_TO_6K, min: 3000, max: 6000 },
  [JOB_SALARY.RANGE_6K_TO_10K]: { code: JOB_SALARY.RANGE_6K_TO_10K, min: 6000, max: 10000 },
  [JOB_SALARY.RANGE_10K_TO_15K]: { code: JOB_SALARY.RANGE_10K_TO_15K, min: 10000, max: 15000 },
  [JOB_SALARY.RANGE_15K_TO_25K]: { code: JOB_SALARY.RANGE_15K_TO_25K, min: 15000, max: 25000 },
  [JOB_SALARY.OVER_25K]: { code: JOB_SALARY.OVER_25K, min: 25000, max: 99999999 }
}

// 年龄范围映射
export const ageRanges: Record<number, AgeRange> = {
  [AGE_RANGE.UNLIMITED]: { code: AGE_RANGE.UNLIMITED, min: 0, max: 0 },
  [AGE_RANGE.AGE_18_TO_30]: { code: AGE_RANGE.AGE_18_TO_30, min: 18, max: 30 },
  [AGE_RANGE.AGE_31_TO_40]: { code: AGE_RANGE.AGE_31_TO_40, min: 31, max: 40 },
  [AGE_RANGE.AGE_41_TO_50]: { code: AGE_RANGE.AGE_41_TO_50, min: 41, max: 50 },
  [AGE_RANGE.AGE_50_PLUS]: { code: AGE_RANGE.AGE_50_PLUS, min: 51, max: 100 }
}

// 公司规模范围映射
export const companySizeRanges: Record<number, CompanySizeRange> = {
  [COMPANY_SIZE.UNLIMITED]: { code: COMPANY_SIZE.UNLIMITED, min: 0, max: 0 },
  [COMPANY_SIZE.STARTUP]: { code: COMPANY_SIZE.STARTUP, min: 1, max: 20 },
  [COMPANY_SIZE.SMALL]: { code: COMPANY_SIZE.SMALL, min: 21, max: 100 },
  [COMPANY_SIZE.MEDIUM]: { code: COMPANY_SIZE.MEDIUM, min: 101, max: 500 },
  [COMPANY_SIZE.LARGE]: { code: COMPANY_SIZE.LARGE, min: 501, max: 1000 },
  [COMPANY_SIZE.ENTERPRISE]: { code: COMPANY_SIZE.ENTERPRISE, min: 1001, max: 999999 }
}

// =====================================================
// 映射对象 (向后兼容)
// =====================================================

/**
 * 性别映射对象
 */
export const GENDER_MAP: Record<number, string> = {
  [GENDER.UNLIMITED]: '不限',
  [GENDER.MALE]: '男',
  [GENDER.FEMALE]: '女'
};

/**
 * 经验映射对象
 */
export const EXPERIENCE_MAP: Record<number, string> = {
  [EXPERIENCE.UNLIMITED]: '不限',
  [EXPERIENCE.FRESH_GRADUATE]: '应届毕业生',
  [EXPERIENCE.UNDER_1_YEAR]: '1年以下',
  [EXPERIENCE.YEARS_1_TO_3]: '1-3年',
  [EXPERIENCE.YEARS_3_TO_5]: '3-5年',
  [EXPERIENCE.YEARS_5_TO_10]: '5-10年',
  [EXPERIENCE.OVER_10_YEARS]: '10年以上'
};

/**
 * 学历映射对象
 */
export const EDUCATION_MAP: Record<number, string> = {
  [EDUCATION.UNLIMITED]: '不限',
  [EDUCATION.JUNIOR_HIGH]: '初中及以下',
  [EDUCATION.HIGH_SCHOOL]: '高中/中专',
  [EDUCATION.ASSOCIATE]: '大专',
  [EDUCATION.BACHELOR]: '本科',
  [EDUCATION.MASTER]: '硕士'
};

// ====================================================================
// 反向映射对象 (向后兼容)
// ====================================================================

/**
 * 创建反向映射的工具函数
 */
function createReverseMap(map: Record<number, string>): Record<string, number> {
  const reverseMap: Record<string, number> = {};
  for (const key in map) {
    if (Object.prototype.hasOwnProperty.call(map, key)) {
      reverseMap[map[key]] = Number(key);
    }
  }
  return reverseMap;
}

export const REVERSE_GENDER_MAP = createReverseMap(GENDER_MAP);
export const REVERSE_EXPERIENCE_MAP = createReverseMap(EXPERIENCE_MAP);
export const REVERSE_EDUCATION_MAP = createReverseMap(EDUCATION_MAP);

// =====================================================
// 注意：工具函数已迁移到 @/utils/business/standards
// =====================================================

// =====================================================
// 业务常量定义 (从 common.ts 合并)
// =====================================================

// 招聘类别
export const jobCategories = [
  { code: 1, name: '普工/操作工', icon: 'icon-pugong' },
  { code: 2, name: '销售', icon: 'icon-xiaoshou' },
  { code: 3, name: '客服', icon: 'icon-kefu' },
  { code: 4, name: '司机', icon: 'icon-siji' },
  { code: 5, name: '餐饮', icon: 'icon-canyin' },
  { code: 6, name: '保安', icon: 'icon-baoan' },
  { code: 7, name: '保洁', icon: 'icon-baojie' },
  { code: 8, name: '技术工人', icon: 'icon-jishu' },
  { code: 9, name: '行政/文员', icon: 'icon-xingzheng' },
  { code: 10, name: '其他', icon: 'icon-qita' }
]

// 工作类型
export const workTypes = [
  { code: 1, name: '全职', color: '#007AFF' },
  { code: 2, name: '兼职', color: '#34C759' },
  { code: 3, name: '实习', color: '#FF9500' },
  { code: 4, name: '临时', color: '#FF3B30' }
]

// 紧急程度
export const urgencyLevels = [
  { code: 1, name: '普通', color: '#8E8E93' },
  { code: 2, name: '紧急', color: '#FF9500' },
  { code: 3, name: '非常紧急', color: '#FF3B30' }
]

// 零工类别
export const gigCategories = [
  { code: 1, name: '搬运装卸', icon: 'icon-banyun' },
  { code: 2, name: '清洁保洁', icon: 'icon-qingjie' },
  { code: 3, name: '派发传单', icon: 'icon-paifa' },
  { code: 4, name: '活动兼职', icon: 'icon-huodong' },
  { code: 5, name: '促销导购', icon: 'icon-cuxiao' },
  { code: 6, name: '数据录入', icon: 'icon-shuju' },
  { code: 7, name: '线上任务', icon: 'icon-xianshangrenwu' },
  { code: 8, name: '跑腿代办', icon: 'icon-paotui' },
  { code: 9, name: '技能服务', icon: 'icon-jineng' },
  { code: 10, name: '其他零工', icon: 'icon-qita' }
]

// 表单验证规则
export const FORM_RULES = {
  REQUIRED: 'required',
  NUMBER: 'number',
  EMAIL: 'email',
  PHONE: 'phone',
  ID_CARD: 'idCard'
};

// 图片上传相关常量
export const IMAGE_UPLOAD = {
  MAX_COUNT: 9,
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['jpg', 'jpeg', 'png', 'webp'],
  QUALITY: 80
};

// 房源状态
export const HOUSE_STATUS = [
  { label: "待审核", value: "pending" },
  { label: "已上架", value: "active" },
  { label: "已下架", value: "inactive" },
  { label: "已租出", value: "rented" },
  { label: "已售出", value: "sold" }
];

// 联系方式类型
export const CONTACT_TYPE = [
  { label: "手机", value: "mobile" },
  { label: "座机", value: "landline" },
  { label: "微信", value: "wechat" },
  { label: "QQ", value: "qq" }
];

// 健康证选项
export const HEALTH_CERTIFICATE_OPTIONS = [
  { label: "不需要", value: 0 },
  { label: "需要", value: 1 }
];

// 结算方式选项
export const SETTLEMENT_OPTIONS = [
  { label: "日结", value: "日结" },
  { label: "周结", value: "周结" },
  { label: "月结", value: "月结" },
  { label: "完工结", value: "完工结" }
];

// 缓存键名
export const CACHE_KEYS = {
  USER_INFO: 'USER_INFO',
  CITY_INFO: 'CITY_INFO',
  SEARCH_HISTORY: 'SEARCH_HISTORY'
};

// =====================================================
// 兼容性导出 - 支持旧的导入方式
// =====================================================

// 为了向后兼容，保留一些常用的大写常量导出
export { genderOptions as GENDER_OPTIONS }
export { ageRangeOptions as AGE_RANGE_OPTIONS }
export { educationOptions as EDUCATION_OPTIONS }
export { experienceOptions as EXPERIENCE_OPTIONS }
export { companySizeOptions as COMPANY_SIZE_OPTIONS }
export { welfareOptions as WELFARE_OPTIONS } 
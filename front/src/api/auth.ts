/**
 * Auth 模块 API 定义 - 命名空间模式
 * 职责：只负责定义与后端接口对应的 alova Method 实例
 * 不包含任何业务逻辑，保持纯粹
 */
import request from '@/utils/network/client'
import type { ApiResponse } from '@/constants/response'
import { AuthResponse, CheckUserResponse } from '@/types/auth';

/**
 * 认证相关 API 命名空间类
 * 采用静态方法组织，提供清晰的模块边界
 */
class AuthApi {

    /**
     * 微信小程序登录
     * @param data 登录数据
     * @returns 登录结果
     */
    static wechatLogin = (data: { loginCode: string; phoneCode?: string; deviceId?: string }) => {
        const deviceId = data.deviceId || AuthApi.getDeviceId()
        return request.Post<AuthResponse>("/auth/wechat-login", {
            loginCode: data.loginCode,
            phoneCode: data.phoneCode,
            deviceId
        })
    }

    /**
     * 开发测试登录 - 仅在开发环境使用
     * @param phone 测试用户手机号
     * @returns 真实登录结果
     */
    static devTestLogin = (phone: string) => {
        // 仅在开发环境且启用开发登录时才可用
        const isDevLoginEnabled = import.meta.env.VITE_ENABLE_DEV_LOGIN === 'true'
        const isDev = import.meta.env.DEV

        if (!isDev || !isDevLoginEnabled) {
            return Promise.reject(new Error('开发登录功能未启用'))
        }

        const deviceId = AuthApi.getDeviceId()
        return request.Post<AuthResponse>("/auth/dev-login", {
            phone: phone,
            device_id: deviceId
        })
    }

    /**
     * 刷新Token
     * @param refreshToken 刷新令牌
     * @returns 新的Token
     */
    static refreshToken = (refreshToken: string) => {
        return request.Post<{ access_token: string; refresh_token: string }>("/auth/refresh", { refreshToken })
    }

    /**
     * 退出登录
     * @returns 退出结果
     */
    static logout = () => {
        return request.Post<{ message: string }>("/auth/logout", {})
    }

    // ====================================================================
    // 用户检查
    // ====================================================================

    /**
     * 检查用户是否注册（根据手机号）
     * @param data 手机号
     * @returns 检查结果
     */
    static checkUser = (data: { phone: string }) => {
        return request.Post<{ is_registered: boolean; message: string }>("/auth/check-user", data)
    }

    /**
     * 检查用户是否注册（根据微信code）
     * @param data 微信code
     * @returns 检查结果
     */
    static checkUserByCode = (data: { code: string; deviceId?: string }) => {
        const deviceId = data.deviceId || AuthApi.getDeviceId()
        return request.Post<CheckUserResponse>("/auth/check-user-by-code", {
            code: data.code,
            deviceId
        })
    }

    /**
     * 绑定手机号
     * @param data 手机号绑定数据
     * @returns 绑定结果
     */
    static bindPhone = (data: { phoneCode: string }) => {
        return request.Post<{ message: string; user: any }>("/users/bind-phone", data)
    }

    // ====================================================================
    // 设备管理 (工具函数)
    // ====================================================================

    /**
     * 获取或生成设备ID
     * @returns 设备唯一标识
     */
    static getDeviceId = (): string => {
        let deviceId = uni.getStorageSync('device_id')

        if (!deviceId) {
            // 生成设备ID：时间戳 + 随机数 + 设备信息
            const timestamp = Date.now()
            const random = Math.random().toString(36).substring(2, 8)
            const systemInfo = uni.getSystemInfoSync()
            const deviceInfo = `${systemInfo.platform}_${systemInfo.model}`.replace(/\s+/g, '_')

            deviceId = `${timestamp}_${random}_${deviceInfo}`.substring(0, 32)

            // 持久化存储
            uni.setStorageSync('device_id', deviceId)
        }

        return deviceId
    }

    /**
     * 重置设备ID（用于特殊情况）
     * @returns 新的设备ID
     */
    static resetDeviceId = (): string => {
        uni.removeStorageSync('device_id')
        return AuthApi.getDeviceId()
    }
}

// ====================================================================
// 导出配置
// ====================================================================

// 默认导出命名空间类
export default AuthApi

// 命名导出（可选）
export { AuthApi }

// ====================================================================
// 向后兼容性导出 (过渡期使用，后续可移除)
// ====================================================================

// export const wechatLogin = AuthApi.wechatLogin
// export const devTestLogin = AuthApi.devTestLogin
// export const refreshToken = AuthApi.refreshToken
// export const logout = AuthApi.logout
// export const checkUser = AuthApi.checkUser
// export const checkUserByCode = AuthApi.checkUserByCode
// export const getUserInfo = AuthApi.getUserInfo
// export const bindPhone = AuthApi.bindPhone
// export const getDeviceId = AuthApi.getDeviceId
// export const resetDeviceId = AuthApi.resetDeviceId
declare namespace ChatMsg {
  interface BaseMessage {
    id: number
    conversationId: string // 会话ID
    messageId: string // 唯一消息ID
    from: 'self' | 'other' | 'system'
    to: 'self' | 'other' | 'group'
    fromId: string // 发送者ID
    toId: string // 接收者ID或群组ID
    from_user_avatar: string
    from_user_id: number
    from_user_name: string
    to_user_avatar?: string
    to_user_id?: number
    to_user_name?: string
    time: string
    status?: 'sending' | 'sent' | 'failed' | 'read'
    isGroup?: boolean // 是否为群聊消息
    mentionedUsers?: string[] // 被@的用户ID数组
    clientMsgId?: string // 客户端消息ID，用于去重和排序
    needReadReceipt?: boolean // 是否需要已读回执
    readCount?: number // 已读计数（群聊）
    isRead?: boolean // 是否已读（单聊）
    replyTo?: {
      // 回复信息
      messageId: string
      content: string
      fromId: string
      fromName: string
    }
  }

  // 文本消息
  interface TextMessage extends BaseMessage {
    type: 'text'
    content: string
    contentType?: 'text' | 'html' | 'markdown' // 文本内容类型
  }

  // 图片消息
  interface ImageMessage extends BaseMessage {
    type: 'image'
    content: string // 图片URL
    ext: {
      width: number // 图片宽度
      height: number // 图片高度
      size: number // 图片大小(字节)
      format: string // 图片格式
      thumbnail?: string // 缩略图
      orientation?: number // 图片方向
    }
  }

  // 语音消息
  interface VoiceMessage extends BaseMessage {
    type: 'voice'
    content: string // 语音文件URL
    ext: {
      duration: number // 语音时长(秒)
      size: number // 文件大小(字节)
      format: string // 音频格式
      waveform?: number[] // 语音波形数据
    }
  }

  // 视频消息
  interface VideoMessage extends BaseMessage {
    type: 'video'
    content: string // 视频URL
    ext: {
      duration: number // 视频时长(秒)
      size: number // 文件大小(字节)
      width: number // 视频宽度
      height: number // 视频高度
      format: string // 视频格式
      thumbnail: string // 视频封面
    }
  }

  // 文件消息
  interface FileMessage extends BaseMessage {
    type: 'file'
    content: string // 文件URL
    ext: {
      name: string // 文件名
      size: number // 文件大小(字节)
      format: string // 文件格式
      icon?: string // 文件图标
    }
  }

  // 位置消息
  interface LocationMessage extends BaseMessage {
    type: 'location'
    content: {
      name: string // 位置名称
      address: string // 位置详细地址
      latitude: number // 纬度
      longitude: number // 经度
      image: string // 地图截图
      zoom?: number // 缩放级别
      poi_id?: string // 位置ID
    }
  }

  // 系统消息
  interface SystemMessage extends BaseMessage {
    type: 'system'
    content: string
    operation?: string // 操作类型，如"加入群组"、"退出群组"等
    operatorId?: string // 操作者ID
    targetId?: string // 被操作者ID
    subType?: 'notification' | 'warning' | 'error' | 'info' // 系统消息子类型
  }

  // 名片消息
  interface ContactCardMessage extends BaseMessage {
    type: 'contact_card'
    content: {
      userId: string
      name: string
      avatar: string
      description?: string
    }
  }

  // 自定义消息
  interface CustomMessage extends BaseMessage {
    type: 'custom'
    content: string
    customType: string
    data: any
  }

  // 联合类型包含所有消息类型
  type Message =
    | TextMessage
    | ImageMessage
    | VoiceMessage
    | VideoMessage
    | FileMessage
    | LocationMessage
    | SystemMessage
    | ContactCardMessage
    | CustomMessage
}

// 导出命名空间
export { ChatMsg }

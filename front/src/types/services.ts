/**
 * 服务层统一类型定义
 * 为所有服务提供通用的类型定义和接口规范
 */

// ====================================================================
// 基础服务类型
// ====================================================================

export interface ServiceResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  code?: number
}

export interface ServiceError {
  message: string
  code?: number
  details?: any
}

export interface ServiceOptions {
  showLoading?: boolean
  loadingText?: string
  showError?: boolean
  throwOnError?: boolean
}

export interface PaginationOptions {
  page?: number
  pageSize?: number
}

export interface PaginatedResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
  hasMore?: boolean
}

export interface SortOptions {
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface FilterOptions {
  keyword?: string
  status?: string | number
  categoryId?: number
  startDate?: string
  endDate?: string
}

// ====================================================================
// 认证服务类型
// ====================================================================

export interface AuthServiceTypes {
  LoginResponse: {
    user: UserInfo
    access_token: string
    refresh_token?: string
    expires_in?: number
  }

  UserInfo: {
    id: number
    nickname: string
    avatar: string
    phone: string
    email?: string
    gender?: 'male' | 'female' | 'other'
    age?: number
    city?: string
    isVip?: boolean
    verified?: boolean
    createdAt: string
  }

  LoginCheckResult: {
    isRegistered: boolean
    hasPhone: boolean
    needsProfile?: boolean
  }
}

// ====================================================================
// 上传服务类型
// ====================================================================

export interface UploadServiceTypes {
  UploadOptions: {
    prefix?: string
    mimeTypes?: string[]
    maxSize?: number
    onProgress?: (percent: number) => void
    quality?: number
    compress?: boolean
  }

  UploadConfig: {
    qiniuUploadUrl: string
    maxFileSize: number
    allowedMimeTypes: string[]
    defaultPrefix: string
  }

  UploadResult: {
    key: string
    url: string
    size: number
    mimeType: string
  }
}

// ====================================================================
// 零工服务类型
// ====================================================================

export interface GigServiceTypes {
  SearchOptions: {
    keyword?: string
    cityId?: number
    categoryId?: number
    salaryMin?: number
    salaryMax?: number
    workType?: string
  } & SortOptions & PaginationOptions

  PublishData: {
    title: string
    description: string
    categoryId: number
    salary: number
    salaryUnit: number
    peopleCount: number
    startTime: string
    endTime: string
    addressName: string
    detailAddress?: string
    longitude: number
    latitude: number
    contactName: string
    contactPhone: string
    requirements?: string
    tags?: string[]
    isUrgent?: boolean
  }

  Gig: {
    id: number
    title: string
    description: string
    categoryId: number
    salary: number
    salaryUnit: number
    peopleCount: number
    currentPeopleCount: number
    startTime: string
    endTime: string
    status: GigStatus
    addressName: string
    detailAddress?: string
    location?: {
      longitude: number
      latitude: number
    }
    contactName: string
    contactPhone: string
    requirements?: string
    tags?: string[]
    isUrgent: boolean
    publisherId: number
    publisherInfo?: AuthServiceTypes['UserInfo']
    createdAt: string
    updatedAt: string
  }

  Application: {
    id: number
    gigId: number
    applicantId: number
    applicantInfo: AuthServiceTypes['UserInfo']
    status: GigApplicationStatus
    message?: string
    appliedAt: string
    respondedAt?: string
    responseMessage?: string
  }
}

export enum GigStatus {
  Draft = 'draft',
  Recruiting = 'recruiting',
  Paused = 'paused',
  Locked = 'locked',
  InProgress = 'in_progress',
  Completed = 'completed',
  Closed = 'closed'
}

export enum GigApplicationStatus {
  Pending = 'pending',
  Confirmed = 'confirmed',
  Rejected = 'rejected',
  Withdrawn = 'withdrawn',
  Cancelled = 'cancelled'
}

// ====================================================================
// 房屋服务类型
// ====================================================================

export interface HouseServiceTypes {
  SearchOptions: {
    keyword?: string
    cityId?: number
    districtId?: number
    houseType?: HouseType
    priceMin?: number
    priceMax?: number
    areaMin?: number
    areaMax?: number
    roomCount?: number
  } & SortOptions & PaginationOptions

  PublishData: {
    title: string
    description: string
    houseType: HouseType
    price: number
    area: number
    roomCount: number
    bedroomCount: number
    bathroomCount: number
    floor: number
    totalFloors: number
    orientation: string
    decoration: string
    facilities: string[]
    images: string[]
    address: {
      province: string
      city: string
      district: string
      street: string
      detail: string
      longitude: number
      latitude: number
    }
    contactName: string
    contactPhone: string
    contactWechat?: string
  }

  House: {
    id: number
    title: string
    description: string
    houseType: HouseType
    price: number
    area: number
    roomCount: number
    bedroomCount: number
    bathroomCount: number
    floor: number
    totalFloors: number
    orientation: string
    decoration: string
    facilities: string[]
    images: string[]
    status: HouseStatus
    address: HouseServiceTypes['PublishData']['address']
    contactName: string
    contactPhone: string
    contactWechat?: string
    publisherId: number
    publisherInfo?: AuthServiceTypes['UserInfo']
    viewCount: number
    favoriteCount: number
    createdAt: string
    updatedAt: string
  }

  Application: {
    id: number
    houseId: number
    applicantId: number
    applicantInfo: AuthServiceTypes['UserInfo']
    appointmentTime: string
    message?: string
    status: HouseApplicationStatus
    appliedAt: string
    respondedAt?: string
    responseMessage?: string
  }
}

export enum HouseType {
  Rent = 'rent',
  Sale = 'sale',
  SecondHand = 'second_hand'
}

export enum HouseStatus {
  Draft = 'draft',
  Published = 'published',
  Rented = 'rented',
  Sold = 'sold',
  Closed = 'closed'
}

export enum HouseApplicationStatus {
  Pending = 'pending',
  Approved = 'approved',
  Rejected = 'rejected',
  Cancelled = 'cancelled'
}

// ====================================================================
// 交友服务类型
// ====================================================================

export interface DatingServiceTypes {
  UserProfile: {
    id: number
    nickname: string
    avatar: string
    age: number
    gender: 'male' | 'female' | 'other'
    city: string
    profession: string
    education: string
    height: number
    weight: number
    hobbies: string[]
    personalityTags: string[]
    selfIntroduction: string
    photos: string[]
    verified: boolean
    lastActiveTime: string
  }

  MatchFilter: {
    ageMin?: number
    ageMax?: number
    gender?: 'male' | 'female' | 'other'
    city?: string
    education?: string
    profession?: string
    heightMin?: number
    heightMax?: number
    hobbies?: string[]
    personalityTags?: string[]
    distance?: number
  }

  Post: {
    id: number
    userId: number
    userInfo: Pick<DatingServiceTypes['UserProfile'], 'id' | 'nickname' | 'avatar' | 'age' | 'city'>
    content: string
    images: string[]
    location?: {
      name: string
      longitude: number
      latitude: number
    }
    likeCount: number
    commentCount: number
    isLiked: boolean
    createdAt: string
  }

  MatchResult: {
    userId: number
    userProfile: DatingServiceTypes['UserProfile']
    matchScore: number
    commonInterests: string[]
    distance: number
  }
}

// ====================================================================
// 即时通讯服务类型
// ====================================================================

export interface MessagingServiceTypes {
  ChatUser: {
    id: number
    nickname: string
    avatar: string
    isOnline: boolean
    lastSeenAt?: string
  }

  ChatRoom: {
    id: string
    name: string
    type: 'private' | 'group' | 'system'
    participants: MessagingServiceTypes['ChatUser'][]
    lastMessage?: MessagingServiceTypes['ChatMessage']
    unreadCount: number
    createdAt: string
    updatedAt: string
  }

  ChatMessage: {
    id: string
    roomId: string
    senderId: number
    senderInfo: MessagingServiceTypes['ChatUser']
    content: string
    messageType: 'text' | 'image' | 'voice' | 'video' | 'file' | 'location' | 'system'
    metadata?: {
      duration?: number
      size?: number
      filename?: string
      thumbnail?: string
      latitude?: number
      longitude?: number
      address?: string
    }
    replyTo?: string
    readBy: number[]
    createdAt: string
    isDeleted: boolean
  }

  ConnectionConfig: {
    centrifugoUrl: string
    token: string
    userId: number
  }
}

// ====================================================================
// API 响应类型
// ====================================================================

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

export interface ApiError {
  code: number
  message: string
  details?: any
  timestamp: number
}

// ====================================================================
// 工具类型
// ====================================================================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type KeyOf<T> = keyof T

export type ValueOf<T> = T[keyof T]

// ====================================================================
// 表单验证类型
// ====================================================================

export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  message?: string
}

export interface ValidationResult {
  valid: boolean
  errors: string[]
  fieldErrors: Record<string, string>
}

export interface FormField {
  name: string
  label: string
  value: any
  rules: ValidationRule[]
}

// ====================================================================
// 常用枚举类型
// ====================================================================

export enum Gender {
  Male = 'male',
  Female = 'female',
  Other = 'other'
}

export enum Education {
  Primary = 'primary',
  Middle = 'middle',
  High = 'high',
  College = 'college',
  Bachelor = 'bachelor',
  Master = 'master',
  Doctor = 'doctor'
}

export enum Experience {
  None = 0,
  LessThanOne = 1,
  OneToThree = 2,
  ThreeToFive = 3,
  FiveToTen = 4,
  MoreThanTen = 5
}

// ====================================================================
// 导出所有类型
// ====================================================================

export type {
  // 基础类型
  ServiceResponse,
  ServiceError,
  ServiceOptions,
  PaginatedResponse,
  
  // 服务类型
  AuthServiceTypes,
  UploadServiceTypes,
  GigServiceTypes,
  HouseServiceTypes,
  DatingServiceTypes,
  MessagingServiceTypes,
  
  // API 类型
  ApiResponse,
  ApiError,
  
  // 工具类型
  DeepPartial,
  RequiredFields,
  OptionalFields,
  
  // 验证类型
  ValidationRule,
  ValidationResult,
  FormField
}
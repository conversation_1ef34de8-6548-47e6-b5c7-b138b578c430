// src/types/auth.ts

import type { UserInfo } from './user'

/**
 * 认证响应 - 与后端LoginResponse保持一致（使用snake_case）
 */
export interface AuthResponse {
  access_token: string
  refresh_token?: string
  user: UserInfo
}

/**
 * 登录请求
 */
export interface LoginRequest {
  loginCode: string
  phoneCode?: string
  deviceId: string
}

/**
 * 微信登录请求
 */
export interface WechatLoginRequest {
  loginCode: string
  phoneCode?: string
  deviceId: string
}

/**
 * 检查用户请求
 */
export interface CheckUserRequest {
  phone: string
}

/**
 * 检查用户响应 - 与后端CheckUserByCodeResponse保持一致
 */
export interface CheckUserResponse {
  is_registered: boolean
  has_phone: boolean
  message: string
  openid?: string
}

/**
 * 开发登录请求
 */
export interface DevLoginRequest {
  phone: string
  device_id: string
}

/**
 * 刷新Token请求
 */
export interface RefreshTokenRequest {
  refreshToken: string
}

/**
 * 绑定手机号请求
 */
export interface BindPhoneRequest {
  phoneCode: string
}
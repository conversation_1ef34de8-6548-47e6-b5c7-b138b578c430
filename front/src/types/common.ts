// src/types/common.ts

/**
 * 分页请求参数
 */
export interface PaginatedRequest {
    page: number;
    page_size: number;
}

/**
 * 通用分页响应数据结构
 */
export interface PaginatedResponse<T> {
    list: T[];
    total: number;
    page: number;
    page_size: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
}

/**
 * 上传凭证响应
 */
export interface UploadTokenResponse {
    token: string;
    domain: string;
} 
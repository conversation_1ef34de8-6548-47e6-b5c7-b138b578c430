/**
 * 零工申请相关服务
 */
import { ref, unref, type Ref } from 'vue'
import { useRequest } from 'alova/client'
import GigApi from '@/api/gig'
import type { GigApplication, ApplyGigRequest, UpdateApplicationStatusRequest } from '@/types/gig'
import { useUserStore } from '@/stores/user'
import { showToast, showSuccessToast } from '@/utils/ui/feedback'
import { validatePhone, validateChineseName, validateLength } from '@/utils/core/validation'

/**
 * 申请零工的业务逻辑 - 增强版
 */
export function useGigApplication() {
  const userStore = useUserStore()
  const { checkUserBasicInfo, promptUserToCompleteInfo } = useUserInfoValidation()
  const { validateApplicationForm } = useApplicationFormValidation()

  /**
   * 完整的申请流程处理
   */
  const applyForGigWithValidation = async (
    gigId: number,
    formData: {
      contact_name: string
      contact_phone: string
      has_experience?: boolean
      message?: string
      agreed_to_terms?: boolean
    }
  ): Promise<{ success: boolean, errors?: Record<string, string> }> => {
    try {
      // 1. 用户信息预检查
      const userCheck = checkUserBasicInfo()
      if (!userCheck.valid) {
        promptUserToCompleteInfo(userCheck.missingFields)
        return { success: false }
      }

      // 2. 表单验证
      const formErrors = validateApplicationForm({
        ...formData,
        agreedToTerms: formData.agreed_to_terms
      })

      if (Object.keys(formErrors).length > 0) {
        return { success: false, errors: formErrors }
      }

      // 3. 提交申请
      const requestData: ApplyGigRequest = {
        gig_id: gigId,
        contact_name: formData.contact_name.trim(),
        contact_phone: formData.contact_phone.trim(),
        has_experience: formData.has_experience || false,
        message: formData.message?.trim() || ''
      }

      await applyForGig(requestData)
      return { success: true }

    } catch (error) {
      console.error('申请提交失败:', error)
      return { success: false }
    }
  }

  // 申请零工
  const {
    loading: isApplying,
    error: applyError,
    send: applyForGig
  } = useRequest(
    (request: ApplyGigRequest) => GigApi.apply(request),
    { immediate: false }
  ).onSuccess(() => {
    showSuccessToast('申请提交成功，请等待雇主确认')
  }).onError((error) => {
    console.error('申请失败:', error)
    showToast(error.error ?? "申请失败，请稍后重试")
  })

  // 撤销申请
  const {
    loading: isWithdrawing,
    error: withdrawError,
    send: withdrawApplication
  } = useRequest(
    (applicationId: number) => GigApi.deleteApplication(applicationId),
    { immediate: false }
  ).onSuccess(() => {
    showSuccessToast('申请已撤销')
  }).onError((error) => {
    console.error('撤销申请失败:', error)
    showToast('撤销失败，请重试')
  })

  // 更新申请状态（雇主操作）
  const {
    loading: isUpdatingStatus,
    error: updateStatusError,
    send: updateApplicationStatus
  } = useRequest(
    (request: UpdateApplicationStatusRequest) => GigApi.updateApplicationStatus(request),
    { immediate: false }
  ).onSuccess(() => {
    showSuccessToast('操作成功')
  }).onError((error) => {
    console.error('更新申请状态失败:', error)
    showToast('操作失败，请重试')
  })

  return {
    // 申请相关
    isApplying,
    applyError,
    applyForGig,
    applyForGigWithValidation, // 新增：带验证的完整申请流程

    // 撤销相关
    isWithdrawing,
    withdrawError,
    withdrawApplication,

    // 状态更新相关（雇主用）
    isUpdatingStatus,
    updateStatusError,
    updateApplicationStatus
  }
}

/**
 * 获取用户的申请列表
 */
export function useMyApplications() {
  const {
    data: applications,
    loading: isLoading,
    error: fetchError,
    send: refresh
  } = useRequest(
    () => GigApi.getMyApplications(),
    { immediate: true }
  ).onError((error) => {
    console.error('获取申请列表失败:', error)
  })

  return {
    applications: applications ?? ref([]),
    isLoading,
    fetchError,
    refresh
  }
}

/**
 * 获取零工的申请列表（雇主用）
 */
export function useGigApplications(gigId: number) {
  const {
    data: applications,
    loading: isLoading,
    error: fetchError,
    send: refresh
  } = useRequest(
    () => GigApi.getGigApplications(gigId),
    { immediate: true }
  ).onError((error) => {
    console.error('获取零工申请列表失败:', error)
  })

  return {
    applications: applications ?? ref([]),
    isLoading,
    fetchError,
    refresh
  }
}

/**
 * 检查用户对特定零工的申请状态
 * @param gigId - 可以是number或ref<number>
 * 
 * 注意：此函数返回的是CheckApplicationStatusResponse，包含has_applied等信息
 * 不是完整的申请对象，如需完整申请信息，请使用getUserApplicationByGigId
 */
export function checkAppStatus(gigId: Ref<number>) {
  const userStore = useUserStore()

  const {
    data: statusInfo,
    loading: isLoading,
    error: fetchError,
    send: checkStatus
  } = useRequest(
    GigApi.checkApplicationStatus(gigId.value),
    {
      immediate: false, // 手动控制调用时机
      // 禁用缓存，确保每次都获取最新数据
      cacheFor: 0,
      // 如果用户未登录，返回null
      transformer: (data) => userStore.isLogin ? data : null
    }
  )

  return {
    statusInfo,
    isLoading,
    fetchError,
    checkStatus
  }
}

/**
 * 用户信息预检查
 */
export function useUserInfoValidation() {
  const userStore = useUserStore()

  /**
   * 检查用户是否设置了必要的基本信息
   */
  const checkUserBasicInfo = (): { valid: boolean, missingFields: string[] } => {
    const missingFields: string[] = []
    const user = userStore.user

    if (!user) {
      return { valid: false, missingFields: ['登录状态'] }
    }

    // 检查姓名
    if (!user.nickname || user.nickname.trim().length === 0) {
      missingFields.push('姓名')
    }

    // 检查手机号
    if (!user.phone || user.phone.trim().length === 0) {
      missingFields.push('手机号')
    }

    return {
      valid: missingFields.length === 0,
      missingFields
    }
  }

  /**
   * 提示用户完善信息
   */
  const promptUserToCompleteInfo = (missingFields: string[]) => {
    const fieldText = missingFields.join('、')
    showToast(`请先完善您的${fieldText}信息`)
    // TODO: 跳转到个人信息页面
    // navigateTo('/pages/mine/profile')
  }

  return {
    checkUserBasicInfo,
    promptUserToCompleteInfo
  }
}

/**
 * 申请表单验证逻辑 - 使用统一的验证函数
 */
export function useApplicationFormValidation() {
  /**
   * 验证联系人姓名
   */
  const validateContactName = (name: string): string | null => {
    if (!name || name.trim().length === 0) {
      return '请输入您的姓名'
    }

    // 使用统一的中文姓名验证，但放宽要求支持英文和数字
    const lengthResult = validateLength(name.trim(), 2, 20)
    if (lengthResult !== true) {
      return lengthResult as string
    }

    // 检查是否包含特殊字符（保留中文、英文、数字）
    const nameRegex = /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/
    if (!nameRegex.test(name.trim())) {
      return '姓名只能包含中文、英文和数字'
    }

    return null
  }

  /**
   * 验证手机号码
   */
  const validateContactPhone = (phone: string): string | null => {
    if (!phone || phone.trim().length === 0) {
      return '请输入手机号码'
    }

    // 使用统一的手机号验证函数
    const result = validatePhone(phone.trim())
    return result === true ? null : result as string
  }

  /**
   * 验证经验描述（备注）
   */
  const validateMessage = (message: string): string | null => {
    if (!message) return null // 备注是可选的

    const result = validateLength(message.trim(), 0, 200, '备注信息不能超过200个字符')
    return result === true ? null : result as string
  }

  /**
   * 验证整个申请表单
   */
  const validateApplicationForm = (formData: any): Record<string, string> => {
    const errors: Record<string, string> = {}

    const nameError = validateContactName(formData.contact_name || formData.contactName)
    if (nameError) errors.contactName = nameError

    const phoneError = validateContactPhone(formData.contact_phone || formData.contactPhone)
    if (phoneError) errors.contactPhone = phoneError

    const messageError = validateMessage(formData.message || formData.remarks)
    if (messageError) errors.message = messageError

    if (!formData.agreed_to_terms && !formData.agreedToTerms) {
      errors.agreement = '请同意申请服务协议'
    }

    return errors
  }

  return {
    validateContactName,
    validateContactPhone,
    validateMessage,
    validateApplicationForm
  }
}

/**
 * 申请表单数据处理
 */
export function useApplicationFormData() {
  /**
   * 将表单数据转换为API请求格式
   */
  const transformFormDataToRequest = (
    gigId: number,
    formData: any
  ): ApplyGigRequest => {
    return {
      gig_id: gigId,
      contact_name: formData.contact_name.trim(),
      contact_phone: formData.contact_phone.trim(),
      has_experience: formData.has_experience,
      message: formData.message?.trim() || ''
    }
  }

  /**
   * 获取经验等级的显示文本
   */
  const getExperienceText = (hasExperience: boolean): string => {
    return hasExperience ? '有相关经验' : '无相关经验'
  }

  return {
    transformFormDataToRequest,
    getExperienceText
  }
}
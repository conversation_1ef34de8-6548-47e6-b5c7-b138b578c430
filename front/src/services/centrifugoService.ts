import { Centrifuge } from 'centrifuge';
import { useImStore } from '@/stores/im';
import type { Message } from '@/types/im';

const CENTRIFUGO_URL = 'ws://localhost:8000/connection/websocket'; // Replace with your Centrifugo URL

class CentrifugoService {
  private centrifuge: Centrifuge | null = null;
  private imStore: ReturnType<typeof useImStore>;

  constructor() {
    // Pinia store can only be accessed after app initialization
    // So, we'll initialize it in the connect method or when needed.
  }

  public init() {
    if (this.centrifuge) return; // Already initialized

    this.imStore = useImStore(); // Get store instance here

    this.centrifuge = new Centrifuge(CENTRIFUGO_URL, {
      token: 'dummy-jwt-token', // In a real app, fetch this from your backend
    });

    this.centrifuge.on('connected', (ctx) => {
      console.log('Centrifugo connected:', ctx);
      this.subscribeToUserChannel();
    });

    this.centrifuge.on('disconnected', (ctx) => {
      console.log('Centrifugo disconnected:', ctx);
      // Implement reconnect logic if needed
    });

    this.centrifuge.on('publication', (ctx) => {
      console.log('Global publication:', ctx.data);
    });

    this.centrifuge.connect();
  }

  private subscribeToUserChannel() {
    const userId = this.imStore.currentUser.id;
    if (!this.centrifuge || !userId) return;

    // Subscribe to a personal channel for direct messages
    const userChannel = `personal:${userId}`;
    const sub = this.centrifuge.newSubscription(userChannel);

    sub.on('publication', (ctx) => {
      console.log(`New message on ${userChannel}:`, ctx.data);
      const message: Message = ctx.data as Message;
      this.imStore.addIncomingMessage(message);
    });

    sub.on('subscribed', (ctx) => {
      console.log(`Subscribed to ${userChannel}:`, ctx);
    });

    sub.on('unsubscribed', (ctx) => {
      console.log(`Unsubscribed from ${userChannel}:`, ctx);
    });

    sub.subscribe();
  }

  public async publish(channel: string, data: any) {
    if (!this.centrifuge || !this.centrifuge.isConnected()) {
      console.warn('Centrifugo not connected, message not published.');
      throw new Error('Centrifugo not connected');
    }
    try {
      await this.centrifuge.publish(channel, data);
      console.log(`Message published to ${channel}:`, data);
    } catch (err) {
      console.error('Failed to publish message:', err);
      throw err;
    }
  }
}

export const centrifugoService = new CentrifugoService();
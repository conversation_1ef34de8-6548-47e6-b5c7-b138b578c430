/**
 * 即时通讯业务服务
 * 基于 Centrifugo 的实时通讯功能
 */

import { BaseService, type ServiceResponse } from './base'

export interface ChatUser {
  id: number
  nickname: string
  avatar: string
  isOnline: boolean
  lastSeenAt?: string
}

export interface ChatRoom {
  id: string
  name: string
  type: 'private' | 'group' | 'system'
  participants: ChatUser[]
  lastMessage?: ChatMessage
  unreadCount: number
  createdAt: string
  updatedAt: string
}

export interface ChatMessage {
  id: string
  roomId: string
  senderId: number
  senderInfo: ChatUser
  content: string
  messageType: 'text' | 'image' | 'voice' | 'video' | 'file' | 'location' | 'system'
  metadata?: {
    duration?: number // 语音/视频时长（秒）
    size?: number // 文件大小（字节）
    filename?: string // 文件名
    thumbnail?: string // 缩略图
    latitude?: number // 位置纬度
    longitude?: number // 位置经度
    address?: string // 位置地址
  }
  replyTo?: string // 回复的消息ID
  readBy: number[] // 已读用户ID列表
  createdAt: string
  isDeleted: boolean
}

export interface ConnectionConfig {
  centrifugoUrl: string
  token: string
  userId: number
}

/**
 * 即时通讯服务类
 */
export class MessagingService extends BaseService {
  protected serviceName = 'MessagingService'
  
  private centrifuge: any = null
  private subscriptions: Map<string, any> = new Map()
  private connectionStatus: 'disconnected' | 'connecting' | 'connected' = 'disconnected'
  private messageHandlers: Map<string, Function[]> = new Map()

  /**
   * 初始化连接
   * @param config 连接配置
   */
  async connect(config?: ConnectionConfig): Promise<ServiceResponse<void>> {
    return this.handleOperation(
      async () => {
        if (this.connectionStatus === 'connected') {
          this.log('info', '连接已存在，跳过初始化')
          return
        }

        this.connectionStatus = 'connecting'
        
        // 获取连接配置
        const connectionConfig = config || await this.getConnectionConfig()
        
        // 动态导入 Centrifuge
        const { Centrifuge } = await import('centrifuge')
        
        // 创建连接
        this.centrifuge = new Centrifuge(connectionConfig.centrifugoUrl, {
          token: connectionConfig.token,
          debug: import.meta.env.DEV
        })

        // 设置事件监听
        this.setupEventHandlers()

        // 建立连接
        this.centrifuge.connect()
        
        this.log('info', '即时通讯连接已初始化')
      },
      {
        showLoading: true,
        loadingText: '连接聊天服务器...',
        showError: true
      }
    )
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<ServiceResponse<void>> {
    return this.handleOperation(
      async () => {
        if (this.centrifuge) {
          // 清理所有订阅
          this.subscriptions.forEach(sub => sub.unsubscribe())
          this.subscriptions.clear()
          
          // 断开连接
          this.centrifuge.disconnect()
          this.centrifuge = null
        }
        
        this.connectionStatus = 'disconnected'
        this.messageHandlers.clear()
        
        this.log('info', '即时通讯连接已断开')
      },
      {
        showLoading: false,
        showError: false
      }
    )
  }

  /**
   * 获取聊天室列表
   * @param page 页码
   * @param pageSize 每页数量
   */
  async getChatRooms(
    page: number = 1, 
    pageSize: number = 20
  ): Promise<ServiceResponse<{list: ChatRoom[], total: number}>> {
    return this.executeWithLogin(
      async () => {
        this.log('info', '获取聊天室列表', { page, pageSize })

        // 这里应该调用实际的API
        // const response = await messagingApi.getChatRooms({ page, pageSize })
        // return response.data

        return {
          list: [],
          total: 0
        }
      },
      {
        showLoading: page === 1,
        loadingText: '加载聊天室...',
        showError: true
      }
    )
  }

  /**
   * 创建聊天室
   * @param participants 参与者ID列表
   * @param name 聊天室名称（群聊时需要）
   * @param type 聊天室类型
   */
  async createChatRoom(
    participants: number[],
    name?: string,
    type: 'private' | 'group' = 'private'
  ): Promise<ServiceResponse<{roomId: string}>> {
    return this.executeWithLogin(
      async () => {
        if (participants.length === 0) {
          throw new Error('参与者不能为空')
        }

        if (type === 'group' && !name) {
          throw new Error('群聊必须设置名称')
        }

        this.log('info', '创建聊天室', { participants, name, type })

        // 这里应该调用实际的API
        // const response = await messagingApi.createChatRoom({ participants, name, type })
        // return response.data

        return {
          roomId: `room_${Date.now()}`
        }
      },
      {
        showLoading: true,
        loadingText: '创建聊天室...',
        showError: true
      }
    )
  }

  /**
   * 加入聊天室
   * @param roomId 聊天室ID
   */
  async joinChatRoom(roomId: string): Promise<ServiceResponse<void>> {
    return this.handleOperation(
      async () => {
        if (!this.centrifuge || this.connectionStatus !== 'connected') {
          throw new Error('请先连接聊天服务器')
        }

        // 如果已经订阅了这个房间，先取消订阅
        if (this.subscriptions.has(roomId)) {
          this.subscriptions.get(roomId).unsubscribe()
        }

        // 订阅聊天室频道
        const subscription = this.centrifuge.newSubscription(`room:${roomId}`)
        
        // 设置消息处理
        subscription.on('publication', (ctx: any) => {
          this.handleIncomingMessage(roomId, ctx.data)
        })

        subscription.on('subscribing', () => {
          this.log('info', `正在加入聊天室: ${roomId}`)
        })

        subscription.on('subscribed', () => {
          this.log('info', `已加入聊天室: ${roomId}`)
        })

        subscription.on('error', (ctx: any) => {
          this.log('error', `聊天室订阅错误: ${roomId}`, ctx.error)
        })

        // 开始订阅
        subscription.subscribe()
        this.subscriptions.set(roomId, subscription)
      },
      {
        showLoading: false,
        showError: true
      }
    )
  }

  /**
   * 离开聊天室
   * @param roomId 聊天室ID
   */
  async leaveChatRoom(roomId: string): Promise<ServiceResponse<void>> {
    return this.handleOperation(
      async () => {
        if (this.subscriptions.has(roomId)) {
          this.subscriptions.get(roomId).unsubscribe()
          this.subscriptions.delete(roomId)
          
          this.log('info', `已离开聊天室: ${roomId}`)
        }
      },
      {
        showLoading: false,
        showError: false
      }
    )
  }

  /**
   * 发送消息
   * @param roomId 聊天室ID
   * @param content 消息内容
   * @param messageType 消息类型
   * @param metadata 消息元数据
   * @param replyTo 回复的消息ID
   */
  async sendMessage(
    roomId: string,
    content: string,
    messageType: ChatMessage['messageType'] = 'text',
    metadata?: ChatMessage['metadata'],
    replyTo?: string
  ): Promise<ServiceResponse<{messageId: string}>> {
    return this.executeWithLogin(
      async () => {
        if (!content.trim() && messageType === 'text') {
          throw new Error('消息内容不能为空')
        }

        const messageData = {
          roomId,
          content,
          messageType,
          metadata,
          replyTo,
          clientId: this.generateMessageId()
        }

        this.log('info', `发送消息到聊天室: ${roomId}`, { messageType, contentLength: content.length })

        // 这里应该调用实际的API
        // const response = await messagingApi.sendMessage(messageData)
        // return response.data

        return {
          messageId: messageData.clientId
        }
      },
      {
        showLoading: false,
        showError: true
      }
    )
  }

  /**
   * 获取聊天记录
   * @param roomId 聊天室ID
   * @param page 页码
   * @param pageSize 每页数量
   * @param beforeMessageId 获取指定消息之前的消息
   */
  async getChatHistory(
    roomId: string,
    page: number = 1,
    pageSize: number = 20,
    beforeMessageId?: string
  ): Promise<ServiceResponse<{list: ChatMessage[], hasMore: boolean}>> {
    return this.executeWithLogin(
      async () => {
        this.log('info', `获取聊天记录: ${roomId}`, { page, pageSize, beforeMessageId })

        // 这里应该调用实际的API
        // const response = await messagingApi.getChatHistory({
        //   roomId, page, pageSize, beforeMessageId
        // })
        // return response.data

        return {
          list: [],
          hasMore: false
        }
      },
      {
        showLoading: page === 1,
        loadingText: '加载聊天记录...',
        showError: true
      }
    )
  }

  /**
   * 标记消息为已读
   * @param roomId 聊天室ID
   * @param messageIds 消息ID列表
   */
  async markMessagesAsRead(roomId: string, messageIds: string[]): Promise<ServiceResponse<void>> {
    return this.executeWithLogin(
      async () => {
        if (messageIds.length === 0) return

        this.log('info', `标记消息已读: ${roomId}`, { count: messageIds.length })

        // 这里应该调用实际的API
        // await messagingApi.markMessagesAsRead(roomId, messageIds)
      },
      {
        showLoading: false,
        showError: false
      }
    )
  }

  /**
   * 删除消息
   * @param messageId 消息ID
   * @param forEveryone 是否对所有人删除
   */
  async deleteMessage(messageId: string, forEveryone: boolean = false): Promise<ServiceResponse<void>> {
    return this.executeWithLogin(
      async () => {
        this.log('info', `删除消息: ${messageId}`, { forEveryone })

        // 这里应该调用实际的API
        // await messagingApi.deleteMessage(messageId, forEveryone)
      },
      {
        showLoading: true,
        loadingText: '删除消息中...',
        showError: true
      }
    )
  }

  /**
   * 添加消息处理器
   * @param roomId 聊天室ID
   * @param handler 消息处理函数
   */
  addMessageHandler(roomId: string, handler: (message: ChatMessage) => void): void {
    if (!this.messageHandlers.has(roomId)) {
      this.messageHandlers.set(roomId, [])
    }
    this.messageHandlers.get(roomId)!.push(handler)
  }

  /**
   * 移除消息处理器
   * @param roomId 聊天室ID
   * @param handler 消息处理函数
   */
  removeMessageHandler(roomId: string, handler: Function): void {
    const handlers = this.messageHandlers.get(roomId)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 获取在线用户列表
   * @param roomId 聊天室ID
   */
  async getOnlineUsers(roomId: string): Promise<ServiceResponse<ChatUser[]>> {
    return this.executeWithLogin(
      async () => {
        this.log('info', `获取在线用户: ${roomId}`)

        // 这里应该调用实际的API
        // const response = await messagingApi.getOnlineUsers(roomId)
        // return response.data

        return []
      },
      {
        showLoading: false,
        showError: true
      }
    )
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): 'disconnected' | 'connecting' | 'connected' {
    return this.connectionStatus
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.centrifuge) return

    this.centrifuge.on('connecting', () => {
      this.connectionStatus = 'connecting'
      this.log('info', '正在连接聊天服务器...')
    })

    this.centrifuge.on('connected', () => {
      this.connectionStatus = 'connected'
      this.log('info', '聊天服务器连接成功')
    })

    this.centrifuge.on('disconnected', (ctx: any) => {
      this.connectionStatus = 'disconnected'
      this.log('warn', '聊天服务器连接断开', ctx)
    })

    this.centrifuge.on('error', (ctx: any) => {
      this.log('error', '聊天服务器连接错误', ctx.error)
    })
  }

  /**
   * 处理接收到的消息
   * @param roomId 聊天室ID
   * @param messageData 消息数据
   */
  private handleIncomingMessage(roomId: string, messageData: any): void {
    try {
      const message: ChatMessage = {
        id: messageData.id,
        roomId,
        senderId: messageData.senderId,
        senderInfo: messageData.senderInfo,
        content: messageData.content,
        messageType: messageData.messageType,
        metadata: messageData.metadata,
        replyTo: messageData.replyTo,
        readBy: messageData.readBy || [],
        createdAt: messageData.createdAt,
        isDeleted: messageData.isDeleted || false
      }

      // 调用消息处理器
      const handlers = this.messageHandlers.get(roomId)
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(message)
          } catch (error) {
            this.log('error', '消息处理器执行错误', error)
          }
        })
      }

      this.log('info', `收到消息: ${roomId}`, { messageId: message.id, type: message.messageType })
    } catch (error) {
      this.log('error', '处理接收消息失败', error)
    }
  }

  /**
   * 获取连接配置
   */
  private async getConnectionConfig(): Promise<ConnectionConfig> {
    this.requireLogin()

    // 这里应该调用API获取连接配置
    // const response = await messagingApi.getConnectionConfig()
    // return response.data

    return {
      centrifugoUrl: import.meta.env.VITE_CENTRIFUGO_URL || 'ws://localhost:8000/connection/websocket',
      token: 'mock_token',
      userId: this.userStore.user?.id || 0
    }
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2)}`
  }
}

// 创建消息服务实例
export const messagingService = new MessagingService()

export default messagingService
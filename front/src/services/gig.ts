/**
 * 零工业务逻辑服务 (Composable / Hooks)
 * 职责：
 * 1. 封装与零工相关的业务逻辑和状态管理。
 * 2. 创建可复用的 Vue Composition API 函数 (Composables)。
 * 3. 调用 api/gig.ts 中定义的纯 API 请求。
 */
import { ref, computed, reactive, watch } from 'vue'
import type { Ref } from 'vue'
import { useRequest } from 'alova/client'

import { useUserStore } from '@/stores'
import GigApi from '@/api/gig'
import { GigStatus, GigApplicationStatus } from '@/constants/gig'
import { showSuccessToast } from '@/utils'
import type {
    Gig,
    ListGigsRequest,
    CreateGigRequest,
    UpdateApplicationStatusRequest,
    ApplyGigRequest,
} from '@/types/gig'

/**
 * @description 管理零工发布/编辑表单的逻辑
 * @param gigId - (可选) 如果是编辑模式，则传入零工ID
 */
export function useGigForm(gigId?: Ref<number | null>) {
    const isEditMode = computed(() => !!gigId?.value);

    // 1. 表单数据
    const formData = reactive<CreateGigRequest>({
        title: '',
        description: '',
        salary: 0,
        salary_unit: 0,
        settlement: 0,
        people_count: 0,
        start_time: '',
        end_time: '',
        address_name: '',
        address: '',
        detail_address: '',
        latitude: 0,
        longitude: 0,
        gender: 0,
        age_min: 0,
        age_max: 0,
        experience: 0,
        education: 0,
        skills: '',
        contact_name: '',
        contact_phone: '',
        is_urgent: false,
        company_name: '',
        tags: [],
        images: [],
        approval_mode: '',
        check_in_method: '',
    });

    // 2. 在编辑模式下，获取初始数据
    const { loading: isLoadingInitialData, error: loadError } = useRequest(
        () => GigApi.detail(gigId?.value!),
        {
            immediate: isEditMode.value && gigId?.value && gigId.value > 0,
            watch: [gigId],
            onSuccess: (res) => {
                // 将获取到的数据填充到表单中
                if (res) {
                    Object.assign(formData, res);
                }
            }
        }
    );

    // 3. 提交表单 (创建或更新)
    const { loading: isSubmitting, error: submitError, send: submit } = useRequest(
        () => {
            const data = { ...formData };
            // 数据预处理，例如金额转换
            if (data.salary) data.salary = Math.round(data.salary * 100);

            return (isEditMode.value
                ? GigApi.update(gigId?.value!, data)
                : GigApi.create(data)) as any;
        },
        {
            immediate: false,
            onSuccess: () => {
                // 提交成功后的处理
                showSuccessToast(isEditMode.value ? '更新成功' : '发布成功');
            },
        }
    );

    return {
        formData,
        isEditMode,
        isLoadingInitialData,
        loadError,
        isSubmitting,
        submitError,
        submit,
    }
}

/**
 * @description 管理我的零工列表（manage.vue 页面）
 * @param statusFilter - 一个包含状态过滤条件的 ref 对象 (e.g., 'all', 'recruiting')
 */
export function useManageGigs(statusFilter: Ref<ListGigsRequest>) {
    // 1. 获取列表的 useRequest Hook
    const {
        data: gigs,
        loading: isLoading,
        error: fetchError,
        send: refresh
    } = useRequest(
        () => GigApi.listMine(statusFilter.value),
        {
            watch: [statusFilter], // 监听 statusFilter 的变化，自动重新请求
            immediate: true,
            initialData: { list: [] as any, total: 0 },
        }
    )

    // 2. 删除零工的 useRequest Hook
    const { loading: isDeleting, send: deleteGig } = useRequest(
        (gigId: number) => GigApi.delete(gigId),
        {
            immediate: false,
            onSuccess: () => {
                // 删除成功后自动刷新列表
                showSuccessToast('删除成功');
                refresh();
            },
        }
    );

    // 3. 返回页面所需的数据和方法
    return {
        gigs: computed(() => gigs.value?.list || []),
        isLoading,
        fetchError,
        isDeleting,
        deleteGig,
        refresh,
    }
}

/**
 * @description 管理特定零工的报名者列表及审核操作
 * @param gigId - 一个包含 gig ID 的 ref 对象
 */
export function useGigApplicants(gigId: Ref<number>) {
    // 1. 获取报名列表的 useRequest Hook
    const {
        data: applicants,
        loading: isLoading,
        error: fetchError,
        send: refresh
    } = useRequest(
        () => GigApi.getApplications(gigId.value, { page: 1, page_size: 100 }),
        {
            // 延迟到 gigId 有值时再发起请求
            immediate: false,
            initialData: { list: [], total: 0 },
        }
    )

    // 监听 gigId 变化，当有效时自动发起请求
    watch(
        gigId,
        (newGigId) => {
            if (newGigId > 0) {
                refresh();
            }
        },
        { immediate: true }
    );

    // 2. 执行审核操作的 useRequest Hook
    const {
        loading: isReviewing,
        error: reviewError,
        send: review
    } = useRequest(
        (payload: UpdateApplicationStatusRequest) => GigApi.reviewApplication(payload),
        {
            immediate: false, // 手动触发
            onSuccess: () => {
                // 审核成功后自动刷新列表
                showSuccessToast('操作成功');
                refresh();
            },
        }
    );

    // 3. 返回页面所需的数据和方法
    return {
        applicants,
        isLoading,
        fetchError,
        isReviewing,
        reviewError,
        review,
        refresh,
    }
}

/**
 * @description 这是一个可复用的 Composable 函数，用于处理零工详情页的核心业务逻辑。
 * 它封装了获取零工详情、申请状态检测以及所有相关的响应式状态。
 * 优化后的版本优先使用零工详情接口返回的申请状态，减少不必要的API调用。
 * @param gigId - 一个包含 gig ID 的 ref 对象。
 */
export function useGigDetail(gigId: Ref<number>) {
    const userStore = useUserStore()

    // =================================================================
    // 1. 主请求：获取零工详情（包含申请状态）
    // =================================================================
    const {
        data: gigDetail,
        loading: isLoading,
        error: fetchError,
        send: refreshGigDetail
    } = useRequest(
        () => GigApi.detail(gigId.value),
        {
            // 延迟到 gigId 有值时再发起请求
            immediate: false,
            // 当 gigId 变化且大于0时自动重新请求
            watch: [gigId],
            // 设置初始数据格式，避免空数据报错
            initialData: {} as Gig,
        }
    )

    // 使用 watch 来控制主请求的触发时机
    watch(
        gigId,
        (newGigId) => {
            // 当 gigId 有效时，发送主请求
            if (newGigId > 0) {
                refreshGigDetail()
            }
        },
        { immediate: true }
    )

    // =================================================================
    // 2. 申请状态相关的计算属性
    // =================================================================

    /**
     * 申请状态信息 - 优先使用零工详情接口返回的数据
     * 格式化为统一的申请状态对象，避免额外的API调用
     */
    const applicationStatusInfo = computed(() => {
        if (!gigDetail.value || !userStore.isLogin) {
            return null;
        }

        // 使用零工详情接口返回的申请状态，避免重复调用
        return {
            has_applied: gigDetail.value.has_applied || false,
            gig_id: gigDetail.value.id,
            user_id: userStore.user?.id || 0,
        };
    });

    /**
     * 判断当前用户是否为零工发布者
     */
    const isPublisher = computed(() => {
        return gigDetail.value &&
            userStore.user &&
            gigDetail.value.user_id === userStore.user.id;
    });

    /**
     * 判断当前用户是否可以申请这个零工
     * 综合考虑零工状态、用户登录状态、是否为发布者等因素
     */
    const canApply = computed(() => {
        // 基础检查：数据是否加载完成
        if (!gigDetail.value || !gigDetail.value.id) {
            return false;
        }

        // 检查用户是否已登录
        if (!userStore.isLogin) {
            return false;
        }

        // 检查是否为发布者（不能申请自己发布的零工）
        if (isPublisher.value) {
            return false;
        }

        // 检查是否已申请
        if (gigDetail.value.has_applied) {
            return false;
        }

        // 检查零工状态是否为招聘中
        if (gigDetail.value.status !== GigStatus.Recruiting) {
            return false;
        }

        // 检查名额是否已满
        if (gigDetail.value.people_count &&
            gigDetail.value.current_people_count >= gigDetail.value.people_count) {
            return false;
        }

        return true;
    })

    // =================================================================
    // 3. 刷新方法
    // =================================================================

    /**
     * 手动刷新申请状态 - 仅在必要时使用（如申请成功后）
     * 这会重新获取零工详情，包含最新的申请状态
     */
    const refreshApplicationStatus = async () => {
        if (gigId.value > 0) {
            await refreshGigDetail();
        }
    };

    // =================================================================
    // 4. 返回给组件使用的接口
    // =================================================================
    return {
        // 数据
        gigDetail,
        applicationStatusInfo,
        isLoading,
        fetchError,

        // 计算属性
        canApply,
        isPublisher,

        // 方法
        refreshGigDetail,
        refreshApplicationStatus,
    }
}

/**
 * @description 管理零工申请相关的业务逻辑
 */
export function useApplyForGig() {
    const { loading: isApplying, error: applyError, send: apply } = useRequest(
        (payload: ApplyGigRequest) => GigApi.apply(payload),
        {
            immediate: false, // 仅在手动调用 apply() 时执行
            onSuccess: () => {
                // 申请成功后的处理
                showSuccessToast('申请成功');
            },
        }
    );

    return { isApplying, applyError, apply }
}

/**
 * @description 管理零工日历页面相关的数据和操作
 * 封装月度统计和每日零工数据的获取和管理
 */
export function useGigCalendar() {
    // 月度统计数据的请求
    const {
        data: monthlyStatsData,
        loading: isLoadingMonthlyStats,
        error: monthlyStatsError,
        send: fetchMonthlyStats
    } = useRequest(
        (year: number, month: number) => GigApi.getMonthlyStats(year, month),
        { immediate: false, initialData: { total_gigs: 0, completed_gigs: 0, total_income: 0, daily_stats: [] } }
    )

    // 每日零工数据的请求
    const {
        data: dailyGigsData,
        loading: isLoadingDailyGigs,
        error: dailyGigsError,
        send: fetchDailyGigs
    } = useRequest(
        (date: string) => GigApi.getDailyGigs(date),
        { immediate: false, initialData: { gigs: [], applications: [] } }
    )

    // 解构月度统计数据
    const monthlyStats = computed(() => monthlyStatsData.value)
    const totalGigs = computed(() => monthlyStats.value?.total_gigs || 0)
    const completedGigs = computed(() => monthlyStats.value?.completed_gigs || 0)
    const pendingGigs = computed(() => totalGigs.value - completedGigs.value)

    // 解构每日零工数据
    const dailyGigs = computed(() => dailyGigsData.value?.gigs || [])
    const dailyApplications = computed(() => dailyGigsData.value?.applications || [])

    return {
        // 月度统计相关
        monthlyStats,
        totalGigs,
        completedGigs,
        pendingGigs,
        isLoadingMonthlyStats,
        monthlyStatsError,
        fetchMonthlyStats,

        // 每日零工相关
        dailyGigs,
        dailyApplications,
        isLoadingDailyGigs,
        dailyGigsError,
        fetchDailyGigs,
    }
}

/**
 * @description 管理零工列表页面（seeker）相关的数据和操作
 * 封装搜索、排序、分页等功能的状态管理
 */
export function useGigList() {
    // 当前搜索和筛选参数
    const searchParams = ref<ListGigsRequest>({
        page: 1,
        page_size: 10,
        sort_by: 'latest',
        keyword: '',
        user_lat: undefined,
        user_lon: undefined,
    })

    // 零工列表数据的请求
    const {
        data: gigsData,
        loading: isLoading,
        error: fetchError,
        send: fetchGigs
    } = useRequest(
        GigApi.list(searchParams.value),
        {
            immediate: true,
            watch: [searchParams], // 监听搜索参数变化，自动重新请求
            initialData: { list: [], total: 0 }
        }
    )

    // 解构响应数据
    const gigList = computed(() => gigsData.value?.list || []);
    const total = computed(() => gigsData.value?.total || 0);

    // 更新搜索参数的方法
    const updateSearchParams = (newParams: Partial<ListGigsRequest>) => {
        searchParams.value = { ...searchParams.value, ...newParams }
    }

    // 搜索方法
    const search = (keyword: string) => {
        updateSearchParams({ keyword, page: 1 }) // 搜索时重置页码
    }

    // 切换排序
    const changeSort = (sortBy: ListGigsRequest['sort_by']) => {
        updateSearchParams({ sort_by: sortBy, page: 1 }) // 切换排序时重置页码
    }

    // 设置用户位置
    const setUserLocation = (latitude: number, longitude: number) => {
        updateSearchParams({ user_lat: latitude, user_lon: longitude })
    }

    // 刷新列表
    const refresh = () => {
        fetchGigs()
    }

    return {
        // 数据
        gigList,
        total,
        searchParams: computed(() => searchParams.value),

        // 状态
        isLoading,
        fetchError,

        // 方法
        updateSearchParams,
        search,
        changeSort,
        setUserLocation,
        refresh,
        fetchGigs,
    }
}

/**
 * @description 管理招聘者仪表板页面（recruiter）相关的数据和操作
 * 封装我的发布列表和最新申请列表的数据管理
 */
export function useRecruiterDashboard() {
    // 获取最近发布的零工 (用于仪表板显示)
    const {
        data: recentPublishedData,
        loading: isLoadingGigs,
        error: gigsError,
        send: fetchRecentPublished
    } = useRequest(
        () => GigApi.listMine({ page: 1, page_size: 3 }),
        {
            immediate: true,
            initialData: { list: [], total: 0 }
        }
    )

    // 获取最新申请 (用于仪表板显示)
    const {
        data: recentApplicationsData,
        loading: isLoadingApps,
        error: appsError,
        send: fetchRecentApplications
    } = useRequest(
        () => GigApi.listMyApplications({ page: 1, page_size: 5 }),
        {
            immediate: true,
            initialData: { list: [], total: 0 }
        }
    )

    // 解构数据
    const recentPublished = computed(() => recentPublishedData.value?.list || []);
    const recentApplications = computed(() => recentApplicationsData.value?.list || []);

    // 刷新所有数据
    const refreshAll = async () => {
        await Promise.all([
            fetchRecentPublished(),
            fetchRecentApplications()
        ])
    }

    return {
        // 我的发布数据
        recentPublished,
        isLoadingGigs,
        gigsError,
        fetchRecentPublished,

        // 最新申请数据
        recentApplications,
        isLoadingApps,
        appsError,
        fetchRecentApplications,

        // 通用方法
        refreshAll,
    }
}
import type { Conversation, Message } from '@/types/im';

class ImService {
  async fetchConversations(): Promise<Conversation[]> {
    // Simulate API call
    return new Promise(resolve => {
      setTimeout(() => {
        resolve([
          {
            id: 'conv-1',
            name: '<PERSON>',
            avatar: 'https://picsum.photos/seed/alice/100/100',
            isOnline: true,
            unreadCount: 2,
            lastMessage: { 
              id: 'msg-1', 
              type: 'text', 
              sender: { id: 'user-2', name: '<PERSON>', avatar: 'https://picsum.photos/seed/alice/100/100' }, 
              payload: { content: 'Hello there!' }, 
              timestamp: Date.now() - 10000, 
              status: 'sent' 
            },
          },
          {
            id: 'conv-2',
            name: '<PERSON>',
            avatar: 'https://picsum.photos/seed/bob/100/100',
            isOnline: false,
            unreadCount: 0,
            lastMessage: { 
              id: 'msg-2', 
              type: 'image', 
              sender: { id: 'user-1', name: '<PERSON>', avatar: 'https://picsum.photos/seed/user-1/100/100' }, 
              payload: { url: 'https://picsum.photos/id/237/200/150', imageWidth: 200, imageHeight: 150 }, 
              timestamp: Date.now() - 200000, 
              status: 'sent' 
            },
          },
          {
            id: 'conv-3',
            name: 'Charlie',
            avatar: 'https://picsum.photos/seed/charlie/100/100',
            isOnline: true,
            unreadCount: 0,
            lastMessage: { 
              id: 'msg-3', 
              type: 'job', 
              sender: { id: 'user-3', name: 'Charlie', avatar: 'https://picsum.photos/seed/charlie/100/100' }, 
              payload: { jobId: 'job-456', title: 'UI设计师', salary: '10-20K', companyName: '设计工作室', companyLogo: 'https://picsum.photos/seed/company-design/100/100', location: '上海' }, 
              timestamp: Date.now() - 300000, 
              status: 'sent' 
            },
          },
        ]);
      }, 500);
    });
  }

  async fetchMessages(conversationId: string, pageNo: number, pageSize: number): Promise<Message[]> {
    // Simulate API call
    return new Promise(resolve => {
      setTimeout(() => {
        const mockMessages: Message[] = [];
        for (let i = 0; i < pageSize; i++) {
          const id = `hist-${conversationId}-${pageNo}-${i}`;
          const isSelf = i % 2 === 0;
          const sender = isSelf ? { id: 'user-1', name: 'Me', avatar: 'https://picsum.photos/seed/user-1/100/100' } : { id: 'user-2', name: 'Alice', avatar: 'https://picsum.photos/seed/alice/100/100' };
          const timestamp = Date.now() - (pageNo * pageSize + i) * 60000;

          if (i % 4 === 0) {
            mockMessages.push({
              id,
              type: 'text',
              sender,
              payload: { content: `这是一条历史文本消息 ${pageNo}-${i}。` },
              timestamp,
              status: 'sent',
            });
          } else if (i % 4 === 1) {
            mockMessages.push({
              id,
              type: 'image',
              sender,
              payload: { url: `https://picsum.photos/id/${100 + i}/300/200`, imageWidth: 300, imageHeight: 200 },
              timestamp,
              status: 'sent',
            });
          } else if (i % 4 === 2) {
            mockMessages.push({
              id,
              type: 'video',
              sender,
              payload: { videoUrl: 'http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4', thumbnailUrl: `https://picsum.photos/id/${200 + i}/300/200`, videoWidth: 300, videoHeight: 200, duration: 60 },
              timestamp,
              status: 'sent',
            });
          } else {
            mockMessages.push({
              id,
              type: 'job',
              sender,
              payload: { jobId: `job-${id}`, title: `招聘${i}号`, salary: '8-15K', companyName: `公司${i}`, companyLogo: `https://picsum.photos/seed/company-${i}/100/100`, location: '北京' },
              timestamp,
              status: 'sent',
            });
          }
        }
        resolve(mockMessages.reverse()); // Newest first for z-paging
      }, 500);
    });
  }
}

export const imService = new ImService();
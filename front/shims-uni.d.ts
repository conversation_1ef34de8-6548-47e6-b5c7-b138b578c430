/// <reference types='@dcloudio/types' />
import 'vue'
declare module '@vue/runtime-core' {
  type Hooks = App.AppInstance & Page.PageInstance;

  interface ComponentCustomOptions extends Hooks {

  }
}

// Vue 3
declare module '*.vue' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 添加SVG文件模块声明
declare module '*.svg' {
  const content: string
  export default content
}

// 添加静态资源模块声明
declare module '*.png'
declare module '*.jpg'
declare module '*.jpeg'
declare module '*.gif'

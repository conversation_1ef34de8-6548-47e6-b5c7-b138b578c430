/* 
 * Job Search App Interface Gradient Analysis
 * 基于提供的设计图分析得出的渐变背景色
 * 应用场景：求职招聘模块顶部背景、用户欢迎区域背景
 */

/* 主要渐变背景 - 顶部区域 */
.job-search-header-gradient {
  background: linear-gradient(180deg, 
    #E8F0FE 0%,     /* 浅蓝紫色 - 顶部 */
    #F3F7FF 30%,    /* 更浅的蓝白色 */
    #FAFBFF 60%,    /* 接近白色的淡蓝 */
    #FFFFFF 100%    /* 纯白色 - 底部 */
  );
}

/* 替代方案1 - 更柔和的渐变 */
.job-search-soft-gradient {
  background: linear-gradient(180deg, 
    #EDF2FF 0%,     /* 柔和浅蓝 */
    #F8FAFF 50%,    /* 淡蓝白 */
    #FFFFFF 100%    /* 白色 */
  );
}

/* 替代方案2 - 三色渐变 */
.job-search-triple-gradient {
  background: linear-gradient(180deg, 
    #E6EFFF 0%,     /* 浅蓝色调 */
    #F0F5FF 40%,    /* 中间过渡色 */
    #FFFFFF 100%    /* 白色结束 */
  );
}

/* 应用示例 - 页面容器 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, 
    #E8F0FE 0%, 
    #F3F7FF 30%, 
    #FAFBFF 60%, 
    #FFFFFF 100%
  );
}

/* 应用示例 - 顶部欢迎区域 */
.welcome-section {
  padding: 60rpx 32rpx 40rpx;
  background: linear-gradient(180deg, 
    #E8F0FE 0%, 
    #F3F7FF 50%, 
    #FFFFFF 100%
  );
  border-radius: 0 0 32rpx 32rpx;
}

/* 应用示例 - 卡片背景渐变 */
.gradient-card {
  background: linear-gradient(135deg, 
    #E8F0FE 0%, 
    #F8FAFF 100%
  );
  border-radius: 16rpx;
  padding: 32rpx;
}

/*
 * 颜色说明：
 * #E8F0FE - 主要起始色，浅蓝紫调
 * #F3F7FF - 中间过渡色，淡蓝白
 * #FAFBFF - 接近白色的极淡蓝
 * #FFFFFF - 纯白色结束
 * 
 * 渐变方向：从上到下 (180deg)
 * 适用场景：
 * 1. 求职招聘模块顶部背景
 * 2. 用户欢迎界面
 * 3. 搜索区域背景
 * 4. 卡片容器背景
 * 5. 页面整体背景
 */

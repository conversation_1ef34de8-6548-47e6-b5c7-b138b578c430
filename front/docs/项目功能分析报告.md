# 项目功能分析报告

## 交友模块

### 主交友页面 (`src/pages/dating/index.vue`)
- **功能**：带标签导航的中心枢纽
- **标签**：
  - 匹配：基于算法的配对
  - 喜欢：显示收到的喜欢
  - 广场：公共内容流
- **字段**：
  - `currentTab`：当前活动标签状态
  - `navBarTitle`：根据标签动态变化的标题
  - `tabbar`：导航配置

### 广场发布 (`src/pages/dating/square/publish.vue`)
- **功能**：内容创作和发布
- **字段**：
  - `postContent`：文本内容（500字符限制）
  - `selectedImages`：图片对象数组
  - `selectedTopics`：话题标签数组
  - `selectedLocation`：位置字符串
  - `privacyIndex`：可见性设置（0-2）
- **隐藏字段**：
  - `userInfo`：包含姓名/头像
  - `isPublishing`：提交状态标志
- **业务逻辑**：
  - 内容验证（需要文本/图片）
  - 图片选择（最多9张）
  - 话题/位置标记
  - 隐私设置

### 广场详情 (`src/pages/dating/square/detail.vue`)
- **功能**：查看单个帖子
- **字段**：（待分析）
- **业务逻辑**：帖子互动（喜欢/评论）

### 话题选择 (`src/pages/dating/square/topic.vue`)
- **功能**：帖子标签管理
- **字段**：`selectedTopics` 数组
- **业务逻辑**：话题过滤/排序

## 零工模块

### 零工管理 (`src/pages/gig/manage.vue`)
- **功能**：管理发布的零工（零散工作）
- **字段**：
  - `totalPublished`：已发布的零工总数
  - `recruitingCount`：正在招聘的零工数量
  - `totalApplicants`：所有零工的申请者总数
  - `currentFilter`：当前状态过滤器
  - `publishedGigs`：零工对象数组
    - 每个零工包含：id, 标题, 价格, 价格单位, 位置, 工作时间, 工作日期, 状态, 申请人数, 查看次数, 创建时间, 要求（年龄、教育、经验、性别）、标签
- **业务逻辑**：
  - 按状态过滤（招聘中、工作中、已完成、已过期）
  - 每个零工状态的操作（编辑、停止、完成、重新发布、删除）
  - 统计计算

### 零工求职者 (`src/pages/gig/seeker.vue`)
- **功能**：浏览可用的零工
- **字段**：（待分析）
- **业务逻辑**：零工发现、过滤、申请

### 零工招聘者 (`src/pages/gig/recruiter.vue`)
- **功能**：招聘者查看零工
- **字段**：（待分析）
- **业务逻辑**：申请者管理、零工推广

## 房屋模块

### 新房列表 (`src/pages/house/newHouse/index.vue`)
- **功能**：新房源列表
- **字段**：（待分析）
- **业务逻辑**：房源过滤、详情查看

## 求职模块

### 职位管理 (`src/pages/job/manage.vue`)
- **功能**：管理职位发布
- **字段**：（待分析）
- **业务逻辑**：职位状态跟踪、申请者管理

## 帖子模块

### 帖子发布 (`src/pages/post/publish.vue`)
- **功能**：创建和发布内容
- **字段**：（待分析）
- **业务逻辑**：内容创建、调度

### 帖子列表 (`src/pages/post/list.vue`)
- **功能**：浏览已发布的帖子
- **字段**：（待分析）
- **业务逻辑**：内容发现、过滤

### 帖子详情 (`src/pages/post/detail.vue`)
- **功能**：查看帖子详情
- **字段**：（待分析）
- **业务逻辑**：互动跟踪、评论

---

---
type: "always_apply"
description: "globs:"
---
# 项目概述

这是一个基于uni-app cli框架的跨平台应用项目，使用Vue 3和TypeScript开发。
- 项目配置了微信小程序的 Skyline 渲染引擎，需注意兼容处理，官方文档地址为：https://developers.weixin.qq.com/miniprogram/dev/framework/runtime/skyline/introduction.html
组件库使用了 Thorui,文档地址:https://thorui.cn/doc/docs/thorui/tui-card.html;
uvui,文档地址:https://www.uvui.cn/components/intro.html

# 项目功能

- 本项目为一个o2o本地生活微信小程序,核心目标用户为地级市(3、4级)城市、县城用户以及普工
- tabbar包括首页、社区、消息、个人中心
- 首页核心功能模块包含:求职招聘、租房、新房/二手房、交友等热门模块
- 其次包含零工、保洁、保姆月嫂、维修、家居装修、养车用车、等细分功能
- 合理设计项目架构以及项目规划


# 任务开发要求

- 然后再以高级开发工程师和架构师的角色进行项目开发与规划工作，编写高质量、稳定、便于维护的代码
- 每次编写代码前先阅读最新代码，完成后复查代码和类型错误
- 对于要求和要实现的功能先进行思考和规划，选择最优的方案实现

## 主要文件

- 入口文件：[main.ts](mdc:src/main.ts)
- 应用组件：[App.vue](mdc:src/App.vue)
- 页面配置：[pages.json](mdc:src/pages.json)

## 项目结构

- `src/pages`: 所有页面组件
- `src/components`: 通用组件
- `src/stores`: Pinia状态管理
- `src/utils`: 工具函数
- `src/styles`: 样式文件

## 技术栈

- 框架：Vue 3 + TypeScript + uni-app
- 状态管理：Pinia
- UI库：uni-ui、ThorUI
- CSS：UnoCSS + SCSS
- 高性能分页列表组件：z-paging


## 模块页面组件复用
- 每个模块或者目录下的避免单文件代码量过大,适当拆分成组件提高可读性和维护便利

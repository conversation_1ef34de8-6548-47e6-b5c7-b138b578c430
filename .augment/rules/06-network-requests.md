---
type: "always_apply"
---

# 网络请求

本项目使用 [Alova.js](https://alova.js.org/) 作为核心请求库，它是一个轻量级的、支持多种请求策略的请求库。我们在其基础上进行了二次封装，以提供更符合项目需求的、类型安全的请求体验。

## 请求封装

网络请求的核心封装位于 `front/src/utils/alova.ts`，并导出了一个更简洁的实例在 `front/src/utils/request.ts`。

### 核心特性

- **请求适配器**: 自动处理 Uni-app 的 `uni.request`。
- **请求拦截器**: 在请求发送前，自动附加认证 `token`。
- **响应拦截器**:
  - 自动处理业务状态码，对非 `0` 的 `code` 进行统一的错误提示。
  - 对 `401` 未授权错误进行特殊处理，清除本地用户状态并引导至登录页。
  - 成功时直接返回 `data` 字段，简化数据获取。
- **类型安全**: 封装的 `http` 实例提供了强大的 TypeScript 类型推断支持。

### 请求实例

我们推荐在所有业务代码中使用从 `front/src/utils/request.ts` 导出的 `http` 实例。

```ts
import { http } from '@/utils/request';
```

该实例严格遵循项目制定的 **GET/POST** 方法规范 [[memory:2561814]]。

- `http.get<T>()`: 发起 GET 请求，用于数据查询。
- `http.post<T>()`: 发起 POST 请求，用于数据提交。

## 使用示例

封装后的 `http` 实例会自动处理 `ResponseData` 的外层包装，您只需要关心核心的业务数据类型。

```vue
<script setup lang="ts">
import { http } from '@/utils/request'

// 1. 定义核心业务数据的类型
interface UserInfo {
  id: number;
  username: string;
  avatar: string;
}

interface LoginResult {
  token: string;
}

// 2. GET 请求示例
async function fetchUserInfo() {
  try {
    // 直接获取 data 字段，类型为 UserInfo
    const userInfo = await http.get<UserInfo>('/user/info');
    console.log(userInfo.username); 
  } catch (error) {
    // 错误在拦截器中已处理，这里可根据业务需要额外处理
    console.error('获取用户信息失败', error);
  }
}

// 3. POST 请求示例
async function login(username: string, password: string) {
  try {
    const loginData = await http.post<LoginResult>('/login', {
      username,
      password,
    });
    // 保存 token
    console.log(loginData.token);
    return loginData;
  } catch (error) {
    console.error('登录失败', error);
    return null;
  }
}
</script>
```

### 类型提示

当您使用 `http.get` 或 `http.post` 时，VSCode 会自动提示泛型 `T` 对应的返回数据类型，无需手动包裹 `ResponseData`。

- **错误用法**: `http.get<ResponseData<UserInfo>>(...)`
- **正确用法**: `http.get<UserInfo>(...)`

  }
}
</script>
```

---
alwaysApply: true
---
# Go 开发工具和流程指南

## 📋 目录

- [环境配置](#环境配置)
- [代码质量工具](#代码质量工具)
- [日常开发流程](#日常开发流程)
- [IDE 配置](#ide-配置)
- [常见问题](#常见问题)

## 环境配置

### 系统要求

- Go 1.21+ (推荐 1.24+)
- Git
- Make

### 快速开始

1. **安装开发工具**
   ```bash
   # 使用脚本安装
   ./backend/scripts/install-tools.sh
   
   # 或者使用 Makefile
   cd backend && make install-tools
   ```

2. **验证安装**
   ```bash
   cd backend
   # 运行基础检查
   make check
   
   # 运行完整检查
   make check-all
   ```

## 代码质量工具

### 工具概览

| 工具          | 用途                 | 说明                      |
| ------------- | -------------------- | ------------------------- |
| `goimports`   | 代码格式化和导入管理 | 官方推荐，支持自动导入    |
| `go vet`      | 静态检查             | Go 官方工具，检查常见错误 |
| `staticcheck` | 静态分析             | 第三方工具，深度分析      |
| `gosec`       | 安全检查             | 安全漏洞扫描              |
| `errcheck`    | 错误检查             | 检查未处理的错误          |

### 使用方法

#### 单独使用
```bash
# 切换到后端目录
cd backend

# 代码格式化
goimports -w .

# 静态检查
go vet ./...

# 静态分析
staticcheck ./...

# 安全检查
gosec ./...
```

#### Makefile 命令

```bash
# 切换到后端目录
cd backend

# 基础检查（日常推荐）
make check

# 完整检查（提交前推荐）
make check-all

# 单独工具
make fmt          # 格式化
make imports      # 整理导入
make vet          # 静态检查
make staticcheck  # 静态分析
make sec          # 安全检查
```

### 工具输出说明

#### staticcheck 输出
```
internal/service/verification_svc.go:437:31: func (*verificationService).decryptData is unused (U1000)
```
- `U1000`: 未使用的代码
- 建议：删除未使用的函数

#### gosec 输出
```
[pkg/storage/qiniu.go:186] - G115: integer overflow conversion int64 -> uint64
```
- `G115`: 整数溢出风险
- 建议：添加边界检查或使用安全的类型转换

## 日常开发流程

### 开发前

1. **拉取最新代码**
   ```bash
   git pull origin main
   ```

2. **检查代码质量**
   ```bash
   cd backend && make check
   ```

### 开发中

1. **编写代码**
   - 遵循 [Go 编程规范](./09-golang-coding-standards.mdc)
   - 编写必要的测试
   - 添加适当的错误处理

2. **实时检查**（如果使用 Cursor/VS Code）
   - 保存时自动格式化
   - 实时显示 lint 错误

### 提交前

1. **完整质量检查**
   ```bash
   cd backend && make check-all
   ```

2. **运行测试**
   ```bash
   cd backend && make test
   ```

3. **修复发现的问题**
   - 根据工具输出修复问题
   - 必要时添加 `#nosec` 注释（安全问题已确认无害时）

4. **提交代码**
   ```bash
   git add .
   git commit -m "feat: 添加新功能"
   ```

## IDE 配置

### Cursor/VS Code 配置

项目已包含配置文件 (`backend/.vscode/settings.json`)，提供：

- 保存时自动格式化
- 自动整理导入
- 实时 lint 检查
- 优化的 Go 开发体验

### 推荐扩展

1. **Go** (golang.go) - 官方 Go 扩展
2. **staticcheck** - 静态分析支持

### 手动配置

如果需要自定义配置，编辑 `backend/.vscode/settings.json`：

```json
{
  "go.formatTool": "goimports",
  "go.lintTool": "staticcheck", 
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit"
  }
}
```

## 常见问题

### Q: staticcheck 检查太严格怎么办？

A: 可以在特定代码行添加注释忽略检查：
```go
func unused() { // staticcheck:ignore U1000 这是临时函数
    // ...
}
```

### Q: gosec 报告了误报怎么办？

A: 确认安全后，可以添加 `#nosec` 注释：
```go
hash := md5.Sum([]byte(data)) // #nosec G401 -- 用于非敏感数据校验
```

### Q: goimports 格式化与团队不一致？

A: 确保所有团队成员使用相同版本的 goimports，建议通过 `make install-tools` 统一安装。

### Q: 如何跳过某个目录的检查？

A: 在 Makefile 中修改命令，例如：
```bash
staticcheck $(shell go list ./... | grep -v /vendor/)
```

### Q: 如何在 CI/CD 中集成这些工具？

A: 在 GitHub Actions 或其他 CI 系统中：
```yaml
- name: Run Go quality checks
  run: |
    cd backend
    make check-all
```

## 性能建议

1. **增量检查**: 使用 `-diff` 参数只检查修改的文件
2. **并行构建**: 使用 `go build -race` 检查并发问题
3. **定期清理**: 运行 `make clean-cache` 清理模块缓存

## 持续改进

- 定期更新工具版本
- 关注新的 Go 版本特性
- 根据项目需求调整规范
- 收集团队反馈，优化工作流程

---

💡 **记住**: 工具是为了提高代码质量，不是为了增加负担。合理使用，保持代码的简洁和可维护性。# Go 开发工具和流程指南

## 📋 目录

- [环境配置](#环境配置)
- [代码质量工具](#代码质量工具)
- [日常开发流程](#日常开发流程)
- [IDE 配置](#ide-配置)
- [常见问题](#常见问题)

## 环境配置

### 系统要求

- Go 1.21+ (推荐 1.24+)
- Git
- Make

### 快速开始

1. **安装开发工具**
   ```bash
   # 使用脚本安装
   ./backend/scripts/install-tools.sh
   
   # 或者使用 Makefile
   cd backend && make install-tools
   ```

2. **验证安装**
   ```bash
   cd backend
   # 运行基础检查
   make check
   
   # 运行完整检查
   make check-all
   ```

## 代码质量工具

### 工具概览

| 工具          | 用途                 | 说明                      |
| ------------- | -------------------- | ------------------------- |
| `goimports`   | 代码格式化和导入管理 | 官方推荐，支持自动导入    |
| `go vet`      | 静态检查             | Go 官方工具，检查常见错误 |
| `staticcheck` | 静态分析             | 第三方工具，深度分析      |
| `gosec`       | 安全检查             | 安全漏洞扫描              |
| `errcheck`    | 错误检查             | 检查未处理的错误          |

### 使用方法

#### 单独使用
```bash
# 切换到后端目录
cd backend

# 代码格式化
goimports -w .

# 静态检查
go vet ./...

# 静态分析
staticcheck ./...

# 安全检查
gosec ./...
```

#### Makefile 命令

```bash
# 切换到后端目录
cd backend

# 基础检查（日常推荐）
make check

# 完整检查（提交前推荐）
make check-all

# 单独工具
make fmt          # 格式化
make imports      # 整理导入
make vet          # 静态检查
make staticcheck  # 静态分析
make sec          # 安全检查
```

### 工具输出说明

#### staticcheck 输出
```
internal/service/verification_svc.go:437:31: func (*verificationService).decryptData is unused (U1000)
```
- `U1000`: 未使用的代码
- 建议：删除未使用的函数

#### gosec 输出
```
[pkg/storage/qiniu.go:186] - G115: integer overflow conversion int64 -> uint64
```
- `G115`: 整数溢出风险
- 建议：添加边界检查或使用安全的类型转换

## 日常开发流程

### 开发前

1. **拉取最新代码**
   ```bash
   git pull origin main
   ```

2. **检查代码质量**
   ```bash
   cd backend && make check
   ```

### 开发中

1. **编写代码**
   - 遵循 [Go 编程规范](./09-golang-coding-standards.mdc)
   - 编写必要的测试
   - 添加适当的错误处理

2. **实时检查**（如果使用 Cursor/VS Code）
   - 保存时自动格式化
   - 实时显示 lint 错误

### 提交前

1. **完整质量检查**
   ```bash
   cd backend && make check-all
   ```

2. **运行测试**
   ```bash
   cd backend && make test
   ```

3. **修复发现的问题**
   - 根据工具输出修复问题
   - 必要时添加 `#nosec` 注释（安全问题已确认无害时）

4. **提交代码**
   ```bash
   git add .
   git commit -m "feat: 添加新功能"
   ```

## IDE 配置

### Cursor/VS Code 配置

项目已包含配置文件 (`backend/.vscode/settings.json`)，提供：

- 保存时自动格式化
- 自动整理导入
- 实时 lint 检查
- 优化的 Go 开发体验

### 推荐扩展

1. **Go** (golang.go) - 官方 Go 扩展
2. **staticcheck** - 静态分析支持

### 手动配置

如果需要自定义配置，编辑 `backend/.vscode/settings.json`：

```json
{
  "go.formatTool": "goimports",
  "go.lintTool": "staticcheck", 
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit"
  }
}
```

## 常见问题

### Q: staticcheck 检查太严格怎么办？

A: 可以在特定代码行添加注释忽略检查：
```go
func unused() { // staticcheck:ignore U1000 这是临时函数
    // ...
}
```

### Q: gosec 报告了误报怎么办？

A: 确认安全后，可以添加 `#nosec` 注释：
```go
hash := md5.Sum([]byte(data)) // #nosec G401 -- 用于非敏感数据校验
```

### Q: goimports 格式化与团队不一致？

A: 确保所有团队成员使用相同版本的 goimports，建议通过 `make install-tools` 统一安装。

### Q: 如何跳过某个目录的检查？

A: 在 Makefile 中修改命令，例如：
```bash
staticcheck $(shell go list ./... | grep -v /vendor/)
```

### Q: 如何在 CI/CD 中集成这些工具？

A: 在 GitHub Actions 或其他 CI 系统中：
```yaml
- name: Run Go quality checks
  run: |
    cd backend
    make check-all
```

## 性能建议

1. **增量检查**: 使用 `-diff` 参数只检查修改的文件
2. **并行构建**: 使用 `go build -race` 检查并发问题
3. **定期清理**: 运行 `make clean-cache` 清理模块缓存

## 持续改进

- 定期更新工具版本
- 关注新的 Go 版本特性
- 根据项目需求调整规范
- 收集团队反馈，优化工作流程

---

💡 **记住**: 工具是为了提高代码质量，不是为了增加负担。合理使用，保持代码的简洁和可维护性。